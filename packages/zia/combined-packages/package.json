{"name": "@zia/combined-packages", "version": "1.0.0", "description": "Combined ZIA Packages", "type": "module", "exports": {"./manifest": "./manifest.json", "./styles/*": "./styles/*"}, "files": ["dist", "styles"], "scripts": {"build": "tsc && node ./build/build.js", "postinstall": "tsc && node ./build/copy.js", "copy": "tsc && node ./build/copy.js"}, "dependencies": {"@zuxp/zia-62.2504": "npm:@zuxp/zia@62.2504.8-6fc6fb7bea45", "@zuxp/zia-62.2505": "npm:@zuxp/zia@62.2505.3-000534e728c8", "@zuxp/zia-62.2506": "npm:@zuxp/zia@62.2506.0-4f320a0d58f7", "@zuxp/zia-63.0": "npm:@zuxp/zia@63.0.32-f485025cc48b"}, "devDependencies": {"typescript": "5.7.3", "prettier": "3.6.2", "@types/node": "^22.13.10", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*"}}