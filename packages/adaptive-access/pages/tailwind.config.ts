import type { Config } from "tailwindcss";
import tailwindDefault from "tailwindcss/defaultConfig";
import { ndsTailwindPreset } from "@zs-nimbus/core";

const config: Config = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  presets: [
    // Tailwind base preset must come first.
    tailwindDefault,
    ndsTailwindPreset,
  ],
  darkMode: ["selector", '[data-mode="dark"]'],
  theme: {
    extend: {},
  },
  plugins: [],
};

export default config;
