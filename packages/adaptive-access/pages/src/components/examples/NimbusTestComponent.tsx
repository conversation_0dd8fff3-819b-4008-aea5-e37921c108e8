import React from "react";
import { But<PERSON> } from "@zs-nimbus/core";

/**
 * Simple Nimbus Button example to verify setup is working correctly
 * This component demonstrates the basic Nimbus Button component
 * following the official Nimbus documentation
 */
const NimbusTestComponent: React.FC = () => {
  const handleClick = () => {
    alert("Nimbus Button clicked!");
  };

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-semibold">Nimbus Button Test</h2>

      {/* Simple Button Example following Nimbus documentation */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Button Examples</h3>

        {/* Primary Button */}
        <div className="space-y-2">
          <p className="text-sm text-gray-600">Primary Button:</p>
          <Button
            onClick={handleClick}
            variant="primary"
          >
            Primary Button
          </Button>
        </div>

        {/* Secondary Button */}
        <div className="space-y-2">
          <p className="text-sm text-gray-600">Secondary Button:</p>
          <Button
            onClick={handleClick}
            variant="secondary"
          >
            Secondary Button
          </Button>
        </div>

        {/* Tertiary Button */}
        <div className="space-y-2">
          <p className="text-sm text-gray-600">Tertiary Button:</p>
          <Button
            onClick={handleClick}
            variant="tertiary"
          >
            Tertiary Button
          </Button>
        </div>

        {/* Disabled Button */}
        <div className="space-y-2">
          <p className="text-sm text-gray-600">Disabled Button:</p>
          <Button
            onClick={handleClick}
            variant="primary"
            disabled
          >
            Disabled Button
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NimbusTestComponent;
