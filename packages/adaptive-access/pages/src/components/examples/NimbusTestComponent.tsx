import React, { useState } from "react";
import { Button, Input, Modal } from "@zs-nimbus/core";

/**
 * Test component to verify Nimbus setup is working correctly
 * This component demonstrates basic Nimbus components:
 * - Button
 * - Input
 * - Modal
 */
const NimbusTestComponent: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
  };

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-semibold">Nimbus Components Test</h2>
      
      {/* Test Button Component */}
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Button Examples</h3>
        <div className="flex gap-2">
          <Button variant="primary" onClick={handleOpenModal}>
            Primary Button
          </Button>
          <Button variant="secondary">
            Secondary Button
          </Button>
          <Button variant="tertiary">
            Tertiary Button
          </Button>
        </div>
      </div>

      {/* Test Input Component */}
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Input Example</h3>
        <Input
          placeholder="Enter some text..."
          value={inputValue}
          onChange={handleInputChange}
          className="max-w-xs"
        />
        {inputValue && (
          <p className="text-sm text-gray-600">You typed: {inputValue}</p>
        )}
      </div>

      {/* Test Modal Component */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title="Test Modal"
      >
        <div className="p-4">
          <p className="mb-4">
            This is a test modal using Nimbus Modal component.
          </p>
          <p className="mb-4">
            Input value from outside: <strong>{inputValue || "No input yet"}</strong>
          </p>
          <div className="flex justify-end gap-2">
            <Button variant="secondary" onClick={handleCloseModal}>
              Cancel
            </Button>
            <Button variant="primary" onClick={handleCloseModal}>
              OK
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default NimbusTestComponent;
