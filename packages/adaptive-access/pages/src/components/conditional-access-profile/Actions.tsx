import type React from "react";
import { useContext } from "react";
import { useTranslation } from "react-i18next";

import { faPlus } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button, Search } from "@zscaler/zui-component-library";

import { noop } from "lodash-es";

import { CRUDPageContext } from "../../contexts/CRUDPageContextProvider";
import { useProfileContext } from "../../ducks/conditional-access-profile";
import { useApiCall } from "../../hooks/useApiCallContext";
import NimbusTestComponent from "../examples/NimbusTestComponent";

const Actions: React.FC = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { getList } = useProfileContext();

  const {
    setModalMode,
    searchTerm,
    setSearchTerm,
    privileges,
    defaultDetail,
    defaultModalMode,
    setDetail,
  } = useContext(CRUDPageContext)!;

  const { hasFullAccess } = privileges;

  const onAddClick = () => {
    setDetail(defaultDetail);
    setModalMode(defaultModalMode);
    setTimeout(() => {
      setModalMode("add");
    }, 0);
  };

  const onSearchEnter = (term: string) => {
    apiCall(() => getList({ name: term })).catch(noop);

    setSearchTerm(term);
  };

  return (
    <div
      className={`is-flex full-width ${hasFullAccess ? "has-jc-sb" : "has-jc-e"}`}
    >
      <section
        className="heading-small page-title"
        style={{ alignSelf: "center" }}
      >
        {t("CONDITIONAL_ACCESS_PROFILE")}
      </section>
      <div className="flex ">
        <Search
          onSearch={onSearchEnter}
          term={searchTerm}
          containerClass="no-m-r"
          containerStyle={{ maxWidth: "258px" }}
        />
        {hasFullAccess ? (
          <div className="buttons" style={{ marginBottom: 0 }}>
            <Button onClick={onAddClick}>
              <FontAwesomeIcon icon={faPlus} className="icon left" />
              <span>{t("ADD_PROFILE")}</span>
            </Button>
            <NimbusTestComponent />
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default Actions;
