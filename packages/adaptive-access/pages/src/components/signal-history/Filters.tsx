"use client";

import type React from "react";

import { useCallback, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Select, Field, DateRangePicker } from "@zs-nimbus/core";
import {
  FieldGroup,
} from "@zscaler/zui-component-library";
import { cloneDeep } from "lodash-es";
import type { FilterOption, TimeRange } from "../../types/dropdown";
import type { SelectedFilter } from "../../types/filters";
import { calendarConfig } from "./helper";
import { useSignalHistory } from "../../ducks/signal-history";
import { useSignalHistorySelectors } from "../../ducks/signal-history/selectors";

type FiltersProps = {
  selectedFilter: SelectedFilter;
  setSelectedFilter: (
    filter: SelectedFilter | ((prev: SelectedFilter) => SelectedFilter),
  ) => void;
};



// Helper functions to convert between TimeRange and DateRangePicker formats
const timeRangeToDateRange = (timeRange: TimeRange[]): { start: string; end: string } | undefined => {
  if (!timeRange || timeRange.length === 0) return undefined;

  const range = timeRange[0];
  if (!range || !range.startTime || !range.endTime) return undefined;

  try {
    // Validate timestamp values (should be reasonable Unix timestamps)
    const startTimestamp = Number(range.startTime);
    const endTimestamp = Number(range.endTime);

    // Check if timestamps are in valid range (after 1970 and before year 2100)
    if (startTimestamp < 0 || startTimestamp > 4102444800 || endTimestamp < 0 || endTimestamp > 4102444800) {
      console.warn('Invalid timestamp range:', { startTimestamp, endTimestamp });
      return undefined;
    }

    // Convert Unix timestamps to ISO date strings
    const startDate = new Date(startTimestamp * 1000);
    const endDate = new Date(endTimestamp * 1000);

    // Validate the created dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.warn('Invalid dates created from timestamps:', { startTimestamp, endTimestamp });
      return undefined;
    }

    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    if (!startDateStr || !endDateStr) return undefined;

    // Additional validation for year range (1900-2100)
    const startYear = startDate.getFullYear();
    const endYear = endDate.getFullYear();

    if (startYear < 1900 || startYear > 2100 || endYear < 1900 || endYear > 2100) {
      console.warn('Date year out of reasonable range:', { startYear, endYear });
      return undefined;
    }

    return { start: startDateStr, end: endDateStr };
  } catch (error) {
    console.error('Error converting TimeRange to DateRange:', error, range);
    return undefined;
  }
};

const dateRangeToTimeRange = (dateRange: { start: string; end: string } | undefined): TimeRange[] => {
  if (!dateRange || !dateRange.start || !dateRange.end) return [];

  try {
    // Convert ISO date strings to Unix timestamps
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end + 'T23:59:59'); // End of day

    // Validate the created dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.warn('Invalid dates from date range:', dateRange);
      return [];
    }

    const startTime = Math.floor(startDate.getTime() / 1000);
    const endTime = Math.floor(endDate.getTime() / 1000);

    // Validate timestamp values
    if (startTime < 0 || endTime < 0 || startTime > endTime) {
      console.warn('Invalid timestamp values:', { startTime, endTime });
      return [];
    }

    return [{
      startTime,
      endTime,
      label: `${dateRange.start} - ${dateRange.end}`,
      value: `${startTime}-${endTime}`,
      displayText: `${dateRange.start} - ${dateRange.end}`,
      isRangeSame: dateRange.start === dateRange.end
    }];
  } catch (error) {
    console.error('Error converting DateRange to TimeRange:', error, dateRange);
    return [];
  }
};

const Filters: React.FC<FiltersProps> = ({
  selectedFilter,
  setSelectedFilter,
}) => {
  const { t } = useTranslation();
  const { getSourcesEnums, getContextTypesEnums, getSubjectsEnums } =
    useSignalHistory();
  const {
    sourcesEnums,
    sourcesEnumsList,
    contextTypesEnums,
    contextTypesEnumsList,
    subjectsEnums,
    subjectsEnumsList,
  } = useSignalHistorySelectors();

  // Loading states for Select components
  const [isSourcesDropDownLoading, setisSourcesDropDownLoading] =
    useState(false);
  const [isContextTypesDropDownLoading, setisContextTypesDropDownLoading] =
    useState(false);
  const [isSubjectsDropDownLoading, setisSubjectsDropDownLoading] =
    useState(false);

  // Load enums on component mount
  useEffect(() => {
    if (!Array.isArray(sourcesEnums) || sourcesEnums.length === 0) {
      setisSourcesDropDownLoading(true);
      void getSourcesEnums().finally(() => {
        setisSourcesDropDownLoading(false);
      });
    }
  }, [sourcesEnums, getSourcesEnums]);

  useEffect(() => {
    if (!Array.isArray(contextTypesEnums) || contextTypesEnums.length === 0) {
      setisContextTypesDropDownLoading(true);
      void getContextTypesEnums().finally(() => {
        setisContextTypesDropDownLoading(false);
      });
    }
  }, [contextTypesEnums, getContextTypesEnums]);

  useEffect(() => {
    if (!Array.isArray(subjectsEnums) || subjectsEnums.length === 0) {
      setisSubjectsDropDownLoading(true);
      void getSubjectsEnums().finally(() => {
        setisSubjectsDropDownLoading(false);
      });
    }
  }, [subjectsEnums, getSubjectsEnums]);

  const getSelectedList = useCallback(
    (filterName: keyof SelectedFilter): FilterOption[] | TimeRange[] => {
      const filterDetail = selectedFilter[filterName];
      return filterDetail?.length ? filterDetail : [];
    },
    [selectedFilter],
  );

  const onFilterSelection = useCallback(
    (filterName: keyof SelectedFilter) =>
      (detail: FilterOption[] | TimeRange[]): void => {
        setSelectedFilter((prevState: SelectedFilter): SelectedFilter => {
          const newState = cloneDeep(prevState);
          return {
            ...newState,
            [filterName]: detail,
          } as SelectedFilter;
        });
      },
    [setSelectedFilter],
  );

  return (
    <FieldGroup containerStyle={{ flex: 1 }}>
      <div className="no-m-r calender-flex-wrap audit-logs-filter-contianer">
        <Field
          label={t("CONTEXT_CREATION")}
          name="context_creation"
        >
        {() => (
          <DateRangePicker
            value={timeRangeToDateRange(getSelectedList("timeRange") as TimeRange[]) || undefined}
            onChange={(dateRange) => {
              if (dateRange) {
                const timeRangeData = dateRangeToTimeRange(dateRange);
                if (timeRangeData.length > 0) {
                  onFilterSelection("timeRange")(timeRangeData);
                }
              }
            }}
            min={calendarConfig.minDate.format('YYYY-MM-DD')}
            max={new Date().toISOString().split('T')[0]} // Today's date
            className="audit-logs-drop-down-container"
            style={{ width: "100%" }}
          />
        )}
        </Field>
      </div>

      <div className="no-m-r audit-logs-filter-contianer">
        <Field
          label={t("SOURCE")}
          name="source"
        >
        {() => (
          <Select
            items={sourcesEnumsList.map(option => ({ id: option.value, name: option.label }))}
            value={(getSelectedList("source_id") as FilterOption[]).map(option => ({ id: option.value, name: option.label }))}
            onChange={(items) => {
              const selectedOptions = items.map(item => ({ label: item.name, value: String(item.id) }));
              onFilterSelection("source_id")(selectedOptions);
            }}
            showLoadingIndicator={isSourcesDropDownLoading}
            placeholder={t("ALL")}
            selectionMode="single"
            searchable
            className="audit-logs-drop-down-container"
            style={{ width: "100%" }}
          />
        )}
        </Field>
      </div>

      <div className="no-m-r audit-logs-filter-contianer">
        <Field
          label={t("CONTEXT_TYPE")}
          name="context_type"
        >
        {() => (
          <Select
            items={contextTypesEnumsList.map(option => ({ id: option.value, name: option.label }))}
            value={(getSelectedList("context_type") as FilterOption[]).map(option => ({ id: option.value, name: option.label }))}
            onChange={(items) => {
              const selectedOptions = items.map(item => ({ label: item.name, value: String(item.id) }));
              onFilterSelection("context_type")(selectedOptions);
            }}
            showLoadingIndicator={isContextTypesDropDownLoading}
            placeholder={t("ALL")}
            selectionMode="single"
            searchable
            className="audit-logs-drop-down-container"
            style={{ width: "100%" }}
          />
        )}
        </Field>
      </div>

      <div className="no-m-r audit-logs-filter-contianer">
        <Field
          label={t("SUBJECT_TYPE")}
          name="subject_type"
        >
        {() => (
          <Select
            items={subjectsEnumsList.map(option => ({ id: option.value, name: option.label }))}
            value={(getSelectedList("subject_type") as FilterOption[]).map(option => ({ id: option.value, name: option.label }))}
            onChange={(items) => {
              const selectedOptions = items.map(item => ({ label: item.name, value: String(item.id) }));
              onFilterSelection("subject_type")(selectedOptions);
            }}
            showLoadingIndicator={isSubjectsDropDownLoading}
            placeholder={t("ALL")}
            selectionMode="single"
            searchable
            className="audit-logs-drop-down-container"
            style={{ width: "100%" }}
          />
        )}
        </Field>
      </div>
    </FieldGroup>
  );
};

export default Filters;
