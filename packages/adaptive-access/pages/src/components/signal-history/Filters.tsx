"use client";

import type React from "react";

import { useCallback, useState, useEffect } from "react";
import { Select, Field } from "@zs-nimbus/core";
import {
  CalendarDropDown,
  FieldGroup,
} from "@zscaler/zui-component-library";
import { cloneDeep } from "lodash-es";
import type { FilterOption, TimeRange } from "../../types/dropdown";
import type { SelectedFilter } from "../../types/filters";
import { calendarConfig } from "./helper";
import { useSignalHistory } from "../../ducks/signal-history";
import { useSignalHistorySelectors } from "../../ducks/signal-history/selectors";

type FiltersProps = {
  selectedFilter: SelectedFilter;
  setSelectedFilter: (
    filter: SelectedFilter | ((prev: SelectedFilter) => SelectedFilter),
  ) => void;
};

const defaultSelectedItem: FilterOption[] = [{ label: "ALL", value: "ALL" }];

const tooltipProps = {
  placement: "right" as const,
  offset: 30,
};

const Filters: React.FC<FiltersProps> = ({
  selectedFilter,
  setSelectedFilter,
}) => {
  const { getSourcesEnums, getContextTypesEnums, getSubjectsEnums } =
    useSignalHistory();
  const {
    sourcesEnums,
    sourcesEnumsList,
    contextTypesEnums,
    contextTypesEnumsList,
    subjectsEnums,
    subjectsEnumsList,
  } = useSignalHistorySelectors();

  // Loading states for Select components
  const [isSourcesDropDownLoading, setisSourcesDropDownLoading] =
    useState(false);
  const [isContextTypesDropDownLoading, setisContextTypesDropDownLoading] =
    useState(false);
  const [isSubjectsDropDownLoading, setisSubjectsDropDownLoading] =
    useState(false);

  // Load enums on component mount
  useEffect(() => {
    if (!Array.isArray(sourcesEnums) || sourcesEnums.length === 0) {
      setisSourcesDropDownLoading(true);
      void getSourcesEnums().finally(() => {
        setisSourcesDropDownLoading(false);
      });
    }
  }, [sourcesEnums, getSourcesEnums]);

  useEffect(() => {
    if (!Array.isArray(contextTypesEnums) || contextTypesEnums.length === 0) {
      setisContextTypesDropDownLoading(true);
      void getContextTypesEnums().finally(() => {
        setisContextTypesDropDownLoading(false);
      });
    }
  }, [contextTypesEnums, getContextTypesEnums]);

  useEffect(() => {
    if (!Array.isArray(subjectsEnums) || subjectsEnums.length === 0) {
      setisSubjectsDropDownLoading(true);
      void getSubjectsEnums().finally(() => {
        setisSubjectsDropDownLoading(false);
      });
    }
  }, [subjectsEnums, getSubjectsEnums]);

  const getSelectedList = useCallback(
    (filterName: keyof SelectedFilter): FilterOption[] | TimeRange[] => {
      const filterDetail = selectedFilter[filterName];
      return filterDetail?.length ? filterDetail : [];
    },
    [selectedFilter],
  );

  const onFilterSelection = useCallback(
    (filterName: keyof SelectedFilter) =>
      (detail: FilterOption[] | TimeRange[]): void => {
        setSelectedFilter((prevState: SelectedFilter): SelectedFilter => {
          const newState = cloneDeep(prevState);
          return {
            ...newState,
            [filterName]: detail,
          } as SelectedFilter;
        });
      },
    [setSelectedFilter],
  );

  return (
    <FieldGroup containerStyle={{ flex: 1 }}>
      <Field
        label="CONTEXT_CREATION"
        name="context_creation"
        className="no-m-r calender-flex-wrap audit-logs-filter-contianer"
      >
        {() => (
          <CalendarDropDown
            level="REPORT"
            selectedList={getSelectedList("timeRange") as TimeRange[]}
            setSelectedList={onFilterSelection("timeRange")}
            selectedItemsProps={{
              containerClass: "audit-logs-drop-down-container",
            }}
            selectedItemsTooltipProps={tooltipProps}
            config={calendarConfig}
          />
        )}
      </Field>

      <Field
        label="SOURCE"
        name="source"
        className="no-m-r audit-logs-filter-contianer"
      >
        {() => (
          <Select
            items={sourcesEnumsList.map(option => ({ id: option.value, name: option.label }))}
            value={(getSelectedList("source_id") as FilterOption[]).map(option => ({ id: option.value, name: option.label }))}
            onChange={(items) => {
              const selectedOptions = items.map(item => ({ label: item.name, value: String(item.id) }));
              onFilterSelection("source_id")(selectedOptions);
            }}
            showLoadingIndicator={isSourcesDropDownLoading}
            placeholder={defaultSelectedItem[0]?.label}
            selectionMode="single"
            searchable
            className="audit-logs-drop-down-container"
          />
        )}
      </Field>

      <Field
        label="CONTEXT_TYPE"
        name="context_type"
        className="no-m-r audit-logs-filter-contianer"
      >
        {() => (
          <Select
            items={contextTypesEnumsList.map(option => ({ id: option.value, name: option.label }))}
            value={(getSelectedList("context_type") as FilterOption[]).map(option => ({ id: option.value, name: option.label }))}
            onChange={(items) => {
              const selectedOptions = items.map(item => ({ label: item.name, value: String(item.id) }));
              onFilterSelection("context_type")(selectedOptions);
            }}
            showLoadingIndicator={isContextTypesDropDownLoading}
            placeholder={defaultSelectedItem[0]?.label}
            selectionMode="single"
            searchable
            className="audit-logs-drop-down-container"
          />
        )}
      </Field>

      <Field
        label="SUBJECT_TYPE"
        name="subject_type"
        className="no-m-r audit-logs-filter-contianer"
      >
        {() => (
          <Select
            items={subjectsEnumsList.map(option => ({ id: option.value, name: option.label }))}
            value={(getSelectedList("subject_type") as FilterOption[]).map(option => ({ id: option.value, name: option.label }))}
            onChange={(items) => {
              const selectedOptions = items.map(item => ({ label: item.name, value: String(item.id) }));
              onFilterSelection("subject_type")(selectedOptions);
            }}
            showLoadingIndicator={isSubjectsDropDownLoading}
            placeholder={defaultSelectedItem[0]?.label}
            selectionMode="single"
            searchable
            className="audit-logs-drop-down-container"
          />
        )}
      </Field>
    </FieldGroup>
  );
};

export default Filters;
