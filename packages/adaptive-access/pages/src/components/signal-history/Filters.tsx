"use client";

import type React from "react";

import { useCallback, useState } from "react";
import { Select, Field } from "@zs-nimbus/core";
import {
  CalendarDropDown,
  FieldGroup,
} from "@zscaler/zui-component-library";
import { cloneDeep } from "lodash-es";
import type { FilterOption, TimeRange } from "../../types/dropdown";
import type { SelectedFilter } from "../../types/filters";
import { calendarConfig } from "./helper";
import { useSignalHistory } from "../../ducks/signal-history";
import { useSignalHistorySelectors } from "../../ducks/signal-history/selectors";

type FiltersProps = {
  selectedFilter: SelectedFilter;
  setSelectedFilter: (
    filter: SelectedFilter | ((prev: SelectedFilter) => SelectedFilter),
  ) => void;
};

const defaultSelectedItem: FilterOption[] = [{ label: "ALL", value: "ALL" }];

const tooltipProps = {
  placement: "right" as const,
  offset: 30,
};

const Filters: React.FC<FiltersProps> = ({
  selectedFilter,
  setSelectedFilter,
}) => {
  const { getSourcesEnums, getContextTypesEnums, getSubjectsEnums } =
    useSignalHistory();
  const {
    sourcesEnums,
    sourcesEnumsList,
    contextTypesEnums,
    contextTypesEnumsList,
    subjectsEnums,
    subjectsEnumsList,
  } = useSignalHistorySelectors();

  // Create action functions without using useDropDownActions
  const [isSourcesDropDownLoading, setisSourcesDropDownLoading] =
    useState(false);
  const [isContextTypesDropDownLoading, setisContextTypesDropDownLoading] =
    useState(false);
  const [isSubjectsDropDownLoading, setisSubjectsDropDownLoading] =
    useState(false);

  // Define manual open handlers that execute our context API functions
  const handleSourcesOpen = useCallback(() => {
    if (Array.isArray(sourcesEnums) && sourcesEnums.length > 0) return;
    if (isSourcesDropDownLoading) return;

    setisSourcesDropDownLoading(true);
    void getSourcesEnums().finally(() => {
      setisSourcesDropDownLoading(false);
    });
  }, [sourcesEnums, isSourcesDropDownLoading, getSourcesEnums]);

  const handleContextTypesOpen = useCallback(() => {
    if (Array.isArray(contextTypesEnums) && contextTypesEnums.length > 0)
      return;
    if (isContextTypesDropDownLoading) return;

    setisContextTypesDropDownLoading(true);
    void getContextTypesEnums().finally(() => {
      setisContextTypesDropDownLoading(false);
    });
  }, [contextTypesEnums, getContextTypesEnums, isContextTypesDropDownLoading]);

  const handleSubjectsOpen = useCallback(() => {
    if (Array.isArray(subjectsEnums) && subjectsEnums.length > 0) return;
    if (isSubjectsDropDownLoading) return;

    setisSubjectsDropDownLoading(true);
    void getSubjectsEnums().finally(() => {
      setisSubjectsDropDownLoading(false);
    });
  }, [subjectsEnums, isSubjectsDropDownLoading, getSubjectsEnums]);

  const getSelectedList = useCallback(
    (filterName: keyof SelectedFilter): FilterOption[] | TimeRange[] => {
      const filterDetail = selectedFilter[filterName];
      return filterDetail?.length ? filterDetail : [];
    },
    [selectedFilter],
  );

  const onFilterSelection = useCallback(
    (filterName: keyof SelectedFilter) =>
      (detail: FilterOption[] | TimeRange[]): void => {
        setSelectedFilter((prevState: SelectedFilter): SelectedFilter => {
          const newState = cloneDeep(prevState);
          return {
            ...newState,
            [filterName]: detail,
          } as SelectedFilter;
        });
      },
    [setSelectedFilter],
  );

  return (
    <FieldGroup containerStyle={{ flex: 1 }}>
      <Field
        label="CONTEXT_CREATION"
        containerClass="no-m-r calender-flex-wrap audit-logs-filter-contianer"
      >
        <CalendarDropDown
          level="REPORT"
          selectedList={getSelectedList("timeRange") as TimeRange[]}
          setSelectedList={onFilterSelection("timeRange")}
          selectedItemsProps={{
            containerClass: "audit-logs-drop-down-container",
          }}
          selectedItemsTooltipProps={tooltipProps}
          config={calendarConfig}
        />
      </Field>

      <Field label="SOURCE" className="no-m-r audit-logs-filter-contianer">
        <Select
          options={sourcesEnumsList}
          value={getSelectedList("source_id") as FilterOption[]}
          onChange={onFilterSelection("source_id")}
          onOpen={handleSourcesOpen}
          loading={isSourcesDropDownLoading}
          placeholder={defaultSelectedItem[0]?.label}
          multiple={false}
          searchable
          className="audit-logs-drop-down-container"
        />
      </Field>

      <Field
        label="CONTEXT_TYPE"
        className="no-m-r audit-logs-filter-contianer"
      >
        <Select
          options={contextTypesEnumsList}
          value={getSelectedList("context_type") as FilterOption[]}
          onChange={onFilterSelection("context_type")}
          onOpen={handleContextTypesOpen}
          loading={isContextTypesDropDownLoading}
          placeholder={defaultSelectedItem[0]?.label}
          multiple={false}
          searchable
          className="audit-logs-drop-down-container"
        />
      </Field>

      <Field
        label="SUBJECT_TYPE"
        className="no-m-r audit-logs-filter-contianer"
      >
        <Select
          options={subjectsEnumsList}
          value={getSelectedList("subject_type") as FilterOption[]}
          onChange={onFilterSelection("subject_type")}
          onOpen={handleSubjectsOpen}
          loading={isSubjectsDropDownLoading}
          placeholder={defaultSelectedItem[0]?.label}
          multiple={false}
          searchable
          className="audit-logs-drop-down-container"
        />
      </Field>
    </FieldGroup>
  );
};

export default Filters;
