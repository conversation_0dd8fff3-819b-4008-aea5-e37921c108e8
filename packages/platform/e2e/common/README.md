# Unified Platform (UP) - @up/e2e-common

Reusable E2E Playwright Automation Library

## Relevant Commands

| Command             | Description                                          |
| ------------------- | ---------------------------------------------------- |
| pnpm build          | build package                                        |
| pnpm test           | run package unit tests                               |
| pnpm test:ui        | run package unit tests with ui                       |
| pnpm test:watch     | run package unit tests in watch mode                 |
| pnpm prettier       | run prettier on this package and fix any issues      |
| pnpm prettier:check | run prettier on this package                         |
| pnpm lint           | run eslint on this package                           |
| pnpm lint:fix       | run eslint on this package and apply automatic fixes |
| pnpm verify         | runs both prettier:check and lint on this package    |
| npm login           | ensure your authenticated into registry              |
| npm publish         | publish current version to registry                  |
