import { defineConfig } from "@playwright/test";

/**
 * A common Playwright configuration object that can be shared across different
 * test packages within the monorepo. It provides a baseline set of configurations
 * for running tests, including settings for parallelism, retries, reporters,
 * and browser behavior.
 *
 * This configuration can be imported and spread into package-specific
 * Playwright configurations to ensure consistency.
 *
 * @see https://playwright.dev/docs/test-configuration for more details on Playwright configuration.
 */
export const commonConfig = defineConfig({
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 0 : 0,
  workers: undefined,
  reporter: [
    ["list"],
    ["html"],
    ["allure-playwright", { outputFolder: "allure-results" }],
  ],
  use: {
    trace: "retain-on-failure",
    headless: true,
    screenshot: "only-on-failure",
    video: "retain-on-failure",
    launchOptions: {
      slowMo: process.env.CI ? 1000 : 1000,
    },
  },
});
