import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";

// Mock the 'dotenv' module to prevent it from reading .env files during tests.
// This gives us control over environment variables for each test case.
vi.mock("dotenv", () => ({
  config: vi.fn(),
}));

describe("envConf", () => {
  // Store the original process.env to restore it after each test
  const originalEnv = process.env;

  beforeEach(() => {
    // vi.resetModules() is crucial. It clears the module cache,
    // so that when we import './envConf', the file is re-evaluated.
    // This allows us to test the module's initialization logic under different
    // process.env conditions.
    vi.resetModules();
    // Reset process.env to a clean state for each test
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    // Restore the original process.env to avoid side-effects between test files.
    process.env = originalEnv;
  });

  it("should set backend.apiUrl from the ONE_UI_BASE_URL environment variable", async () => {
    // Arrange: Set the environment variable for this test case.
    const testUrl = "http://test.api.url";
    process.env.ONE_UI_BASE_URL = testUrl;

    // Act: Dynamically import the configuration module.
    // The module will be executed with the process.env we just set.
    const { envConf } = await import("./envConf");

    // Assert: Check if the apiUrl was set correctly.
    expect(envConf.backend.apiUrl).toBe(testUrl);
  });

  it("should default backend.apiUrl to an empty string if ONE_UI_BASE_URL is not set", async () => {
    // Arrange: Ensure the environment variable is not set.
    delete process.env.ONE_UI_BASE_URL;

    // Act: Dynamically import the configuration module.
    const { envConf } = await import("./envConf");

    // Assert: Check if the apiUrl defaults to an empty string.
    expect(envConf.backend.apiUrl).toBe("");
  });

  it("should have the correct default headers", async () => {
    // Act: Dynamically import the configuration module.
    const { envConf } = await import("./envConf");

    // Assert: Verify the static headers object.
    expect(envConf.backend.headers).toEqual({
      api: {
        "Content-Type": "application/json",
      },
    });
  });
});
