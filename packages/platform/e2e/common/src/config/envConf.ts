/**
 * @file Loads and exports environment-specific configurations for the E2E test suite.
 * This file uses `dotenv` to load variables from a `.env` file into `process.env`.
 */

import { config } from "dotenv";

// Initialize dotenv to load environment variables.
config();

/**
 * Configuration object for E2E tests, containing backend-related settings.
 */
export const envConf = {
  /**
   * Backend service configurations.
   */
  backend: {
    /**
     * The base URL for the backend API.
     * Sourced from the `ONE_UI_BASE_URL` environment variable.
     * Defaults to an empty string if not set.
     */
    apiUrl: process.env.ONE_UI_BASE_URL ?? "",
    /**
     * Default headers for API requests.
     */
    headers: {
      /**
       * Headers for standard API calls.
       */
      api: {
        "Content-Type": "application/json",
        // You can add authorization here if needed
        // 'Authorization': `Bearer ${process.env.TOKEN}`,
      },
    },
  },
};
