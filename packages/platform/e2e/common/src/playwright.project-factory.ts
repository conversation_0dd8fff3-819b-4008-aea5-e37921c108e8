import { devices, Project } from "@playwright/test";
import { defineBddConfig } from "playwright-bdd";

/**
 * Defines the viewport dimensions for a Playwright project.
 */
interface LocalViewport {
  /** The width of the viewport in pixels. */
  width: number;
  /** The height of the viewport in pixels. */
  height: number;
}

/**
 * The default viewport size for Playwright projects.
 */
const DEFAULT_VIEWPORT: LocalViewport = { width: 1280, height: 720 };

/**
 * Defines the options for creating a BDD-configured Playwright project.
 */
type BddProjectOptions = {
  /** The name of the Playwright project. */
  name: string;
  /** The root directory for feature files. */
  featuresRoot: string;
  /** The directory where test results and reports will be generated. */
  outputDir: string;
  /** The path to a file containing authentication state. */
  storageState?: string;
  /** A list of project names that this project depends on. */
  dependencies?: string[];
  /** The timeout for each test in milliseconds. */
  timeout?: number;
  /** Additional 'use' options for the project, e.g., `{ headless: false }`. */
  use?: Record<string, any>;
  /** Whether to ignore HTTPS errors during navigation. */
  ignoreHTTPSErrors?: boolean;
  /** Whether to run tests in this project in parallel. */
  fullyParallel?: boolean;
};

/**
 * Creates a Playwright project configuration for BDD tests.
 * This factory function simplifies the creation of Playwright projects
 * that use `playwright-bdd`.
 *
 * @param options - The configuration options for the BDD project.
 * @returns A Playwright {@link Project} configuration object.
 */
export function createBddProject(options: BddProjectOptions): Project {
  const {
    name,
    featuresRoot,
    outputDir,
    storageState,
    dependencies,
    timeout,
    use: additionalUseOptions = {},
    ignoreHTTPSErrors,
    fullyParallel,
  } = options;

  const projectUse: Record<string, any> = {
    ...devices["Desktop Chrome"],
    viewport: DEFAULT_VIEWPORT,
    ...additionalUseOptions,
    ...(storageState ? { storageState } : {}),
    ...(ignoreHTTPSErrors !== undefined ? { ignoreHTTPSErrors } : {}),
  };

  const baseConfig: Project = {
    name,
    testDir: defineBddConfig({
      featuresRoot,
      outputDir,
    }),
    use: projectUse,
    dependencies,
    timeout,
    ...(fullyParallel ? {} : { fullyParallel }),
  };

  return baseConfig;
}
