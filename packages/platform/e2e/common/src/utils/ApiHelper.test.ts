import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  afterEach,
  beforeAll,
} from "vitest";
import {
  APIResponse,
  APIRequestContext,
  request as playwrightRequest,
} from "@playwright/test";
import { envConf as mockEnvConf } from "../config/envConf";

// Mock dependencies at the top level
vi.mock("@playwright/test", async (importOriginal) => {
  const actual = await importOriginal<typeof import("@playwright/test")>();
  return {
    ...actual,
    request: {
      newContext: vi.fn(),
    },
  };
});

vi.mock("dotenv", () => ({
  config: vi.fn(),
}));

// Mock the config module. This will be the default mock.
vi.mock("../config/envConf", () => ({
  envConf: {
    backend: {
      apiUrl: "http://mock-api.com",
      headers: {
        api: {
          "X-Test-Header": "default",
        },
      },
    },
  },
}));

// The tests for initialization need to be in a separate describe block
// to handle module mocking and resetting correctly.
describe("ApiHelper Initialization", () => {
  const originalEnvConf = { ...mockEnvConf };

  beforeEach(() => {
    vi.resetModules();
  });

  afterEach(() => {
    // Restore the original mock for other tests
    vi.doMock("../config/envConf", () => ({ envConf: originalEnvConf }));
  });

  it("should throw an error if apiUrl is not defined", async () => {
    vi.doMock("../config/envConf", () => ({
      envConf: { backend: { headers: { api: {} } } },
    }));
    await expect(import("./ApiHelper")).rejects.toThrow(
      "API URL is not defined in the environment configuration.",
    );
  });

  it("should throw an error if api headers are not defined", async () => {
    vi.doMock("../config/envConf", () => ({
      envConf: { backend: { apiUrl: "http://test.com", headers: {} } },
    }));
    await expect(import("./ApiHelper")).rejects.toThrow(
      "API headers are not defined in the environment configuration.",
    );
  });

  it("should not throw if config is valid", async () => {
    vi.doMock("../config/envConf", () => ({ envConf: originalEnvConf }));
    await expect(import("./ApiHelper")).resolves.toBeDefined();
  });
});

// Main test suite for the ApiHelper instance methods
describe("ApiHelper", () => {
  let ApiHelper: any;
  let mockContext: APIRequestContext;
  let mockFetch: ReturnType<typeof vi.fn>;

  // Dynamically import the module once with the valid config
  beforeAll(async () => {
    const module = await import("./ApiHelper");
    ApiHelper = module.default;
  });

  beforeEach(() => {
    vi.clearAllMocks();

    mockFetch = vi.fn();
    mockContext = {
      fetch: mockFetch,
    } as unknown as APIRequestContext;

    (
      playwrightRequest.newContext as ReturnType<typeof vi.fn>
    ).mockResolvedValue(mockContext);

    // Reset token before each test to ensure test isolation
    ApiHelper.authToken = null;
  });

  describe("Token Management", () => {
    it('should set the bearer token correctly, stripping "Bearer "', () => {
      const token = "my-secret-token";
      ApiHelper.setBearerToken(`Bearer ${token}`);
      expect(ApiHelper.authToken).toBe(token);
    });

    it('should set the bearer token correctly when "Bearer " is not present', () => {
      const token = "my-secret-token";
      ApiHelper.setBearerToken(token);
      expect(ApiHelper.authToken).toBe(token);
    });

    it("should throw an error if token is not set when making a request", async () => {
      await expect(ApiHelper.sendGetRequest("/test")).rejects.toThrow(
        "Bearer token is not set. Please set the token using setBearerToken() before making requests.",
      );
    });
  });

  describe("sendRequest", () => {
    beforeEach(() => {
      ApiHelper.setBearerToken("test-token");
    });

    it("should make a successful GET request and return JSON", async () => {
      const mockResponse = {
        ok: () => true,
        json: async () => ({ success: true }),
        headers: () => ({ "content-type": "application/json" }),
      };
      mockFetch.mockResolvedValue(mockResponse as unknown as APIResponse);

      const response = await ApiHelper.sendGetRequest("/test");

      expect(playwrightRequest.newContext).toHaveBeenCalled();
      expect(mockFetch).toHaveBeenCalledWith("http://mock-api.com/test", {
        method: "GET",
        data: {},
        headers: {
          "X-Test-Header": "default",
          "Content-Type": "application/json",
          Authorization: "Bearer test-token",
        },
        timeout: 60000,
        ignoreHTTPSErrors: true,
      });
      expect(response).toEqual({ success: true });
    });

    it("should correctly construct URL when base URL has a trailing slash", async () => {
      mockEnvConf.backend.apiUrl = "http://mock-api.com/";
      const mockResponse = {
        ok: () => true,
        json: async () => ({}),
        headers: () => ({ "content-type": "application/json" }),
      };
      mockFetch.mockResolvedValue(mockResponse as unknown as APIResponse);

      await ApiHelper.sendGetRequest("/test");

      expect(mockFetch).toHaveBeenCalledWith(
        "http://mock-api.com/test",
        expect.any(Object),
      );
      // restore
      mockEnvConf.backend.apiUrl = "http://mock-api.com";
    });

    it("should handle non-JSON success responses by returning text", async () => {
      const mockResponse = {
        ok: () => true,
        text: async () => "OK",
        headers: () => ({ "content-type": "text/plain" }),
      };
      mockFetch.mockResolvedValue(mockResponse as unknown as APIResponse);

      const response = await ApiHelper.sendGetRequest("/health");
      expect(response).toBe("OK");
    });

    it("should retry a failed request and succeed on the second attempt", async () => {
      const failedResponse = {
        ok: () => false,
        status: () => 500,
        text: async () => "Internal Server Error",
        headers: () => ({}),
      };
      const successResponse = {
        ok: () => true,
        json: async () => ({ success: true }),
        headers: () => ({ "content-type": "application/json" }),
      };

      mockFetch
        .mockResolvedValueOnce(failedResponse as APIResponse)
        .mockResolvedValueOnce(successResponse as unknown as APIResponse);

      const response = await ApiHelper.sendGetRequest("/retry-test");

      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(response).toEqual({ success: true });
    });

    it("should throw an error after all retries fail for non-ok responses", async () => {
      const failedResponse = {
        ok: () => false,
        status: () => 500,
        text: async () => "Internal Server Error",
        headers: () => ({}),
      };
      mockFetch.mockResolvedValue(failedResponse as APIResponse);

      await expect(ApiHelper.sendGetRequest("/fail-test")).rejects.toThrow(
        "All 3 attempts failed. Last error: API request failed with status 500: Internal Server Error",
      );
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it("should throw an error after all retries fail for network errors", async () => {
      mockFetch.mockRejectedValue(new Error("Network error"));

      await expect(ApiHelper.sendGetRequest("/network-fail")).rejects.toThrow(
        "Network error",
      );
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });
  });

  describe("HTTP Method Helpers", () => {
    beforeEach(() => {
      ApiHelper.setBearerToken("test-token");
      const mockResponse = {
        ok: () => true,
        json: async () => ({}),
        headers: () => ({ "content-type": "application/json" }),
      };
      mockFetch.mockResolvedValue(mockResponse as unknown as APIResponse);
    });

    it("sendGetRequest should call sendRequest with GET", async () => {
      const spy = vi.spyOn(ApiHelper, "sendRequest");
      await ApiHelper.sendGetRequest("/get");
      expect(spy).toHaveBeenCalledWith("/get", "GET", {}, {});
    });

    it("sendPostRequest should call sendRequest with POST", async () => {
      const spy = vi.spyOn(ApiHelper, "sendRequest");
      const body = { data: "value" };
      await ApiHelper.sendPostRequest("/post", body);
      expect(spy).toHaveBeenCalledWith("/post", "POST", body, {});
    });

    it("sendPutRequest should call sendRequest with PUT", async () => {
      const spy = vi.spyOn(ApiHelper, "sendRequest");
      const body = { data: "value" };
      await ApiHelper.sendPutRequest("/put", body);
      expect(spy).toHaveBeenCalledWith("/put", "PUT", body, {});
    });

    it("sendPatchRequest should call sendRequest with PATCH", async () => {
      const spy = vi.spyOn(ApiHelper, "sendRequest");
      const body = { data: "value" };
      await ApiHelper.sendPatchRequest("/patch", body);
      expect(spy).toHaveBeenCalledWith("/patch", "PATCH", body, {});
    });

    it("sendDeleteRequest should call sendRequest with DELETE", async () => {
      const spy = vi.spyOn(ApiHelper, "sendRequest");
      await ApiHelper.sendDeleteRequest("/delete");
      expect(spy).toHaveBeenCalledWith("/delete", "DELETE", {}, {});
    });
  });
});
