import { describe, it, expect, vi, beforeEach } from "vitest";
import { Page } from "@playwright/test";
import BrowserStorageHelper from "./BrowserStorageHelper";

describe("BrowserStorageHelper", () => {
  let mockPage: Page;
  let mockEvaluate: ReturnType<typeof vi.fn>;
  let mockStorageState: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    // Reset mocks before each test to ensure isolation
    vi.clearAllMocks();

    // Mock the page.evaluate method
    mockEvaluate = vi.fn();

    // Mock the page.context().storageState method
    mockStorageState = vi.fn().mockResolvedValue(undefined);

    // Create a mock Page object with the methods we need to test
    mockPage = {
      evaluate: mockEvaluate,
      context: () => ({
        storageState: mockStorageState,
      }),
    } as unknown as Page;
  });

  describe("getOktaToken", () => {
    it("should retrieve the token from sessionStorage and save the state on success", async () => {
      const fakeToken = "test-token-123";
      mockEvaluate.mockResolvedValue(fakeToken);

      const token = await BrowserStorageHelper.getOktaToken(mockPage);

      // Verify that page.evaluate was called to get the token
      expect(mockEvaluate).toHaveBeenCalledTimes(1);

      // Verify that the storage state was saved with the correct path
      expect(mockStorageState).toHaveBeenCalledTimes(1);
      expect(mockStorageState).toHaveBeenCalledWith({
        path: "playwright/.auth/user.json",
      });

      // Verify that the token is correctly formatted
      expect(token).toBe(`Bearer ${fakeToken}`);
    });

    it("should throw an error if the token is not found in sessionStorage", async () => {
      // Simulate the token not being found in sessionStorage
      mockEvaluate.mockResolvedValue(null);

      // Assert that the promise rejects with the expected error
      await expect(BrowserStorageHelper.getOktaToken(mockPage)).rejects.toThrow(
        "Bearer token not found in sessionStorage.",
      );

      // Verify that page.evaluate was still called
      expect(mockEvaluate).toHaveBeenCalledTimes(1);

      // Ensure storageState was NOT called because the token retrieval failed
      expect(mockStorageState).not.toHaveBeenCalled();
    });
  });
});
