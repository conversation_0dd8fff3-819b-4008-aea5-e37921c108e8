/**
 * @file BrowserStorageHelper.ts
 * @description This file contains a helper class for interacting with browser storage,
 * specifically for retrieving authentication tokens for E2E tests.
 */

import { type Page } from "@playwright/test";

/**
 * @constant {string} authFile
 * @description The path to the file where the authentication state will be stored.
 * This allows tests to reuse the authentication state and bypass login for subsequent test runs.
 */
const authFile = "playwright/.auth/user.json";

/**
 * @class BrowserStorageHelper
 * @description Provides methods to interact with the browser's storage,
 * such as retrieving tokens from sessionStorage.
 */
class BrowserStorageHelper {
  /**
   * Retrieves the Okta bearer token from the browser's sessionStorage.
   * After retrieving the token, it saves the browser's storage state
   * to a file for reuse in subsequent test runs.
   * @param {Page} page - The Playwright Page object to interact with.
   * @returns {Promise<string>} A promise that resolves to the bearer token string, prefixed with "Bearer ".
   * @throws {Error} If the token is not found in sessionStorage.
   */
  async getOktaToken(page: Page): Promise<string> {
    const oktaTokenStorageFromBrowser = await page.evaluate(() => {
      return sessionStorage.getItem("bearer-token");
    });
    if (!oktaTokenStorageFromBrowser) {
      throw new Error("Bearer token not found in sessionStorage.");
    }
    const token = `Bearer ${oktaTokenStorageFromBrowser}`;
    // Save storage state after obtaining the token
    await page.context().storageState({ path: authFile });
    return token;
  }
}

export default new BrowserStorageHelper();
