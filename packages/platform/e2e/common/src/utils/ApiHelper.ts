import {
  request,
  type APIRequestContext,
  type APIResponse,
} from "@playwright/test";
import { config } from "dotenv";
import { envConf } from "../config/envConf";

config();
/**
 * Defines the options for an API request.
 * These options are used to configure the behavior of the underlying Playwright `APIRequestContext`.
 */
type RequestOptions = {
  /**
   * Maximum time in milliseconds to wait for the request to complete.
   * Defaults to 60000 (60 seconds).
   * @default 60000
   */
  timeout?: number;
  /**
   * The maximum number of times to retry a failed request.
   * This applies to network errors and non-ok HTTP responses.
   * @default 3
   */
  maxRetries?: number;
  /**
   * Whether to ignore HTTPS errors during the request.
   * Useful for testing against environments with self-signed certificates.
   * @default true
   */
  ignoreHTTPSErrors?: boolean;
  /**
   * Additional headers to be sent with the request.
   * These will be merged with the default headers.
   */
  headers?: RequestHeaders;
};
/**
 * @interface RequestHeaders
 * @description Represents a dictionary of request headers.
 */
type RequestHeaders = Record<string, string>;
const { backend } = envConf;

if (!backend?.apiUrl) {
  throw new Error("API URL is not defined in the environment configuration.");
}
if (!backend?.headers?.api) {
  throw new Error(
    "API headers are not defined in the environment configuration.",
  );
}
/**
 * @class ApiHelper
 * @description A helper class to simplify making API requests with Playwright.
 * It handles token authentication, request retries, and verbose logging.
 */
class ApiHelper {
  /**
   * @private
   * @property {RequestOptions} setConf - Default configuration for API requests.
   */
  private setConf: RequestOptions;
  /**
   * @private
   * @property {boolean} verbose - Flag to enable or disable verbose logging.
   */
  private verbose: boolean;
  /**
   * @private
   * @property {string | null} authToken - The bearer token for authentication.
   */
  private authToken: string | null = null;
  /**
   * @constructor
   * Initializes the ApiHelper with default request configurations.
   */
  constructor() {
    this.setConf = {
      timeout: 60 * 1000,
      maxRetries: 3, // Default to 3 retries
      ignoreHTTPSErrors: true,
    };
    this.verbose = true;
  }
  /**
   * Sets the bearer token for authentication.
   * @param {string} raw - The raw token string, which may include "Bearer ".
   */
  setBearerToken(raw: string) {
    this.authToken = raw.replace(/^Bearer\s+/i, "");
    console.debug("Bearer token set:", this.authToken); // Debug log
  }
  /**
   * Validates that the bearer token has been set.
   * @throws {Error} If the bearer token is not set.
   */
  validateToken() {
    if (!this.authToken) {
      throw new Error(
        "Bearer token is not set. Please set the token using setBearerToken() before making requests.",
      );
    }
  }
  /**
   * Logs detailed information about an API request and its response if verbose mode is enabled.
   * @private
   * @param {string} url - The URL of the request.
   * @param {RequestOptions} options - The options used for the request.
   * @param {APIResponse} response - The API response object.
   * @returns {Promise<void>}
   */
  async verboseLog(
    url: string,
    options: RequestOptions,
    response: APIResponse,
  ): Promise<void> {
    if (this.verbose) {
      console.debug("API Request Debug Information:");
      console.debug("URL:", url);
      console.debug("Options:", options);
      console.debug("Headers:", options.headers);
      const contentType = response.headers()["content-type"];
      if (contentType?.includes("application/json")) {
        console.debug(
          "Response:",
          JSON.stringify(await response.json(), null, 2),
        );
      } else {
        const text = await response.text();
        console.debug("Response (non-JSON):", text);
      }
    }
  }
  /**
   * Sends an API request with retry logic.
   * @param {string} endpoint - The API endpoint to call.
   * @param {string} method - The HTTP method (e.g., 'GET', 'POST').
   * @param {object} [body={}] - The request body.
   * @param {RequestHeaders} [headers={}] - Additional request headers.
   * @returns {Promise<any>} The JSON response from the API.
   * @throws {Error} If the request fails after all retries or if a non-JSON response is received.
   */
  async sendRequest(
    endpoint: string,
    method: string,
    body: object = {},
    headers: RequestHeaders = {},
  ): Promise<any> {
    this.validateToken();
    const context: APIRequestContext = await request.newContext();
    const baseUrl = backend.apiUrl.endsWith("/")
      ? backend.apiUrl.slice(0, -1)
      : backend.apiUrl;
    const normalizedEndpoint = endpoint.startsWith("/")
      ? endpoint.slice(1)
      : endpoint;
    const url = `${baseUrl}/${normalizedEndpoint}`;
    const mergedHeaders: RequestHeaders = {
      ...backend.headers.api,
      ...headers,
      "Content-Type": "application/json",
      Authorization: `Bearer ${this.authToken}`,
    };
    const options = {
      method,
      data: body,
      headers: mergedHeaders,
      timeout: this.setConf.timeout,
      ignoreHTTPSErrors: this.setConf.ignoreHTTPSErrors,
    };

    const maxRetries = this.setConf.maxRetries ?? 3;
    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response: APIResponse = await context.fetch(url, options);
        await this.verboseLog(url, options, response);

        if (response.ok()) {
          const contentType = response.headers()["content-type"];
          if (contentType?.includes("application/json")) {
            return await response.json();
          } else {
            // For non-JSON success responses, return the text body.
            return await response.text();
          }
        }

        lastError = new Error(
          `API request failed with status ${response.status()}: ${await response.text()}`,
        );
        console.warn(`Attempt ${attempt} failed: ${lastError.message}`);
      } catch (error) {
        if (error instanceof Error) {
          lastError = error;
        } else {
          lastError = new Error(String(error));
        }

        if (attempt === maxRetries) {
          console.error(`All ${maxRetries} attempts failed.`);
          throw lastError;
        }
        console.warn(
          `Retrying request (${attempt}/${maxRetries}) due to error: ${lastError.message}`,
        );
      }
    }
    // This part is reached if all retries fail due to non-ok responses
    throw new Error(
      `All ${maxRetries} attempts failed. Last error: ${lastError?.message}`,
    );
  }
  /**
   * Sends a GET request to the specified endpoint.
   * @param {string} endpoint - The API endpoint.
   * @param {object} [body={}] - The request body (typically not used for GET, but included for consistency).
   * @param {RequestHeaders} [headers={}] - Additional request headers.
   * @returns {Promise<any>} The JSON response from the API.
   */
  async sendGetRequest(
    endpoint: string,
    body: object = {},
    headers: RequestHeaders = {},
  ): Promise<any> {
    return await this.sendRequest(endpoint, "GET", body, headers);
  }
  /**
   * Sends a POST request to the specified endpoint.
   * @param {string} endpoint - The API endpoint.
   * @param {object} [body={}] - The request body.
   * @param {RequestHeaders} [headers={}] - Additional request headers.
   * @returns {Promise<any>} The JSON response from the API.
   */
  async sendPostRequest(
    endpoint: string,
    body: object = {},
    headers: RequestHeaders = {},
  ): Promise<any> {
    return await this.sendRequest(endpoint, "POST", body, headers);
  }
  /**
   * Sends a PUT request to the specified endpoint.
   * @param {string} endpoint - The API endpoint.
   * @param {object} [body={}] - The request body.
   * @param {RequestHeaders} [headers={}] - Additional request headers.
   * @returns {Promise<any>} The JSON response from the API.
   */
  async sendPutRequest(
    endpoint: string,
    body: object = {},
    headers: RequestHeaders = {},
  ): Promise<any> {
    return await this.sendRequest(endpoint, "PUT", body, headers);
  }
  /**
   * Sends a PATCH request to the specified endpoint.
   * @param {string} endpoint - The API endpoint.
   * @param {object} [body={}] - The request body.
   * @param {RequestHeaders} [headers={}] - Additional request headers.
   * @returns {Promise<any>} The JSON response from the API.
   */
  async sendPatchRequest(
    endpoint: string,
    body: object = {},
    headers: RequestHeaders = {},
  ): Promise<any> {
    return await this.sendRequest(endpoint, "PATCH", body, headers);
  }
  /**
   * Sends a DELETE request to the specified endpoint.
   * @param {string} endpoint - The API endpoint.
   * @param {RequestHeaders} [headers={}] - Additional request headers.
   * @returns {Promise<any>} The JSON response from the API.
   */
  async sendDeleteRequest(
    endpoint: string,
    headers: RequestHeaders = {},
  ): Promise<any> {
    return await this.sendRequest(endpoint, "DELETE", {}, headers);
  }
}
export default new ApiHelper();
