{"name": "@up/e2e-common", "version": "0.0.1", "description": "Reusable E2E Playwright Automation Library", "main": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "prettier:config": "prettier 'src/**/*.{ts,js}'", "prettier:check": "pnpm prettier:config --check", "prettier": "pnpm prettier:config --write", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.t  s --fix", "verify": "pnpm prettier:check && pnpm lint"}, "dependencies": {"playwright-bdd": "catalog:playwright", "@playwright/test": "catalog:playwright", "dotenv": "^16.4.7", "allure-commandline": "^2.33.0", "allure-playwright": "^3.2.1"}, "devDependencies": {"@types/node": "24.0.8", "@up/eslint-config": "workspace:*", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*", "@vitest/ui": "catalog:vitest", "eslint": "8.57.1", "prettier": "3.6.2", "tsup": "8.5.0", "type-fest": "4.41.0", "typescript": "5.7.3", "vitest": "catalog:vitest"}, "prettier": "@up/prettier-config"}