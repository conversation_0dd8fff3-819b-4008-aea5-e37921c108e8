import { cva } from "class-variance-authority";
import { Layer } from "../core";

export const buttonVariants = cva("flex cursor-pointer", {
  variants: {
    layer: {
      [Layer.Primary]:
        "py-s px-l mx-2 cursor-pointer rounded-80 focus-visible-on-color typography-paragraph1",
      [Layer.Secondary]:
        "py-3.5 px-xl text-[13px] cursor-pointer leading-5 relative text-semantic-content-base-secondary focus-visible-default",
    },
    active: {
      true: "",
      false: "",
    },
    loading: {
      true: "hover:bg-transparent cursor-default",
      false: "",
    },
    current: {
      true: "",
      false: "",
    },
    parent: {
      true: "",
      false: "",
    },
  },

  compoundVariants: [
    {
      layer: Layer.Primary,
      parent: true,
      loading: false,
      class: "bg-semantic-surface-nav-topBar-default",
    },
    {
      layer: Layer.Primary,
      active: false,
      class: "hover:bg-semantic-surface-nav-topBar-hover",
    },
    {
      layer: Layer.Primary,
      active: true,
      class: "bg-semantic-surface-nav-topBar-active ", // hover:bg-semantic-surface-nav-topBar-active
    },
    {
      layer: Layer.Secondary,
      active: true,
      class:
        "text-semantic-content-base-primary font-medium after:content-[''] after:absolute after:w-full after:h-xxs after:left-none after:-bottom-[1px] after:bg-semantic-surface-interactive-primary-default after:rounded-40",
    },
    {
      layer: Layer.Primary,
      current: true,
      active: false,
      class: "bg-semantic-surface-nav-topBar-default",
    },
  ],
});

export const outerWrapperVaraints = cva("flex cursor-pointer", {
  variants: {
    orientation: {
      horizontal: "flex justify-between items-center w-full",
      vertical: "",
    },
    layer: {
      [Layer.Root]: "",
      [Layer.Primary]: "",
      [Layer.Secondary]:
        "border-b-semantic-border-base-primary border border-x-0 border-t-0",
      [Layer.Tertiary]: "",
    },
    dismissible: {
      true: "",
      false: "",
    },
  },
});

// For the wrapping <div/>
export const wrapperVaraints = cva("flex cursor-pointer", {
  variants: {
    orientation: {
      horizontal: "flex-row items-center justify-center grow",
      vertical: "flex-col",
    },
    layer: {
      [Layer.Root]: "",
      [Layer.Primary]: "text-semantic-content-immutable-white",
      [Layer.Secondary]:
        "relative typography-paragraph1  w-[100%] items-center",
      [Layer.Tertiary]: "",
    },
    dismissible: {
      true: "relative",
      false: "",
    },
  },
});

export const iconVaraints = cva("", {
  variants: {
    layer: {
      [Layer.Secondary]:
        "pr-s icon-p1 text-semantic-content-base-tertiary fa-regular",
    },
    active: {
      true: null,
      false: null,
    },
  },
  compoundVariants: [
    {
      layer: Layer.Secondary,
      active: true,
      class: "text-semantic-content-interactive-primary-default fa-solid",
    },
  ],
});
