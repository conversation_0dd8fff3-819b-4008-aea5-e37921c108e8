import { cn } from "@up/components/common";
import { LoadingSkeleton, Tooltip } from "@zs-nimbus/core";
import { props, restProps, testIdFrom } from "@up/std";
import {
  Layer,
  type NavigationVisibility,
  Visibility,
  type TreeNode,
  filterHiddenNodes,
  resolveRoute,
} from "../core";
import {
  getAlternateIndex,
  useNavigationContext,
  useNavigationStore,
} from "../context";
import { type BaseNavigationProps, type NavigateFn } from "./types";
import { MenuExpansion } from "./MenuExpansion";
import {
  activeVariants,
  buttonVariants,
  indentationVariants,
  wrapperVariants,
} from "./NavigationTreeNodeStyle";

type Props<T> = {
  node: TreeNode<T>;
  onNavigate: NavigateFn<T>;
  active?: string;
  selection: string[];
  disabledText?: string;
  onExpanded: (node: TreeNode<T>) => void;
  primaryLayer?: Layer;
  collapsed?: boolean;
} & BaseNavigationProps<T>;

export const NavigationTreeNode = <T extends object>({
  node,
  entitlements,
  onNavigate,
  active,
  selection,
  disabledText = "FEATURE_DISABLED",
  onExpanded,
  primaryLayer = Layer.Tertiary,
  collapsed = false,
  ...rest
}: Props<T>) => {
  let layer = node?.option.layer ?? Layer.Primary;
  const { t, navigator } = useNavigationContext();
  const { store, setActive, selectedIds } = useNavigationStore();

  let disabledTooltip = disabledText;
  if (node.option.options?.disabledTooltip) {
    disabledTooltip = t(node.option.options.disabledTooltip ?? disabledText);
  }

  const sids = selectedIds();

  let isActive = node.id === active;

  if (collapsed && sids.includes(node.id)) {
    isActive = true;
  }

  if (!isActive && selection.includes(node.id)) {
    node.expanded = true;
    isActive = !isActive && !node.expanded;
  }

  if (isActive) {
    node.expanded = true;
  }

  let state: NavigationVisibility = "visible";
  if (entitlements) {
    state = node.option.visibility?.(entitlements) ?? "visible";
  }

  if (layer === Layer.Quaternary) {
    const pnode = store.tree.get(node.parentId!);
    if (store.tree.isContainer(pnode!)) {
      layer = Layer.Tertiary;
    }
  }

  const isDisabled = state === "disabled";

  const classes = cn(
    wrapperVariants({
      active: isActive,
      disabled: isDisabled,
    }),
  );

  // TODO: Convert to CVA
  const hoverItemClasses = isDisabled
    ? ""
    : isActive
      ? ""
      : "hover:[&:not(:has(.fa-caret-down:hover,.fa-caret-right:hover))]:bg-semantic-surface-nav-sideBar-hover";

  const nodeClasses = cn(
    classes,
    isDisabled
      ? "cursor-default text-semantic-content-interactive-primary-disabled"
      : "cursor-pointer",
    isActive ? "text-semantic-border-interactive-primary-default" : "",
  );

  const buttonClasses = cn(
    buttonVariants({
      active: isActive,
      border: layer as number,
    }),
  );

  let itemIcon = node.option.icon;
  if (itemIcon?.split(" ").length === 1) {
    itemIcon = `${isActive ? "fa-solid" : "fa-regular"}  ${itemIcon}`;
  }

  if (state === "hidden") {
    return null;
  }

  let filteredChildren = store.tree.resolveChildren(
    node as TreeNode<object>,
    true,
    getAlternateIndex(),
  );

  if (entitlements) {
    filteredChildren = filteredChildren?.filter((n: TreeNode<T>) => {
      return !filterHiddenNodes(n, entitlements);
    });
  }

  const hasChildren = filteredChildren.length > 0;

  const renderChildNodes = () => (
    <>
      {filteredChildren.map((n: string | TreeNode<T>) => {
        if (typeof n === "string") {
          n = store.tree.get(n) as TreeNode<T>;
        }

        return (
          <NavigationTreeNode<T>
            key={n.id}
            node={n}
            onNavigate={onNavigate}
            entitlements={entitlements}
            active={active}
            selection={selection}
            disabledText={disabledText}
            onExpanded={onExpanded}
            primaryLayer={primaryLayer}
            testId={testIdFrom(rest, ["sub-menu"])}
          />
        );
      })}
    </>
  );

  const handlePrefetch = (n: TreeNode<T>) => {
    if (navigator) {
      const href = resolveRoute(
        n.option.route ?? "/",
        entitlements,
        getAlternateIndex(),
      );
      navigator.prefetch(href);
    }
  };

  return (
    <div className={classes} {...restProps(rest)} data-nodeid={node.id}>
      {state === Visibility.LOADING ? (
        <div
          className={cn(
            "w-full",
            indentationVariants({ layer: layer as number }),
          )}
        >
          <LoadingSkeleton variant="text" />
        </div>
      ) : (
        <div className="flex flex-col w-full" data-navid={node.id}>
          <div
            className={cn(
              "flex items-center justify-between",
              hoverItemClasses,
              activeVariants({ active: isActive }),
              layer === Layer.Quinary ? "" : "py-default", // TODO: Fix
              indentationVariants({ layer: layer as number }),
            )}
          >
            <a
              type="button"
              href={resolveRoute(
                node.option.route ?? "/",
                entitlements,
                getAlternateIndex(),
              )}
              className={cn("flex justify-between", nodeClasses)}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();

                if (node && state !== Visibility.DISABLED) {
                  if (node.option.route !== undefined) {
                    setActive(node as TreeNode<object>);
                    onNavigate(
                      node.option.route ?? "",
                      node.option.external ?? false,
                      entitlements,
                    );
                  } else {
                    onExpanded(node);

                    // The following is added under protest. While it mimicks legcy nav functionality, it is an bad UX
                    // behaviour to route someone unexpectadly.
                    // 04/16/05 - Reverting to support a Risk360 Requirement, we will work with UX post to get acceptance
                    // or work on another solution.
                    // const routeableChild = findFirstRouteNode(
                    //   node,
                    //   entitlements,
                    // );
                    // if (routeableChild) {
                    //   setActive(routeableChild as TreeNode<object>);
                    //   onNavigate(
                    //     routeableChild.option.route ?? "",
                    //     routeableChild.option.external ?? false,
                    //     entitlements,
                    //   );
                    // }
                  }
                }
              }}
              onMouseEnter={() => {
                if (
                  (node && state === Visibility.DEFAULT) ||
                  state === Visibility.NEW
                ) {
                  if (node.option.route !== undefined) {
                    handlePrefetch(node);
                  }
                }
              }}
              {...props({
                testId: testIdFrom(rest, [
                  node.option.key.toLowerCase(),
                  "button",
                ]),
              })}
              tabIndex={0}
            >
              <div className={buttonClasses}>
                {node.option.icon && (
                  <i
                    className={cn(
                      itemIcon,
                      "text-[16px] leading-5 min-w-[20px]",
                    )}
                  />
                )}
                {!collapsed && (
                  <div className="typography-paragraph1 w-full line-clamp-2 break-words items-center">
                    <div className="flex items-center gap-x-2">
                      {t(node.option.key)}
                      {node.option.external && (
                        <div className="text-semantic-content-interactive-primary-default">
                          <i
                            aria-label={t("UP_RIGHT_ICON")}
                            className="fa-regular fa-arrow-up-right-from-square fa-2xs"
                          />
                        </div>
                      )}

                      {state === Visibility.DISABLED && (
                        <Tooltip
                          description={t(disabledTooltip)}
                          placement="right"
                        >
                          <button
                            disabled={state == Visibility.DISABLED}
                            type="button"
                            className="typography-paragraph2 cursor-default text-semantic-content-interactive-primary-disabled"
                          >
                            <i
                              aria-label={t("DISABLED_ICON")}
                              className="fa-solid fa-lock text-semantic-content-interactive-primary-disabled pl-m"
                            />
                          </button>
                        </Tooltip>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </a>
            {hasChildren && !collapsed && !isDisabled && (
              <MenuExpansion
                node={node}
                active={isActive}
                layer={layer}
                onToggleExpansion={onExpanded}
                primaryLayer={primaryLayer}
                testId={testIdFrom(rest, [
                  node.option.key.toLowerCase(),
                  "expansion",
                ])}
              />
            )}
          </div>
          {!collapsed && node.expanded && renderChildNodes()}
        </div>
      )}
    </div>
  );
};
