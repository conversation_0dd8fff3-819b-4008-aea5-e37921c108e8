import tailwindDefault from "tailwindcss/defaultConfig";
import { datavizColorsPreset } from "@zs-nimbus/dataviz-colors";
import { foundationsPreset } from "@zs-nimbus/foundations";

const isBuild = process.env.BUILD === "true";

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,ts,jsx,tsx,mdx}"],
  corePlugins: {
    preflight: false, //isBuild ? false : true,
  },
  presets: [tailwindDefault, foundationsPreset, datavizColorsPreset],
  theme: {
    extend: {
      spacing: {
        ...tailwindDefault.theme?.spacing,
      },
    },
  },
  plugins: [],
};
