import { Children, useEffect, useMemo, useState } from "react";
import { cn } from "../common";
import { type CarouselProps } from "./types";

export const Carousel = ({
  children,
  autoSlide = true,
  slideInterval = 3000,
  hideSideNav = false,
  hideFooterNav = false,
  sideNavButtons,
  style,
  containerClass = "",
  id,
  ...rest
}: CarouselProps) => {
  const ID = `carousel${id ? `-${id}` : ""}`;
  const [current, setCurrent] = useState(0);

  const components = useMemo(() => Children.toArray(children), [children]);
  const componentLength = components.length;

  useEffect(() => {
    setCurrent(0);
  }, [componentLength]);

  useEffect(() => {
    if (!autoSlide) return;
    const timer = setInterval(() => {
      setCurrent((prev) => (prev === componentLength - 1 ? 0 : prev + 1));
    }, slideInterval);
    return () => clearInterval(timer);
  }, [autoSlide, slideInterval, componentLength]);

  if (componentLength === 0) return null;

  const { sideNavClass, footerNavClass } = style ?? {};
  const { generic, active, inactive } = footerNavClass ?? {};
  const commonButtonClass =
    "absolute top-1/2 transform -translate-y-1/2 flex items-center justify-center w-10 h-10 rounded-240 border border-semantic-surface-interactive-primary-default shadow-2xl text-semantic-surface-interactive-primary-default";

  const styles = {
    cardSize: cn("relative overflow-hidden", containerClass),
    footerNav: {
      left: cn(commonButtonClass, "left-1", sideNavClass ?? ""),
      right: cn(commonButtonClass, "right-1", sideNavClass ?? ""),
    },
    sideNav: (isCurrent: boolean) =>
      cn(
        "w-3 h-3 rounded-240",
        isCurrent
          ? `bg-semantic-surface-interactive-primary-default ${active ?? ""}`
          : `bg-semantic-content-base-subdued ${inactive ?? ""}`,
        generic ?? "",
      ),
  };

  return (
    <div className={styles.cardSize} {...rest} data-testid={ID}>
      <div
        className="flex transition-transform duration-700 ease-in-out justify-stretch"
        style={{ transform: `translateX(-${current * 100}%)` }}
      >
        {components.map((child, index) => (
          <div
            key={index}
            className="w-full flex-shrink-0"
            data-testid={`${ID}-${index}`}
          >
            {child}
          </div>
        ))}
      </div>
      {!hideSideNav && componentLength > 1 && (
        <>
          {current !== 0 && (
            <button
              onClick={() =>
                setCurrent(current === 0 ? componentLength - 1 : current - 1)
              }
              className={styles.footerNav.left}
              data-testid={`${ID}-show-side-left`}
            >
              {sideNavButtons.left}
            </button>
          )}
          {current !== componentLength - 1 && (
            <button
              onClick={() =>
                setCurrent(current === componentLength - 1 ? 0 : current + 1)
              }
              className={styles.footerNav.right}
              data-testid={`${ID}-show-side-right`}
            >
              {sideNavButtons.right}
            </button>
          )}
        </>
      )}
      {!hideFooterNav && componentLength > 1 && (
        <div
          className="absolute bottom-0 left-0 right-0 flex justify-center space-x-2 pb-4"
          data-testid={`${ID}-dots`}
        >
          {components.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrent(index)}
              className={styles.sideNav(index === current)}
              data-testid={`${ID}-dot-${index}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};
