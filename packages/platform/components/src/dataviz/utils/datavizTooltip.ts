import { cn } from "../../common/utils";
import { DatavizTooltipDataType } from "../common/types";

const tooltipContainerClass = `py-2 px-3  max-w-xs rounded-40 text-semantic-content-base-primary bg-semantic-surface-fields-default `;
const labelClass =
  "items-center text-semantic-content-base-primary typography-paragraph1-strong";

const renderLabel = (label?: string, className?: string): string => {
  const style = cn(labelClass, className);

  return label ? `<div class="${style}">${label}</div>` : "";
};

const renderContentItem = ({
  label,
  value,
}: DatavizTooltipDataType): string => {
  return label || value
    ? `<div class="flex items-center justify-between my-xs">
        ${label ? `<div class="${value?.toString() ? "mr-rem-160" : ""} text-semantic-content-base-primary typography-paragraph2">${label}</div>` : ""}
        ${value ? `<div class="text-semantic-content-base-tertiary typography-paragraph2">${value}</div>` : ""}
       </div>`
    : "";
};

const renderContentList = (data?: DatavizTooltipDataType[]): string => {
  return data ? data.map(renderContentItem).join("") : "";
};

const renderSection = (
  data?: DatavizTooltipDataType[],
  isLast = false,
): string => {
  return data
    ? `<div class="${isLast ? "" : "mb-rem-40 border-b-[1px] border-semantic-border-base-subdued"}">
        ${renderContentList(data)}
       </div>`
    : "";
};

const renderTooltip = (data?: DatavizTooltipDataType[]): string => {
  return data
    ? data
        .map(
          (d, i) => `
        ${renderLabel(d.label, d.className)}
        ${renderSection(d.data, i === data.length - 1)}
       `,
        )
        .join("")
    : "";
};

export const getDatavizLabelHtml = (data: DatavizTooltipDataType[]): string => {
  const className = cn(tooltipContainerClass, data?.[0]?.container_class);
  return `
    <div class="${className}">
      ${renderTooltip(data)}
    </div>
  `;
};

export const getDatavizTooltip = getDatavizLabelHtml;
