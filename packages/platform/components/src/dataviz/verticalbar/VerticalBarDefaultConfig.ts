import { DatavizTooltipDataType, type ThemeType } from "../common/types";
import { type VerticalBarDefaultConfigProps } from "./types";
import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { getDatavizTooltip } from "../utils/datavizTooltip";

export const getVerticalBarDefaultConfig = (
  theme: ThemeType,
): VerticalBarDefaultConfigProps => {
  return {
    containerClass: "flex h-[550px]",
    chartProps: {
      chartSettings: {
        panX: false,
        panY: false,
        pinchZoomX: false,
        pinchZoomY: false,
      },
    },
    columnProps: {
      strokeOpacity: 0,
      maxWidth: 24,
    },
    roundedColumnProps: {
      cornerRadiusTL: 5,
      cornerRadiusTR: 5,
    },
    axisProps: {
      xAxisProps: { visible: false },
      yAxisProps: {
        visible: false,
      },
      xGridProps: {
        visible: false,
      },
      yAxisSetting: {
        min: 0,
      },
      xLabelProps: {
        rotation: 0,
      },
    },
    tooltipProps: {
      showTooltip: true,
      getTooltipLabelHtml: getDatavizTooltip,
      getTooltipData: (data: unknown) => {
        if (typeof data === "object" && data !== null) {
          const d = data as { country: string; value: number };
          return [
            { label: `${d.country} ${d.value}` },
          ] as DatavizTooltipDataType[];
        }

        // Fallback for cases where data does not contain a label
        return [{ label: "" }] as DatavizTooltipDataType[];
      },
    },
    gradientProps: {
      showGradient: true,
      stops: [
        {
          color: datavizColors[theme].dataviz.categorical.primary.chart01,
          opacity: 1,
        },
        {
          color: "#4B7EEC", // No Nimbus equivalent
          opacity: 1,
        },
      ],
      rotation: 90,
    },

    scrollProps: {
      scrollSetting: {
        orientation: "horizontal",
      },
      scrollBackgroundSetting: {
        scrollColor: datavizColors[theme].dataviz.neutrals.gaps,
        orientation: "horizontal",
      },
      thumbSetting: {
        thumbColor: datavizColors[theme].dataviz.sequential.blue.scale02,
        fillOpacity: 0.2,
        opacity: 1,
      },
      overlaySetting: {
        overlayColor: datavizColors[theme].dataviz.neutrals.gaps,
      },
    },
  };
};
