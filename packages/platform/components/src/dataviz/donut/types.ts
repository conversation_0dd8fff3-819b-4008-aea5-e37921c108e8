import {
  type IContainerSettings,
  type ILabelSettings,
  type IGraphicsSettings,
  type INumberFormatterSettings,
} from "@amcharts/amcharts5";
import { type IPieChartSettings } from "@amcharts/amcharts5/percent";
import {
  type DatavizLegendsProps,
  type ThemeType,
  type DatavizTooltipProps,
  type DatavizColorType,
} from "../common/types";

export type DonutDataType = {
  count: number;
  category: string;
  color?: string;
  type?: string;
  categoryLabel?: string;
};

export type DatavizDonutDataType = DonutDataType;

type TextProp = ILabelSettings & {
  fillColor?: string;
  centerX?: number;
  centerY?: number;
  labels?: string[];
};

type LabelsProps = {
  alignLabels?: boolean;
  centerLabel?: TextProp;
  fontSmall?: number;
  fontMedium?: number;
  fontLarge?: number;
  labelSetting?: ILabelSettings & {
    fillColor?: string;
  };
};

export type DonutChartProps = {
  chartSettings?: IPieChartSettings & { radius: number; innerRadius: number };
  leftContainer?: IContainerSettings;
  rightContainer?: IContainerSettings;
  chartContainer?: IContainerSettings;
};

export type DatavizDonutConfig = {
  pieSetting?: IGraphicsSettings &
    DatavizColorType & {
      radius?: number;
      innerRadius?: number;
    };
  labelProps?: LabelsProps;
  containerClass?: string;
  tooltipProps?: DatavizTooltipProps;
  legendProps?: DatavizLegendsProps;
  numberFormatter?: INumberFormatterSettings;
  chartProps?: DonutChartProps;
  emptyProp?: {
    showEmpty: boolean;
    data?: DatavizDonutDataType[];
    emptyText?: string;
  };
  isNestedDonut?: boolean;
};

export type DatavizDonutProps = {
  data: {
    slice: DatavizDonutDataType[];
    outerSlice?: DatavizDonutDataType[];
  };
  dataKeys: [string, string];
  config: DatavizDonutConfig;
  chartId?: string | undefined;
  theme: ThemeType;
};
