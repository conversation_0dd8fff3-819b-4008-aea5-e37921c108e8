import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { colors } from "@zs-nimbus/foundations";
import { type ThemeType } from "../common/types";
import { type DatavizDonutProps } from "./types";
import { getDatavizTooltip } from "../utils/datavizTooltip";

export const getDefaultConfig = (
  theme: ThemeType = "light",
): DatavizDonutProps["config"] => {
  return {
    labelProps: {
      centerLabel: {
        fontSize: 24,
        fontWeight: "500",
        populateText: true,
        fillColor: colors[theme].surface.alpha.overlay,
        oversizedBehavior: "fit",
        textAlign: "center",
        centerX: 50,
        centerY: 50,
      },
      labelSetting: {
        visible: false,
        fontSize: 10,
        fontWeight: "200",
      },
    },
    tooltipProps: {
      tooltipSetting: {
        keepTargetHover: true,
        interactive: true,
        pointerOrientation: "right",
        tooltipPosition: "fixed",
      },
    },
    pieSetting: {
      strokeColor: datavizColors[theme].dataviz.neutrals.gaps,
      strokeWidth: 1,
      tooltipHTML: getDatavizTooltip([
        { label: "{category} {count}" },
        { label: `{valuePercentTotal.formatNumber('#.00')}%` },
      ]),
    },
    chartProps: {
      chartSettings: { radius: 100, innerRadius: 75 },
    },

    legendProps: {
      showLegend: true,
      legendSetting: {
        nameField: "name",
        fillField: "color",
        strokeField: "color",
        centerX: 50,
        x: 50,
      },
      legendMarkerProps: {
        height: 8,
        width: 8,
      },
      legendLabelProps: {
        legendLabelColor: colors[theme].content.base.primary,
        fontSize: 13,
        fontWeight: "400",
      },
      legendRectangleProps: {
        cornerRadiusTL: 5,
        cornerRadiusTR: 5,
        cornerRadiusBL: 5,
        cornerRadiusBR: 5,
      },
    },
    emptyProp: {
      showEmpty: false,
      data: [
        {
          category: "a",
          count: 1,
          color: datavizColors[theme].dataviz.neutrals.others,
        },
      ],
    },
  };
};
