import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Card } from "@zs-nimbus/core";
import { colors } from "@zs-nimbus/foundations";
import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { Donut as DonutChart } from "./Donut";
import { DonutData } from "./mock.data";
import { getDatavizTooltip } from "../utils";

const meta = {
  title: "dataviz/Donut",
  component: DonutChart,
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/wLxNEe788V2qdOKQDy31Of/CXO---Desktop-(Analytics)?type=design&node-id=2434-55252&mode=design&t=deBeYFA7WP3acawi-0",
    },
  },
} as Meta<typeof DonutChart>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Donut: Story = {
  args: {
    theme: "light",
  },
  decorators: [(Story) => <Story />],
};
const theme = "light";
Donut.decorators = [
  () => (
    <div className="mx-l">
      <Card>
        <Card.Header>Donut chart with legends, tooltip</Card.Header>
        <div>
          <DonutChart
            theme="light"
            data={DonutData}
            dataKeys={["count", "category"]}
            config={{
              containerClass: "flex h-[300px]",
              labelProps: {
                centerLabel: {
                  labels: [`{valueSum.formatNumber()}`, " Threats Blocked"],
                  fillColor: colors.light.content.base.primary,
                },
                labelSetting: {
                  visible: false,
                  text: "{category}",
                  fontSize: 10,
                  fontWeight: "400",
                },
                fontLarge: 13,
                fontMedium: 10,
                fontSmall: 9,
              },
              legendProps: {
                legendSetting: {
                  nameField: "name",
                  fillField: "color",
                  strokeField: "color",
                  centerX: 50,
                  x: 50,
                },
                legendMarkerProps: {
                  height: 15,
                  width: 15,
                },
                legendLabelProps: {
                  legendLabelColor: colors.light.content.base.primary,
                  fontSize: 13,
                  fontWeight: "400",
                },
                legendRectangleProps: {
                  cornerRadiusTL: 15,
                  cornerRadiusTR: 15,
                  cornerRadiusBL: 15,
                  cornerRadiusBR: 15,
                },
              },
              pieSetting: {
                strokeColor: colors.light.content.inverted.base.primary,
                strokeWidth: 2,
              },
              tooltipProps: {
                showTooltip: false,
                tooltipSetting: {
                  pointerOrientation: "left",
                  paddingBottom: 0,
                  paddingLeft: 0,
                  paddingRight: 0,
                  paddingTop: 0,
                },
              },
              emptyProp: {
                showEmpty: false,
                data: [
                  {
                    category: "a",
                    count: 1,
                    color: datavizColors.light.dataviz.neutrals.others,
                  },
                ],
              },
            }}
          />
        </div>
      </Card>

      <Card>
        <Card.Header>Donut chart with right legends</Card.Header>
        <div>
          <DonutChart
            theme="light"
            data={DonutData}
            dataKeys={["count", "category"]}
            config={{
              containerClass: "flex h-[220px] mb-rem-160",

              labelProps: {
                centerLabel: {
                  labels: [`{valueSum.formatNumber()}`, " Threats Blocked"],
                },
              },
              legendProps: {
                direction: "right",
                legendLabelProps: {
                  legendLabelColor: colors[theme].content.base.tertiary,
                  fontSize: 12,
                },
                legendMarkerProps: {
                  height: 15,
                  width: 15,
                },
                legendRectangleProps: {
                  cornerRadiusTL: 4,
                  cornerRadiusTR: 4,
                  cornerRadiusBL: 4,
                  cornerRadiusBR: 4,
                },
              },
              pieSetting: {
                strokeColor: colors[theme].content.inverted.base.primary,
                strokeWidth: 1,
                tooltipHTML: getDatavizTooltip([
                  { label: "{category}" },
                  { label: `{count.formatNumber()}` },
                ]),
              },
            }}
          />
        </div>
      </Card>

      <Card className="mt-2">
        <Card.Header>Empty Donut</Card.Header>
        <div>
          <DonutChart
            theme="light"
            data={DonutData}
            dataKeys={["count", "category"]}
            config={{
              containerClass: "flex h-[300px]",
              labelProps: {
                centerLabel: {
                  dy: -15,
                  labels: ["0", "Total Threats"],
                },
                labelSetting: {
                  visible: false,
                  text: "{category}",
                  fontSize: 10,
                  fontWeight: "400",
                },
                fontLarge: 13,
                fontMedium: 10,
                fontSmall: 9,
              },
              pieSetting: {
                strokeColor: colors.light.content.inverted.base.primary,
                strokeWidth: 2,
              },
              emptyProp: {
                showEmpty: true,
                data: [
                  {
                    category: "a",
                    count: 1,
                    color: datavizColors.light.dataviz.neutrals.others,
                  },
                ],
              },
            }}
          />
        </div>
      </Card>
    </div>
  ),
];
