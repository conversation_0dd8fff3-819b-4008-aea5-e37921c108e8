{"name": "@up/components", "version": "0.0.7", "description": "A frontend Component Package.", "type": "module", "files": ["dist"], "exports": {"./carousel": {"import": "./dist/carousel/index.js", "types": "./dist/carousel/index.d.ts"}, "./common": {"import": "./dist/common/index.js", "types": "./dist/common/index.d.ts"}, "./toggle": {"import": "./dist/input/index.js", "types": "./dist/input/index.d.ts"}, "./table": {"import": "./dist/Table/index.js", "types": "./dist/Table/index.d.ts"}, "./dataviz": {"import": "./dist/dataviz/index.js", "types": "./dist/dataviz/index.d.ts"}, ".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./css": "./dist/index.css"}, "scripts": {"build": "BUILD=true tsup", "dev": "tsup --watch", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "prettier:config": "prettier 'src/**/*.{ts,js}'", "prettier:check": "pnpm prettier:config --check", "prettier": "pnpm prettier:config --write", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "verify": "pnpm prettier:check && pnpm lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build-storybook-test": "storybook build --test", "lost-pixel": "LOST_PIXEL_DISABLE_TELEMETRY=1 lost-pixel", "lost-pixel:update": "LOST_PIXEL_DISABLE_TELEMETRY=1 lost-pixel update --configDir ./.lostpixel/config"}, "dependencies": {"class-variance-authority": "0.7.1", "clsx": "2.1.1", "react-aria": "3.37.0", "react-aria-components": "1.7.1", "react-i18next": "15.0.2", "tailwind-merge": "2.6.0", "lodash.merge": "4.6.2"}, "devDependencies": {"@chromatic-com/storybook": "catalog:storybook", "@storybook/addon-designs": "catalog:storybook", "@storybook/addon-essentials": "catalog:storybook", "@storybook/addon-interactions": "catalog:storybook", "@storybook/addon-links": "catalog:storybook", "@storybook/addon-mdx-gfm": "catalog:storybook", "@storybook/addon-onboarding": "catalog:storybook", "@storybook/addon-webpack5-compiler-swc": "catalog:storybook", "@storybook/addon-styling-webpack": "catalog:storybook", "@storybook/blocks": "catalog:storybook", "@storybook/nextjs": "catalog:storybook", "@storybook/react": "catalog:storybook", "@storybook/react-vite": "catalog:storybook", "@storybook/react-webpack5": "catalog:storybook", "@storybook/test": "catalog:storybook", "@types/lodash.merge": "4.6.9", "@types/node": "24.0.8", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@up/eslint-config": "workspace:*", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*", "@vitest/ui": "catalog:vitest", "autoprefixer": "10.4.21", "css-loader": "7.1.2", "style-loader": "3.3.3", "eslint": "8.57.1", "lost-pixel": "3.22.0", "postcss": "8.5.6", "postcss-loader": "8.1.1", "postcss-url": "10.1.3", "prettier": "3.6.2", "storybook": "catalog:storybook", "tailwindcss": "3.4.17", "tsup": "8.5.0", "type-fest": "4.41.0", "typescript": "5.7.3", "vitest": "catalog:vitest"}, "peerDependencies": {"react": ">= 18.3.1 < 19", "react-dom": ">= 18.3.1 < 19", "@zs-nimbus/core": "1.2.0", "@zs-nimbus/dataviz-colors": "1.1.0", "@zs-nimbus/foundations": "1.3.0", "@fortawesome/fontawesome-pro": "~6.5.1", "@fortawesome/fontawesome-svg-core": "~6.6.0", "@fortawesome/free-brands-svg-icons": "~6.6.0", "@fortawesome/free-solid-svg-icons": "~6.6.0", "@fortawesome/pro-light-svg-icons": "~6.5.1", "@fortawesome/pro-regular-svg-icons": "~6.5.1", "@fortawesome/pro-solid-svg-icons": "~6.5.1", "@fortawesome/react-fontawesome": "~0.2.2", "@amcharts/amcharts5": "~5.10.5", "@amcharts/amcharts5-geodata": "~5.1.4", "@tanstack/react-table": "^8.21.2"}, "prettier": "@up/prettier-config", "publishConfig": {"registry": "https://nexus.corp.zscaler.com/repository/up-npm-hosted/"}, "tsup": {"entry": ["src/input/index.ts", "src/carousel/index.ts", "src/common/index.ts", "src/Table/index.ts", "src/dataviz/index.ts", "src/index.ts", "!src/**/*.test.*", "!src/**/*.stories.*"], "format": ["esm"], "splitting": true, "sourcemap": false, "treeshake": true, "minify": false, "clean": true, "silent": true, "dts": {"resolve": true}, "external": ["react", "react-dom", "@amcharts/amcharts5", "@amcharts/amcharts5-geodata"]}}