{"$schema": "https://json.schemastore.org/tsconfig", "display": "@zcell/pages typescript configuration", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "target": "ESNext", "moduleResolution": "bundler", "skipLibCheck": true, "strictNullChecks": true}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "src/**/*.test.ts", "src/**/*.data.ts", "dist"]}