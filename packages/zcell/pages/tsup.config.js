import { defineConfig } from "tsup";

export default defineConfig((options) => {
  // NOTE: if you pass ./src to tsup, this entry will be ignored
  return {
    entry: ["src/index.ts", "!src/**/*.test.*"],
    format: ["esm"],
    splitting: false,
    sourcemap: false,
    minify: !options.watch,
    clean: true,
    silent: true,
    dts: {
      resolve: true,
    },
    external: ["react", "react-dom"],
    jsx: true,
  };
});
