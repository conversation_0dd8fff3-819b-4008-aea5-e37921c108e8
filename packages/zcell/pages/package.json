{"name": "@zcell/pages", "version": "0.0.1", "description": "Zscaler Cellular", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "build-storybook": "storybook build", "build-storybook-test": "storybook build --test", "dev": "tsup --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.t  s --fix", "lost-pixel": "LOST_PIXEL_DISABLE_TELEMETRY=1 lost-pixel", "lost-pixel:update": "LOST_PIXEL_DISABLE_TELEMETRY=1 lost-pixel update --configDir ./.lostpixel/config", "prettier": "pnpm prettier:config --write", "prettier:check": "pnpm prettier:config --check", "prettier:config": "prettier 'src/**/*.{ts,js}'", "storybook": "storybook dev -p 6006", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "verify": "pnpm prettier:check && pnpm lint"}, "dependencies": {"@amcharts/amcharts5": "5.10.5", "@amcharts/amcharts5-geodata": "5.1.4", "clsx": "2.1.1", "react": "18.3.1", "react-aria": "3.37.0", "react-dom": "18.3.1", "swr": "2.2.5", "tailwind-merge": "2.6.0", "zephyr": "25.318.1"}, "devDependencies": {"@chromatic-com/storybook": "catalog:storybook", "@storybook/addon-designs": "catalog:storybook", "@storybook/addon-essentials": "catalog:storybook", "@storybook/addon-interactions": "catalog:storybook", "@storybook/addon-links": "catalog:storybook", "@storybook/addon-onboarding": "catalog:storybook", "@storybook/addon-styling-webpack": "catalog:storybook", "@storybook/addon-webpack5-compiler-swc": "catalog:storybook", "@storybook/blocks": "catalog:storybook", "@storybook/nextjs": "catalog:storybook", "@storybook/react": "catalog:storybook", "@storybook/react-vite": "catalog:storybook", "@storybook/react-webpack5": "catalog:storybook", "@storybook/test": "catalog:storybook", "@testing-library/react": "16.2.0", "@types/node": "22.13.5", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@vitest/ui": "catalog:vitest", "autoprefixer": "10.4.20", "css-loader": "7.1.2", "eslint": "8.57.1", "lost-pixel": "3.22.0", "postcss": "8.5.3", "postcss-loader": "8.1.1", "postcss-url": "10.1.3", "prettier": "3.5.2", "storybook": "catalog:storybook", "style-loader": "3.3.3", "tailwindcss": "3.4.17", "tsup": "8.3.5", "type-fest": "4.37.0", "typescript": "5.7.3", "vitest": "catalog:vitest"}, "peerDependencies": {"@zs-nimbus/core": "4.0.0", "react": ">= 18.3.1", "react-dom": ">= 18.3.1"}}