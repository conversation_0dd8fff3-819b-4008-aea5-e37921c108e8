import { generateRandomId } from "@up/std";
import { C3ChannelName, type BroadcastMessage, type HandlerFn } from "./types";
import { EventLogEvent, EventLogout, EventsAll } from "./events";
import { C3ConsoleLogger } from "./logger";
import { isRunningLocally } from "@/utils/environment";
import { handleLogout } from "@/utils/auth/handleLogout";

// Cross Context Communication (C3) (using BroadcastChannel)
class C3EventBus {
  // channel to send events on
  private channel?: BroadcastChannel;
  // optional data to send along with each event
  private data: Record<string, unknown>;
  // list of event listeners
  private listeners: Record<string, HandlerFn[]>;
  // logger, defaults to C3ConsoleLogger
  private logger?: HandlerFn;
  // option to ignore forced logout on EventLogout
  supressLogout: boolean;
  // id of this instance
  id: string;

  constructor() {
    this.id = generateRandomId();
    this.channel = undefined;
    this.data = {};
    this.listeners = {};
    this.logger = undefined;
    this.supressLogout = false;
  }

  start() {
    this.channel = new BroadcastChannel(C3ChannelName);

    this.channel.onmessage = (msg: MessageEvent) => {
      const event = msg.data as BroadcastMessage;

      // if the event didnt origionate from this context,  log it
      if (this.logger && event.from !== this.id) {
        this.logger(event);
      }

      switch (event.name) {
        // force a logout across all contexts
        case EventLogout: {
          // if we are supressing logout (inactivity debugger), skip logout
          if (!this.supressLogout) {
            handleLogout();
          }
          break;
        }

        case EventLogEvent: {
          this.listeners[EventsAll]?.forEach((lfn) => {
            lfn(event);
          });
          break;
        }

        default: {
          if (event.id !== this.id) {
            // Broadcast to any listerns on all events
            this.listeners[EventsAll]?.forEach((lfn) => {
              lfn(event);
            });

            // broadcast to specific event listeners
            this.listeners[event.name]?.forEach((lfn) => {
              lfn(event);
            });
          }
          break;
        }
      }
    };
  }

  stop() {
    this.channel?.close();
  }

  setLogger(fn: HandlerFn) {
    this.logger = fn;
  }

  removeListener(event: string, fn: HandlerFn) {
    this.listeners[event] = this.listeners[event].filter((f) => f !== fn);
  }

  addListener(
    event: string,
    fn: HandlerFn,
    data: Record<string, unknown> = {},
  ) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }

    if (!this.listeners[event].includes(fn)) {
      this.listeners[event].push(fn);
      this.data = Object.assign({}, this.data, data);
    }
  }

  // add data attributes to go with the events. In this case used to include tenant_id
  setAttribute(key: string, value: unknown): void {
    this.data[key] = value;
  }

  private sendAndLogMessage(msg: BroadcastMessage): void {
    try {
      this.channel?.postMessage(msg);
      this.logger?.(msg);
    } catch (error: unknown) {
      this.handleChannelError(error, msg);
    }
  }

  private handleChannelError(err: unknown, msg: BroadcastMessage): void {
    if (!(err instanceof DOMException)) {
      console.error(err);
    }

    switch ((err as DOMException).name) {
      case "InvalidStateError":
        // can safely ignore since this means the channel is closed
        return;
      case "DataCloneError":
        console.error("[c3] message data could not be serialized:", msg);

        return;
      default:
        console.error("[c3] unhandled dom exception:", err);
    }
  }

  send(msg: BroadcastMessage) {
    if (!this.channel && isRunningLocally()) {
      console.warn("[c3] ignoring send, no defined channel");
    }
    msg.id = generateRandomId();
    msg.from = this.id;
    msg.data = Object.assign({}, msg.data, this.data);

    this.sendAndLogMessage(msg);
  }
}

// Create global instance for convenience w/default logger
const EventBus = new C3EventBus();
EventBus.setLogger(C3ConsoleLogger);
EventBus.start();

export default EventBus;
