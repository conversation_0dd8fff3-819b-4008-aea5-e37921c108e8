{"ACCEPT": "Accept", "ACCESS_ICON": "Access icon", "ACCESS_TOKEN_REQUIRED_MESSAGE": "Access Token Lifetime is required.", "ACCESS_TOKEN_VALIDITY": "Access Token Validity", "ACCESS_TYPE": "Access Type", "ACCESS_VALID_UNTIL": "Access Valid Until", "ACCOUNT_CPI_PASS_NOT_MATCH": "Passwords do not match", "ACCOUNT_CPM_CHECKS_MIN_CHAR": "8 characters minimum", "ACCOUNT_CPM_CHECKS_ONE_LOW": "At least 1 lowercase character (a-z)", "ACCOUNT_CPM_CHECKS_ONE_NUM": "At least 1 numeric character (0-9)", "ACCOUNT_CPM_CHECKS_ONE_UPP": "At least 1 uppercase character (A-Z)", "ACCOUNT_CPM_CHECKS_SPL_CHAR": "At least 1 special character", "ACCOUNT_MANAGEMENT": "Account Management", "ACCOUNT_POPOVER_ACCOUNT_SETTINGS": "Account <PERSON><PERSON>", "ACCOUNT_POPOVER_CLOUD_SELECT": "Zscaler Cloud", "ACCOUNT_POPOVER_LANG": "Language", "ACCOUNT_POPOVER_SETTINGS": "Settings", "ACCOUNT_POPOVER_SIGN_OUT": "Sign Out", "ACCOUNT_POPOVER_TZ": "Time Zone", "ACCOUNT_SETTING_ICON": "{{text}} Icon", "ACCOUNT_USER_ERR_MSG": "Error while saving changes.", "ACCOUNT_USER_ORG_ID": "Organization ID", "ACCOUNT_USER_PASS_UPDATE_SUCCESS": "Password has been successfully updated.", "ACCOUNT_USER_SUCCESS_MSG": "Changes has been saved.", "ACCOUNT_USER_USERNAME": "User Name", "ACROSS_AREAS": "Across {{count}} Areas", "ACTION": "Action", "ACTIONS": "Actions", "ACTIVATE": "Activate", "ACTIVE_ICON": "Active icon", "ACTIVATION": "Activation", "ACTIVATION_BADGE_ICON": "Activation badge icon", "ACTIVATION_COMPLETED": "Activation Completed!", "ACTIVATION_FAILED": "Activation Failed", "ACTIVATION_PENDING": "Activation Pending", "ACTIVATION_STATUS_LABEL": "My Activation Status", "ACTIVE": "Active", "ACTIVE_DEVICES": "Active Devices", "ACTIVE_GEOLOCATIONS": "Geolocations", "ACTIVE_PARTICIPANTS": "Active Participants", "ACTIVE_STATUS": "Active Status", "ACTIVE_USERS": "Active Users", "ACTIVE_USERS_WITH_SELF_SERVICE": "Active Users With Self Service", "ADD": "Add", "ADD_APPLICATION": "Add Application", "ADD_TAGS": "Add tags", "CUSTOM_SAAS_APPLICATION": "Custom SaaS Application", "EG_ACME_APP": "e.g., Acme App", "APP_NAME": "App Name", "UNSANCTIONED": "Unsanctioned", "SANCTIONED": "Sanctioned", "URL_PATH": "URL Path", "EG_ACME_COM": "e.g., acme.com", "ADD_LOGO": "Add Logo", "UPLOAD_CUSTOM_APP_LOGO": "Upload a custom application logo", "YOUR_FILE_UPLOADED_SUCCESSFULLY": "Your file uploaded successfully!", "THIS_CANNOT_BE_EMPTY": "This field cannot be empty", "SET_UP_APP": "Set Up Application", "INSTANCE_NAME": "Instance Name", "EG_MARKETING_INSTANCE": "e.g., Marketing Instance", "ENTRY_TEXT": "Enter text", "INSTANCE_ID": "Instance Identifier", "SET_UP_INSTANCE": "Set Up Instance", "VERSION_1": "Version 1", "VERSION_2": "Version 2", "INSTANCE": "Instance", "THROUGH_API_INTEGRATION": "Through API Integration", "MANUALLY": "Manually", "PROFILE": "Profile", "DIRECTORY_ID": "Directory ID", "PROBE_NAME": "Probe Name", "FREQUENCY": "Frequency", "USAGE": "Usage", "INSTANCES": "Instances", "TENANT_RESTRICTIONS": "Tenant Restrictions", "DIGITAL_EXPERIENCE_PROBES": "Digital Experience Probes", "TAGS": "Tags", "ADD_PROBES_START_MONITORING": "Add probes to start monitoring", "VIEW_ANALYTICS": "View Analytics", "VIEW_ALL": "View All", "UNIQUE_USERS": "Unique Users", "ADD_INSTANCE": "Add instance", "ASSIGN_OWNER": "Assign Owner", "ADDITIONAL_ATTRIBUTES": "Additional Attributes", "TENANT_RESTRICTION_ACCOUNT_ID": "Account IDs", "TENANT_RESTRICTION_ACTION_OR_ADVENTURE": "Action/Adventure", "TENANT_RESTRICTION_ALLOWED_WORKSPACE_ID": "Allowed Workspace ID", "TENANT_RESTRICTION_AMAZON_S3_INFO_MESSAGE": "Enter the account IDs for which you want to provide access", "TENANT_RESTRICTION_ANIME_OR_ANIMATION": "Anime/Animation", "TENANT_RESTRICTION_AUTOS_AND_VEHICLES": "Autos & Vehicles", "TENANT_RESTRICTION_CATEGORY_ID": "YouTube Category ID", "TENANT_RESTRICTION_CHANNEL_ID": "YouTube Channel ID", "TENANT_RESTRICTION_CLASSICS": "Classics", "TENANT_RESTRICTION_COMEDY": "Comedy", "TENANT_RESTRICTION_DOCUMENTARY": "Documentary", "TENANT_RESTRICTION_DOMAIN": "Domains", "TENANT_RESTRICTION_DRAMA": "Drama", "TENANT_RESTRICTION_EDUCATION": "Education", "TENANT_RESTRICTION_ENTERPRISE_SLUG": "Enterprise Slug for GitHub", "TENANT_RESTRICTION_ENTERTAINMENT": "Entertainment", "TENANT_RESTRICTION_EXP_BUCKET_OWNERID": "AWS Account Number", "TENANT_RESTRICTION_EXP_BUCKET_SRC_OWNERID": "Expected Source Bucket Owner ID", "TENANT_RESTRICTION_FAMILY": "Family", "TENANT_RESTRICTION_FILM_AND_ANIMATION": "Film & Animation", "TENANT_RESTRICTION_FOREIGN": "Foreign", "TENANT_RESTRICTION_GAMING": "Gaming", "TENANT_RESTRICTION_GOOGLE_CLOUD_PLATFORM": "Allowed Organization IDs", "TENANT_RESTRICTION_HORROR": "Horror", "TENANT_RESTRICTION_HOWTO_AND_STYLE": "Howto & Style", "TENANT_RESTRICTION_IBMSMARTCLOUD": "IBM Account IDs", "TENANT_RESTRICTION_MOVIES": "Movies", "TENANT_RESTRICTION_MSLOGIN_VERSION": "Select a version. By default, Version 1 is selected.", "TENANT_RESTRICTION_MUSIC": "Music", "TENANT_RESTRICTION_NEWS_AND_POLITICS": "News & Politics", "TENANT_RESTRICTION_NONPROFITS_AND_ACTIVISM": "Nonprofits & Activism", "TENANT_RESTRICTION_PEOPLE_AND_BLOGS": "People & Blogs", "TENANT_RESTRICTION_PETS_AND_ANIMALS": "Pets & Animals", "TENANT_RESTRICTION_POLICY_LABEL": "Policy Label", "TENANT_RESTRICTION_PROFILE": "Tenant Restriction Profile", "TENANT_RESTRICTION_REQUEST_WORKSPACE_ID": "Workspace ID", "TENANT_RESTRICTION_RESTRICT_MSA": "Consumer Apps Restrict", "TENANT_RESTRICTION_SCHOOL_ID": "YouTube School ID", "TENANT_RESTRICTION_SCIENCE_AND_TECHNOLOGY": "Science & Technology", "TENANT_RESTRICTION_SCIFI_OR_FANTASY": "Sci-Fi/Fantasy", "TENANT_RESTRICTION_SHORTS": "Shorts", "TENANT_RESTRICTION_SHORT_MOVIES": "Short Movies", "TENANT_RESTRICTION_SHOWS": "Shows", "TENANT_RESTRICTION_SPORTS": "Sports", "TENANT_RESTRICTION_TEAM_ID": "Dropbox Team ID", "TENANT_RESTRICTION_TENANT_DIRECTORY": "Tenant Directory ID", "TENANT_RESTRICTION_TENANT_DIRECTORY_POLICY": "Tenant Directory ID:Policy Id", "TENANT_RESTRICTION_TENANT_NAME": "Office 365 Tenants or Tenant IDs", "TENANT_RESTRICTION_TENANT_ORG_ID": "Zoho ID", "TENANT_RESTRICTION_TENANT_POLICY_ID": "tenant directory id", "TENANT_RESTRICTION_TENANT_POLICY_ID_DESC": "policy id. This is for msft trv2", "TENANT_RESTRICTION_THRILLER": "Thriller", "TENANT_RESTRICTION_TRAILERS": "Trailers", "TENANT_RESTRICTION_TRAVEL_AND_EVENTS": "Travel & Events", "TENANT_RESTRICTION_VIDEOBLOGGING": "Videoblogging", "TENANT_RESTRICTION_WEBEX_TENANTS": "Webex Tenants(Teams and Meetings)", "TENANT_RESTRICTION_WORKSPACE_ID": "Workspace ID for ChatGPT", "TENANT_STATUS": "Tenant Status", "TENANT_TYPE": "Tenant Type", "TENANT_USERNAME": "User ID", "TENANT_PROFILE_NAME": "Tenant Profile Name", "YOUTUBE_CONFIGURATION": "YouTube Configuration", "SELECT_ONE": "Select one", "ADD_API_CLIENT": "Add API Client", "ADD_API_RESOURCE": "Add API Resource", "ADD_ATTRIBUTE": "Add Attribute", "ADD_CLAIM": "<PERSON><PERSON>", "ADD_CONDITIONAL_ACCESS_PROFILE": "Add Profile", "ADD_PROFILE_PLACEHOLDER": "Enter Profile Name...", "ADD_PROFILE_DESC_PLACEHOLDER": "Enter Description...", "ADD_DEPARTMENT": "Add Department", "ADD_GROUP": "Add Group", "ADD_INTEGRATIONS": "Add Integrations", "ADD_ITEMS": "Add Items", "ADD_LOCATION": "Add Location", "ADD_LOCATION_GROUP": "Add Location Group", "ADD_MORE": "Add More", "ADD_PRIMARY_IDENTITY_PROVIDER": "Add Primary Identity Provider", "ADD_PRIMARY_IDP": "Add Primary IdP", "ADD_PROFILE": "Add Profile", "ADD_ROLE": "Add Role", "ADD_RULE": "Add Rule", "ADD_SECONDARY_IDENTITY_PROVIDER": "Add Secondary Identity Provider", "ADD_SECONDARY_IDP": "Add Secondary IdP", "ADD_SESSION_ATTRIBUTE": "Add Session Attribute", "ADD_SIGN_ON_POLICY": "Add Admin Sign-On Policy", "ADD_TOKEN_VALIDATOR": "Add Token Validator", "ADD_USER": "Add User", "ADD_USER_GROUP": "Add User Group", "ADMINISTRATION": "Administration", "ADMINISTRATION_CONTROLS": "Administration Controls", "ADMINISTRATIVE": "Administrative", "ADMINISTRATIVE_ENTITLEMENTS": "Administrative Entitlements", "ADMINISTRATOR_INACTIVITY_TIMEOUT_DURATION_IN_MIN": "Administrator Inactivity Timeout Duration (in Minutes)", "ADMIN_ASSIGNMENT": "Admin Assignment", "ADMIN_ID": "Admin ID", "ADMIN_PORTAL_SIGN_IN": "Admin Portal Sign In", "ADMIN_SIGN_ON": "Admin Sign-On", "ADSPYWARE": "Suspected Spyware or Adware", "ADSPYWARE_SITES": "Adware/Spyware Sites", "ADVANCED": "Advanced", "ADVANCED_SETTINGS": "Advanced Settings", "ADVANCED_THREAT_CATEGORIES": "Advanced Threat Categories", "ADVANCED_THREAT_INCIDENTS": "Advanced Threat Incidents", "ADVANCE_SETTINGS": "Advance Settings", "AFFECTED_METRIC": "Affected Metric", "ALL": "All", "ALLOW": "Allow", "ALLOWED": "Allowed", "ALLOWED_ICON": "Allowed icon", "ALLOW_ADMIN_SET_PASSWORD": "Allow administrator to create or change user's password", "ALLOW_AND_DO_NOT_SCAN": "Allow/Do Not Scan", "ALLOW_AND_SCAN": "Allow & Scan", "ALLOW_EMAIL_OTP_AS_PRIMARY": "Allow Em<PERSON> as Primary Authenticator", "ALLOW_FIDO_AS_PRIMARY": "Allow FIDO2 as Primary Authenticator", "ALL_APPLICATIONS": "All Applications", "ALL_CHANGES_SAVED": "All changes have been saved", "ALL_LAST_MILE_ISP": "Last Mile ISPs", "ALL_LOCATIONS": "Locations", "ALL_ORGANIZATIONS": "All Organizations", "ALL_THREATS": "All Threats", "ALL_USERS": "All Users", "ALL_ZSCALER_LOCATIONS": "Zscaler Locations", "AM": "AM", "ANDROID": "Android", "ANGLE_DOWN_ICON": "Angle down icon", "ANGLE_ICON": "{{side}} angle icon", "ANGLE_LEFT_ICON": "<PERSON><PERSON> left icon", "ANGLE_RIGHT_ICON": "<PERSON>le right icon", "API_CLIENT": "API Client", "API_CLIENTS": "API Clients", "API_CLIENTS_AND_RESOURCES": "API Clients & Resources", "API_RESOURCE": "API Resource", "API_RESOURCES": "API Resources", "APK": "Android Application Package (apk)", "APPLIANCE_LOCATIONS": "Locations", "APPLICATION": "Application", "APPLICATIONS": "Applications", "APPLICATION_CATEGORY": "Application Category", "APPLICATION_DETAIL": "Application Detail", "APPLICATION_EXPERIENCE": "Application Experience", "APPLICATION_EXPERIENCE_TREND": "Application Experience Trend", "APPLICATION_NAME": "Application Name", "APPLICATION_PDF_PDF": "PDF Document", "APPLICATION_TABLE_COLUMN": "Application", "APPLY": "Apply", "APPS": "Apps", "APPS_WITH_POOR_SCORE": "Apps with Poor Score", "APP_CONNECTOR": "App Connector", "AP_LATENCY_FIRST_HOP_AVG": "Average Wi-Fi Access Point Latency", "AP_LATENCY_FIRST_HOP_MAX": "Maximum Wi-Fi Access Point Latency", "ARBITRARY_GUEST_DOMAINS": "Arbitrary Guest Domains", "ASN_SCORE_DROP": "ZDX Score Drop", "ASSIGN": "Assign", "ASSIGN_ONWER": "Assign Owner", "ASSIGN_APPLICATION_OWNER": "Assign an admin to configure the application", "ASSIGNED_USERS": "Assigned Users", "ASSIGNMENT": "Assignment", "ASSIGN_GROUP": "Assign Group", "ASSIGN_GROUPS": "Assign Groups", "ASSIGN_ROLE": "Assign Role", "ASSIGN_SCOPE": "Assign <PERSON>", "ASSIGN_USER": "Assign User", "ASSIGN_USERS": "Assign Users", "ASSIGN_USERS_AND_GROUPS": "Assign Users and User Groups", "ASSIGN_USER_GROUPS": "Groups Assignment", "ATTRIBUTE": "Attribute", "ATTRIBUTES": "Attributes", "ATTRIBUTE_ALREADY_MAPPED": "Attribute {{value}} already mapped", "ATTRIBUTE_NAME": "Attribute Name", "ATTRIBUTE_REQUIRED": "Attribute Required", "AT_REQ_ADSPYWARE_DENIED": "IPS block outbound request: adware/spyware traffic", "AT_REQ_ANONYMIZER_DENIED": "Reputation block outbound request: anonymization site", "AT_REQ_BOTNETS_DENIED": "Reputation block outbound request: botnet site", "AT_REQ_BOTNET_CNC_DENIED": "IPS block outbound request: botnet command and control traffic", "AT_REQ_BROWSER_EXPLOIT_DENIED": "IPS block outbound request: page contains known browser exploits", "AT_REQ_COOKIESTEAL_DENIED": "IPS block outbound request: browser cookie theft", "AT_REQ_IRC_TUNNELING_DENIED": "IPS block outbound request: IRC use/tunneling", "AT_REQ_MALWARE_DENIED": "Reputation block outbound request: malicious URL", "AT_REQ_PHISHING_DENIED": "Reputation block outbound request: phishing site", "AT_REQ_WEBSPAM_DENIED": "Reputation block outbound request: webspam", "AT_REQ_XSSATTPATT_DENIED": "IPS block outbound request: cross-site scripting (XSS) attack", "AT_RES_ACTIVEXBLOCK_DENIED": "IPS block inbound response: page contains known dangerous ActiveX controls", "AT_RES_ADSPYWARE_DENIED": "IPS block inbound response: adware/spyware traffic", "AT_RES_ANONYMIZER_DENIED": "IPS block inbound response: anonymization site", "AT_RES_BOTNET_CNC_DENIED": "IPS block inbound response: botnet command and control traffic", "AT_RES_BROWSER_EXPLOIT_DENIED": "IPS block inbound response: page contains known browser exploits", "AT_RES_CSB_DENIED": "Sandbox block inbound response: malicious file", "AT_RES_IRC_TUNNELING_DENIED": "IPS block inbound response: IRC use/tunnelingB", "AT_RES_MALWARE_DENIED": "IPS block inbound response: malicious content", "AT_RES_PHISHING_DENIED": "IPS block inbound response: phishing content", "AT_RES_WEBSPAM_DENIED": "IPS block inbound response: webspam traffic", "AT_RES_WRI_DENIED": "PageRisk block inbound response: page is unsafe", "AT_TUNNEL_DENIED": "Not allowed to use tunnels", "AT_UNKUA_DENIED": "Not allowed to browse with unknown user agent", "AUDIENCE": "Audience", "AUDITOR_DIRECT_LOGIN_NOT_ALLOWED": "An auditor cannot log in from here", "AUDITOR_OVERRIDE": "Auditor Override", "AUDIT_LOGS": "<PERSON><PERSON>", "AUTHENTICATION": "Authentication", "AUTHENTICATION_CONTEXT": "Authentication Context", "AUTHENTICATION_EVENT_LOG": "Authentication Event Log", "AUTHENTICATION_LEVELS": "Authentication Levels", "AUTHENTICATION_METHOD": "Authentication Method", "AUTHENTICATION_METHODS": "Authentication Methods", "AUTHENTICATION_POLICY": "Authentication Policy", "AUTHENTICATION_REQUEST": "Authentication Request", "AUTHENTICATION_SESSION": "Authentication Session", "AUTHENTICATION_SESSION_FOR_SERVICE_ENTITLEMENT": "Authentication Session for Service Entitlement", "AUTHENTICATION_SETTINGS": "Authentication Settings", "AUTHENTICATORS": "Authenticators", "AUTHORIZATION_ENDPOINT": "Authorization Endpoint", "AUTOSENSE_CLOUD_PATH": "Autosense Cloud Path", "AUTO_DISCOVERED": "Auto-discovered", "AUTO_GENERATED": "Auto-generated", "AUTO_SCALE": "Auto Scaling", "AVAILABILITY": "Availability", "AVERAGE_INCIDENT_DURATION": "Average Incident Duration", "AVERAGE_LATENCY": "Average Latency", "AVG": "Avg", "AVG_DNS_P95": "DNS Latency", "AVG_DNS_RESOLUTION_TIME": "Avg DNS Resolution Time", "AVG_LATENCY": "Avg Latency", "AVG_LEG_LATENCY": "Leg Latency", "AVG_PACKET_LOSS": "Packet Loss", "AZURE_AD_AS_TENANT": "Azure AD tenant A (OIDC)", "Administration": "Administration", "Analytics": "Analytics", "AZURE": "Azure", "BACK": "Back", "BASE_64_CONVERSION_FAILED": "Unable to convert image to base 64 encoded version", "BASE_URL": "Base URL", "BASE_URL_REQUIRED_MESSAGE": "Base URL is required", "BASIC": "Basic", "BA_ADWARE": "Sandbox Adware", "BA_MALWARE": "Sandbox Malware", "BEARER_TOKEN": "<PERSON><PERSON>", "BEARER_TOKEN_SUCCESSFULLY_GENERATED": "Bear<PERSON> generated successfully", "BEHAVIORAL_ANALYSIS": "BEHAVIORAL_ANALYSIS", "BENIGN": "Benign", "BI": "Business Insights", "BLOCKED": "Blocked", "BLOCKS": "Blocks", "BLOCKS_BY_POLICIES": "Blocks that are caused by policies you have configured to limit exposure to certain types of web content.", "BOOLEAN": "Boolean", "BOTNET": "Botnet Callback", "BOXNET": "Box", "BRANCH": "Branch", "BRANCH_AND_CLOUD": "Branch and Cloud", "BRANCH_CONNECTOR": "Branch Connector", "BRANCH_CONNECTORS": "Branch Connectors", "BRANCH_AND_CLOUD_CONNECTOR": "Branch and Cloud Connector", "BRANCH_AND_CLOUD_CONNECTORS": "Branch & Cloud Connectors", "CC_VDI_DEVICE_MANAGEMENT": "VDI Device Management", "CC_VDI_PROFILE": "VDI Profile", "CC_IPGROUP_FQDN": "IP Group & FQDN", "CC_SRC_IPGROUP": "Source IP Groups", "CC_DEST_IPGROUP": "Destination IP Groups", "CC_DNS_INSIGHTS": "DNS Insights", "CC_IP_POOL": "IP Pool", "CC_VM_IMAGES": "Device Images", "CC_API_KEY_MGMT": "API Key Management", "CC_AND_EDGE_VM_DEPLOYMENT": "Cloud and Edge VM Deployment", "CC_EDGE_DEVICE": "<PERSON>", "CC_SESSION_INSIGHTS": "Session Insights", "CC_TUNNEL_INSIGHTS": "Tunnel Insights", "BENEFITS": "Benefits", "BRANDING": "Branding", "BREAKDOWN_LOCATIONS": "The number of threats detected in each location", "BREAKDOWN_THREATS": "A breakdown of threats stopped by predefined categories.", "BROWSER_NOT_SUPPORTED": "Browser version not supported", "BROWSE_FILE": "Browse File", "BULK": "Bulk", "BULK_ACTIVATE": "Bulk Activate", "BULK_DEACTIVATE": "Bulk Deactivate", "BULK_DELETE": "Bulk Delete", "BULK_DELETE_LOCATION_CONFIRMATION_MESSAGE": "Are you sure you want to bulk delete these locations? The changes cannot be undone.", "BULK_DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "Are you sure you want to bulk delete these location groups? The changes cannot be undone.", "BULK_DELETE_USERS_GROUPS_ROLE": "Bulk Delete Users and Groups", "BUSINESS_PRODUCTIVITY": "Productivity and CRM Tools", "CANCEL": "Cancel", "CASB_TENANT_VALIDATION_FAIL": "Validation Failed", "CASB_TENANT_VALIDATION_SUCCESS": "Validation Success", "CASB_TENANT_ACTIVE": "Active", "CASB_TENANT_INACTIVE": "Inactive", "CASB_TENANT_VALIDATING": "Validating", "SAAS_APP_FEATURE_APP_GOVERNANCE": "App Governance", "SAAS_APP_FEATURE_CASB": "DLP and Malware scanning SaaS API", "SAAS_APP_FEATURE_RT_DLP": "Real-time DLP", "SAAS_APP_FEATURE_SSPM": "SSPM Scan", "SAAS_APP_FEATURE_WORKFLOW_AUTOMATION": "Workflow Automation", "CATEGORY": "Category", "CATEGORY_ICON": "{{status}} icon", "CCN_BENCHMARK": "CCN benchmark", "CCP": "Client Connector Portal", "CC_ADVANCED_SETTINGS": "Advanced Settings", "CC_BRANCH_DEVICES": "Branch Devices", "CC_BRANCH_PROVISIONING": "Branch Provisioning", "CC_CLOUD_CONFIGURATION": "Cloud Configuration", "CC_CLOUD_CONNECTOR_GROUPS": "Cloud Connector Groups", "CC_DEPLOYMENT_TEMPLATES": "Deployment Templates", "CC_LOG_AND_STREAMING": "Log and Streaming", "CC_MANAGEMENT": "Management", "CC_NSS": "Nanolog Streaming Service", "CC_PARTNER_INTEGRATIONS": "Partner Integrations", "CC_PROVISIONING": "Provisioning", "CC_TRAFFIC_STEERING": "Traffic Steering", "CC_CONNECTION_TEMPLATE": "Deployment Templates", "CC_FWD_POLICY": "Forwarding Policy", "CC_DNS_FOR_VM": "DNS for VM", "CC_ZIA": "Internet Access", "CC_LOG_AND_CONTROL": "Log and Control", "CC_DNS": "DNS", "CC_APP_STORE_FOR_VDI": "App Store for VDI", "CC_CLOUD_CONNECTOR_DETAILS": "Cloud Connector Monitoring", "CC_BC_TRAFFIC": "Branch Connector Monitoring", "CC_ZERO_TRUST_GATEWAY": "Zero Trust Gateway", "CC_ZT_DEVICES": "ZT Devices", "CERTIFICATE": "Certificate", "CERTIFICATES_AND_PUBLIC_KEYS": "Certificates and Public Keys", "CERTIFICATES_PUBLIC_KEYS": "Certificates and Public Keys", "CERTIFICATE_PUBLIC_KEY": "Certificates/Public Keys", "CHANGE_PASSWORD": "Change Password", "CHANGE_PASSWORD_ERROR": "Change Password Error", "CHANGE_PASSWORD_ICON": "Change Password Icon", "CHANGE_PASSWORD_SETTINGS": "Change Password Settings", "CHART_NETWORK_ICON": "Chart network icon", "CHECK_ICON": "{{label}} icon", "CHECK_PASSWORD": "Check password", "CLAIM_REQUIREMENTS": "Claim Requirements", "CLEAR": "Clear", "CLEAR_ALL": "Clear All", "CLEAR_ICON": "Clear icon", "CLICK_CONTINUE_MSG": "Click on \"Continue\" to complete the setup.", "CLIENT": "Client", "CLIENT_AUTHENTICATION": "Client Authentication", "CLIENT_CONNECTOR_DEVICE_TOKEN": "Client Connector Device Token", "CLIENT_EXT_IP": "Client Ext IP", "CLIENT_ID": "Client ID", "CLIENT_ID_REQUIRED_MESSAGE": "Client ID is required", "CLIENT_INFORMATION": "Client Information", "CLIENT_IP": "Client IP", "CLIENT_JWK": "Client JWK", "CLIENT_JWKS": "Client JWKs", "CLIENT_JWKS_URL": "Client JWKs URL", "CLIENT_JWK_URL": "Client JWKs URL", "CLIENT_SECRET": "Client Secret", "CLIENT_SECRETS": "Client Secrets", "CLIENT_SECRET_BASIC": "Client Secret Basic", "CLIENT_SECRET_POST": "Client Secret Post", "CLIENT_SECRET_REQUIRED_MESSAGE": "Client secret is required", "CLIENT_SECRET_WARNING": "Copy Client Secret. It won't be displayed later.", "CLIENT_ZEN": "Client - ZIA Public Service Edge", "CLOCK_ICON": "Clock icon", "CLONE_MEMBERSHIP_FOR_APPLICATION": "Clone Membership From This Application", "CLOSE": "Close", "CLOSE_ICON": "Close icon", "CLOUD": "Cloud", "CLOUD_HEADING": "Cloud", "CLOUD_AND_ORG_ID": "Cloud and Org ID", "CLOUD_AVERAGE": "Cloud Average", "CLOUD_CONFIGURATION": "Cloud Configuration", "CLOUD_CONNECTOR": "Cloud Connector", "CLOUD_CONNECTORS": "Cloud Connectors", "CLOUD_ID": "Cloud Id", "CLOUD_NAME": "Cloud Name", "CLOUD_PATH": "Cloud Path", "CLOUD_SERVICES": "Cloud Services", "CLOUD_LOCATIONS": "Cloud Locations", "CLOUD_ACTIVITY": "Cloud Activity", "COLLAPSE": "Collapse", "COLLAPSE_ICON": "Collapse icon", "COMPLETE": "Complete", "COMPLETED_ICON": "{{header}} completed icon", "CONDITIONAL_ACCESS_PROFILE": "Profiles", "CONDTIONAL_ACCESS": "Adaptive Access", "CONFIGURATION_CHANGES": "Configuration Changes", "CONFIGURATION_SETTINGS": "Configuration Settings", "CONFIGURATION_TYPE": "Configuration Type", "CONFIRM": "Confirm", "CONFIRMATION_REMOVE_ALL": "Remove All", "CONFIRMATION_REMOVE_PAGE": "Remo<PERSON>", "CONFIRM_NEW_PASSWORD": "Confirm New Password", "CONFIRM_NEW_PASSWORD_LABEL": "Confirm New Password", "CONFIRM_PASSWORD": "Confirm Password", "CONNECTOR_ACTIVITY_STATUS": "Status", "CONNECTOR_LOCATION": "Connector Location", "CONNECTOR_TYPE": "Connector Type", "CONNECTORS": "Connectors", "CONNECTOR": "Connector", "CONNECTORS_HEADING": "Locations with Zscaler Connectors", "CONNECTOR_SERVER": "App Connector - Application", "CONTEXT_COUNT": "Context Count", "CONTEXT_CREATION": "Context Creation", "CONTEXT_EXPIRY": "Context Expiry", "CONTEXT_TYPE": "Context Type", "CONTEXT_VALUE": "Context Value", "CONTINUE": "Continue", "COOKIES_DISABLED": "Cookies must be allowed to use this application. Please enable your browser\"s cookie support for this site.", "COOKIES_NOT_ALLOWED": "Cookies Not Allowed", "COPYRIGHT": "Copyright", "COPYRIGHT_STATEMENT": "All rights reserved.", "COPY_ICON": "Copy icon", "CORPORATE_MARKETING": "Corporate Marketing", "COUNT": "Count", "COUNTRY": "Country", "COUNTRY_REQUIRED_MESSAGE": "Country is required", "CPU": "CPU", "CREATE": "Create", "CREATED": "Created", "CREATED_ON": "Create On", "CREATE_NEW_ATTRIBUTES": "Create New Attributes", "CREATION": "Creation", "CRITERIA": "Criteria", "CRYPTOMINING": "Crypto Mining", "CSV": "Csv", "CSV_FILE": "CSV File", "CSV_FORMAT_INVALID": "Invalid CSV Format", "CYBERSECURITY_INCIDENTS_FILTER_SUBHEADER": "The data that is shown here is for the last 14 days.", "CS_TOP_LOCATIONS_FILTER_SUBHEADER": "The data that is shown here is for the last 7 days.", "CURRENTLY_EDITING": "Currently Editing", "CURRENT_DAY": "Today", "CURRENT_MONTH": "Current Month", "CURRENT_PASSWORD": "Current Password", "CURRENT_PSEUDO_DOMAIN_NAME": "Current Pseudo Domain Name", "CURRENT_WEEK": "Current Week", "CURSOR_ICON": "Cursor icon", "CUSTOM": "Custom", "CUSTOMER_ID": "Customer ID", "CUSTOMER_ID_REQUIRED_MESSAGE": "Customer ID is required", "CUSTOMISE_COLUMNS": "CUSTOMISE COLUMNS", "CUSTOMIZE_EMAIL_ADDRESS_SENT_BY": "Customize Email Address Sent By", "CUSTOMIZE_EMAIL_SUBJECT": "Customize Email Subject", "CUSTOMIZE_HOME_PAGE": "Customize Home Page", "CUSTOM_ATTRIBUTE": "Custom Attribute", "CUSTOM_USER_ATTRIBUTE": "Custom User Attribute", "CXO_INSIGHT": "Executive Insights", "DEPLOYMENT_TYPE": "Deployment Type", "DEPLOYMENT_DETAILS": "Deployment Details", "DASHBOARD": "Dashboard", "DATA_CENTER": "Data Center", "DATA_CENTERS": "Data Centers", "DATA_CENTERS_HEADING": "Top Zscaler Data Centers Used", "DATA_CENTERS_SUBHEADING": "This chart is displaying data from last 30 days", "DATA_CENTER_DESC": "The data that is shown here is for the following time frame: from", "DATA_CENTER_TABLE_COLUMN": "ZIA Public Service Edge", "DATA_NETWORKING": "The data that is shown here is for the last {{day}} days.", "DATA_TYPE": "Data Type", "DATE": "Date", "DAY": "Day", "DAYS": "Days", "DC_SCORE": "ZDX Score", "DC_SCORE_DROP": "ZDX Score for Impacted Users", "DEACTIVATE_USER": "Deactivate user after 10 unsuccessful attempts", "DECIMAL": "Decimal", "DEFAULT": "<PERSON><PERSON><PERSON>", "DEFAULT_GATEWAY_IP_ADDRESS": "Default Gateway IP Address", "DEFAULT_REQUESTED_AUTHENTICATION_CONTEXT": "<PERSON><PERSON><PERSON> Requested Authentication Context (Optional)", "DELETE": "Delete", "DELETED": "Deleted", "DELETE_AL": "Delete Authentication Level", "DELETE_AL_CONFIRM_MSG": "This action is irreversible and will completely delete the authentication level. Are you sure you want to proceed?", "DELETE_API_CLIENT": "Delete API Client", "DELETE_API_CLIENT_MESSAGE": "Are you sure you want to delete this API Client? The changes cannot be undone.", "DELETE_ASSIGNMENT": "Delete Assignment", "DELETE_ATTRIBUTE": "Delete Attribute", "DELETE_AUTHENTICATOR": "Delete MFA Authenticator", "DELETE_CONDITIONAL_ACCESS_PROFILE": "Delete Profiles", "DELETE_DEPARTMENT": "Delete Department", "DELETE_GROUP": "Delete Group", "DELETE_GROUPS_ROLE": "Delete Group", "DELETE_ICON": "Delete row", "DELETE_INTEGRATIONS": "Delete Integrations", "DELETE_LOCATION": "Delete Location", "DELETE_LOCATION_CONFIRMATION_MESSAGE": "Are you sure you want to delete this location? The changes cannot be undone.", "DELETE_LOCATION_GROUP": "Delete Location Group", "DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "Are you sure you want to delete this location group? The changes cannot be undone.", "DELETE_PRIMARY_IDENTITY_PROVIDER": "Delete Primary Identity Provider", "DELETE_ROLE": "Delete Role", "DELETE_SECONDARY_IDENTITY_PROVIDER": "Delete Secondary Identity Provider", "DELETE_SESSION_ATTRIBUTE": "Delete Session Attribute", "DELETE_SIGN_ON_POLICY": "Delete Admin Sign-on Policy", "DELETE_TOKEN_VALIDATOR": "Delete Token Validator", "DELETE_USER": "Delete User", "DELETE_USERS_ROLE": "Delete User", "DEL_ICON": "Delete icon", "DENY": "<PERSON><PERSON>", "DEPARTMENT": "Department", "DEPARTMENTS": "Departments", "DEPARTMENT_NAME": "Department Name", "DEPLOYED": "Deployed", "DEPLOYED_CONNECTORS": "Deployed Connectors", "DEPLOYMENT_STATUS": "Deployment Status", "DESCRIPTION": "Description", "DESCRIPTION_OPTIONAL": "Description (Optional)", "DESTINATION": "Destination", "DESTINATION_IP": "Destination IP", "DESCRIPTION_NETWORKING": "Traffic, data center, and devices in your organization", "DESCRIPTION_RISK": "Secondary label explaining what this dashboard is for", "DESCRIPTION_DE": "Users, devices, applications, and unified communication", "DESCRIPTION_CS": "Malicious activity and threats", "DESCRIPTION_APPLICATIONS": "Secondary label explaining what this dashboard is for", "DESCRIPTION_DATA_PROTECTION": "Secondary label explaining what this dashboard is for", "DESCRIPTION_OPERATIONAL": "Appliances and devices", "DETAILS": "Details", "DEVICE": "<PERSON><PERSON>", "DEVICE_DISTRIBUTION": "Device Distribution", "DEVICE_OPERATING_SYSTEM": "Device Operating System", "DEVICES_DISCOVERED": "Devices Discovered", "DEVICES_DISCOVERED_DESC": "These are new unmanaged devices that were discovered in the last 24hrs.", "DEVICES_ON_WI_FI_CONNECTIVITY": "Devices on Wi-Fi Connectivity", "DEVICES_OS_DISTRIBUTION": "DeviceOS Distribution", "DEVICE_COUNT": "<PERSON><PERSON>", "DEVICE_GROUPS": "Device Groups", "DEVICE_GROUP_ASSIGNMENT": "Device Group Assignment", "DEVICE_GROUP_RESTRICTIONS": "Device Group Restrictions", "DEVICE_ICON": "{{label}} icon", "DEVICE_MODEL": "Device Model", "DEVICE_NAME": "Device Name", "DEVICE_TOKEN": "<PERSON><PERSON>", "DEVICE_SEGMENTATION": "Zero Trust Branch", "ZERO_TRUST_BRANCH": "Zero Trust Branch", "DEVICE_STATE": "Device State", "DEVICE_OS": "Device Operating System", "DE_ACTIVATE": "Deactivate", "DIDNT_RECEIVE": "Didn't receive?", "DIGICERT": "<PERSON><PERSON><PERSON><PERSON>", "DIRECT": "Direct", "DIRECTORY": "Directory", "DISABLE": "Disable", "DISABLED": "Disabled", "DISABLED_ICON": "Disabled icon", "DISABLE_FIDO": "Disable FIDO2", "DISABLE_FIDO_MSG": "You might lock yourself out of the account if you are only using a security key or biometric for authentication. The only way to access your account is through password-based authentication if you disable M<PERSON>. In case you don’t remember your password or haven’t configured it yet, request a password reset.", "DISABLE_MFA_MSG": "Disabling Multi-Factor Authentication is a global change. This makes your users less secure. Are you sure you want to proceed?", "DISABLE_MULTIFACTOR": "Disable Multi-Factor Authentication", "DISPLAY_NAME": "Display Name", "DNS": "DNS", "DNS_DETAILS": "DNS Details", "DNS_SERVER": "DNS Server", "DNS_RESOLUTION": "DNS Resolution", "DNS_RESOLVE_ERR": "The domain is invalid or not resolvable. Verify your domain.", "DOMAIN": "Domain", "DOMAINS": "Domains", "DOMAIN_NAME": "Domain Name", "DONE": "Done", "DOUBLECLICK": "DoubleClick", "DOWNLOAD": "Download", "DOWNLOAD_CERTIFICATE": "Download Certificate", "DOWNLOAD_PUBLIC_KEY": "Download Public Key", "DOWNLOAD_SP_METADATA": "Download SP Metadata", "DOWN_ICON": "Down icon", "DO_NOT_EDIT MENU SECTION": "DO NOT TRANSLATE", "DUPLICATE_ITEM": "The given name is already in use", "DUPLICATE_RECORD": "Duplicate Record", "DURATION": "Duration", "DXP_OVERALL_EXPERIENCE": "How is my overall Digital Experience?", "DXP_OVERALL_EXPERIENCE_DESCRIPTION": "This shows the count of applications with ZDX scores. Applications can appear more than once if scores have changed within the selected time period.", "DXP_SCORE_MEASURES": "This score measures your organization’s digital experience across all locations, devices and apps.", "EC": "Edgeconnector", "ECODE_DISTRIBUTION": "HTTP Errors", "ECSELF": "Logs & Control", "EDGE": "Edge", "EDGE_LOCATIONS": "Edge Locations", "EDGE_HEADING": "Edge", "EDGE_ACTIVITY": "Edge Activity", "EDIT": "Edit", "EXPORT": "Export", "EDIT_API_CLIENT": "Edit API Client", "EDIT_API_RESOURCE": "Edit API Resource", "EDIT_ATTRIBUTE": "Edit Attribute", "EDIT_CONDITIONAL_ACCESS_PROFILE": "Edit Profiles", "EDIT_DEPARTMENT": "Edit Department", "EDIT_EMAIL": "Edit Email Address", "EDIT_GROUP": "Edit Group", "EDIT_GROUPS_ROLE": "Edit Role", "EDIT_ICON": "Edit row", "EDIT_INTEGRATIONS": "Edit Integrations", "EDIT_LOCATION": "Edit Location", "EDIT_LOCATION_GROUP": "Edit Location Group", "EDIT_LOGO": "Edit <PERSON>", "EDIT_NAME": "Edit Name", "EDIT_PRIMARY_IDENTITY_PROVIDER": "Edit Primary Identity Provider", "EDIT_ROLE": "Edit Role", "EDIT_SECONDARY_IDENTITY_PROVIDER": "Edit Secondary Identity Provider", "EDIT_SESSION_ATTRIBUTE": "Edit Session Attribute", "EDIT_SIGN_ON_POLICY": "Edit Admin Sign-On Policy", "EDIT_TOKEN_VALIDATOR": "<PERSON>", "EDIT_USER": "Edit User", "EDIT_USERS_ROLE": "Edit Role", "EGRESS_BROKER": "Egress -ZPA Service Edge", "EGRESS_FIRST_HOP_LATENCY": "Egress to First Hop Latency", "EGRESS_TO_FIRST_HOP_LATENCY": "Egress First Hop Latency", "EMAIL_ADDRESS": "Email Address", "EMAIL_FROM_ADDRESS": "Email From Address", "EMAIL_HOST": "Webmail", "EMAIL_LOGIN": "Email OTP", "EMAIL_OTP": "Email OTP", "EMAIL_PLACEHOLDER": "Enter your e-mail address...", "EMAIL_SUBJECT": "Email Subject", "ENABLE": "Enable", "ENABLED": "Enabled", "ENABLING": "<PERSON><PERSON><PERSON>", "DISABLING": "Disabling", "ENABLE_DEVICE_GROUP_RESTRICTIONS": "Enable Device Group Restrictions", "ENABLE_ENCRYPTED_SAML_ASSERTION": "Enable Encrypted SAML Assertion", "ENABLE_FOR_JIT_PROVISIONING": "Enable for JIT provisioning", "ENABLE_FULL_ACCESS": "Enable Full Access", "ENABLE_MULTIFACTOR_AUTH_ADMIN": "Enable Multi-Factor Authentication (MFA) for Administrators", "ENABLE_MULTIFACTOR_AUTH_USER": "Enable Multi-Factor Authentication (MFA) for Service Enrollment", "ENABLE_MULTIFACTOR_SETTINGS": "Enable Multifactor Settings", "ENABLE_SAML_REQUEST_SIGNING": "Enable SAML Request Signing", "ENABLE_TO_ALLOW_ALL_DOMAINS": "Enable to allow all the domains", "EGRESS_DETAILS": "Egress Details", "ENABLE_VIEW_ONLY_ACCESS": "Enable View Only Access", "ENCRYPT": "Password Protected / Encrypted", "ENCRYPTED": "Encrypted (SSL)", "ENCRYPTED_SAML_ASSERTION": "Encrypted SAML Assertion", "ENCRYPTED_SAML_RESPONSE": "Encrypted SAML Response", "ENDED_ON": "Ended On", "ENDPOINT_ERROR": "API endpoint not founds", "END_POINT_SELF_SERVICE": "End Point Self Service", "END_TIME": "End Time", "END_TO_END": "End-to-end", "END_USER": "End User", "ENTERPRISE_COLLABORATION": "Collaboration and Online Meetings", "ENTER_EMAIL_OTP": "Enter Email OTP", "ENTER_TEXT": "Enter Text...", "ENTITLEMENTS": "Entitlements", "ENTITLEMENTS_LABEL": "Entitlements", "ENVIRONMENT": "Environment", "EPICENTER": "Epicenter", "ERROR_ICON": "Error icon", "EUSA_AGREEMENT": "End User Subscription Agreement", "EVENT_TYPE": "Event Type", "EXCLUDE": "Exclude", "EXE": "Windows Executable", "EXE64": "64-bit Windows Executable", "EXECS": "Executable files analysis", "EXECUTABLES": "Executable", "EXPAND_ICON": "Expand icon", "EXPERIENCE": "Experience", "EXPIRED": "Expired", "EXPIRES": "Expires", "EXPIRES_AT": "Expires At", "EXPIRES_ON": "Expires On", "EXPIRY": "Expiry", "EXTERNAL_IDENTITIES": "External Identities", "EXTERNAL_IDP": "External IdP", "FACEBOOK": "Facebook", "FAIL": "Fail", "FAILED_RECORDS": "Failed Records", "FAILURE": "Failure", "FETCH_WITH_URL": "Metadata URL", "FIDO_LOGIN": "FIDO Authentication", "FIDO_TOGGLE_TOOLTIP": "Enabling FIDO2 as primary allows the users to skip passwords and use a biometric or security key as an authentication method.", "FIELD_VALUE_REQUIRED": "{{fieldName}} is required", "FILE_HOST": "FileHost", "FILE_INFORMATION": "File Information", "FILE_NAME": "File Name", "FILE_SHARE": "File Sharing", "FILE_SIZE": "File Size", "FILE_TYPE": "File Type", "FINISH": "Finish", "FIRST_KNOWN_ATTEMPT": "First Known Attempt", "FIRST_KNOWN_DATE": "First Known Date", "FIRST_NAME": "First Name", "FORCE_ACTIVATE": "Force Activate", "FORCE_AUTHENTICATION_FOR_PRIVATE_ACCESS_REAUTHENTICATION": "Force Authentication for Private Access Reauthentication", "FORCE_PASSWORD_CHANGE": "Enforce password change after the initial login", "FORGOT_PASSWORD": "Forgot Password?", "FOUND_IT_HELPFUL": "Found it Helpful?", "FOURTEEN_DAYS": "14 Days", "FORWARDING": "Forwarding", "FTCATEGORY_APK": "apk Files", "FTCATEGORY_AVI": "AVI Files", "FTCATEGORY_BITMAP": "Bitmap Files", "FTCATEGORY_BZIP2": "BZIP2", "FTCATEGORY_CAB": "Cab Archive", "FTCATEGORY_COD": "cod black berry", "FTCATEGORY_COMPILED_HTML_HELP": "Compiled HTML help files", "FTCATEGORY_DWG": "autocad drawing", "FTCATEGORY_ENCRYPT": "Encrypt", "FTCATEGORY_FLASH": "Flash", "FTCATEGORY_FLASH_VIDEO": "Flash Video", "FTCATEGORY_GZIP": "GZIP", "FTCATEGORY_IPA": "ipa Files", "FTCATEGORY_ISO": "Iso Archive", "FTCATEGORY_JAVASCRIPT": "JavaScript", "FTCATEGORY_JAVA_APPLET": "Java Applet", "FTCATEGORY_MICROSOFT_INSTALLER": "Microsoft Installer", "FTCATEGORY_MP3": "MP3 Files", "FTCATEGORY_MP4": "MP4 Files", "FTCATEGORY_MPEG": "MPEG Video", "FTCATEGORY_MS_EXCEL": "MS Excel", "FTCATEGORY_MS_MDB": "MS Mdb", "FTCATEGORY_MS_POWERPOINT": "MS Powerpoints", "FTCATEGORY_MS_RTF": "MS Rtf", "FTCATEGORY_MS_WORD": "MS Word", "FTCATEGORY_OGG_VORBIS": "Ogg Vorbis", "FTCATEGORY_PDF_DOCUMENT": "PDF Documents", "FTCATEGORY_PHOTOSHOP": "Photoshop Files", "FTCATEGORY_POSTSCRIPT": "Postscript Documents", "FTCATEGORY_QUICKTIME_VIDEO": "Quicktime Video Files", "FTCATEGORY_RAR": "RAR Files", "FTCATEGORY_STUFFIT": "Stuffit Archive", "FTCATEGORY_TAR": "TAR Files", "FTCATEGORY_THREEGPP": "3GPP Files", "FTCATEGORY_WAV": "WAV Files", "FTCATEGORY_WINDOWS_EXECUTABLES": "Windows Executable (exe, exe64, scr)", "FTCATEGORY_WINDOWS_LIBRARY": "Windows Library files", "FTCATEGORY_WINDOWS_MEDIA_MOVIE": "Windows Media Video", "FTCATEGORY_WINDOWS_META_FORMAT": "Window Meta Files", "FTCATEGORY_ZIP": "ZIP", "FULL_ACCESS": "Full Access", "FULL_ADMIN": "Full Admin", "FULL_NAME": "Full Name", "GATEWAY_DETAILS": "Gateway Details", "GENERAL": "General", "GENERAL_BROWSING": "General Browsing", "GENERAL_DETAILS": "General Details", "GENERAL_INFORMATION": "General Information", "GENERATE_KEYS": "Generate Keys", "GENERATE_TOKEN": "Generate Token", "GEOLOCATION": "Geolocation", "GEO_LOCATION": "Geolocation", "GET": "Get", "GLOBAL": "GLOBAL", "GOOD": "Good (66-100)", "GOOD_EXPERIENCE": "Good Experience", "GOOD_LABEL": "Good", "GOOGLE_WEBSEARCH": "Google Search", "GO_TO_FULL_VIEW": "Go to Full View", "GROUP": "Group", "GROUPS": "Groups", "GROUPS_ASSIGNMENT": "User Group Assignment", "GROUP_NAME": "Group Name", "GUEST_DOMAIN": "Guest Domain", "GUEST_DOMAINS": "Guest Domains", "GUEST_USER": "Guest User", "GZIP": "GZIP (gzip, gz)", "HEADER_POPOVER_GET_SUPPORT": "Get Support", "HA_STATUS": "HA Status", "HEADER_POPOVER_HELP_DOC": "Help Docs", "HEADER_POPOVER_LEGAL_NOTICE": "Legal Notices", "HEADER_POPOVER_PRIVACY_POLICY": "Privacy Policy", "HEADER_POPOVER_SUBSCRIPTION_AGREEMENT": "Subscription Agreement", "HEALTH_STATUS": "Health Status", "HELP": "Help", "HELP_BROWSER": "Help", "HIGH_CPU": "High CPU", "LOWEST_THREATS": "Lowest Threats", "HORN_ICON": "Horn icon", "HOST": "Host", "HOSTED": "Hosted", "HOSTNAME": "Hostname", "HOURGLASS_ICON": "Hourglass icon", "HOURS": "{{hours}} Hours", "HOUR": "Hour", "HPP_ANALYTICS_TITLE": "What analytics are you most interested in?", "HPP_ANALYTICS_SUBTITLE": "Select a category to view on your personalized homepage.", "IAM": "Identity and Access Management", "ICON": "{{name}} icon", "IDENTITY_PROVIDERS": "Identity Providers", "IDENTITY_VENDOR": "Identity Vendor", "IDLE_SESSION_TIMEOUT_DURATION_IN_MIN": "Idle Session Timeout Duration (in Minutes)", "IDP_ADVANCED_SETTINGS": "IdP Advanced Settings", "IDP_ATTRIBUTE": "IdP Attribute", "IDP_AUTHENTICATION_NOT_SUCCESSFUL": "Verification of External Identity Provider Configuration Unsuccessful", "IDP_CERTIFICATE": "IdP Certificate", "IDP_ENTITY_URI": "IdP Entity URI", "IDP_METADATA": "IdP <PERSON>", "IDP_METADATA_URL": "IdP Metadata URL", "IDP_SINGLE_SIGNON_URL": "IdP Single Sign-On URL", "IMPACT": "Impact", "INGRESS_DETAILS": "Ingress Details", "IMPACTED_DEPARTMENTS": "Impacted Departments", "IMPACTED_MEETINGS": "Impacted Meetings", "IMPACTED_REGIONS": "Impacted Regions", "IMPACTED_SYSTEMS": "Impacted Systems", "IMPACTED_USERS": "Impacted Users", "IMPACTED_USERS_BY_GEOLOCATIONS": "Impacted Users by Geolocations", "IMPACTED_USERS_BY_GEOLOCATIONS_FOOTER": "User volume drives size of points", "IMPACTED_USERS_OVER_TIME": "Impacted Users Over Time", "IMPACTED_ZSCALER_LOCATIONS": "Impacted Zscaler Locations", "IMPORT": "Import", "IMPORT_ATTRIBUTE": "Import Attributes", "IMPORT_CSV": "Import CSV", "IMPORT_DEPARTMENT": "Import Department", "IMPORT_GROUPS": "Import Groups", "IMPORT_LOCATION": "Import Location", "IMPORT_LOCATION_GROUP": "Import Location Group", "IMPORT_RESULTS": "Import Results", "IMPORT_USERS": "Import Users", "INACTIVE": "Inactive", "INACTIVE_ICON": "Inactive icon", "INCIDENT": "Incident", "INCIDENTS": "Incidents", "DP_TOP_ENDPOINTS_WITH_SENSITIVE_DATA_BEING_EXFILTRATED": "Top Endpoints with Sensitive Data Being Exfiltrated", "INCIDENTS_ARE_ISSUES_THAT_IMPACT_THE_DEVICE_PERFORMANCE_OF_MULTIPLE_USERS": "Incidents are issues that impact the device performance of multiple users.", "INCIDENTS_BY_EPICENTERS": "Incidents by Epic<PERSON>s", "INCIDENTS_BY_EPICENTERS_TOOLTIP": "The number of incidents are calculated based on the epicenter.", "INCIDENTS_OVER_TIME": "Incidents Over Time", "INCIDENTS_OVER_TIME_TOOLTIP": "The number of incidents that occur based on impacted devices within a time range.", "INCIDENT_ACROSS_KEY_AREAS": "Incidents Across Key Areas", "INCIDENT_DATE": "Incident Date", "INCIDENT_DETAILS": "Incident Details", "INCIDENT_DURATION": "Incident Duration", "INCIDENT_NAME": "Incident Name", "INCIDENT_TYPE": "Incident Type", "INCLUDE": "Include", "INCOMING_REAL_THREATS": "Incoming Real Time Threats", "INFORMATION": "Information", "INFO_ICON": "Info icon", "INITIAL_DOMAIN_NAME": "Initial Domain Name", "INPUT_METHOD": "Input Method", "INSPECTED": "Inspected", "INTEGER": "Integer", "INTEGRATION": "Integration", "INTEGRATIONS": "Integrations", "INTERFACE": "Interface", "INTERMEDIATE_ISP": "Intermediate ISP", "INTERNET": "Internet", "INTERNAL_GATEWAY_IP_ADDRESS": "Internal Gateway IP Address", "INVALID_CREDENTIALS_MESSAGE": "Invalid Login ID or Password", "INVALID_FILE_SIZE": "Invalid file size", "INVALID_IMAGE_TYPE": "Invalid Image Type", "IN_OFFICE": "In-Office", "IOS": "iOS", "IP_ADDRESS": "IP Address", "IP_ADDRESS_REQUIRED_MESSAGE": "IP Address is required", "IP_LOCATIONS": "IP Locations", "IP_LOCATIONS_AND_GROUPS": "IP Locations & Groups", "IP_LOCATION_GROUPS": "IP Location Groups", "ISOLATED": "Isolated", "ISSUED_AT": "Issued At", "ISSUER": "Issuer", "ITEMS_HAVE_BEEN_DELETED": "Items have been deleted", "ITEM_HAS_BEEN_DELETED": "The Item has been deleted", "IT_SERVICES": "IT Services", "Infrastructure": "Infrastructure", "JAVASCRIPT": "JavaScript", "JAVA_APPLET": "Java Applet (jar, class, applet)", "JIT": "jit", "JITTER_HOP1": "Jitter on hop 1", "JITTER_HOP2": "Jitter on hop 2", "JIT_ATTRIBUTE_MAPPING": "JIT Attribute Mapping", "JIT_PROVISIONING": "Just-in-time (JIT) Provisioning", "JTI": "JTI", "JUST_IN_TIME_ATTRIBUTE": "Just-in-time Attribute", "JUST_IN_TIME_ATTRIBUTE_MAPPING": "JUST-IN-TIME Attribute Mapping", "JUST_IN_TIME_USER_GROUP_ATTRIBUTE": "Just-in-time User Group Attribute", "JWKS_ENDPOINT": "JWKS Endpoint", "JWT": "JWT", "KEY_METRICS": "Key Metrics", "LABEL": "Label", "LABEL_BADGE": "Label Badge {{text}}", "LANGUAGE": "English (US)", "LANGUAGE_LABEL": "Language", "LAPTOP_ICON": "Laptop icon", "LAST_FIFTEEN_MINUTES": "Last 15 Minutes", "LAST_FORTY_EIGHT_HOURS": "Last 48 Hours", "LAST_KNOWN_ATTEMPT": "Last Known Attempt", "LAST_KNOWN_DATE": "Last Known Date", "LAST_MILE_ISP": "Last Mile ISP", "LAST_MILE_ISP_BLACKOUT": "Blackout", "LAST_MILE_ISP_BLACKOUT_TABLE_COLUMN": "Last Mile ISP", "LAST_MILE_ISP_BROWNOUT": "Brownout", "LAST_MILE_ISP_BROWNOUT_TABLE_COLUMN": "Last Mile ISP", "LAST_MODIFIED_ON": "Last Modified On", "LAST_NAME": "Last Name", "LAST_ONE_HOUR": "Last 1 Hour", "LAST_THIRTY_MINUTES": "Last 30 Minutes", "LAST_TWENTY_FOUR_HOURS": "Last 24 Hours", "LAST_UPDATED": "Last Updated", "LAST_USED": "Last Used", "LATENCY_SPIKE_HOP1": "First Hop Latency Around SME", "LATENCY_SPIKE_HOP2": "Second Hop Latency Around SME", "LAUNCH": "Launch", "LEARN_MORE": "Learn more", "LEFT_ICON": "Left icon", "LEGEND_ICON": "{{name}} icon", "LEVELS": "Levels", "LEVELS_TO_AUTHENTICATION_CONTEXT_MAPPING": "Levels to Authentication context mapping", "LEVEL_NAME": "Level Name", "LINK": "Link", "LINKED": "Linked", "LINKED_SERVICES": "Linked Services", "LINKED_TENANTS": "Linked Services", "LINK_TENANT": "<PERSON>", "LINUX": "Linux", "LIST_ICON": "{{title}} icon", "LM_ANALYTICS_COPILOT": "Copilot", "LM_ANALYTICS_CS_ADV_THREATS": "Advanced Threats", "LM_ANALYTICS_CS_CS": "Cybersecurity", "LM_ANALYTICS_CS_SBOX_THREATS": "Sandbox Threats", "LM_ANALYTICS_CS_THREATS_LOCS": "Threat Locations", "LM_ANALYTICS_CS_TLS_INSPECT": "SSL/TLS Inspection", "LM_ANALYTICS_CS_TRANS_ACTIVITY": "Transactional Activity", "LM_ANALYTICS_DE_ACTIVITY": "Activity", "LM_ANALYTICS_DE_APPLICATIONS": "Applications", "LM_ANALYTICS_DE_DE": "Digital Experience", "LM_ANALYTICS_DE_INCIDENTS": "Incidents", "LM_ANALYTICS_DE_MEETINGS": "Meetings", "LM_ANALYTICS_DE_RISK": "Risk", "LM_ANALYTICS_DE_SELF_SVC": "Self Service", "LM_ANALYTICS_NETWORKING_NETWORKING": "Networking", "LM_ANALYTICS_NETWORKING": "Networking", "LM_ANALYTICS_RISK": "Risk", "LM_ANALYTICS_NETWORKING_CONNECTOR_ACTIVITY": "Connector Activity", "LM_ANALYTICS_OPERATIONAL_OPERATIONAL": "Operational", "LM_ANALYTICS_OPERATIONAL_DEVICES": "Devices", "LM_ANALYTICS_OPERATIONAL_APPLIANCES": "Appliances", "LM_ANALYTICS_NETWORKING_DESCRIPTION": "Provides visibility into organizational traffic for both internet and private applications, including insights into data centers and connectors.", "LM_ANALYTICS_DE_DE_DESCRIPTION": "Delivers monitoring insights for end-users, applications, and devices.", "LM_ANALYTICS_CS_CS_DESCRIPTION": "Offers insights into zero-day threats, suspicious activity, and security events.", "LM_ANALYTICS_DATA_PROTECTION_DESCRIPTION": "Provides centralized insights into data at rest and data in motion in the organization.", "LM_ANALYTICS_OPERATIONAL_DEVICES_DESCRIPTION": "Provides insights into user devices, including their characteristics (e.g., versions, operating systems, etc.)", "LM_ANALYTICS_OPERATIONAL_APPLIANCES_DESCRIPTION": "Delivers information about appliances, covering their deployment and active status.", "REPORTS_PA_APPS_DESCRIPTION": "Offers insights into private applications, including recent access events, discovered policy blocks, and related activity.", "LM_ANALYTICS_RPTS_INET_ANOMALY_RPT": "Anomaly Report", "LM_ANALYTICS_RPTS_INET_EXEC_INSIGHT_RPT": "Executive Insights Report", "LM_ANALYTICS_RPTS_INET_INST_DISC_RPT": "Instance Discovery Report", "LM_ANALYTICS_RPTS_INET_SASS_ASSET_SUM_RPT": "SaaS Asset Summary Report", "LM_ANALYTICS_RPTS_INET_SASS_ATTACK_SFC_RPT": "Attack Surface Report", "LM_ANALYTICS_RPTS_INET_SASS_BANDWIDTH_CTRL": "Bandwidth Control", "LM_ANALYTICS_RPTS_INET_SASS_BROWSING": "Web Browsing", "LM_ANALYTICS_RPTS_INET_SASS_CFG_RISK_RPT": "Configuration Risk Report", "LM_ANALYTICS_RPTS_INET_SASS_CLOUD_APPS": "Cloud Applications", "LM_ANALYTICS_RPTS_INET_SASS_CS_INSIGHTS": "Cybersecurity Insights", "LM_ANALYTICS_RPTS_INET_SASS_DATA_DISC_RPT": "Data Discovery Report", "LM_ANALYTICS_RPTS_INET_SASS_DNS_OVERVIEW": "DNS Overview", "LM_ANALYTICS_RPTS_INET_SASS_EMAIL_DLP": "Email DLP Overview", "LM_ANALYTICS_RPTS_INET_SASS_EMAIL_SEC_RPT": "Email Security Reports", "LM_ANALYTICS_RPTS_INET_SASS_ENDPOINT_DLP": "Endpoint DLP Overview", "LM_ANALYTICS_RPTS_INET_SASS_EPDLP_RPT": "Endpoint DLP Reports", "LM_ANALYTICS_RPTS_INET_SASS_FW_OVERVIEW": "Firewall Overview", "LM_ANALYTICS_RPTS_INET_SASS_GENAI_RPT": "Generative AI Security Reports", "LM_ANALYTICS_RPTS_INET_SASS_INTERACTIVE_RPTS": "Interactive Reports", "LM_ANALYTICS_RPTS_INET_SASS_IOT_DISC_RPT": "IoT Discovery Report", "LM_ANALYTICS_RPTS_INET_SASS_IPS_OVERVIEW": "IPS Overview", "LM_ANALYTICS_RPTS_INET_SASS_MOBILE_APPS": "Mobile Applications", "LM_ANALYTICS_RPTS_INET_SASS_O365": "Office 365", "LM_ANALYTICS_RPTS_INET_SASS_ONBOARDING": "Unified SaaS Applications", "LM_ANALYTICS_RPTS_INET_SASS_PEER_COMPARISON": "Industry Peer Comparison", "LM_ANALYTICS_RPTS_INET_SASS_QBR_RPT": "QBR Reports", "LM_ANALYTICS_RPTS_INET_SASS_RISK_SCORE_RPT": "Company Risk Score Report", "LM_ANALYTICS_RPTS_INET_SASS_SBOX_ACTIVITY_RPT": "Sandbox Activity Report", "LM_ANALYTICS_RPTS_INET_SASS_SECURITY": "Security", "LM_ANALYTICS_RPTS_INET_SASS_SEC_POLICY_AUDIT_RPT": "Security Policy Audit Report", "LM_ANALYTICS_RPTS_INET_SASS_SEC_RPT": "SaaS Security Report", "LM_ANALYTICS_RPTS_INET_SASS_SYSTEM_AUDIT_RPT": "System Audit Report", "LM_ANALYTICS_RPTS_INET_SASS_THREAT_INSIGHTS": "Threat Insights", "LM_ANALYTICS_RPTS_INET_SASS_WEB_OVERVIEW": "Web Overview", "LM_ANALYTICS_DATA_PROTECTION": "Data Protection", "LM_ANALYTICS_DATA_CHANNELS": "Data Channels", "LM_RISK_FACTORS": "Factors", "LM_RISK_ASSETS": "Assets", "LM_RISK_INSIGHTS": "Insights", "LM_RISK_FINANCIAL_RISK": "Financial Risk", "LM_RISK_FRAMEWORKS": "Frameworks", "LM_RISK_MITRE": "MITRE ATT&CK", "LM_RISK_NIST": "NIST CSF", "LM_RISK_REPORTS": "Reports", "MK_RISK360": "Risk360", "RISK360_ROLES": "Risk360", "MK_RISK360_FRAMEWORKS": "Frameworks", "RISK360_ADV_SUB_MSG": "Feature not subscribed. To access this feature, you need Risk360 Advanced subscription. Please contact your Zscaler Account Team for more information.", "LOADER_ICON": "Loader icon", "LOAD_FAILED": "Failed to Load", "LOAD_MORE": "Load More", "LOCALE": "en-US", "LOCATION": "Location", "LOCATIONS": "Locations", "LOCATIONS_REQUIRED_MESSAGE": "Locations is required", "LOCATION_COUNT": "Location Count", "LOCATION_GROUP": "Location Groups", "LOCATION_ICON": "Location icon", "LOCATION_INFORMATION": "Location Information", "LOCK_ICON": "Lock icon", "LOGIN": "<PERSON><PERSON>", "LOGIN_HINT": "<PERSON><PERSON>", "LOGIN_ID": "Login ID", "LOGIN_ID_ATTRIBUTE": "Login Id Attribute", "LOGIN_ID_LABEL": "Login ID", "LOGIN_ID_PLACEHOLDER": "Enter Your Login ID...", "LOGIN_NAME": "Login Name", "LOGIN_PASSWORD_PLACEHOLDER": "Enter Your Password...", "LOGO": "Logo", "LOGOUT": "Logout", "LOGOUT_ICON": "{{text}} Icon", "LOGO_NOT_FOUND": "No logo is associated with this organization.", "LOW_MEMORY": "Low Memory", "HIGHEST_THREATS": "Highest Threats", "Logs": "Logs", "LOG_AND_CONTROL": "Log & Control", "MACOS": "MacOS", "MAC_ICON": "Mac icon", "MALICIOUS": "Malicious", "MALICIOUS_TRANSACTIONS": "Malicious Transactions", "MALICIOUS_TRANSACTIONS_THREATS": "Malicious transactions are threats that have been detected and blocked by Zscaler.", "MALWARE_SITE": "Malicious Content", "MANAGE": "Manage", "MANAGEMENT": "Management", "MANAGEMENT_IP_ADDRESS": "Management IP Address", "MANAGEMENT_DETAILS": "Management Details", "MANAGEMENT_DEFAULT_GATEWAY": "Management Default Gateway", "MANAGE_DEVICE_GROUP_RESTRICTIONS": "Manage Device Group Restrictions", "MANAGE_ROLES": "Manage Roles", "MANUAL": "Manual", "MANUAL_ENTRY": "Enter Manually", "MAX": "Max", "MD5": "MD5", "MEETINGS": "Meetings", "MEETING_DETAILS": "Meeting Details", "MEETING_ID": "Meeting ID", "MESSAGE_TO_USER": "Message to the user (optional)", "METADATA_URL": "Metadata URL", "METRIC": "Metric", "MFA_AUTHENTICATOR": "MFA Authenticator", "MFA_ENROLLMENT_GRACE_PERIOD": "MFA Enrollment Grace Period (in Days)", "MFA_TOGGLE_TOOLTIP": "Enabling Multi-Factor Authentication allows users to select a secondary authentication factor along with passwords.", "MICROSEGMENTATION": "Microsegmentation", "MICROSEGMENTATION_AGENT": "Agent", "MICROSEGMENTATION_AGENTS": "Agents", "MICROSEGMENTATION_AGENT_CONNECTION_STATUS_LOG": "Agent Connection Status Logs", "MICROSEGMENTATION_AGENT_GROUPS": "Agent Groups", "MICROSEGMENTATION_AGENT_PROVISIONING": "Agent Provisioning Keys", "MICROSEGMENTATION_AGENT_TELEMETRY": "Agent Telemet<PERSON>", "MICROSEGMENTATION_APPLICATION_MAP": "Application Map", "MICROSEGMENTATION_APP_ZONES": "App Zones", "MICROSEGMENTATION_FLOW": "Flow", "MICROSEGMENTATION_FLOW_LOGS": "Flow Logs", "MICROSEGMENTATION_POLICIES": "Resource Policies", "MICROSEGMENTATION_POLICY_MAP": "Policy Map", "MICROSEGMENTATION_POLICY_SETTINGS": "Policy Settings", "MICROSEGMENTATION_RESOURCES": "Resources", "MICROSEGMENTATION_RESOURCE_GROUPS": "Resource Groups", "MICROSOFT_INSTALLER": "Microsoft Installer (msi)", "MICROSOFT_TEAMS_CALL_QUALITY": "Microsoft Teams Call Quality", "MIN": "Min", "MINUTES": "Minutes", "MIN_LENGTH_REQUIRED": "{{value}} characters minimum", "MIN_LOWER_CASE_REQUIRED": "At least {{value}} lowercase character (a-z)", "MIN_LWR_CASE": "Minimum No. of Lowercase Letters", "MIN_NUMERIC": "Minimum No. of Numeric Characters", "MIN_NUMERIC_REQUIRED": "At least {{value}} numeric character (0-9)", "MIN_SPECIAL_CHAR_REQUIRED": "At least {{value}} special character", "MIN_SPL_CHAR": "Minimum No. of Special Characters", "MIN_UPPER_CASE_REQUIRED": "At least {{value}} uppercase character (A-Z)", "MIN_UPPR_CASE": "Minimum No. of Uppercase Letters", "MISCELLANEOUS_OR_UNKNOWN": "Miscellaneous or Unknown", "MISC_AIML": "Miscellaneous AI & ML Apps", "MISC_BUSINESS": "Miscellaneous Productivity and CRM Tools Apps", "MISC_CONSUMER": "Miscellaneous Consumer Apps", "MISC_ENTERPRISE": "Miscellaneous Collaboration and Online Meetings Apps", "MISC_FILESHARE": "Miscellaneous File Sharing Apps", "MISC_FINANCE": "Miscellaneous Finance Apps", "MISC_HEALTHCARE": "Miscellaneous Health Care Apps", "MISC_HOSTPRO": "Miscellaneous Hosting Providers Apps", "MISC_HUMANRESOURCES": "Miscellaneous Human Resources Apps", "MISC_IM": "Miscellaneous Instant Messaging Apps", "MISC_ITSERVICES": "Miscellaneous IT Services Apps", "MISC_LEGAL": "Miscellaneous Legal Apps", "MISC_SALESMARKETING": "Miscellaneous Sales & Marketing Apps", "MISC_SOCNET": "Miscellaneous Social Networking Apps", "MISC_STREAMING": "Miscellaneous Streaming Media Apps", "MISC_SYSDEV": "Miscellaneous System & Development Apps", "MISC_WEBMAIL": "Miscellaneous Web Mail Apps", "MISC_WEBSEARCH": "Miscellaneous Web Search Apps", "MMC_ADMIN_ALERTS_DEM": "<PERSON><PERSON><PERSON>", "MMC_ADMIN_ALERTS_DEM_RULES": "Rules", "MMC_ADMIN_ALERTS_DEM_TEMPLATES": "Templates", "MMC_ADMIN_ALERTS_DEM_WEBHOOKS": "Webhooks", "MMC_ADMIN_ALERTS_PA_NOTIFICATIONS": "Notifications", "MMC_ADMIN_ALERTS_PLATFORM": "Platform Alerts", "MMC_ADMIN_ALERTS_SEC_UEBA": "Security & UEBA Alerts", "MMC_ADMIN_AS_PA_MICRO_TNT": "Private App Microtenants", "MMC_ADMIN_AUDIT_DE": "Digital Experience", "MMC_ADMIN_AUDIT_INET_SASS": "Internet & SaaS", "MMC_ADMIN_AUDIT_MA": "Mobile Administration", "MMC_ADMIN_AUDIT_PRIV_ACCESS": "Private Access", "MMC_ADMIN_AUDIT_UNIFIED_UI": "Unified User Interface", "MMC_ADMIN_BACKUP_INET_SAAS": "Internet & SaaS Applications", "MMC_ADMIN_BACKUP_PA": "Private Applications", "MMC_ADMIN_BACKUP_SCHED_CFG": "Scheduled Backup Configuration", "MMC_ADMIN_CLIENT_CONN_TOKEN": "IDP Device <PERSON>", "MMC_ADMIN_COMPANY_BRANDING": "Branding", "MMC_ADMIN_COMPANY_LINKED_SERVICES": "Linked Services", "MMC_ADMIN_COMPANY_PROFILE_ORG": "Organization", "MMC_ADMIN_COMPANY_PROV_ACCESS_TENANT": "Private Access Tenant", "MMC_ADMIN_COMPANY_SUBSCRIPTIONS": "Subscriptions", "MMC_ADMIN_DOMAINS_DOMAINS": "Domains", "MMC_ADMIN_DOMAINS_EXTRANET_TIPL": "Trusted IP Locations", "MMC_ADMIN_DOMAINS_EXTRANET_TIPLG": "Trusted IP Location Groups", "MMC_ADMIN_ENT_ADMIN_ENT": "Administrative Entitlements", "MMC_ADMIN_ENT_CCSE_DE": "Digital Experience", "MMC_ADMIN_ENT_CCSE_DECEPTION": "Deception", "MMC_ADMIN_ENT_CCSE_INET_ACCESS": "Internet Access", "MMC_ADMIN_ENT_CCSE_PRIVATE_ACCESS": "Private Access", "MMC_ADMIN_ENT_END_USER_ENT": "End User Entitlements", "MMC_ADMIN_ENT_PA_EXEC_INSIGHTS": "Private Access Exec Insights", "MMC_ADMIN_ID_EXT_IDENTITIES": "External Identities", "MMC_ADMIN_ID_ZID_PORTAL": "ZIdentity Portal", "MMC_ADMIN_INET_SASS_AUTH_SETTINGS": "Internet Authentication Settings", "MMC_ADMIN_INET_SASS_ID_PROXY_SETTINGS": "Identity Proxy Settings", "MMC_ADMIN_INET_SASS_SCIM_EVENT_LOGS": "SCIM Event Logs", "MMC_ADMIN_INET_SASS_USER_MGMT": "User Management", "MMC_ADMIN_LEGACYAPI_CCAPI": "Client Connector API", "MMC_ADMIN_LEGACYAPI_DEAPI": "Digital Experience API", "MMC_ADMIN_LEGACYAPI_INET_SAAS": "Internet & SaaS API", "MMC_ADMIN_LEGACYAPI_PRIVACCESS_API": "Private Access API", "MMC_ADMIN_MGMT_INET_ACCESS_ADMIN": "Internet Access Administrators", "MMC_ADMIN_ONEAPI_CLIENTS": "API Clients", "MMC_ADMIN_ONEAPI_RESOURCES": "API Resources", "MMC_ADMIN_ONEAPI_TOKENS": "Tokens", "MMC_ADMIN_ONEAPI_TOKEN_VALIDATORS": "Token Validators", "MMC_ADMIN_PASSAUTH_AUTH_LEVELS": "Authentication Levels", "MMC_ADMIN_PASSAUTH_AUTH_METHODS": "Authentication Methods", "MMC_ADMIN_PASSAUTH_AUTH_SESSION": "Authentication Session", "MMC_ADMIN_PASSAUTH_COMPLEXITY": "Password Complexity", "MMC_ADMIN_API_CLIENT_ACCESS_POLICY": "API Client Access Policy", "MMC_ADMIN_PA_DEVAUTH_MACHINE_GROUP": "Machine Groups", "MMC_ADMIN_PA_DEVAUTH_MACHINE_PROV_KEYS": "Machine Provisioning Keys", "MMC_ADMIN_PA_IDP_CFG": "IDP Configuration", "MMC_ADMIN_PA_IDP_SETTINGS": "Settings", "MMC_ADMIN_PA_PARTNER_LOGIN": "Partner Login", "MMC_ADMIN_PA_PRA_EMERGENCY_ACCESS": "Emergency Access", "MMC_ADMIN_PA_PRA_EMERGENCY_ACCESS_USERS": "Emergency Access Users", "MMC_ADMIN_PA_SAML_ATTR": "SAML Attributes", "MMC_ADMIN_PA_SCIM_ATTR": "SCIM Attributes", "MMC_ADMIN_PA_SCIM_GROUPS": "SCIM Groups", "MMC_ADMIN_PA_SCIM_SYNC_LOGS": "SCIM Sync Logs", "MMC_ADMIN_PA_SCIM_USERS": "SCIM Users", "MMC_ADMIN_PA_USER_RISK_SCORES": "User Risk Scores", "MMC_ADMIN_RBAC_CC": "Connectors", "MMC_ADMIN_RBAC_DE": "Digital Experience", "MMC_ADMIN_RBAC_INET_SASS": "Internet & SaaS", "MMC_ADMIN_RBAC_PRIV_ACCESS": "Private Access", "MMC_ADMIN_RBAC_UNIFIED_UI": "Unified User Interface", "MMC_ADMIN_SIGN_ON": "Sign-On Policies", "MMC_ADMIN_USERMGMT_ATTR": "Attributes", "MMC_ADMIN_USERMGMT_DEPT": "Departments", "MMC_ADMIN_USERMGMT_USERS": "Users", "MMC_ADMIN_USERMGMT_USER_GROUPS": "User Groups", "MMC_INFRA_APP_CONNECTORS": "App Connectors", "MMC_INFRA_APP_CONNECTOR_GROUPS": "App Connector Groups", "MMC_INFRA_APP_CONNECTOR_KEYS": "App Connector Keys", "MMC_INFRA_BANDWIDTH_CLASSES": "Bandwidth Classes", "MMC_INFRA_BANDWIDTH_CTRL": "Bandwidth Control", "MMC_INFRA_BC_DISASTER_RECOVERY": "Disaster Recovery", "MMC_INFRA_BC_PRV_CLOUDS": "Private Clouds", "MMC_INFRA_BC_PRV_CLOUD_CTRLS": "Private Cloud Controllers", "MMC_INFRA_BC_PRV_CLOUD_CTRL_GROUPS": "Private Cloud Controller Groups", "MMC_INFRA_BC_PRV_CLOUD_CTRL_PROV_KEYS": "Private Cloud Controller Provisioning Keys", "MMC_INFRA_BC_SETTINGS": "Settings", "MMC_INFRA_BRANCH_CONNECTOR": "Branch Connector", "MMC_INFRA_BRANCH_CONNECTOR_GROUPS": "Branch Connector Groups", "MMC_INFRA_CCP_CLIENT_FWD_POLICIES": "Client Forwarding Policies", "MMC_INFRA_CCP_IP_ASSIGNMENT": "IP Assignment", "MMC_INFRA_CCP_REDIRECTION_POLICIES": "Redirection Policy", "MMC_INFRA_CC_CA_IP_BASED": "IP Based", "MMC_INFRA_CC_CA_PROCESS_BASED": "Process Based", "MMC_INFRA_CC_DEPLOY_FRO": "Feature Roll out", "MMC_INFRA_CC_DEPLOY_PR": "Platform Releases", "MMC_INFRA_CC_DEPLOY_RD": "Registered Devices", "MMC_INFRA_CC_DEPLOY_ZDXR": "ZDX Releases", "MMC_INFRA_CC_ED_DG": "Device Groups", "MMC_INFRA_CC_ED_DO": "Device Overview", "MMC_INFRA_CC_ED_MT": "Machine Tunnel", "MMC_INFRA_CC_ED_FPD": "Failed Posture Devices", "MMC_INFRA_CC_ED_PD": "Partner Devices", "MMC_INFRA_CC_FP_PLATFORMS": "Forwarding Profile for Platforms", "MMC_INFRA_CC_GS_DPP": "Dedicated Proxy Port", "MMC_INFRA_CC_GS_DSCRC": "Directory Sync & Custom Root Cert", "MMC_INFRA_CC_GS_EC": "Endpoint Configuration", "MMC_INFRA_CC_GS_UA": "User Agent", "MMC_INFRA_CC_GS_ZD": "Deception", "MMC_INFRA_CC_PS_ANDROID": "Android", "MMC_INFRA_CC_PS_IOS": "iOS", "MMC_INFRA_CC_PS_LINUX": "Linux", "MMC_INFRA_CC_PS_MACOS": "MacOS", "MMC_INFRA_CC_PS_WINDOWS": "Windows", "MMC_INFRA_CC_SUPPORT_AFO": "App Fail Open", "MMC_INFRA_CC_SUPPORT_AS": "App Supportability", "MMC_INFRA_CC_SUPPORT_CCDM": "Client Connector Device Management", "MMC_INFRA_CC_SUPPORT_BC": "Business Continuity", "MMC_INFRA_CC_SUPPORT_NP": "Network Performance", "MMC_INFRA_CC_SUPPORT_UP": "User Privacy", "MMC_INFRA_CLOUD_CONNECTOR": "Cloud Connector", "MMC_INFRA_CLOUD_CONNECTOR_GROUPS": "Cloud Connector Groups", "MMC_INFRA_DC_EXCLUSION": "DC Exclusion", "MMC_INFRA_DEDICATED_IP": "Dedicated IP", "MMC_INFRA_EC_ED_VDIDM": "VDI Device Management", "MMC_INFRA_EC_FP_VDI": "Forwarding Profile for VDI", "MMC_INFRA_ENROLLMENT_CERTS": "Enrollment Certificates", "MMC_INFRA_FORWARDING_CTRL_POLICY": "Forwarding Control Policy", "MMC_INFRA_HOSTED_PAC_FILES": "Hosted PAC Files", "MMC_INFRA_IPV6_CFG": "Ipv6 Configurations", "MMC_INFRA_LOCATION_CC_TN": "Trusted Networks", "MMC_INFRA_LOCATION_MGMT": "Location Management", "MMC_INFRA_LOCATIONS": "Locations", "MMC_INFRA_LOCATION_GROUPS": "Location Groups", "MMC_INFRA_AZURE_VIRTUAL_WAN_LOCATIONS": "Azure Virtual WAN Locations", "MMC_INFRA_PINT_AZURE_VWAN": "Partner Integrations: Azure Virtual WAN", "MMC_INFRA_PINT_SDWAN": "Partner Integrations: SDWAN", "MMC_INFRA_PROXIES_GATWEWAYS": "Proxies and Gateways", "MMC_INFRA_PRV_SVC_EDGES": "Private Service Edges", "MMC_INFRA_ROOT_CERTS": "Root Certificates", "MMC_INFRA_STATICIP_GRE_TUNNELS": "Static IPs & GRE Tunnel", "MMC_INFRA_SUBCLOUDS": "Subclouds", "MMC_INFRA_SVC_EDGE_GROUPS": "Service Edge Groups", "MMC_INFRA_SVC_EDGE_KEYS": "Service Edge Keys", "MMC_INFRA_VIRT_SRVC_EDGES": "Virtual Service Edges", "MMC_INFRA_VIRT_ZENS": "Virtual Zens", "MMC_INFRA_VPN_CONNECTED_USERS": "VPN Connected Users", "MMC_INFRA_VPN_CONNECTORS": "Network Connectors", "MMC_INFRA_VPN_CONNECTOR_GROUPS": "Network Connector Groups", "MMC_INFRA_VPN_CONNECTOR_PROV_KEYS": "Network Connector Provisioning Keys", "MMC_INFRA_VPN_CREDS": "VPN Credentials", "MMC_INFRA_VPN_DASHBOARD": "Network Connectors Dashboard", "MMC_INFRA_VPN_SEGMENT": "Network Segments", "MMC_INFRA_VPN_USER_ENABLEMENT": "User Enablement", "MMC_INFRA_VPN_SVC_EDGES": "VPN Service Edges", "MMC_INFRA_ZSCALER_PA": "Zscaler Private Access", "MMC_LOGS_DNS_INSIGHTS": "DNS Insights", "MMC_LOGS_EMAIL_DLP_INSIGHTS": "Email DLP Insights", "MMC_LOGS_ENDPOINT_DLP_INSIGHTS": "Endpoint DLP Insights", "MMC_LOGS_EXTRANET_INSIGHTS": "Extranet Insights", "MMC_LOGS_FIREWALL_INSIGHTS": "Firewall Insights", "MMC_LOGS_MOBILE_INSIGHTS": "Mobile Insights", "MMC_LOGS_PA_DIAGNOSTICS": "Diagnostics", "MMC_LOGS_PA_LIVE_LOGS": "Live Logs", "MMC_LOGS_PA_MITRE_ATTACK": "MITRE ATT&CK", "MMC_LOGS_PA_SUPPORT_INFO": "Support Information", "MMC_LOGS_PA_USAGE": "Usage", "MMC_LOGS_PRR_PRIV_FILES": "Files", "MMC_LOGS_PRR_PRIV_REQUESTS": "Requests", "MMC_LOGS_PRR_PRIV_RECORDINGS": "Recordings", "MMC_LOGS_PRR_PRIV_SESSIONS": "Live Sessions", "MMC_LOGS_SAASSEC_INSIGHTS": "SaaS Security Insights", "MMC_LOGS_STRM_LOG_CONNECTOR_GROUPS": "Log Connector Groups", "MMC_LOGS_STRM_LOG_RECEIVERS": "Log Receivers", "MMC_LOGS_STRM_NANOLOG_STREAMING_SVC": "Nanolog Streaming Service", "MMC_LOGS_TCAP_TRAFFIC_CAPTURE": "Traffic Capture", "MMC_LOGS_TUNNEL_INSIGHTS": "Tunnel Insights", "MMC_LOGS_WEB_INSIGHTS": "Web Insights", "MMC_POLICIES_CC_AA_INTEGRATIONS": "Integrations", "MMC_POLICIES_CC_AA_OVERRIDES": "Overrides", "MMC_POLICIES_CC_AA_PROFILES": "Profiles", "MMC_POLICIES_CC_AA_SIGNAL_HISTORY": "Signal History", "MMC_POLICIES_CC_ADV_SETTINGS": "Advanced Settings", "MMC_POLICIES_CC_ADV_VIEW_POLICIES": "View All Policies", "MMC_POLICIES_CC_APP_TENANTS": "SaaS Application Tenants", "MMC_POLICIES_CC_EMAIL_LABELS": "Email Labels", "MMC_POLICIES_CC_RES_BROWSER_ISOLATION": "Browser Isolation", "MMC_POLICIES_CC_RES_BROWSER_ISOLATION_THREAT": "Browser Isolation Threat", "MMC_POLICIES_CC_RES_CC_NOTIF": "Client Connector Notifications", "MMC_POLICIES_CC_RES_DEVICE_MGMT": "Device Management", "MMC_POLICIES_CC_RES_DEVICE_POSTURE": "<PERSON><PERSON>", "MMC_POLICIES_CC_RES_DP_USER_CONF": "Data Protection User Confirmation", "MMC_POLICIES_CC_RES_END_USER_NOTIF": "End User Notifications", "MMC_POLICIES_CC_RES_INET_SASS_POSTURE": "Internet & SaaS Posture Profiles", "MMC_POLICIES_CC_RES_RULE_LABELS": "Rule Labels", "MMC_POLICIES_CC_RES_TIME_INTERVALS": "Time Intervals", "MMC_POLICIES_CC_SCAN_CFG": "Scan Configuration", "MMC_POLICIES_CC_SCAN_EXEMPTIONS": "Scanning Exceptions", "MMC_POLICIES_CC_SSL_INSPECTION_INT_CERT": "SSL/TLS Inspection Intermediate Certificate", "MMC_POLICIES_CC_SSL_INSPECTION_POLICY": "SSL/TLS Inspection Policy", "MMC_POLICIES_CC_TOMBSTONE_TEMPLATE": "Tombstone Template", "MMC_POLICIES_CLESS_ACCESS_BROWSER": "Browser Access", "MMC_POLICIES_CLESS_ACCESS_CERTS": "Certificates", "MMC_POLICIES_CLESS_ACCESS_PRIV_PORTALS": "Privileged Portals", "MMC_POLICIES_CLESS_ACCESS_USER_PORTALS": "User Portals", "MMC_POLICIES_CLESS_AUP": "Acceptable Use Policy", "MMC_POLICIES_CLESS_CCDL_LINKS": "Client Connector Download Links", "MMC_POLICIES_CLESS_ISOLATION_BANNERS": "Banners", "MMC_POLICIES_CLESS_ISOLATION_POLICY": "Isolation Policy", "MMC_POLICIES_CLESS_ISOLATION_PROFILES": "Profiles", "MMC_POLICIES_CLESS_ISOLATION_ROOT_CERT": "Root Certificates", "MMC_CHROME_ENTERPRISE_SETTINGS": "Settings", "MMC_CHROME_ENTERPRISE": "Chrome Posture Profile", "MMC_POLICIES_CLESS_PORTAL_CFG": "Portal Links", "MMC_POLICIES_CLESS_PRA": "Consoles Policy", "MMC_POLICIES_CLESS_PRA_APPS": "Privileged Applications", "MMC_POLICIES_CLESS_PRA_CREDENTIALS": "Credentials Policy", "MMC_POLICIES_CLESS_PRA_PORTALS": "Portals Policy", "MMC_POLICIES_CLESS_RES_PRIV_APPROVALS": "Privileged Approvals", "MMC_POLICIES_CLESS_RES_PRIV_CONSOLE": "Privileged Consoles", "MMC_POLICIES_CLESS_RES_PRIV_CRED": "Privileged Credentials", "MMC_POLICIES_CLESS_RES_SANDBOX_INTEGRATIONS": "Sandbox Integration", "MMC_POLICIES_CS_IS_CUSTOM_IPS_SIG": "Custom IPS Signatures", "MMC_POLICIES_CS_IS_IPS_CTR": "IPS Control", "MMC_POLICIES_CS_IS_PAP_POLICIES": "Protection Policies", "MMC_POLICIES_CS_IS_PAP_PROTECTED_APPS": "Protected Applications", "MMC_POLICIES_CS_IS_PAP_PROTECTION_CTRLS": "Protection Controls", "MMC_POLICIES_CS_IS_PAP_PROTECTION_PROFILES": "Protection Profiles", "MMC_POLICIES_CS_IS_SANDBOX": "Sandbox", "MMC_POLICIES_CS_IS_SEC_BROWSING": "Secure Browsing", "MMC_POLICIES_CS_IS_SSI_MALWARE_SCANNING": "Malware Scanning", "MMC_POLICIES_CS_IS_TP_ATP": "Advanced Threat Protection", "MMC_POLICIES_CS_IS_TP_MMP": "Mobile Malware Protection", "MMC_POLICIES_CS_IS_TP_MP": "Malware Protection", "MMC_POLICIES_CS_PT_CROWDSTRIKE": "Crowdstrike", "MMC_POLICIES_CS_PT_MICROSOFT_DEFENDER_FOR_ENDPOINT": "Microsoft Defender For Endpoint", "MMC_POLICIES_CS_PT_VOTIRO": "Votiro", "MMC_POLICIES_DEM_CFG": "Configuration", "MMC_POLICIES_DEM_RES_LABELS": "Labels", "MMC_POLICIES_DEM_SELF_SVC": "Self Service", "MMC_POLICIES_DEM_SETTINGS_DC_INTEGR": "Data Collection Integrations", "MMC_POLICIES_DEM_SETTINGS_INVENTORY": "Inventory Settings", "MMC_POLICIES_DP_ADV_SETTINGS": "Advanced Settings", "MMC_POLICIES_DP_DC_DICT_ENGINES": "Dictionaries & Engines", "MMC_POLICIES_DP_DC_IDX_TEMPLATES": "Index Templates", "MMC_POLICIES_DP_DC_IDX_TOOL": "Index Tool", "MMC_POLICIES_DP_DC_MIP_LABELS": "MIP Labels", "MMC_POLICIES_DP_DLP": "Data Loss Prevention", "MMC_POLICIES_DP_EC_DLP": "Data Loss Prevention", "MMC_POLICIES_DP_EC_ENDPOINT_DLP": "Endpoint DLP Resources", "MMC_POLICIES_DP_EMAIL_DOMAIN_PROFILES": "Email Domain Profiles", "MMC_POLICIES_DP_IM_DLP_INC_RECV": "DLP Incident Receiver", "MMC_POLICIES_DP_IM_ICAP_RECV": "ICAP Receiver", "MMC_POLICIES_DP_IM_NOTIF_TEMPLATES": "Notification Templates", "MMC_POLICIES_DP_OEC_DLP": "Data Loss Prevention", "MMC_POLICIES_DP_OEC_EMAIL_TENANTS": "Email Tenants", "MMC_POLICIES_DP_OOB_CASB": "Out-of-Band CASB", "MMC_POLICIES_DP_RIGHTS_MGMT": "Rights Management", "MMC_POLICIES_DP_SAAS_POSTURE_MGMT": "SaaS Security Posture Management", "MMC_POLICIES_DP_WL_WORKLOAD_GROUPS": "Workload Groups", "MMC_POLICIES_FILECTRL": "File Type Control", "MMC_POLICIES_FILECTRL_CUSTOM_FILE_TYPES": "Custom File Types", "MMC_POLICIES_FILECTRL_MOBILE_APPSTORE_CTRL": "Mobile App Store Control", "MMC_POLICIES_PA_ACCESS_POLICY": "Access Policy", "MMC_POLICIES_PA_APPSEGMENT": "App Segments", "MMC_POLICIES_PA_FW_DNS_APP_GROUP": "DNS Application Group", "MMC_POLICIES_PA_FW_DNS_CTRL": "DNS Control", "MMC_POLICIES_PA_FW_DNS_EDNS_SUBNET": "EDNS Client Subnet", "MMC_POLICIES_PA_FW_FILTER_POLICY": "Firewall Filtering Policy", "MMC_POLICIES_PA_FW_FTP_CTRL": "FTP Control", "MMC_POLICIES_PA_FW_IPFQDN_GROUPS": "IP & FQDN Groups", "MMC_POLICIES_PA_FW_NET_APPS": "Network Applications", "MMC_POLICIES_PA_FW_NET_APP_GROUPS": "Network Application Groups", "MMC_POLICIES_PA_FW_NET_APP_SVCS": "Application Services", "MMC_POLICIES_PA_FW_NET_SVCS": "Network Services", "MMC_POLICIES_PA_SEGMENT_GROUPS": "Segment Groups", "MMC_POLICIES_PA_SERVERS": "Servers", "MMC_POLICIES_PA_SERVER_GROUPS": "Server Groups", "MMC_POLICIES_PA_TIMEOUT_POLICY": "Timeout Policy", "MMC_POLICIES_SAASCTRL": "SaaS Applications", "MMC_POLICIES_SAASCTRL_APP_TAGS": "Application Tags", "MMC_POLICIES_SAASCTRL_POLICIES": "Policies", "MMC_POLICIES_SAASCTRL_RISK_PROFILES": "Risk Profiles", "MMC_POLICIES_SAASCTRL_TENANT_PROFILES": "Tenant Profiles", "MMC_POLICIES_URLCTRL_ADV_SETTINGS": "Advanced Settings", "MMC_POLICIES_URLCTRL_CATEGORIES": "URL Categories", "MMC_POLICIES_URLCTRL_FILTERING": "URL Filtering", "MMC_POLICIES_URLCTRL_HTTP_HEADER_CONTROL": "HTTP Header Control", "MMC_RISK_AUDITLOGS": "Risk360", "MMC_RISK_ALERTS": "<PERSON><PERSON><PERSON>", "MMC_RISK_RULES": "Rules", "MMC_RISK_WEBHOOKS": "Webhooks", "MMC_RISK_SCORE_LOGS": "Score Change Log", "MMC_RISK_SEEDS_MANAGEMENT": "Seeds Management", "MMV_TAB": "{{tab}} icon", "MM_ACCCTRL_VTAB_CLIENTLESS": "Clientless", "MM_ACCCTRL_VTAB_FIREWALL": "Firewall", "MM_ACCCTRL_VTAB_INET_SASS": "Internet & SaaS", "MM_ACCCTRL_VTAB_PRIV_APPS": "Private Applications", "MM_ACCCTRL_VTAB_SEGMENTATION": "Segmentation", "MM_ADMIN_MGMT_VTAB_ADMINISTRATOR_MGMT": "Administrator Management", "MM_ADMIN_MGMT_VTAB_AUDITLOGS": "<PERSON><PERSON>", "MM_ADMIN_MGMT_VTAB_RBAC": "Role Based Access Control", "MM_ADMIN_TAB_ACCOUNT_MGMT": "Account Management", "MM_ADMIN_TAB_ADMIN_MGMT": "Admin Management", "MM_ADMIN_TAB_ALERTS": "<PERSON><PERSON><PERSON>", "MM_ADMIN_TAB_API_CONFIG": "API Configuration", "MM_ADMIN_TAB_BACKUP_RESTORE": "Backup & Restore", "MM_ADMIN_TAB_ENTITLEMENTS": "Entitlements", "MM_ADMIN_TAB_IDENTITY": "Identity", "MM_APICFG_VTAB_LEGACY": "Legacy API", "MM_APICFG_VTAB_ONEAPI": "OneAPI", "MM_CLIENT_CONNECTOR_VTAB_APP_PROFILE": "Application Profile", "MM_CLIENT_CONNECTOR_VTAB_CONFIG": "Configuration", "MM_CLIENT_CONNECTOR_VTAB_DEPLOYMENT": "Deployment", "MM_CLIENT_CONNECTOR_VTAB_ENROLLED_DEVICES": "Enrolled Devices", "MM_COMMONCFG_VTAB_ADAPTIVE_ACCESS": "Adaptive Access", "MM_COMMONCFG_VTAB_ADVANCED": "Advanced", "MM_COMMONCFG_VTAB_OOB_CASB": "Out-of-Band CASB", "MM_COMMONCFG_VTAB_RESOURCES": "Resources", "MM_COMMONCFG_VTAB_SSL_INSPEC": "SSL/TLS Inspection", "MM_COMMON_RESOURCES_VTAB_APPLICATION": "Application", "MM_COMMON_RESOURCES_VTAB_CC_DEPLOYMENT": "Client Connector Deployment", "MM_CONNECTOR_VTAB_CLIENT": "Client", "MM_CONNECTOR_VTAB_CLOUD": "Cloud", "MM_CONNECTOR_VTAB_EDGE": "Edge", "MM_CYBERSEC_VTAB_INLINE": "Inline Security", "MM_CYBERSEC_VTAB_INTEGRATIONS": "Integrations", "MM_CYBERSEC_VTAB_SASS_SEC_API": "SaaS Security API", "MM_DP_VTAB_COMMON_RES": "Common Resources", "MM_DP_VTAB_POLICY": "Policy", "MM_IDENTITY_VTAB_INET_SASS": "Internet & SaaS", "MM_IDENTITY_VTAB_PRIVATE_ACCESS": "Private Access", "MM_IDENTITY_VTAB_ZIDENTITY": "ZIdentity", "MM_INFRA_TAB_COMMON_RESOURCES": "Common Resources", "MM_INFRA_TAB_CONNECTOR": "Connectors", "MM_INFRA_TAB_INET_SASS": "Internet & SaaS", "MM_INFRA_TAB_LOCATION": "Location", "MM_INFRA_TAB_LOCATIONS": "Locations", "MM_INFRA_TAB_LEGACY_LOCATIONS": "Legacy Locations", "MM_INFRA_TAB_PRIV_ACCAESS": "Private Access", "MM_INFRA_LOCATIONS_GROUPS": "Location Groups", "MM_INFRA_LOCATIONS_TEMPLATES": "Location Templates", "MM_INF_INET_SASS_TC_CAPTURE_CONTROLS": "Capture Controls", "MM_INF_INET_SASS_TC_TRAFFIC_CAPTURE_POLICY": "Traffic Capture Policy", "MM_INF_INET_SASS_TC_TRAFFIC_CAPTURE_SETTING": "Traffic Capture Setting", "MM_INF_INET_SASS_TRAFFIC_CAPTURE": "Traffic Capture", "MM_INF_INET_SASS_VTAB_FORWARDING": "Traffic Forwarding", "MM_INF_INET_SASS_VTAB_NET_POLICY": "Network Policies", "MM_LOGS_TAB_INSIGHTS": "Insights", "MM_LOGS_TAB_LOG_STREAMING": "Log Streaming", "MM_POLICY_TAB_ACCESS_CONTROL": "Access Control", "MM_POLICY_TAB_COMMON_CFG": "Common Configuration", "MM_POLICY_TAB_CYBERSEC": "Cybersecurity", "MM_POLICY_TAB_DATA_PROTECTION": "Data Protection", "MM_POLICY_TAB_DIGEXP_MON": "Digital Experience Monitoring", "MM_PRIV_ACCESS_VTAB_B2B_EXCHANGE": "B2B Exchange", "MM_PRIV_ACCESS_VTAB_BUS_CONTINUITY": "Business Continuity", "MM_PRIV_ACCESS_VTAB_CC_POLICIES": "Client Connector Policies", "MM_PRIV_ACCESS_VTAB_COMPONENT": "Component", "MM_PRIV_ACCESS_VTAB_EXTRANET": "Extranet", "MM_COMMON_RESOURCES_VTAB_GATEWAYS": "Gateways", "GATEWAYS": "Gateways", "MM_TAB": "{{tab}} icon", "MODEL": "Model", "MOS_SCORE": "MOS Score", "MOVE": "Move", "MSG_EMAIL_OTP_SENT": "Email OTP sent to {{email}}", "MSG_INVALID_OTP": "Incorrect OTP Entered", "MSLOGINSERVICES": "Microsoft Login Services", "MSN_PHOTOS": "MSN", "MSP": "Microsoft Installer Patch File (msp)", "MSTEAM": "Microsoft Teams", "MS_EXCEL": "Microsoft Excel (xls, xlsx, xlsm, xlam, xlsb, slk, xltm)", "MS_POWERPOINT": "Microsoft PowerPoint (ppt, pptx, pptm, ppsx, ppam)", "MS_PST": "Microsoft Outlook Data File (pst)", "MS_RTF": "Microsoft Rich Text Format (rtf)", "MS_WORD": "Microsoft Word (doc, docx, docm, dotx)", "MTR_SME_NOT_REACHABLE": "Cloud Path packets are not reaching the Zscaler Service Edge. Ensure the underlying network allows ICMP packets.", "MULTIFACTOR_BYPASS": "Multi-factor Bypass", "MY_ACCOUNT": "My Account", "MY_PROFILE": "My Profile", "Multi_Days": "{{amount}} Days", "Multi_Hours": "{{amount}} Hours", "MAX_LIMIT_MB": "Max limit: {{size}} MB", "NA": "N/A", "NAT_IP_ADDRESS": "NAT IP Address", "NAME": "Name", "NAME_REQUIRED_MESSAGE": "Name is required", "NAV_ITEM_ICON": "{{label}} icon", "NETWORKING": "Networking", "NETWORK_LATENCY_GEOVIEW": "Network Latency Geoview", "NEVER": "Never", "NEWS_AND_MEDIA": "News and Media", "NEW_EMAIL": "Enter New Email Address", "NEW_PASSWORD": "New Password", "NEW_PASSWORD_LABEL": "New Password", "NEW_PSEUDO_DOMAIN_NAME": "New Pseudo Domain Name", "NEXT": "Next", "NEXT_FIFTEEN_MINUTES": "Next 15 Minutes", "NEXT_ONE_HOUR": "Next 1 Hour", "NEXT_THIRTY_MINUTES": "Next 30 Minutes", "NEXT_TWENTY_FOUR_HOURS": "Next 24 Hours", "NINTY_DAYS": "90 Days", "NO": "No", "NONE": "None", "NOTIFICATIONS": "Notifications", "NOTIFICATIONS_BY_TYPE": "Notifications By Type", "NOTIFICATIONS_SENT_OVER_TIME": "Notifications Sent Over Time", "NOTIFICATION_TYPE": "Notification Type", "NOTIFICATION_TYPES": "Notification Types", "NOTIF_TYPE_DEFAULT": "<PERSON><PERSON><PERSON>", "NOTIF_TYPE_FEEDBACK": "<PERSON><PERSON><PERSON>", "NOTIF_TYPE_INFORMATION": "Information", "NOTIF_TYPE_SUBOPTIMAL_WIFI_ACCESS_POINT": "Suboptimal Wi-Fi Access Point", "NOTIF_TYPE_SUBOPTIMAL_WIFI_BAND": "Suboptimal Wifi Band", "NOTIF_TYPE_SUBOPTIMAL_WIFI_SIGNAL": "Suboptimal Wi-Fi Signal", "NOTIF_TYPE_SUBOPTIMAL_WIFI_SSID": "Suboptimal Wifi SSID", "NOTIF_TYPE_SYSTEM_HIGH_CPU": "System High CPU", "NOTIF_TYPE_SYSTEM_HIGH_IOPS": "System High IOPS", "NOTIF_TYPE_SYSTEM_HIGH_SWAP": "System High Swap", "NOTIF_TYPE_SYSTEM_LOW_MEMORY": "System Low Memory", "NOTIF_TYPE_SYSTEM_OTHER": "System Other", "NOTIF_TYPE_USER_POPUP_DISABLED": "User pop-up Disabled", "NOTIF_TYPE_USER_POPUP_ENABLED": "User popup Enabled", "NOTIF_TYPE_WIFI_OTHER": "Wifi Other", "NOT_ALLOWED_ICON": "Not allowed icon", "NOT_AVAILABLE": "Not Available", "NOT_CONFIGURED": "Not Configured", "NOT_DEPLOYED": "Not Deployed", "NOT_FOUND_RESULT": "We couldn’t find any page matching", "NOT_GOOD": "Not Good", "NOT_MALICIOUS": "Not Malicious", "FEATURE_DISABLED": "You do not have access to this feature", "NOT_SUBSCRIBED": "This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.", "NO_ACTIVATION": "There are no activations in the queue", "NO_ADMIN": "No other admins are making changes", "NO_DATA_AVAILABLE": "No Data Available", "NO_DATA_FOUND": "No Data found", "NO_FILE_CHOSEN": "No File Chosen", "NO_ITEMS_FOUND": "No items found", "NO_MATCHING_ITEMS_FOUND": "No Matching items found", "NO_PENDING": "No pending changes to activate", "NO_RECORD_EXISTS": "No record Exists", "NO_RESULT": "No Results Found", "NO_SANDBOX_DETONATION_REQUIRED": "No Sandbox Detonation Required", "NUMBER_OF_TRANSACTIONS": "Number of Transactions", "OAP_ICON": "{{text}} icon", "OB_ACCOUNT_LOGIN_ID": "Login ID", "OB_ACCOUNT_ORGANIZATION": "Organization ID", "OB_CTP_BOTNET_SUBTITLE": "Botnets are systems in which attackers have secretly installed their software. This software is designed to communicate periodically with a command and control center, and a primary application instructs the infected computers to send spam, phishing email, or perform other malicious tasks.", "OB_CTP_BOTNET_TITLE": "Botnet Protection", "OB_CTP_HEADER": "Cyber Threat Protection", "OB_CTP_ISOLATION_HEADER": "Isolation", "OB_CTP_ISOLATION_SUBHEADER": "Isolating malicious files, applications, or network segments enables organizations to create barriers that prevent threats from spreading.", "OB_CTP_MALICIOUS_SUBTITLE": "The Zscaler service blocks access to websites that attempt to download dangerous content to your browser, vulnerable ActiveX controls, and exploited web browsers. You can also denylist specific URLs for your organization.", "OB_CTP_MALICIOUS_TITLE": "Malicious Active Content Protection", "OB_CTP_MALWARE_SUBTITLE": "The Zscaler service uses an industry-leading AV vendor for signature-based detection and protection so it can provide comprehensive web security. In addition to virus and spyware protection, the service uses malware feeds from its trusted partners, such as Microsoft and Adobe, as well as its own technologies to detect and block malware.", "OB_CTP_MALWARE_TITLE": "Malware Protection", "OB_CTP_PHISING_SUBTITLE": "Phishing sites are websites that mimic legitimate banking and financial sites (e.g., citibank.com, paypal.com, etc.). Their purpose is to fool you into thinking you can safely submit bank account, password, and other personal information that criminals can use to steal your money.", "OB_CTP_PHISING_TITLE": "Phishing & Fraud Protection", "OB_CTP_PROTECTION_SUBTITLE": "P2P file sharing refers to internet resources that allow users to easily share files with each other. The danger is that users might illegally share copyrighted or protected content. The file-sharing applications listed are some of the more common ones in use today.", "OB_CTP_PROTECTION_TITLE": "P2P File Sharing Protection", "OB_CTP_ZERODAY_SUBTITLE": "Sandbox provides an additional layer of security against zero-day threats and Advanced Persistent Threats (APTs) through Sandbox analysis, an integrated file behavioral analysis. To ensure your organization's web security, the Zscaler service runs and analyzes files in a virtual environment to detect malicious behavior.", "OB_CTP_ZERODAY_TITLE": "Zero-Day Attack Protection", "OB_DLP_BENEFIT_LABEL": "Benefits", "OB_DLP_DATA_CONTENT": "After onboarding 50 or more users, allow for 48 hours of data collection for this data to appear.", "OB_DLP_DATA_HEADER": "Data Classification Dashboard", "OB_DLP_FIRST_BENEFIT": "Use Zscaler DLP policy to monitor and prevent the leakage of sensitive data.", "OB_DLP_HEADER": "Data Protection", "OB_DLP_SECOND_BENEFIT": "Use Zscaler custom and predefined DLP engines to detect and take action on sensitive data.", "OB_DLP_THIRD_BENEFIT": "Forward information about transactions that trigger DLP policies to your third-party DLP solution.", "OB_DLP_VISIBILITY_CONTENT": "A DLP dictionary contains a set of patented algorithms that are designed to detect specific kinds of information in your users  traffic and activities. The Zscaler service provides predefined dictionaries that you can modify and, in some cases, clone. You can also create custom dictionaries for content not covered by predefined dictionaries.", "OB_DLP_VISIBILITY_HEADER": "Visibility & Classification", "OB_DRAG_INFO": "You can drag and drop the below categories from one section to another. These settings are applicable for", "OB_HU_FILE_UPLOADED": "File Uploaded Successfully!", "OB_HU_USER_ADDED": "User has been added successfully.", "OB_HU_USER_REMOVED": "User has been successfully removed from the system.", "OB_HU_USER_UPDATED": "User has been updated successfully.", "OB_IDP_DATA_AD_HEADING": "Link Your Active Directory Federation Service Identities", "OB_IDP_DATA_AZURE_HEADING": "Link Your Azure Identities", "OB_IDP_DATA_CA_HEADING": "Link Your CA Single Sign-On Identities", "OB_IDP_DATA_CENTRIFY_HEADING": "Link Your Centrify Identities", "OB_IDP_DATA_GA_HEADING": "Link Your Google Apps Identities", "OB_IDP_DATA_ME_HEADING": "Link Your Microsoft Entra Identities", "OB_IDP_DATA_OKTA_FETCH_NOTE": "Note: If you do not have the URL, go to", "OB_IDP_DATA_OKTA_HEADING": "Link Your Okta Identities", "OB_IDP_DATA_OL_HEADING": "Link Your One Login Identities", "OB_IDP_DATA_OTHERS_FETCH_HEADER": "SAML Metadata URL", "OB_IDP_DATA_OTHERS_HEADING": "Link the Identities from Your IdP", "OB_IDP_DATA_PF_HEADING": "Link Your Ping Federate Identities", "OB_IDP_DATA_PI_HEADING": "Link Your Ping Identities", "OB_IDP_DATA_PO_HEADING": "Link Your Ping One Identities", "OB_IDP_DATA_SP_HEADING": "Link Your Sail Point Identities", "OB_INSTRUCTION_TEXT": "Review the internet security settings that will be set for all your users", "OB_IP_DROPDOWN_LABEL": "More Identity Providers", "OB_IP_HEADER": "Select Identity Provider", "OB_IP_IDP_LIST_ADFC": "Active Directory Federation Service", "OB_IP_IDP_LIST_CASSO": "CA Single Sign-On", "OB_IP_IDP_LIST_CENTRIFY": "Centrify", "OB_IP_IDP_LIST_GA": "Google Apps", "OB_IP_IDP_LIST_OL": "OneLogin", "OB_IP_IDP_LIST_OTHERS": "Others", "OB_IP_IDP_LIST_PF": "PingFederate", "OB_IP_PC_MICROSOFT_LABEL": "Microsoft Entra ID", "OB_IP_PC_OKTA_LABEL": "Okta", "OB_IP_PC_PING_LABEL": "Ping One", "OB_IP_PC_SAIL_LABEL": "SailPoint", "OB_MISC_BC_HEADER": "Block Countries", "OB_MISC_BC_SUBHEADER": "Choose countries you want to block. You can block requests to any country based on ISO3166 mapping of countries to their IP address space. Websites are blocked based on the location of the web server.", "OB_MISC_HEADER": "Miscellaneous", "OB_MISC_LOGO_ALT": "Uploaded Logo", "OB_MISC_LOGO_HEADER": "Company Logo", "OB_MISC_LOGO_SUBHEADER": "Upload your company logo to personalize your end user notifications, banners, and emails.", "OB_MISC_LOGO_UPLOAD_SUCC": "Logo uploaded successfully", "OB_MISC_LOGO_VALIDATION_MSG": "Logo file is too large. Maximum allowed size is 10 KB", "OB_MISC_UPLOAD_BUTTON": "Upload File", "OB_POLICY_SSL_RWBD_NAME": "Name", "OB_POLICY_SSL_RWBD_REGION": "Region", "OB_POLICY_SSL_RWCSR_CN": "Common Name (CN)", "OB_POLICY_SSL_RWCSR_COUNTRY": "Country", "OB_POLICY_SSL_RWCSR_CSR_FN": "CSR File Name", "OB_POLICY_SSL_RWCSR_CSR_GENERATED": "CSR Generated Successfully", "OB_POLICY_SSL_RWCSR_CUSTOM_CSR": "Custom Certificate CSR", "OB_POLICY_SSL_RWCSR_DN": "Department Name", "OB_POLICY_SSL_RWCSR_GENERATE_NEW_CSR": "Generate New CSR", "OB_POLICY_SSL_RWCSR_ORG": "Organization", "OB_POLICY_SSL_RWCSR_PATH_LENGTH": "Path Length Constraints", "OB_POLICY_SSL_RWCSR_SA": "Signature Algorithm", "OB_POLICY_SSL_RWCSR_TOWN": "Town/ City", "OB_POLICY_SSL_RW_BASIC_DETAILS": "Basic Details", "OB_POLICY_SSL_RW_CERT_NO_FILE": "No file chosen. File type .pem", "OB_POLICY_SSL_RW_CERT_UPLOAD": "Upload .pem file", "OB_POLICY_SSL_RW_CERT_UPLOAD_FILE": "Upload File", "OB_POLICY_SSL_RW_CERT_UPLOAD_SUCCESS": "Your file uploaded successfully!", "OB_POLICY_SSL_RW_GENERATE_CSR": "Generate CSR", "OB_POLICY_SSL_RW_GENERATE_KEYPAIR": "Generate Key Pair", "OB_POLICY_SSL_RW_IC": "Intermediate Certificate", "OB_POLICY_SSL_RW_KP_DEFAULT": "Lorem Ipsum sur dit", "OB_POLICY_SSL_RW_KP_GENERATED": "Key Pair Generated on", "OB_POLICY_WIZARD_COMMON_EXEMPTIONS_SUBHEADER": "Configure common exemptions", "OB_POLICY_WIZARD_CYBER_PROTECTION_SUBHEADER": "Configure cyber protection", "OB_POLICY_WIZARD_DATA_PROTECTION_SUBHEADER": "Enable data protection", "OB_POLICY_WIZARD_INTERNET_DESTINATION_SUBHEADER": "Configure internet destinations", "OB_POLICY_WIZARD_MISCELLANEOUS_SETTING_SUBHEADER": "Miscellaneous settings", "OB_POLICY_WIZARD_PROTECT_USER_DATA_SUBHEADER": "Protect user data", "OB_POLICY_WIZARD_REVIEW_POLICY_SUBHEADER": "Review Policies", "OB_PP_COLLECT_DEVICE_HEADER": "Collect device owner information and traffic", "OB_PP_COLLECT_DEVICE_SUBHEADER": "When enabled, the Zscaler service collects device info and enables packet capture locally. Disable for GDPR compliance.", "OB_PP_CRASH_REPORT_HEADER": "Turn off automatic crash reporting", "OB_PP_CRASH_REPORT_SUBHEADER": "If disabled, the Zscaler service does not collect any device info, and device-level protection is limited.", "OB_PP_HEADER": "Privacy", "OB_PP_ICON": "{{header}} icon", "OB_PREVIEW_POLICY_EDIT": "Edit {{header}}", "OB_REVIEW_ACTIVATE_BTN": "Activate and Launch", "OB_REVIEW_ALLOW_CAT": "20 categories", "OB_REVIEW_BLOCK_CAT": "30 categories", "OB_REVIEW_CATEGORIES": "categories", "OB_REVIEW_CTP_BOTNET_DESC": "Botnet Protection, Phishing, Malicious Content, P2P Protection, Malware Protection, Zero-Day Attack Protection", "OB_REVIEW_DLP_PCI": "PCI Engine", "OB_REVIEW_DLP_VISIBILITY": "Visibility & Data Classification", "OB_REVIEW_INSTRUCTION_TEXT": "Review and Activate Policies", "OB_REVIEW_ISOLATE_CAT": "10 categories", "OB_REVIEW_MISC_COUNTRIES_LOGO": "Blocked Countries and Company Logo", "OB_REVIEW_MISC_COUNTRIES": "Blocked Countries", "OB_REVIEW_MISC_COMP_LOGO": "Company Logo", "OB_REVIEW_MISC_NOT_SET": "Blocked Countries and Company Logo not set", "OB_REVIEW_MISC_TITLE": "Miscellaneous", "OB_REVIEW_PP_PII": "PII and Device Data Collection", "OB_REVIEW_SSL_EXEMPT": "Exempt", "OB_REVIEW_Z_CERT": "Zscaler Certificate", "OB_REVIW_EXEMPT_CAT": "2 categories", "OB_SECTION_ALLOW_HEADER": "Allow", "OB_SECTION_ALLOW_SUB_HEADER": "These super categories are allowed for all users to visit with minimal risk.", "OB_SECTION_BLOCK_HEADER": "Block", "OB_SECTION_BLOCK_SUB_HEADER": "These super categories are blocked for all users in your organization.", "OB_SECTION_ISOLATE_HEADER": "Isolate", "OB_SECTION_ISOLATE_SUB_HEADER": "These super categories enable browser isolation to protect all users from potentially malicious destinations.", "OB_SKIP_CONFIRMATION_TEXT": "Skipping will take you directly to the Experience Center, but you'll need to configure everything manually instead of using the guided setup.", "OB_SSL_DRAWER_HEADER": "Microsoft 365 Exemptions", "OB_SSL_DRAWER_HEADING": "Advanced SSL Settings", "OB_SSL_DRAWER_RADIOITEMS_FIRST_SUB_TITLE": "Zscaler's recommended settings enable inspection of web-based Microsoft applications such as OneDrive, SharePoint Online, and others. You can further customize these settings later under the SSL policy configuration.", "OB_SSL_DRAWER_RADIOITEMS_FIRST_TITLE": "Z<PERSON>ler Recommended Settings", "OB_SSL_DRAWER_RADIOITEMS_SECOND_SUB_TITLE": "Microsoft’s recommended settings exempt all Microsoft traffic from SSL/TLS Inspection. ", "OB_SSL_DRAWER_RADIOITEMS_SECOND_TITLE": "M365 Exempted", "OB_SSL_DRAWER_SUB_HEADER": "Choose your default settings for Microsoft 365 applications.", "OB_SSL_INFO_PILL_HEADER": "Exempted Categories", "OB_SSL_INFO_PILL_SUB_HEADER": "To avoid privacy concerns, exemption from SSL/TLS Inspection is recommended for the URL categories below.", "OB_SSL_INSPECTION_HEADER": "SSL/TLS Inspection", "OB_SSL_INSPECTION_SUB_HEADER": "As more websites use HTTPS, including social media such as Facebook and Twitter, the ability to control and inspect traffic to and from these sites becomes an important piece of an organization’s security posture. Major security threats are usually encrypted, so a strong SSL/TLS Inspection policy is highly recommended. This default policy significantly reduces the risk of a cyber attack.", "OB_SSL_ROOTSSL_CERT_DESCRIPTION": "Description", "OB_SSL_ROOTSSL_CERT_GLOBAL": "Global", "OB_SSL_ROOTSSL_CERT_HEADING": "Intermediate CA Certificate Details", "OB_SSL_ROOTSSL_CERT_NAME": "Name", "OB_SSL_ROOTSSL_CERT_PLACEHOLDER": "e.g. Microsoft", "OB_SSL_ROOTSSL_CERT_PROTECTION_TYPE": "Protection Type", "OB_SSL_ROOTSSL_CERT_REGION": "Region", "OB_SSL_ROOTSSL_CERT_SP": "Software Protection", "OB_SSL_ROOTSSL_CERT_STATUS": "Status", "OB_SSL_ROOT_HEADER": "SSL Root Certificate", "OB_SSL_ROOT_SUB_HEADER": "Zscaler provides an intermediate root certificate bundled with the Zscaler Client Connector. You can install your own certificate later from the Admin Portal.", "OB_SSL_SETTING": "Advanced SSL Settings", "OB_SSL_USER_PROPS_HEADER": "All Users, All Other Destinations", "OB_SSL_USER_PROPS_SUB_HEADER": "When you enable SSL/TLS Inspection, the Zscaler service establishes a separate SSL tunnel with the user’s browser and the destination server.", "OB_ST_BTN_ADD": "Add", "OB_ST_EMAIL_DESKTOP": "Desktop", "OB_ST_EMAIL_LOGO_ALT": "logo", "OB_ST_EMAIL_MAC": "macOS", "OB_ST_EMAIL_MSG": "<PERSON> from Acme, Inc. is inviting you to download Zscaler Client Connector and secure your internet traffic. Select your operating system below to begin the download.", "OB_ST_EMAIL_SALUTATION": "Hello,", "OB_ST_EMAIL_WIN": "Windows", "OB_ST_LF_REMOVE_ALL": "Remove All", "OB_ST_STEP_FOUR_HEADER": "Distribute Zscaler Client Connector Application", "OB_ST_STEP_FOUR_SECTION_HEADER": "Your users receive the following email with instructions to download and install Zscaler Client Connector:", "OB_ST_STEP_FOUR_SUBHEADER": "As a final step, start flowing traffic through the Zscaler Client Connector application to your users.", "OB_ST_STEP_FOUR_UPDATE_TOGGLE": "Enable automatic client updates (recommended)", "OB_ST_STEP_ONE_CONFIGURATION_TEXT": "Help us secure your organization by answering a few questions so we can optimize your initial security configuration. Don’t worry, you can refine these initial settings after you are up and running with <PERSON><PERSON><PERSON>.", "OB_ST_STEP_ONE_HEADLINE": "Clients", "OB_ST_STEP_ONE_PLATFORM": "Which of these platforms do you use?", "OB_ST_STEP_THREE_HEADLINE": "Select Users for Zscaler Client Connector Distribution", "OB_ST_STEP_THREE_TEXT": "Select users to receive an email to download Zscaler Client Connector. Installing Zscaler Client Connector allows them to connect to the Zero Trust Exchange and be protected through Zscaler. Users must have admin rights on the endpoint to install Zscaler Client Connector.", "OB_ST_STEP_TWO_HEADLINE": "Traffic Forwarding", "OB_ST_STEP_TWO_INPUT_NOTE": "Note: This traffic will bypass Zero Trust security.", "OB_ST_STEP_TWO_INPUT_PROMPT": "What are the IP addresses or hostnames of your VPN?", "OB_ST_STEP_TWO_RADIO_PROMPT": "Do you currently have a VPN configured?", "OB_ST_TEXTAREA_PLACEHOLDER": "e.g., ********, vpn.acme.com\n(separate each entry with a comma)", "OB_ST_URL_INFO": "Enter a valid URL address, without an http:// or https:// prefix, or a valid IP address. A URL must have a host.domain pattern to qualify", "OB_ST_VALID_IP": "This must be a valid IP address.", "OB_ST_VALID_IP_RANGE": "This must be a valid IP address range. The start address should come before the end address.", "OB_TS_ALMOST_THERE": "Almost there!", "OB_TS_COMPLETED_STEPS": "Completed Steps", "OB_TS_CONFIGURED_USERS": "Configured Users", "OB_TS_CONNECTED_USERS": "Connected Users", "OB_TS_DEFAULT_CC_HEADING": "...and you'll be all set!", "OB_TS_DEFAULT_CC_SUBHEADING": "Complete these steps to secure internet access", "OB_TS_DEFAULT_HEADER_HEADING": "Welcome! Secure Internet Access in minutes", "OB_TS_DEFAULT_HEADER_SUBHEADING": "Complete the 3 steps below to secure your internet traffic.", "OB_TS_STEPS_SP_HEADING": "Set Up Policies", "OB_TS_STEPS_SP_META": "Enable critical security controls and policies to get started with securing internet access in your organization.", "OB_TS_STEPS_ST_HEADING": "Secure Traffic", "OB_TS_STEPS_ST_META": "Set up Traffic Forwarding to allow Zscaler policies and security controls to take effect.", "OB_TS_STEPS_SU_BTN": "Initiate Setup", "OB_TS_STEPS_SU_HEADING": "Set Up Users", "OB_TS_STEPS_SU_META": "Onboard your users to secure them quickly and efficiently", "OB_TS_TEST_SECURITY": "Test Security", "OB_TS_UP_STEP_META": "Identity setup assigned to", "OB_URL_ALL": "All Users.", "OB_URL_CB_ISOLATION_MISSING_DESC": "CBI (cloud browser isolation) is not enabled for Internet Access subscription. Please contact Zscaler Support.", "OB_URL_HEADER": "URL Filtering", "OB_URL_NO_DATA": "No Data", "OB_URL_SUB_HEADER": "Zscaler organizes URLs into a hierarchy of categories for granular filtering and policy creation. There are 6 predefined classes, which are then each divided into predefined super categories, and then further divided into predefined categories.", "OB_USERS_ADD_HEADER": "Add more users", "OB_USERS_ADD_SUB_HEADER": "You can add more users by one of the following methods", "OB_USERS_ALERT_MSG": "User has been added successfully.", "OB_USERS_BLOCKQUOTE_TEXT": "After you complete onboarding, you can assign more granular administrators like Cybersecurity, Data Protection, Networking and others.", "OB_USERS_CONFIRMATION": "Are you sure you want to disconnect", "OB_USERS_CONNECT_IDP": "Connect IdP", "OB_USERS_DELETE_INFO": "You cannot undo this action or restore the account later", "OB_USERS_DISCONNECT_IDP": "Disconnect", "OB_USERS_DOWNLOAD_CSV": "Download CSV Template", "OB_USERS_EMAIL_LABEL": "Email", "OB_USERS_EU_HEADER": "End User", "OB_USERS_EU_SUB_HEADER": "Employee of your organization who does not have administrative privileges", "OB_USERS_FA_HEADER": "Full Admin", "OB_USERS_FA_SUB_HEADER": "Has full access to all Zscaler features inside your organization", "OB_USERS_HELP_INFO": "Are you sure you want to delete this user?", "OB_USERS_LOGIN_LABEL": "LoginID", "OB_USERS_MODAL_TEXT": "Users will no longer be able to connect to Zscaler services.", "OB_USERS_NAME_LABEL": "Name", "OB_USERS_SELECT_FILE": "No file chosen. Max limit: 8 MB", "OB_USERS_SELECT_ROLE": "Choose <PERSON>", "OB_USERS_TOOLTIP_MSG": "IdP configured users cannot be edited or deleted", "OB_USERS_UPLOAD_FILE": "Upload File", "OB_USERS_WARNING": "You cannot undo this action!", "OB_USER_ADD_LABEL": "Add User", "OB_USER_CONNECT_IDP": "Connect Identity Provider", "OB_USER_EDIT_LABEL": "Edit User", "OB_USER_UPLOAD_CSV": "Upload CSV", "OFF": "Off", "OFW": "Firewall", "OFWBYPASS": "OFW Bypass", "OFWBYPASS_OTHERS": "ofwbypass others", "OFWDNAT": "OFW dnat policies", "OFWDNS": "OFW dns policies", "OFWDNS_RESPONSE": "OFW dns policies - Response (Pseudo type. Value not used in DB, only for SMCA-SME company config)", "OFWIPS": "Intrusion Prevention Systems", "OFWRDR": "Forwarding Control", "OFWSNAT": "OFW snat policies", "OFWTKG": "OFW tracking policies", "OFW_AUTO_REDIR_ENABLED": "SME proxy redir feature; off by default until featurebit is turned ON", "OFW_DISABLE": "Disable Outbound Firewall", "OFW_DNAT_FAILED": "DNAT with redirect to FQDN failed", "OFW_DNSTCP": "The port contains dns traffic over tcp", "OFW_DNSUDP": "The port contains dns traffic over udp", "OFW_DNS_DISABLE": "Disable OFW DNS policy enforcement", "OFW_FTP_DENIED": "FTP access is blocked by a firewall policy", "OFW_HTTP": "The port contains http traffic", "OFW_HTTPS_BYPASS": "Internet Bound HTTPS Bypass", "OFW_HTTPS_BYPASS_DESC": "This identifies HTTPS proxy/firewall traffic for which ZS firewall policies including DPI is skipped", "OFW_HTTP_BYPASS": "Internet Bound HTTP Bypass", "OFW_HTTP_BYPASS_DESC": "This identifies HTTP proxy/firewall traffic for which ZS firewall policies including DPI is skipped", "OFW_ICMP_BYPASS": "Internet Bound ICMP Bypass", "OFW_ICMP_BYPASS_DESC": "This identifies UDP proxy/firewall traffic for which ZS firewall policies including DPI is skipped", "OFW_INSIGHTS": "Firewall Insights", "OFW_IPS_DENIED": "IPS policy blocked access", "OFW_LOGGING_DISABLE": "Disable firewall logging", "OFW_PDNS_REDIR_GW": "ZIA pdns gw", "OFW_RTREE_ENABLE": "Enable radix tree for fw policies. Off by default. And if it is off mtree is used", "OFW_SSL": "The port contains https traffic", "OFW_TCP_BYPASS": "Internet Bound TCP Bypass", "OFW_TCP_BYPASS_DESC": "This identifies TCP proxy/firewall traffic for which ZS firewall policies including DPI is skipped", "OFW_UDP_BYPASS": "Internet Bound UDP Bypass", "OFW_UDP_BYPASS_DESC": "This identifies UDP proxy/firewall traffic for which ZS firewall policies including DPI is skipped", "OFW_WEBAPP_DENIED": "Web application is blocked by Firewall rule", "OIDC": "OIDC", "OIDC_CONFIGURATION": "OIDC CONFIGURATION", "OIDC_LOGIN": "OIDC", "OK": "Ok", "OKAY": "Okay (34-65)", "OKAY_EXPERIENCE": "Okay Experience", "OKAY_LABEL": "Okay", "OKAY_MAP": "Okay", "OKTA_TENANT": "Okta tenant B (SAML)", "OLD_PASSWORD_LABEL": "Old Password", "ON": "On", "ONBOARD_SAAS_APPLICATION": "Onboard SaaS Application", "ONE_APPLICATION": "1 Application", "ONE_DAY": "1 Day", "ON_USER_FILE_UPLOAD_SUCCESS": "File Uploaded Successfully!", "OPEN_HELP_BROWSER": "Open Help Browser", "OPERATING_SYSTEMS": "Operating System", "OPERATIONS": "Operations", "OPERATIONAL": "Operational", "OPERATIONAL_DETAILS": "Operational Details", "OPERATIONAL_STATUS": "Operational Status", "OPERATION_ICON": "{{label}} icon", "OR": "OR", "ORGANIZATION_ID": "Org ID", "ORGANIZATION_NAME": "Org Name", "ORG_NAME": "Organization Name", "ORIGIN": "Origin", "OS": "OS", "OSS_UPDATES": "Operating System and Software Updates", "OTHER": "Other", "OTHERS": "Others", "OTHER_BUSINESS_AND_ECONOMY": "Other Business and Economy", "OTHER_OFFICE365": "Common Office 365 Applications", "OUTGOING_GATEWAY_IP_ADDRESS": "Outgoing Gateway IP Address", "OVERLAY_HEADER_TEXT": "ABRisk.HLHR-41", "OVERRIDE": "Override", "OVERRIDES": "Overrides", "OVERRIDE_COUNT": "Override Count", "OVERRIDE_EXISTING_ENTRIES": "Override Existing Entries", "OVERRIDE_EXPIRY": "Override Expiry", "OVERRIDE_VALUE": "Override value", "PACKET_COUNT": "Packet Count", "PACKET_LOSS": "Packet Loss", "PACKET_LOSS_HOP1": "Packet Loss at Wi-Fi Access Point (Hop 1)", "PACKET_LOSS_HOP2": "Packet Loss after Wi-Fi Access Point (Hop 2)", "PAGE_FETCH_TIME": "Page Fetch Time", "PAGE_TITLE_CYBERSECURITY": "Cybersecurity", "PAGE_TITLE_DIGITAL_EXPERIENCE": "Digital Experience", "PAGE_TITLE_NETWORKING": "Networking", "PAGE_TITLE_DATA_PROTECTION": "Data Protection", "PARENT": "Parent", "PARTIALLY_FAILED": "Partially Failed", "PARTIAL_TENANT_MSG": "Your Zscaler tenant setup is partially complete.", "PARTICIPANTS": "Participants", "PASSWORD_CHANGE": "Password Change", "PASSWORD_COMPLEXITY": "Password Complexity", "PASSWORD_CRITERIA": "Password Criteria", "PASSWORD_DOES_NOT_INCLUDE": "Password must not include company name, username, first name, or last name", "PASSWORD_EXPIRY": "Password expiration period (in days)", "PASSWORD_LABEL": "Password", "PASSWORD_LENGTH": "Password Length", "PASSWORD_OPTION": "Password Option", "PASSWORD_PLACEHOLDER": "Enter Password", "PASSWORD_POLICY": "Password Policy", "PASSWORD_POLICY_CHANGE": "Password Policy Change", "PDF_DOCUMENT": "Portable Document Format (pdf)", "PEER": "<PERSON><PERSON>", "PEER_ISP": "Intermediate ISP", "PEER_TO_PEER": "Peer to Peer", "PENDING_ACTIVATION": "Pending Activation", "PHISHING": "<PERSON><PERSON>", "PHONE_NUMBER": "Phone number", "PHYSICAL": "Physical", "PING_IDENTITY": "Ping Identity tenant C (SAML)", "PLATFORM": "Platform", "PLUS_ICON": "Plus icon", "POLICIES": "Policies", "POLICIES_SAAS_INSTANCE_NAME": "Instance Name", "POLICIES_SAAS_SETUP_API_INTEGRATION_HEADER": "Set Up API Integration", "POLICIES_SAAS_FEATURE_SELECTION_LABEL": "Onboard SaaS Application for", "POLICIES_SAAS_AUTHORIZE_HEADER": "Authorize the SaaS Application", "POLICIES_SAAS_ONEDRIVE_AUTHORIZE_DESCRIPTION": "To configure Data Loss Protection and Malware Detection policies for SaaS Security API, you must give Zscaler access to OneDrive.", "POLICIES_SAAS_ZSCALER_APP_ID": "Zscaler SaaS Connector", "POLICIES_SAAS_ZSCALER_MS_TENANT_ID": "Tenant ID", "POLICIES_SAAS_CONNECTOR_TYPE_HEADER": "SaaS Connector", "POLICIES_SAAS_CONNECTOR_ZSCALER_DEFINED": "Zscaler Defined", "POLICIES_SAAS_CONNECTOR_CUSTOM": "Custom", "POLICY": "Policy", "POLICY_ACTION": "Policy Action", "POLICY_ACTIONS_VERDICTS_KNOWN_FILES": "Policy Actions & Verdicts for Files Known by Cloud Effect", "POLICY_ACTIONS_VERDICTS_UNKNOWN_FILES": "Policy Actions & Verdicts for Unknown Files", "POLICY_BLOCKS": "Policy Blocks", "POOR": "Poor (0-33)", "POOR_EXPERIENCE": "Poor Experience", "POOR_LABEL": "Poor", "POST_CHANGES_CONFIGURATION": "Post-Changes Configuration", "POWERED_BY": "Powered By", "POWERSHELL": "Windows PowerShell Script (ps1)", "PREVIOUS_DAY": "Previous Day", "PREVIOUS_MONTH": "Previous Month", "PREVIOUS_REQUEST_ERROR": "No previous request to retry", "PREVIOUS_WEEK": "Previous Week", "PRE_CHANGES_CONFIGURATION": "Pre-Changes Configuration", "PRIMARY_EMAIL": "Primary Email", "PRIMARY_IDENTITY_PROVIDER": "Primary Identity Provider", "PRIMARY_IDP_NOT_AVAILABLE": "Primary Identity Provider is not added.", "PRIMARY_TRAFFIC_SOURCES": "Top Locations sending Internet Traffic to Zscaler", "PRIVATE": "Private", "PRIVATE_KEY_JWT": "Private Key JWT", "PRIV_ACCESS_MICROTENANT": "Private Access Microtenant", "PROBES_ERROR_HEADING": " Probes with “No Internet Access” Error", "PROBES_INTERNET_ERROR": "Probes Internet Error", "PROBE_STATUSES": "Probe Statuses", "PROBE_WEB_HTTP_RESPONSE_CODE_MISMATCH": "HTTP response code mismatch", "PROCEED": "Proceed", "PROCESSED_RECORD": "Processed Records", "PRODUCT": "Product", "PROFESSIONAL_SERVICES": "Professional Services", "PROFILE_NAME": "Profile Name", "PROMPT_PASSWORD_FIRST_LOGIN": "Prompt for Password Change After the Initial Log In", "PROTECTED_ICON": "Protected icon", "PROTOCOL": "Protocol", "PROVISIONING": "Provisioning", "PROVISIONING_SETTINGS": "Provisioning Settings", "PUBLIC_KEYS_URL": "Public Keys URL", "PWD_LOGIN": "Password", "Policies": "Policies", "QUALIFIED": "Qualified", "QUARANTINE": "Quarantine", "QUARANTINED": "Quarantined", "QUESTION_ICON": "Question icon", "QUEUED_ACTIVATION": "Queued Activation", "QUOTE_ICON": "{{text}} icon", "RBA_LIMITED": "Access Restricted", "RBA_LIMITED_LEARN_MORE": "This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.", "READY_TO_DEPLOY": "Ready to Deploy", "RECENT_SEARCH_RESULT": "Recent Searches", "RECOMMENDED": "Recommended", "RECOMMENDED_APPLICATIONS": "Recommended Apps", "REDIRECT_COUNT_DISTRIBUTION": "Number of Redirects", "REDIRECT_URI": "Redirect URI", "REFINE_FILTER": "Try adjusting your search or filter to find what you’re looking for.", "REGIONS": "Regions", "REGIONS_BY_AVERAGE_APPLICATION_EXPERIENCE_SCORE": "Regions by Average Application Experience Score", "REGIONS_BY_ZDX_SCORE": "Regions by ZDX Score", "REGISTERED": "Registered", "REJECT_REUSE": "Reject reuse of last 5 passwords", "REMEMBER_ME": "Remember me", "REMOTE_ASSISTANCE": "Remote Assistance", "REMOVE": "Remove", "REMOVED": "Removed", "REMOVE_ALL": "Remove All", "REMOVE_ASSIGNMENTS": "Remove Assignments", "REMOVE_PAGE": "Remo<PERSON>", "REMOVE_PASSWORD": "Remove Password", "REMOVAL_PENDING": "Removal <PERSON>", "REPORTS_CC_DASHBOARD": "Platform Details", "REPORTS_CC_DEVICE_EVENTS": "Device Events", "REPORTS_DEM_APPLICATIONS": "Applications", "REPORTS_DEM_DEVICE_INV": "<PERSON><PERSON> Inventory", "REPORTS_DEM_DEVICE_OVERVIEW": "Device Overview", "REPORTS_DEM_DIAG": "Diagnostics", "REPORTS_DEM_INV_PROCESSING_OVERVIEW": "Processing Overview", "REPORTS_DEM_INV_PROCESS_INV": "Process Inventory", "REPORTS_DEM_INV_PROCESS_OVERVIEW": "Process Overview", "REPORTS_DEM_INV_SOFTWARE_INVENTORY": "Software Inventory", "REPORTS_DEM_INV_SOFTWARE_OVERVIEW": "Software Overview", "REPORTS_DEM_INV_SOFTWARE_PATCH_OVERVIEW": "Software Patch Overview", "REPORTS_DEM_PERF_DASHBOARD": "Performance Dashboard", "REPORTS_DEM_DH_DASH": "Devices Dashboard", "REPORTS_DEM_PERF_INCIDENTS_DASH": "Incidents Dashboard", "REPORTS_DEM_PERF_SELFSERV_DASH": "Self Service Dashboard", "REPORTS_DEM_PERF_WIFI_DASH": "Wi-Fi Dashboard", "REPORTS_DEM_NI_DASH": "Network Intelligence", "REPORTS_DEM_RPT_DATA_EXP": "Data Explorer", "REPORTS_DEM_RPT_QBR": "Quarterly Business Reviews", "REPORTS_DEM_RPT_SYSTEM_GENERATED": "System Generated Reports", "REPORTS_DEM_RPT_ZDX_SNAPSHOTS": "ZDX Snapshots", "REPORTS_DEM_RPT_ZS_HOSTED": "Zscaler Hosted Monitoring", "REPORTS_DEM_USERS": "Users", "REPORTS_DEM_USR_DEV_SEARCH": "User/Device Search", "REPORTS_PA_APPS": "Applications", "REPORTS_PA_APP_CNCTR": "App Connectors", "REPORTS_PA_EXTRANET": "Extranet", "REPORTS_PA_HEALTH": "Health", "REPORTS_PA_PCC": "Private Cloud Controllers", "REPORTS_PA_PSE": "Private Service Edges", "REPORTS_PA_SECURITY": "AppProtection", "REPORTS_PA_SECURITY_AD_PROTECTION": "Active Directory Protection", "REPORTS_PA_SECURITY_API_PROTECTION": "API Protection", "REPORTS_PA_SECURITY_BROWSER_SESSION_PROTECTION": "Browser Protection", "REPORTS_PA_SECURITY_PROTOCOL_DISCOVERY": "Protocol Discovery", "REPORTS_PA_SRCIP_ANCHORING": "Source IP Anchoring", "REPORTS_PA_USERS": "Users", "REQUESTED_SCOPES": "Requested <PERSON><PERSON><PERSON>", "REQUEST_SIGNING_CERTIFICATE": "Request Signing Certificate", "REQUIRED_VALIDATION_MESSAGE": "Required", "RESEND": "Resend", "RESEND_CODE": "Resend Code", "RESET": "Reset", "RESET_PASSWORD": "Reset Password", "RESOURCE": "Resource", "RESOURCES": "Resources", "RESOURCE_NAME": "Resource Name", "RESOURCE_NOT_FOUND": "No logo is associated with this organization.", "RESPONSE_ERROR": "Unable to find the request.", "RESTART_TENANT_CREATION": "Restart Tenant Creation", "RESULT": "Result", "RETRY": "Retry", "REVERT": "<PERSON><PERSON>", "REVERT_TO_PROVISIONED": "Revert to Provisioned", "REVIEW": "Review", "REVOKE": "Revoke", "REVOKED": "Revoked", "REVOKE_TOKEN": "Revoke Token", "REVOKE_TOKEN_MESSAGE": "Are you sure you want to revoke this Token? The changes cannot be undone.", "RE_TRY_AGAIN": "Try Again", "RIGHT_ICON": "Right icon", "ROAD_WARRIOR": "Road Warrior", "ROLE": "Role", "ROLES": "Roles", "ROLE_ASSIGNMENT": "Role Assignment", "RULE_ACTION": "Rule Action", "RULE_NAME": "Rule Name", "RULE_ORDER": "Rule Order", "RULE_STATUS": "Rule Status", "RUNTIME_ASSIGNMENT": "Runtime Assignment", "SAAS_APPLICATION": "SaaS Application", "SAAS_APPLICATIONS_HEADER": "Applications", "SAME_AS_LOGIN_ID": "Same As Login ID", "SAME_ROLE_FOR_SELECTED_GROUPS": "Set same role for all selected groups", "SAME_ROLE_FOR_SELECTED_USERS": "Set same role for all selected users", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_GROUPS": "Set same role and scope for all selected groups", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_USERS": "Set same role and scope for all selected users", "SAML": "SAML", "SAML_ATTRIBUTE": "SAML Attribute", "SAML_ATTRIBUTE_MAPPING": "SAML Attribute Mapping", "SAML_CONFIGURATION": "SAML CONFIGURATION", "SAML_ENCRYPTION_CERTIFICATE": "SAML Encryption Certificate", "SAML_LOGIN": "SAML", "SAML_REQUEST_SIGNING": "SAML Request Signing", "SAMPLE_CSV_DOWNLOAD": "Download Sample", "SANDBOX_CATEGORY": "Sandbox Category", "SANDBOX_INCIDENTS": "Sandbox Incidents", "SANDBOX_MALWARE": "Sandbox Malware", "SANDBOX_REQUIRED": "Sandbox Required", "SANDBOX_THREATS": "Sandbox Threats", "SANDBOX_THREATS_CARD_DESC": "Files blocked against zero day threats with Advanced Sandbox", "SANDBOX_THREATS_FOOTER_TITLE": "View All Sandbox Threats", "SAVE": "Save", "SAVE_AND_NEXT": "Save and Next", "SCIM": "scim", "SCIM_API": "SCIM API", "SCIM_ATTRIBUTE": "SCIM Attribute", "SCIM_ATTRIBUTE_MAPPING": "SCIM Attribute Mapping", "SCIM_ENDPOINT_URL": "SCIM Endpoint URL", "SCIM_PROVISIONING": "SCIM Provisioning", "SCIM_PROVISIONING_STATUS": "SCIM Provisioning Status", "SCOPE": "<PERSON><PERSON>", "SCOPES_N_ROLES": "Scopes and Roles", "SCORE": "Score", "SCORE_DISTRIBUTION": "ZDX Score", "SCORE_DROP": "ZDX Score Drop", "SCZIP": "ZIP w/Suspicious Script File (js, vbs, svg, ps1, hta, cmd, lnk)", "SEARCH": "Search", "SEARCH_ICON": "Search icon", "SEARCH_INCIDENTS": "Search Incidents", "ENDPOINTS": "Endpoints", "SEARCH_ENDPOINTS": "Search Top Endpoints Sensitive Data Being Exfiltrate Table", "DATA_CHANNELS": "Data Channels", "TOTAL_SENSITIVE_FILES": "Total Sensitive Files", "SOURCE_CODE": "Source Code", "SEARCH_MALWARE": "Search Incidents", "SEARCH_MEETINGS": "Search Meetings", "SEARCH_MENU": "Search menu", "SEARCH_NOTIFICATIONS": "Search Notifications", "SEARCH_PARTICIPANTS": "Search Participants", "SEARCH_PLACEHOLDER": "Search...", "SECONDARY_EMAIL": "Secondary Email", "SECONDARY_IDENTITY_PROVIDER": "Secondary Identity Providers", "SECONDARY_IDP_NOT_AVAILABLE": "Secondary Identity Provider Not Available", "SECRET": "Secret", "SECURITY": "Security", "SECURITY_SETTINGS": "Security Settings", "SEGMENTATION": "Zero Trust Branch", "SEGMENTATION_ADMINISTRATORS": "Administrators", "SEGMENTATION_ALERTS": "Alarms", "SEGMENTATION_API_DOCS": "Zero Trust Branch API Docs", "SEGMENTATION_API_KEYS": "Zero Trust Branch API Keys", "SEGMENTATION_ASSETS": "Assets", "SEGMENTATION_ASSET_AI": "Asset Intelligence", "SEGMENTATION_CHARTS": "Charts", "SEGMENTATION_DASHBOARDS": "Dashboards", "SEGMENTATION_DEPLOYMENTS": "Deployments", "SEGMENTATION_FIREWALL": "Segmentation", "SEGMENTATION_FLOW_LOGS": "Flow Logs", "SEGMENTATION_GLOBAL": "Settings", "SEGMENTATION_HUBS": "<PERSON><PERSON>", "SEGMENTATION_INCIDENT_RESPONSE": "Incident Response", "SEGMENTATION_INSIGHTS": "Insights", "SEGMENTATION_INTEGRATIONS": "Integrations", "SEGMENTATION_LOGS": "Logs", "SEGMENTATION_MONITORING": "Monitoring & Settings", "SEGMENTATION_NAC_LITE": "NAC-MAC Authentication", "SEGMENTATION_OBJECTS": "Objects & Groups", "SEGMENTATION_PACKET_LOGS": "Packet Logs", "SEGMENTATION_POLICIES": "Policies", "SEGMENTATION_RANSOMWARE_KILL_SWITCH": "Kill Switch", "SEGMENTATION_RESOURCES": "Resources", "SEGMENTATION_SETTINGS": "Settings", "SEGMENTATION_SITES": "Sites", "SEGMENTATION_TEMPLATES": "Site Templates", "SEGMENTATION_VLANS": "VLANs", "SEGMENT_ICON": "{{label}} icon", "SELECT": "Select", "SELECTED": "Selected", "SELECTED_CRITERIA_INVALID": "Selected criteria is invalid", "SELECTED_LOCATIONS": "Selected Locations", "SELECTED_ROLES": "Selected Roles", "SELECT_ALL": "Select All", "SELECT_DATE": "Select Date", "SELECT_GROUPS": "Select Groups", "SELECT_GROUPS_AND_ROLE": "Select Groups & Roles", "SELECT_ICON": "Selected icon", "SELECT_ROLE": "Select Role", "SELECT_SCOPE": "Select Scope", "SELECT_USERS": "Select Users", "SELECT_USERS_AND_ROLE": "Select Users & Roles", "SELF_SERVICE_PROVIDES_NOTIFICATIONS_WHEN_ISSUES_ARE_DETECTED_AND_MIGHT_NEED_ATTENTION": "Self Service provides notifications when issues are detected and might need attention.", "SENT_ICON": "<PERSON><PERSON><PERSON> sent icon", "SEPARATOR_ICON": "Seperator icon", "SERVER_LEG_LATENCY_TREND": "Last Server Leg Latency", "SERVER_NOT_RESPONDING": "Server is not responding.", "SERVER_RESPONSE_TIME": "Server Response Time", "SERVICE": "Service", "SERVICENOW": "ServiceNow", "SERVICES": "Services", "SERVICE_IP_ADDRESS": "Service IP Address", "SERVICE_ASSIGNMENT": "Service Assignment", "SERVICE_ENTITLEMENT_SESSION_TIMEOUT_DURATION_IN_MIN": "Service Entitlement Session Timeout (in Minutes)", "SERVICE_ENTITLEMENTS": "Service Entitlements", "SERVICE_ENTITLEMENTS_TEXT": "Service Entitlements", "SERVICE_NAME": "Service Name", "SERVICE_RUNTIME_ASSIGNMENT": "Service Runtime Assignment", "SESSIONS": "Sessions", "SESSION_ACROSS_SERVICE": "Session Across Service", "SESSION_ATTRIBUTE": "Session Attribute", "SESSION_ATTRIBUTE_MAPPING": "Session Attribute Mapping", "SESSION_EXPIRED": "Session Expired", "SESSION_EXPIRED_TEXT": "Your session has expired due to inactivity. You will be redirected to the Login page.", "SESSION_EXPIRING_EXTEND": "Extend Session", "SESSION_EXPIRING_LOGOUT": "Log Out Now", "SESSION_EXPIRING_PREFIX": "Your login session is expiring in", "SESSION_EXPIRING_SEC": "sec.", "SESSION_EXPIRING_SOON": "Session Expiring Soon", "SESSION_EXPIRING_SUFFIX": "To prevent automatic logout, please extend your session", "SESSION_ID": "Session ID", "SESSION_TIMEOUT": "Session Timeout", "SESSION_TIMEOUT_DURATION_IN_MIN": "Session Timeout Duration (in Minutes)", "SET_BY_ADMIN": "Set By Administrator", "SET_BY_USER": "Set By User", "SET_RISK_INDEX": "Set Risk Index", "SET_SANCTION": "Set Sanction State", "SET_UP": "Set Up", "SEVEN_DAYS": "7 Days", "SEVERITY": "Severity", "SHA2_256_BIT": "SHA-2 (256-bit)", "SHARED_SECRET": "Shared Secret", "SHOW_BADGE_ICON": "Show badge icon", "SHOW_PASSWORD": "Show password", "SIGNAL": "Signal", "SIGNAL_HISTORY": "Signal History", "SIGNATURE_VALIDATION": "Signature Validation", "SIGNING_ALGORITHM": "Signing Algorithm", "SIGN_IN": "Sign In", "SIGN_IN_LABEL": "Sign In", "SIGN_ON_POLICIES": "Admin Sign-On Policy", "SIGN_ON_POLICY": "Sign-On Policy", "SIGN_OUT": "Sign Out", "SIXTY_DAYS": "60 Days", "SKIP": "<PERSON><PERSON>", "SKIP_ONBOARDING": "Skip Onboarding", "SKIP_SECOND_FACTOR_AUTHENTICATION": "Skip Second Factor Authentication", "SKIP_UNTIL": "<PERSON><PERSON>", "SKYPE": "Skype", "SMS_LOGIN": "SMS Login", "SOCIALGEST": "SocialGest", "SOMETHING_WENT_WRONG": "Something went wrong.", "SOPHISTICATED_THREATS": "Sophisticated threats are often hidden in encrypted traffic. SSL/TLS Inspection allows for analysis on encrypted traffic in order to block these threats.", "SOURCE": "Source", "SPECIALIZED_SHOPPING": "Online Shopping", "SPLIT_ICON": "Split icon", "SPOTLIGHT_FIND_TEXT": "Can’t find what you’re looking for?", "SPOTLIGHT_VIEW_HELP-DOC": "View Help Docs", "SP_ENTITY_ID": "SP Entity ID", "SP_METADATA": "SP Metadata", "SP_SAML_CERTIFICATE": "SP SAML Certificate", "SP_URL": "SP URL", "SSL_INSPECTION_DESCRIPTION_HEADER_DESC": "Zscaler recommends enabling SSL/TLS Inspection in the below areas to block incoming threats.", "SSL_INSPECTION_DESCRIPTION_HEADER_TITLE": "What areas are you not inspecting traffic?", "STARTED_ON": "Started On", "START_TIME": "Start Time", "START_TIME_BEFORE_END_TIME": "Start Time should be before End Time", "STATE": "State", "STATUS": "Status", "STATUS_INDICATIOR_ICON": "Status Indicator Icon", "STEP_ICON": "{{heading}} icon", "STRING": "String", "SUBJECT": "Subject", "SUBJECT_CLAIM": "Subject Claim", "SUBJECT_COUNT": "Subject Count", "SUBJECT_ID": "Subject ID", "SUBJECT_IDENTIFIER": "Subject Identifier", "SUBJECT_TYPE": "Subject Type", "SUBSCRIPTION_REQUIRED": "Subscription Required", "SUBSCRIPTION_REQ_LEARN_MORE": "This feature requires a subscription that your organization does not currently have. To learn more, contact sales representative.", "SUB_CATEGORY": "Sub-Category2", "SUB_LEVEL_NAME": "Sub-Level Name", "SUCCESS": "Success", "SUCCESS_ICON": "Success icon", "SUMMARY": "Summary", "SUPPORT_ERROR_DESCRIPTION": "This tenant is not registered with Salesforce.", "SUPPORT_ERROR_TITLE": "Can't connect to Zscaler Support", "SUPPORT_LOADING": "Loading Zscaler Support", "SUPPORTED": "Supported", "UNSUPPORTED": "Unsupported", "SWITCH_REPORT": "Switch to Existing Reports", "SYNC": "Sync", "SYNC_DOMAINS": "Sync Domains", "SYSTEM": "System", "SYSTEM_AND_DEVELOPMENT": "System & Development", "SYSTEM_DEFINED": "system-defined", "SYSTEM_THEME": "System Theme", "Single_Day": "1 Day", "Single_Hour": "1 Hour", "TABLE_NUMBER": "No.", "TABLE_REQUIRED": "Required", "TENANTS": "Tenants", "TENANT_ID": "Tenant ID", "TEST_INTEGRATION": "Test Integration", "THEME": "Theme", "THESE_ARE_THE_TOP_COUNTRIES_WHERE_THREATS_ARE_ORIGINATING_FROM": "These are the top countries where threats are originating from.", "THE_AVERAGE_TIME_TAKEN_TO_REACH_ZSCALER_DATA_CENTERS_FROM_A_USERS_LOCATION": "The average time taken to reach Zscaler Data Centers from a user's location.", "THE_TOP_LOCATIONS_WHERE_WE_SEE_HIGH_NUMBER_OF_APPLICATIONS_WITH_POOR_SCORES": "The top locations where we see high number of applications with poor scores.", "THIRTY_DAYS": "30 Days", "THREAT": "Threat", "THREATS": "Threats", "THREATS_BLOCKED": "Threats Blocked", "THREATS_BLOCKED_BY_SSL_INSPECTION": "Threats Blocked by SSL/TLS Inspection", "THREATS_BLOCKED_BY_SSL_INSPECTION_DESC": "A high volume of threats were blocked from the encrypted traffic due to SSL/TLS Inspection being enabled.", "THREAT_CATEGORY": "Threat Category", "TILE_ICON": "{{headline}} icon", "TIMESTAMP": "Timestamp", "TIMEZONE": "Timezone", "TIME_AM": "AM", "TIME_OUT_ERROR": "Connection timed out", "TIME_PM": "PM", "TIME_RANGE": "Time Range", "TIME_RANGE_MESSAGE_DEFAULT": "Selected range should be within 2 hours and 48 hours", "TIME_RANGE_MESSAGE_STD_SUBSCRIPTION": "Selected range should be within 2 hours and 24 hours", "TIME_STAMP": "Time stamp", "TN_ADMINISTRATION_ADMINISTRATOR_MGMT": "Administrator Management", "TN_ADMINISTRATION_AUDIT_LOGS": "<PERSON><PERSON>", "TN_ADMINISTRATION_AUTH_SETTINGS": "Internet Authentication Settings", "TN_ADMINISTRATION_BACKUP": "Backup & Restore", "TN_ADMINISTRATION_CLIENT_CONNECTOR": "Connectors", "TN_ADMINISTRATION_COMPANY_PROFILE": "Company Profile", "TN_ADMINISTRATION_CONNECTOR_SERVICE_ENTL": "Client Connector Service Entitlements", "TN_ADMINISTRATION_DEVICE_AUTH": "<PERSON>ce Au<PERSON>cation", "TN_ADMINISTRATION_DOMAINS": "Domains", "TN_ADMINISTRATION_ENTILEMENTS": "Entitlements", "TN_ADMINISTRATION_EVENT_LOGS": "SCIM Event Logs", "TN_ADMINISTRATION_IDP_CONFIGURATION": "IDP Configuration", "TN_ADMINISTRATION_INT_SAAS": "Internet & SaaS", "TN_ADMINISTRATION_LEGACY_API": "Legacy API", "TN_ADMINISTRATION_MONITORING": "Digital Experience Monitoring", "TN_ADMINISTRATION_ONE_API": "OneAPI", "TN_ADMINISTRATION_PARTNER_LOGIN": "Partner Login", "TN_ADMINISTRATION_PRIVATE_ACCESS": "Private Access", "TN_ADMINISTRATION_PROXY": "Identity Proxy", "TN_ADMINISTRATION_PWD_AUTH": "Passwords & Authentication", "TN_ADMINISTRATION_REMOTE_ACCESS": "Privileged Remote Access", "TN_ADMINISTRATION_ROLES": "Roles", "TN_ADMINISTRATION_SCIM_CONFIGURATION": "SCIM Configuration", "TN_ADMINISTRATION_SCOPE": "Administrative Scope", "TN_ADMINISTRATION_SERVICE_ENTL": "Service Entitlements", "TN_ADMINISTRATION_SIGN_ON": "Sign-On", "TN_ADMINISTRATION_USER_MGMT": "User Management", "TN_LOG_INT_SAAS": "Internet & SaaS", "TN_LOG_INT_STREAMING": "Internet Log Streaming", "TN_LOG_PRIVATE_APP": "Private Applications", "TN_LOG_PRIVATE_STREAMING": "Private Log Streaming", "TN_LOG_TRAFFIC_CAPTURE": "Traffic Capture", "TN_NETWORKING_CC_ADV_SETTINGS": "Advanced Settings", "TN_NETWORKING_CC_CA": "Custom Applications", "TN_NETWORKING_CC_DEPLOYMENT": "Deployment", "TN_NETWORKING_CC_DEVICES": "Devices", "TN_NETWORKING_CC_ED": "Enrolled Devices", "TN_NETWORKING_CC_FP": "Forwarding Profiles", "TN_NETWORKING_CC_GLOBAL_SET": "Global Settings", "TN_NETWORKING_CC_PLATFORM_SET": "Platform Settings", "TN_NETWORKING_CC_PS": "Platform Settings", "TN_NETWORKING_CC_ST": "Supportability", "TN_NETWORKING_CR_CA": "Custom Application", "TN_NETWORKING_CR_CCD": "Client Connector Deployment", "TN_NETWORKING_CR_DEVICE": "Device Category", "TN_NETWORKING_CR_EXTRANET": "Extranet", "TN_NETWORKING_IS_BANDWIDTH_CONTROL": "Bandwidth Control", "TN_NETWORKING_IS_FC": "Forwarding Control", "TN_NETWORKING_IS_IP_CONF": "Ipv6 Configurations", "TN_NETWORKING_IS_LOCATION_MGT": "Location Management", "TN_NETWORKING_IS_PI": "Partner Integrations", "TN_NETWORKING_IS_PRIVATE_SE": "Private Service Edge", "TN_NETWORKING_IS_TRAFFIC_STEERING": "Traffic Steering", "TN_NETWORKING_PI_APP_CONNECTOR": "App Connectors", "TN_NETWORKING_PI_BUSINESS_CONT": "Business Continuity", "TN_NETWORKING_PI_CBCR": "Cloud & Branch Connector Resources", "TN_NETWORKING_PI_CCP": "Client Connector Policies", "TN_NETWORKING_PI_DISASTER_REC": "Disaster Recovery", "TN_NETWORKING_PI_IC": "Infrastructure Certificates", "TN_NETWORKING_PI_IPA": "IP Assignment", "TN_NETWORKING_PI_PRIVATE_SE": "Private Service Edges", "TN_NETWORKING_PI_VPN": "VPN (For Legacy Apps)", "TN_POLICY_ACCESS": "Access Methods", "TN_POLICY_BROWSER_ISOLATION": "Browser Isolation", "TN_POLICY_BROWSER_EXTENSION": "Browser Extension", "TN_POLICY_CRC_ADAPTIVE": "Adaptive Access", "TN_POLICY_CRC_WORKLOADS": "Workloads", "TN_POLICY_CRC_ADV_SETTINGS": "Advanced Settings", "TN_POLICY_CRC_BROWSER_ISOLATION": "Browser Isolation", "TN_POLICY_CRC_CLIENT_CONNECTOR": "Connectors", "TN_POLICY_CRC_CONTENT_SCAN": "Content Scanning", "TN_POLICY_CRC_END_USER": "End User Notifications", "TN_POLICY_CRC_SAAS": "SaaS Application Tenants", "TN_POLICY_CRC_SSLINS": "SSL/TLS Inspection", "TN_POLICY_CRC_VIEW_ALL": "View All Policies", "TN_POLICY_DEM_CONFIGURTION": "Configuration", "TN_POLICY_DEM_RESOURCES": "Resources", "TN_POLICY_DEM_SELF_SERVICE": "Self Service", "TN_POLICY_DEM_SETTINGS": "Settings", "TN_POLICY_DNS": "DNS Control", "TN_POLICY_DP_ADVANCE_SETTINGS": "Advanced Settings", "TN_POLICY_DP_DATA_CLASSIFICATION": "Data Classification", "TN_POLICY_DP_EC": "Endpoint Controls", "TN_POLICY_DP_EMAIL": "Email", "TN_POLICY_DP_IC": "Inline Controls", "TN_POLICY_DP_INCIDENT_MGT": "Incident Management", "TN_POLICY_DP_OEC": "Outbound Email Controls", "TN_POLICY_DP_PI": "Partner Integrations", "TN_POLICY_DP_SAAS": "SaaS Security", "TN_POLICY_FC": "Firewall Control", "TN_POLICY_FILETYPE": "File Type Control", "TN_POLICY_IP": "IP & FQDN Groups", "TN_POLICY_ISC_IPS": "IPS", "TN_POLICY_ISC_PAP": "Private App Protection", "TN_POLICY_ISC_PARTNER_INT": "Partner Integrations", "TN_POLICY_ISC_SAAS": "SaaS Security API", "TN_POLICY_ISC_SANDBOX": "Sandbox", "TN_POLICY_ISC_SB": "Secure Browsing", "TN_POLICY_ISC_THREAT": "Threat Prevention", "TN_POLICY_NETWORK_APP": "Network Applications", "TN_POLICY_NETWORK_SERVICES": "Network Services", "TN_POLICY_POLICIES": "Policies", "TN_POLICY_PRIVILEGED_REMOTE": "Privileged Remote Access", "TN_POLICY_RESOURCES": "Resources", "TN_POLICY_SAAS": "SaaS Application Control", "TN_POLICY_SEGMENT": "App Segments", "TN_POLICY_SERVER": "Servers", "TN_POLICY_URL": "URL Control", "TN_POLICY_USER_PORTAL": "User Portal Configuration", "TODAY": "Today", "TOKEN": "<PERSON><PERSON>", "TOKENS": "Tokens", "TOKEN_ENDPOINT": "Token Endpoint", "TOKEN_ENDPOINT_AUTHENTICATION_METHOD": "Token Endpoint Authentication Method", "TOKEN_VALIDATOR": "Token Validator", "TOKEN_VALIDATORS": "Token Validators", "TOP": "Top", "TOP_APPLICATIONS_GENERATING_THREATS": "Top Applications Generating Threats", "TOP_APPLICATION_CLASS": "Top Application Class", "TOP_BLOCKED_FILE_TYPES": "Top Blocked File Types", "TOP_CLOUD_APPS_BLOCKED": "Top Cloud Applications Blocked", "TOP_CLOUD_APPS_WITH_SECURITY_THREATS": "Top Cloud Applications with Security Threats", "TOP_COUNTRIES_WITH_THREATS": "Top Countries with Threats", "TOP_DEPARTMENTS_GENERATING_THREATS": "Top Departments Generating Threats", "TOP_HIGH_VOLUME_APPS": "Top High Volume Applications", "TOP_LOCATIONS": "Top Locations", "TOP_THREAT_LOCATIONS": "Top Threat Locations", "TOP_URL_CATEGORIES": "Top URL Categories", "TOP_URL_CATEGORIES_BLOCKED": "Top URL Categories Blocked", "TOP_URL_CATEGORIES_GENERATING_THREATS": "Top URL Categories Generating Threats", "TOP_USED_APPLICATIONS_BY_EXPERIENCE_SCORE": "Top 5 Applications with Lowest Experience Score", "TOP_USERS_GENERATING_SANDBOX_THREATS": "Top Users Generating Sandbox Threats", "TOP_USERS_GENERATING_THREATS": "Top Users Generating Threats", "TOTAL": "TOTAL", "TOTAL_DEVICES": "Total Devices", "TOTAL_APPLICATIONS": "Total Applications", "TOTAL_BYTES": "Total Bytes", "TOTAL_CONNECTORS": "Total Connectors", "TOTAL_INCIDENTS": "Total Incidents", "TOTAL_FILES": "Total Files", "TOTAL_NOTIFICATIONS_SENT": "Total Notifications Sent", "TOTAL_NUMBER_OF_HOPS": "Total Number of Hops", "TOTAL_POLICIES_BLOCK": "Total Policy Blocks", "TOTAL_RECORDS_ADDED": "Total Records Added", "TOTAL_RECORDS_DELETED": "Total Records Deleted", "TOTAL_RECORDS_IN_IMPORT": "Total Records In Import", "TOTAL_RECORDS_UPDATED": "Total Records Updated", "TOTAL_THREATS": "Total Threats", "TOTAL_THREATS_BLOCKED": "Total Threats Blocked", "TOTAL_THREATS_TREND": "Total Threats Trend", "TOTAL_THREAT_CATEGORIES": "Top Threat Categories", "TOTAL_TRAFFIC": "Total Traffic", "TOTAL_TRANSACTIONS": "Total Transactions", "TOTAL_USERS": "Total Users", "TOTAL_USERS_IMPACTED": "Total Users Impacted", "TOTAL_USERS_NOTIFIED": "Total Users Notified", "TOTAL_VOLUME": "Total Volume", "TOTP_LOGIN": "TOTP Login", "TRAFFIC": "Traffic", "TRAFFIC_DISTRIBUTION": "Internet Traffic Distribution", "TRAFFIC_IN_ORG": "Traffic in Org", "TRAFFIC_IN_ORGANIZATION": "Traffic in my Organization", "TRAFFIC_VOLUME": "Traffic Volume", "TRAFFIC_VOLUME_LABEL": "Traffic Volume (GB)", "TRAFFIC_VOLUME_ACROSS_SERVICE": "Traffic Volume Across Service", "TRANSACTIONS": "Transactions", "TRANSACTIONS_FILES": "Transactions with Files", "TRY_AGAIN": "Please refresh and try again.", "TTFB_PFT_RATIO": "TTFB:PFT Ratio", "TUMBLR": "Tumblr", "TWO_DAYS": "2 Days", "TXT": "Text (txt)", "TYPE": "Type", "PRODUCT_AREA": "Product Area", "UNAUTH_COMM": "Unauthorized Communication", "UNEXPECTED_ERROR": "Unexpected Error", "UNIFIED_COMMUNICATION_EXP": "Unified Communication Experience", "UNIFIED_COMMUNICATION_EXP_DESC": "Your meeting experience across all of your meeting applications.", "UNIQUE_USERS_NOTIFIED": "Unique Users Notified", "UNKNOWN": "Unknown", "UNKNOWN_ERROR": "An unknown error occured", "UNLINK": "Unlink", "UNLINKED": "UnLinked", "UNLINK_TENANT": "Unlink Tenant", "UNPROTECTED_ICON": "Unprotected icon", "UNSELECTED_LOCATIONS": "Unselected Locations", "UNSELECTED_ROLES": "Unselected Roles", "UNREGISTERED": "Unregistered", "UPDATE": "Update", "UPDATED": "Updated", "UPDATE_EMAIL": "Update Email Address", "UPDATE_PSEUDO_DOMAIN": "Update Pseudo Domain", "UPDATE_PSEUDO_DOMAIN_SUCCESS": "Update Pseudo Domain Success !!", "UPLOAD": "Upload", "UPLOADED": "Uploaded", "UPLOAD_CERTIFICATE": "Upload Certificate", "UPLOAD_FILE": "Upload File", "UPLOAD_LOGO": "Upload Logo", "UPLOAD_LOGO_ERROR": "There is a problem with this file and it cannot be uploaded. Choose a different file.", "UPLOAD_METADATA": "Upload Metadata", "UP_ARROW_ICON": "Up arrow icon", "UP_RIGHT_ICON": "{{text}} icon", "URL_CATEGORY": "URL Category", "USER": "User", "USERS": "Users", "USERS_AND_GROUPS": "User and User Groups", "USERS_AND_GROUPS_TEXT": "Users & Groups", "USERS_ASSIGNMENT": "Users Assignment", "USERS_CREDENTIALS": "Users Credentials", "USERS_FOUND_NOTIFICATIONS_HELPFUL": "Users Found Notifications Helpful", "USERS_ICON": "{{label}} icon", "USERS_IMPACTED": "Users Impacted", "USERS_WHO_DISABLED_NOTIFICATIONS": "Users Who Disabled Notifications", "USER_ATTRIBUTE": "User Attribute", "USER_ATTRIBUTES": "User Attributes", "USER_COUNT": "User Count", "USER_DEFINED": "user-defined", "USER_DEVICE": "User Device", "USER_DEVICES": "User Devices", "USER_DEVICES_TOOLTIP": "This data represent the amount of registered devices currently available in your Organization", "USER_DISTRIBUTION_BY_EXPERIENCE_SCORE": "User Distribution by Experience Score", "USER_GROUPS": "User Groups", "USER_GROUPS_ASSIGNMENT_LIMIT": "Can't assign more that {{value}} groups in one go", "USER_GROUPS_ROLES_ASSIGNMENT_LIMIT": "Can't assign more that {{value}} roles to users/groups in one go", "USER_GROUP_NAME": "User Group Name", "USER_GROUP_SAML_ATTRIBUTE": "User Group SAML Attribute", "USER_ICON": "User icon", "USER_ID": "User ID", "USER_INFORMATION": "User Information", "USER_INFORMATION_ENDPOINT": "User Information Endpoint", "USER_MANAGEMENT": "User Management", "USER_NAME": "User Name", "USER_VOLUME_DRIVES_SIZE_OF_POINTS": " | User volume drives size of points", "VALIDATE_EMAIL": "Validate Email", "VALIDATION_CODE": "Enter Validation Code", "VALIDATION_TYPE": "Validation Type", "VALIDITY": "Validity", "VALUE": "Value", "VALUE_TYPE": "Value Type", "VERIFIED": "Verified", "VERIFY": "Verify", "VERIFY_EMAIL_ADDRESS": "Verify Em<PERSON> Address", "VERSION": "Version", "VERSION_DISTRIBUTION": "Version Distribution", "VERTICAL_DOTS_ICON": "{{name}} dot icon", "VIDEO_ICON": "Video icon", "VIEW": "View", "VIEW_ACTIVITY": "View Activity", "VIEW_ALL_ADVANCED_THREATS": "View All Advanced Threats", "VIEW_ALL_APPLICATIONS": "View All Applications", "VIEW_ALL_IMPACTED_USERS": "View all impacted users.", "VIEW_ALL_INCIDENTS": "View All Incidents", "VIEW_ALL_MEETINGS": "View All Meetings", "VIEW_ALL_THREAT_LOCATIONS": "View All Threat Locations", "VIEW_ALL_USERS": "View All Users", "VIEW_API_CLIENT": "View API Client", "VIEW_BY": "View by", "VIEW_CHANGES": "View Changes", "VIEW_GROUP": "View User Group", "VIEW_JWT": "View JWT", "VIEW_ONLY_ACCESS": "View Only Access", "VIEW_ROLES": "View Roles", "VIEW_SELF_SERVICE": "View Self Service", "VIEW_SSL_INSPECTION": "View SSL/TLS Inspection", "VIEW_TOKENS": "View Tokens", "VIEW_TRANSACTION_ACTIVITY": "View Transaction Activity", "VIEW_USER": "View User", "VIEW_USERS": "View Users", "VIEW_EDGE_ACTIVITY": "View Edge Activity", "VIEW_CLOUD_ACTIVITY": "View Cloud Activity", "VIEW_CONNECTOR_ACTIVITY": "View Connector Activity", "VIRUS": "Virus", "VIRTUAL": "Virtual", "VIRTUAL_IP_ADDRESS": "Virtual IP Address", "VM_SIZE": "VM Size", "WARNING_ICON": "Warning icon", "WATCH_VIDEO": "Watch Video", "WEB": "Web", "WEBEX_CALL_QUALITY": "Webex Call Quality", "WEBHOOK_URL": "Webhook URL", "WEB_APP_NOT_OPTIMIZED_FOR_CURRENT_BROWSER": "This browser is not supported and may break this site\"s functionality. We suggest that you update your browser to the latest version. To disregard this message, click OK.", "WEB_HTTP_CONNECTION_TIMEOUT": "TCP connection timed out during HTTP request to application", "WEB_HTTP_RESPONSE_CODE_MISMATCH": "HTTP response code not a success code", "WEB_PROBES_COUNT": "Web Probes Count", "WEB_PROBE_ERRORS": "Web Probe Errors", "WEB_PROBE_TIMED_OUT": "Web probe timed out", "WEB_SSL_EXCEPTION": "HTTPS connection failed due to SSL error", "WHAT_IS_IMPACTING_MY_USER_EXPERIENCE": "What is impacting my User Experience?", "WIFI_ACCESS_POINT": "Wi-Fi Access Point", "WIFI_ISSUES": "Wifi Issues", "WIFI_SIGNAL_STRENGTH": "Wi-Fi Signal Strength", "WIFI_TABLE_COLUMN": "Wi-Fi", "WINDOWS": "Windows", "WINDOWS_EXECUTABLES": "Windows Executable (exe, exe64, src)", "WINDOW_ICON": "Window icon", "WI_FI": "Wi-Fi", "WI_FI_ISSUES": "Wi-Fi Issues", "WI_FI_PERFORMANCE": "Wi-Fi Performance", "WORDPRESS": "WordPress", "XSS": "XSS", "YES": "Yes", "YES_SKIP": "Yes, <PERSON><PERSON>", "YOU": "You", "YOUR_END_TO_END_APPLICATION_SCORE_BASED_ON_PERFORMANCE_ACROSS_ALL_APPLICATIONS_MONITORED": "Your end-to-end application score based on performance across all applications monitored.", "YOUR_MALICIOUS_TRANSACTIONS": "Your Malicious Transactions", "YOUR_POLICY_BLOCKS": "Your Policy Blocks", "YOUR_SSL_INSPECTION": "Your SSL/TLS Inspection Review", "YOUR_TRANSACTIONS": "Your Cybersecurity Transactions", "YOUTUBE": "YouTube", "ZBI": "Browser Isolation", "ZCC_ASSIGNMENT": "ZCC Assignment", "ZDK_INSTANCE": "ZDK Instance", "ZDX": "Zscaler Digital Experience", "ZDX_SCORE": "ZDX Score", "ZDX_SCORE_FOR_IMPACTED_USERS": "ZDX Score for Impacted Users", "ZIA": "Zscaler Internet Access", "ZIA_PUBLIC_SERVICE_EDGE": "ZIA Public Service Edge", "ZIA_GATEWAY": "ZIA Gateway", "ZPA_BROKER": "ZPA Broker", "ZIDENTITY_DASHBOARD": "ZIdentity Dashboard", "ZIDENTITY_FOR_ADMINISTRATORS": "ZIdentity for Administrators", "ZIDENTITY_ISSUED_TOKENS": "ZIdentity Issued Tokens", "ZIDENTITY_MIGRATION_STATUS": "ZIdentity Migration Status", "ZIDENTITY_ROLES": "ZIdentity Roles", "ZIP": "ZIP (zip)", "ZOOM_CALL_QUALITY": "Zoom Call Quality", "ZPA": "ZPA Public Service Edge", "ZPA_ERRORS": "ZPA Public Service Edge Cloudpath Errors", "ZPA_JUSTIFICATION_LAG0": "Current Week Traffic", "ZPA_JUSTIFICATION_LAG1": "Last Week Traffic", "ZPA_JUSTIFICATION_LAG2": "2 Week Old Traffic", "ZPA_JUSTIFICATION_LAG3": "3 Week Old Traffic", "ZPA_JUSTIFICATION_P50": "P50 of 4 weeks", "ZPA_LATENCY_SPIKE_HOP1": "First Hop Latency around ZPA", "ZPA_LATENCY_SPIKE_HOP2": "Second Hop Latency Around ZPA", "ZPA_PUBLIC_SERVICE_EDGE": "ZPA Public Service Edge", "ZPA_SCORE_DROP": "ZPA Score Drop", "ZPA_TABLE_COLUMN": "ZPA Public Service Edge", "ZPA_BROKER_TABLE_COLUMN": "ZPA Broker", "ZRA": "Risk360", "ZSCALER": "<PERSON><PERSON><PERSON>", "ZSCALER_API": "ZSCALER APIs", "ZSCALER_HOSTED": "Zscaler Hosted", "ZSCALER_SERVICES": "Zscaler Services", "ZSLOGIN_AS_IDENTITY_PROVIDER": "<PERSON><PERSON><PERSON>in as Identity Provider", "ZSLOGIN_FOR_ADMINISTRATORS": "ZSLogin for Administrators", "ZSLOGIN_MIGRATION_STATUS": "ZSLogin Migration Status", "ZSPROXY_IPS": "Zscaler Proxy IPs", "ZS_LOGIN": "ZSL<PERSON>in", "ZS_SERVICES": "Zscaler Services", "Z_IDENTITY": "ZIdentity", "MENU_ICON": "Menu {{name}}", "RESET_ICON": "Reset", "AT": "at", "ACTION_ICON": "Action Icon", "ADVANCED_SECURITY": "Advanced Security", "BROWSER_EXPLOIT": "Browser Exploit", "CROSS_SITE_SCRIPTING": "Cross-site Scripting", "DGA": "Domain Generation Algorithm (DGA) Domains", "PAGE_RISK_IND": "Suspicious Content", "SUSPICIOUS_DEST": "Suspicious Destination", "WEBSPAM": "Web Spam", "LIGHT_THEME": "Light Theme", "DARK_THEME": "Dark Theme", "MAX_CHARACTER_LIMIT": "This field cannot contain more than {{max_len}} characters.", "ACCESS_POLICY": "Access Policy", "ADD_POLICY_RULE": "Add Policy Rule", "API_CLIENT_ACCESS_POLICY": "API Client Access Policy", "ASSIGNED_API_CLIENTS": "Assigned API Clients", "ASSIGN_API_CLIENTS": "Assign API Clients", "BASIC_INFORMATION": "Basic Information", "BLOCK": "Block", "CRITERIA_IF": "Criteria (If)", "DEFINE_CRITERIA": "Define Criteria", "DELETE_POLICY_RULE": "Delete Policy Rule", "EDIT_POLICY_RULE": "Edit Policy Rule", "IF": "If", "OATH2_CLIENT": "OAuth2 Client", "POLICY_RULE": "Policy Rule", "RULE_ACTION_THEN": "Rule Action (Then)", "SSO_REMOTE_ASSISTANCE_DETAILS": "SSO Remote Assistance Details", "THEN": "Then", "USER_AUTHENTICATOR": "User Authenticator", "VIEW_API_CLIENT_POLICY": "View API Client's Policy", "VIEW_REMOTE_ASSISTANCE_DETAILS": "View Remote Assistance Details", "ZIA_AUTO_SERVICE_UPDATE": "ZIA Auto Service Update", "ZSDK_CONFIG": "ZSDK Configuration", "MALICIOUS_CONTENT": "Malicious Content", "SUSPICIOUS_DESTINATION": "Suspicious Destination", "account": {"alertMessage": "Password has been successfully updated.", "changePasswordLabel": "Change Password", "changePasswordModalHeader": "Change Password", "confirmNewPasswordLabel": "Confirm New Password", "confirmNewPasswordPlaceholder": "Re-type New Password", "currentPasswordLabel": "Current Password", "currentPasswordPlaceholder": "Enter Current Password", "newPasswordLabel": "New Password", "newPasswordPlaceholder": "Enter New Password", "passwordLabel": "Password"}, "appliances": {"unnamed-appliance": "Unnamed appliance", "details": {"appliance": "Appliance", "name": "Name", "model": "Model", "serial-number": "Serial Number", "status": "Status", "appliance-group": "Appliance Group", "next-upgrade": "Next Upgrade", "current-version": "Current Version", "next-version": "Next Version", "branch-connector-group": "Branch Connector Group", "vendor": "<PERSON><PERSON><PERSON>", "vm-size": "VM Size", "version": "Version", "admin-status": "Admin Status", "provisioning-status": "Provisioning Status", "zt-device-status": "ZT Device Status", "appliance-details": "Appliance Details", "deployment-status-helptext": "means the configuration is available for the appliance to download and provision. Some parts of the configuration will become read-only after this.", "toast-message": {"update-prov-status": "Appliance provisioning status successfully updated."}}, "drawer": {"shared": {"vlanid": "VLAN ID", "radioLabel": "Admin Status", "up": "Up", "down": "Down", "ipAddressInfoLabel": "IP Address Info", "dhcp": "Use DHCP for IP Address", "uplinkModeHeading": "Uplink Mode", "active": "Active", "standby": "Standby", "ipAddress": "IP Address", "gateway": "Default Gateway", "priDNS": "Primary DNS", "secDNS": "Secondary DNS", "description": "Description", "primaryDNS": "Primary DNS", "mtuBytes": "MTU (bytes)", "ipaddress": "IP Address", "dhcpServer": "DHCP Server", "highAvailability": "High-Availability", "customDNS": "Custom DNS", "vLanId": "VLAN ID", "haVirtualIp": "Virtual IP", "haDeploymentStatus": "High Availability Deployment", "dns": "DNS", "automatic": "Automatic", "peerDHCP": "Peer DHCP", "automaticExample": "Automatic {{example}}", "cidrError": "Please Enter valid IP {{withcidr}}", "ServiceIP": "Service IP Address", "serviceIP1": "Service IP Address 1", "serviceIP2": "Service IP Address 2", "serviceIP3": "Service IP Address 3", "vLanReq": "VLAN id is required", "validVLanId": "Value should be a valid number", "vLanMinReq": "Value is below the limit of 1", "vLanMaxReq": "Value exceeded the limit of 4094"}, "wan-vm": {"heading": "Forwarding Interface", "high-availability-label": "High Availability Deployment"}, "mgtDrawer": {"heading": "MGT Interface"}, "wanInterface": {"heading": "WAN Interface", "sub-interface-heading": "WAN Sub-interface", "mtuBytes_err_message": "Enter MTU", "mtuBytes_max_errors": "Value Exeeded the Limit of {{limit}}"}, "lanSubInterface": {"heading": "LAN Interface", "radioLabel": "Admin Status", "ip-address-info-label": "IP Address Info", "high-availability-label": "High-Availability", "radio-option-label": {"up": "Up", "down": "Down"}, "locationSwitch-desc": "Use DHCP", "ipSubnetError": "Ensure that IP addresses are in the same subnet.", "ipAddressError": "Please Enter valid IP", "dhcpStartError": "DHCP start range is required", "dhcpEndError": "DHCP end range is required", "defaultGatewayReq": "Default gateway is required", "dhcpRangeError": "Start IP address must be less than End IP address.", "haConfigError": "Ha config is required", "haIdReq": "Id is required", "haIdValidErr": "Please enter a valid ID between 1 and 255.", "passphraseReq": "Passphrase is required", "passphraseReqLimit": "Passphrase cannot be longer than 128 characters.", "virtualIdReq": "Virtual IP Address is required", "macAddressError": "Please Enter valid MAC Address"}, "lanInterface": {"heading": "LAN Interface", "sub-interface-heading": "LAN Sub-interface", "id": "ID", "id-metatext": "This ID should be the same HA peers.", "virtualIp": "Virtual IP Address", "virtualIp-metatext": "This address is shared among HA peers, and should be in the same network as configured for this interface.", "startRange": "DHCP Range Start", "endRange": "DHCP Range End", "dhcpOptionsLabel": "DHCP Options", "staticIpLabel": "Static IPs", "passphrase": "Passphrase", "leaseTime": "Lease Time", "maxLeaseTime": "<PERSON> Time", "preferred": "Preferred interface", "preferred-metatext": "Enable to make this interface always try to be preferred among the HA peers. Only one interface should be preferred.", "dhcp-lease": "DHCP Lease", "add-range": "Add Range", "add-option": "Add Option", "add-static-ip": "Add Static IP", "cidr": "CIDR", "seconds": "seconds", "maxLeaseTime-error": "Value exceeded the limit of {{limit}}", "leaseTime-error": "Default Lease Time should be smaller of equal to Max Lease Time"}, "appliance": {"heading": "Appliance:", "config-name": "Name", "config-desc": "Description", "details-model": "Model", "details-serial": "Serial Number", "details-status": "Status", "add-heading": "Add Appliance"}, "appConnector": {"heading": "App Connector", "groupLabel": "App Connector Group", "deploymentStatus": "Deployment Status", "provisionKey": "Provision Key", "groupNamePlaceholder": "Select Group Name", "deploymentPlaceholder": "Select Deployment Status", "provisionPlaceholder": "Select Provision key", "connectorInterface": {"heading": "App Connector Interface", "ipAddress": "IP Address", "defaultGateway": "Dafault gateway", "primaryDNS": "Primary DNS", "secondaryDNS": "Secondary DNS"}}}, "modal": {"confirm-delete": "Confirm Delete", "interface-text": "Are you sure you want to delete this interface’s configuration? It will be restored to the default disabled state.", "interface-subtext": "This will also delete any sub-interfaces.", "subinterface-text": "Are you sure you want to delete this sub-interface? This cannot be undone.", "delete-interface-text": "Are you sure you want to delete this interface’s configuration? It will be restored to the default disabled state.", "delete-appliance-text": "Are you sure you want to delete this appliance?", "note": "Note:", "location-text": "You are about to delete this location. This can disrupt the flow of your web traffic to Zscaler and may render some policies invalid.", "location-subtext": "Are you sure want to continue?", "appliance-subtext": "Are you sure you want to delete this appliance?", "sublocation-text": "You are about to delete this location. This can disrupt the flow of your web traffic to Zscaler and may render some policies invalid.", "sublocation-subtext": "Are you sure want to continue?", "undo-warning": "This action cannot be undone.", "vm_warning_text": "This does not delete the VM. Please make sure you delete the resources separately."}, "highAvailabilityConfig": {"virtual-ip": "Virtual IP Address", "load-balancer": "Load Balancer IP Address"}, "app-connector": {"heading": "Enable App Connector", "group": "App Connector Group", "deployment-status": "Deployment Status", "provisioning-key": "Provisioning Key", "defaultgateway": "Dafault gateway", "status": {"active-active": "Active-Active"}, "connector-interface": {"heading": "App Connector Interface", "ipAddress": "IP Address", "defaultGateway": "Dafault gateway", "primaryDNS": "Primary DNS", "secondaryDNS": "Secondary DNS"}, "toast-message": {"update-appConnector": "AppConnector has been successfully Updated."}}, "list": {"tabs": {"location-goups": "Appliance Groups", "locations": "Appliances"}, "title": "Appliances", "sync-appliance": "Sync Appliances", "unregistered": "Unregistered", "staged": "Staged"}, "menu": {"appliances": "Appliances", "appliance-details": "Appliance Details", "network": "Network", "interfaces": "Interfaces", "app-connector": "App Connector", "upgrade-schedule": "Upgrade Schedule", "troubleshooting": "Troubleshooting"}, "upgradeSchedule": {"title": "Upgrade Schedule", "currentVersionTitle": "Current Version", "nextVersionTitle": "Next Version", "version": "Version", "lastUpgraded": "Last Upgraded", "scheduled": "Scheduled"}, "vmInterface": {"heading": {"forwardInterface": "Forwarding Interface", "mgtInterface": "Management Interface"}}}, "cloud": {"picker_default": "De<PERSON><PERSON>"}, "errors": {"no_results_found": "No Results Found", "something_went_wrong": "Something went wrong! {{message}}", "gateway_error": "This is taking longer than expected. Please wait a few minutes and try again.", "404": {"title": "Page Not Found", "summary": "The page you are looking for could not be found.", "description": "This may have happened because the URL is incorrect or the page has been deleted.", "options": {"title": "You can:", "one": "Double-check the URL.", "two": "Use the search bar at the top.", "three": "Return to the dashboard for other options."}}}, "legends": {"good": "Good", "okay": "Okay", "poor": "Poor"}, "locations": {"list": {"tabs": {"location-goups": "Location Groups", "locations": "Locations"}, "sync-locations": "Sync Locations", "title": "Locations", "table": {"name": "Name", "location": "Location", "type": "Type", "details": "Details", "ipsec-gre": "IPSec/GRE", "connection-types": "Connection Types", "sublocations": "Sublocations"}, "fitler": {"connection": "Connection"}, "add-ipsec-location": "Add IPSec/GRE Location", "add-edge-location": "Add Edge Location", "add-cloud-location": "Add Cloud Location", "add-import-locations": "Import Location (CSV)"}, "menu": {"locations": "Locations", "appliances": "Appliances", "overview": "Overview", "ipsec-gre": "IPSec/GRE", "vm-appliances": "VM's", "connection-options": "Connection Options", "sublocations": "Sublocations"}, "switcher": {"cloud": "Cloud", "edge": "Edge", "all-types": "All Types"}, "overview": {"cityState": "City/State/Province", "country": "Country", "dynamic": "Dynamic Location Groups", "latitude": "Latitude", "location": "Location:", "longitude": "Longitude", "managedby": "Managed By", "manual": "Manual Location Groups", "overviewcard": "Overview Card", "template": "Template:", "timezone": "Time Zone", "traffictype": "Traffic Type"}, "troubleshooting": {"allowSupport": "Allow Zscaler Support to start and stop the support tunnel", "description": "The support tunnel allows Zscaler Support to access this appliance for support and troubleshooting purposes.", "enableSupport": "Enable support tunnel", "title": "Support Tunnel", "port": "Port", "tunnelInfo": "Tunnel Info"}, "connectionOptions": {"title": "Connection Options", "xff-forwarding": "XFF Forwarding", "authentication": "Authentication", "authentication-types": "Authentication Types", "basic": "Basic", "digest": "Digest", "kerberos": "<PERSON><PERSON><PERSON>", "jwt": "JWT", "surrogateIp": "IP Surrogate", "caution-warning": "Caution Warning", "aup-warning": "AUP Warning", "firewall-control": "Firewall Control", "ips-control": "IPS Control", "bandwidth-control": "Bandwidth Control", "download": "Download (Mbps)", "upload": "Upload (Mbps)", "iot-discovery": "IoT Discovery", "max-limit-speed": "Enter the maximum {{name}} speed (0.1 - 99,999 Mbps)", "max-limit-time": "Enter how long to retain the user to IP address mapping before idling out and releasing the mapping (1 - {{time}} {{name}})", "max-limit-frequency": "This number must be between {{min}} and {{max}}", "bandwidthLimitError": "You have allocated all available bandwidth for this location. Increase the location bandwidth, or reduce a sub-location override bandwidth under this location"}, "appliance": {"APPLIANCE": "Appliance", "sync-appliance": "Sync Appliances", "table": {"branch-connector": "Branch Connector Group", "name": "Name", "appliance": "Appliance", "type": "Type", "status": "Status", "operational-status": "Operational Status", "provisioning-status": "Provisioning Status", "ip-address": "IP Address", "location": "Location", "appliance-group": "Appliance Group", "branch-connector-group": "Branch Connector Group", "mgt-ip-address": "MGT IP Address", "deployementStatus": "Deployment Status"}, "interface": {"interface": "Interface", "type": "Type", "ip-address": "IP Address", "dhcp": "DHCP", "details": "Details", "admin-status": "Admin Status", "link-status": "Link Status", "add-lan": "Add LAN Settings", "add-wan": "Add WAN Settings", "add-sub-interface": "Add Sub-Interface", "filters": {"all-types": "All Types", "lan-interfaces": "LAN Interfaces", "wan-interfaces": "WAN Interfaces", "sub-interfaces": "Show Sub-interfaces"}}, "dnsCache": {"heading": "Auto-Populate DNS Cache", "label1": "Enabled", "label2": "Disabled"}, "tunnelMode": {"heading": "ZIA Tunnel Mode", "label1": "Encrypted", "label2": "Unencrypted"}, "distributionMode": {"heading": "Multiple WAN Traffic Distribution Mode", "label1": "Balanced", "label2": "Best-link"}, "no-data-name": "No Appliances", "no-data-description": "Secure your location with Branch Connector appliances that forward traffic to Zscaler services.", "no-data-buttonText": "Add Appliance"}, "ipsec": {"title": "IPSec/GRE", "static-ips": "Static IPs & GRE", "proxy-ports": "Proxy Ports", "vpn-credentials": "VPN Credentials", "virtual-zens": "Virtual Service Edges", "virtual-clusters": "Virtual Service Edge Clusters", "gre-tunnel-info": "IPSec/GRE Tunnel Info", "destination-ip": "Destination IP", "destination-range": "Destination Internal Range", "primary-secondary": "Primary/Secondary", "tunnel-ip": "Tunnel Source IP", "export": "IPsec-export", "edit": "IPsec-edit", "serial-number": "No."}, "sublocation": {"xff": "XFF", "bandwidth": "Bandwidth", "sync-sublocation": "Sync Sublocations", "no-data-name": "No Sublocations", "no-data-description": "Sublocations provide a way to differentiate certain IP addresses or subnets for policy purposes.", "no-data-buttonText": "Add Sublocation"}, "emptySubLocation": {"btn-text": "Add Sub-Location", "title": "No Sub-Locations", "description": "Sub-Locations provide a way to differentiate certain IP addresses or subnets for policy purposes."}, "location": {"add-heading": "Add Location", "edit-heading": "Location", "name": "Name", "cityState": "City/State/Province", "latitude": "Latitude", "longitude": "Longitude", "description": "Description", "country": "Country", "timezone": "Time Zone", "locationGroups": {"heading": "Location Groups", "exclude-manual-location": "Exclude from Manual Location Groups", "manual-location": "Manual Location Groups", "exclude-dynamic-location": "Exclude from Dynamic Location Groups", "dynamic-location": "Dynamic Location Groups"}, "internetAccess": {"heading": "Internet Access Settings", "traffic-type": "Traffic Type", "managedby": "Managed By"}, "edit": {"heading": "IPSec/GRE", "staticIPAddresses": "Static IP Addresses and GRE Tunnels", "proxyPorts": "Proxy Ports", "vpnCredentials": "VPN Credentials", "virtualZENs": "Virtual Service Edges", "virtualClusters": "Virtual Service Edge Clusters"}, "upload": {"heading": "Import Locations", "btnUpload": "Upload CSV File", "noFile": "No file chosen. Max Limit:8 MB", "override": "Override existing locations", "example": "Example CSV file", "import": "Import"}}, "subLocation": {"add-heading": "Add Sublocation", "edit-heading": "Sublocation", "properties": {"label": "Properties", "name": "Sublocation Name", "description": "Description", "addressing": {"heading": "Addressing", "internal-ip-address": "Internal IP Addresses"}}, "sublocation": "Sublocation", "internal-ips": "Internal IPs", "authentication": "Authentication", "firewall": "Firewall", "xff": "XFF", "bandwidth": "Bandwidth", "connectionOptions": {"label": "Connection Options", "firewall-control": "Enforce Firewall Control", "ips-control": "Enable IPS Control", "authentication": "Enforce Authentication", "basic-authentication": "Enable Basic Authentication", "digest-authentication": "Enable Digest Authentication", "kerberos-authentication": "Enable <PERSON><PERSON>", "jwt-authentication": "Enable Proxy JWT Authentication", "ip-surrogate": "Enable IP Surrogate", "unmap-users": "Unmap users from IPs when idle for:", "use-ip-surrogate": "Use IP Surrogate for known browsers also", "revalidation-time": "Revalidation Time:", "authentication-methods": "Supported authentication methods:", "caution-warning": "Enable Caution Warning", "aup-warning": "Enable AUP Warning", "aup-frequency": "Custom AUP Frequency (days)", "aupBehavior": {"heading": "First time AUP behavior", "internet-access": "Block Internet access", "ssl-inspection": "Force SSL Inspection"}, "iot-discovery": "Enable IoT Discovery", "iot-policy": "Enforce IoT policy control", "xff-forwarding": "Enable XFF Forwarding", "bandwidth-control": "Enforce Bandwidth Control", "download-speed": "Download Speed (Mbps)", "upload-speed": "Upload Speed (Mbps)", "location-bandwidth": "Override parent Location bandwidth", "location-bandwidth-prefix": "When checked, configured bandwidth is dedicated to this sub-location and will not be shared with others.", "bandwidth-control-type": {"shared": "Shared", "shared-helper-text": "Bandwidth is shared with other sublocations at this location.", "dedicated": "Dedicated", "dedicated-helper-text": "Bandwidth is dedicated to this sublocation."}}}, "traffic-type": {"corporate": "Corporate user traffic", "server": "Server traffic", "guestwifi": "Guest Wi-Fi traffic", "iot": "IoT traffic", "extranet": "Extranet", "workload": "Workload Traffic type"}, "toast-message": {"delete-location": "Location has been successfully removed from the system.", "delete-sublocation": "Sub Location has been successfully removed from the system.", "delete-appliance": "Appliance has been successfully removed from the system.", "add-location": "Location has been successfully added to the system."}}, "subscriptions": {"title": "Subscriptions", "table": {"name": "Name", "type": "Type", "sku": "SKU", "status": "Status", "usage": "Usage", "licenses": "Licenses", "startDate": "Start Date", "endDate": "End Date"}, "customizeColumns": {"title": "Customize Columns", "display": "Display All Columns"}}, "sku": {"AV_AS": "Anti Virus and Anti Spam", "ZIA_ATP": "Advanced Threat Protection", "URL_FILTER": "URL Filtering", "ZMAN_WEB_2_0": "Cloud App Controls", "ZDLP_WEB": "Data Loss Protection (DLP)", "Z_REPORTING": "Basic Reporting", "ZIA_BW_CTRL": "Bandwidth Controls", "Z_SMTP_PLATFORM_I": "SMTP Platform I", "Z_SMTP_SECURE_I": "SMTP Secure I", "Z_SMTP_SPAM_I": "SMTP Spam I", "Z_SMTP_DLP_I": "SMTP Data Loss Prevention I", "Z_SMTP_PLATFORM_II": "SMTP Platform II", "ZSC_SYNCBRIDGE": "S<PERSON><PERSON>", "Z_VPN_S2S": "Zscaler CVPN S2S", "Z_VPN_MOBILE": "Zscaler VPN Mobile", "Z_LOGFEED": "NSS Log Feed for Web", "Z_EZ_AGENT": "Zscaler EZ Agent", "Z_RPTBLDR": "Report Builder", "Z_PROXYPORT": "Dedicated Proxy Port", "Z_MOBILE_SEC": "Mobile Security", "ZSCALER_CLIENT_CONNECTOR": "Zscaler Client Connector", "ZSSL_PVT_CERT": "Custom Root Certificate", "ZSC_PRIV_ZABSW": "<PERSON><PERSON>ler Auth Bridge", "ZSEC_WEB_ABA": "Cloud Sandbox", "Z_FIREWALL_BASIC": "Firewall Basic", "ZFW_NG_WITH_LOG": "Advanced Cloud Firewall", "ZFW_STD_LOG": "Standard FW Full Logging", "Z_LOGFEED_FW": "NSS Log Feed for Firewall", "ZSC_VZEN_MEDIUM": "Virtual ZEN Medium", "Z_ICAP": "ICAP", "Z_CLOUD_ID_BROKER": "Cloud Identity Broker", "Z_LOGFEED_LIVE": "NSS Live for Web", "ZSC_VZEN_SMALL": "Virtual ZEN Small", "ZSC_VZEN_LARGE": "Virtual ZEN Large", "Z_ICAP_RESPONSE": "ICAP Response", "Z_NONHTTP": "Non HTTP", "Z_HFTP": "FTP over HTTP", "Z_NFTP": "Native FTP", "Z_DLP_EDM": "Exact Data Match", "Z_API": "Z-API", "Z_FIREWALL_IPS": "Firewall IPS", "Z_LOGFEED_FW_LIVE": "NSS Live for Firewall", "ZIA_ENC_VPN": "Encrypted VPN", "Z_CASB": "CASB", "ZIA_ADD50K_URLS": "URL Increment - 50k", "ZDX_ADVANCED": "Digital Experience Advanced", "ZS_SIPA": "Source IP Anchoring", "V_SVC_EDGE": "Virtual Service Edge", "B_AND_C_CONNECTOR": "Total Cloud & Branch Connector", "BC_DEVICE_CONNECTOR_VM": "BC Device Connector VM", "C_CONNECTOR": "Cloud Connector", "BC_DEVICE_ZIA": "BC Device ZIA", "BC_DEVICE_ZPA": "BC Device ZPA", "BC_DEVICE_ADV_LOG": "BC Device Advanced Logging", "BC_DEVICE_TUNNEL_ENCRYPT": "BC Device Tunnel Encryption", "Z_DLP_IDM": "Index Document Match", "Z_ZDX_STD": "Digital Experience Standard", "ZIA_ISO_5": "Cloud Browser Isolation", "Z_CLOUD_NSS_FW": "Cloud to Cloud Streaming FW", "Z_CLOUD_NSS_WEB": "Cloud to Cloud Streaming Web", "ZIA_ICAP_FWD_PRE": "Forwarding for web traffic to ICAP server", "BC_DEVICE_NSS_LOGFEED": "BC Device NSS logfeed", "Z_NSS_LIVE_BC_CONNECTOR": "NSS Live For Cloud and Branch Connector", "ZS_SSPM_M365": "SSPM for Microsoft 365", "ZS_CSPM_CLOUD_INFRA": "CSPM for Cloud Infrastructure", "ZSC_SSPM_M365": "ZSC SSPM for Microsoft 365", "ZSC_CSPM_CLOUD_INFRA": "ZSC CSPM for Cloud Infrastructure", "Z_ZDX_M365": "Digital Experience - Microsoft 365", "Z_CUSTOMROOTCERT_HSM": "Custom Root Certificate - HSM", "ZIA_CST_IPS_SIG": "Custom IPS Signature control", "ZS_DECEPTION_ADV": "Deception Advanced", "ZS_DECEPTION_STANDARD": "Deception Standard", "ZS_DECEPTION_NTW_DECOY": "Deception NTW Decoy", "ZS_DECEPTION_DECOY_CONN": "Deception Decoy Connection", "ZS_DECEPTION_ESS": "Deception Essentials", "ZS_ZSS": "Threat Insights", "ZDX_ADV_PROBES": "Digital Experience - Advanced Probes", "ZDX_ADV_PROBES_M365": "Digital Experience- Advanced Probes - M365", "WORKLOAD_ZIA": "Workload - Internet ", "WORKLOAD_ZPA": "Workload - Private Access", "WORKLOAD_CONNECTOR_VM": "Workload Connector VM", "WORKLOAD_ADVANCED_LOG": "Workload Advanced Log", "WORKLOAD_TUNNEL_ENCRYPT": "Workload Tunnel Encryption", "WORKLOAD_NSS_LOGFEED": "Workload NSS Logfeed", "IPV6_BASIC": "IPv6 Basic", "ZT_IOT_VIS": "IoT Device Visibility", "ZDX_ADV_PLUS": "Digital Experience Advanced Plus", "Z_DNS_ESS": "DNS Essential", "Z_DNS_ADVANCED": "DNS Advanced", "Z_FIREWALL_BASIC_EXTRA_RULES": "Firewall Basic Extra Rules", "ZIA_ISO_25": "Cloud Browser Isolation 25", "ZIA_ISO_100": "Cloud Browser Isolation 100", "ZIA_ISO_STD": "Cloud Browser Isolation STD", "ZIA_ISO_PROXY": "Cloud Browser Isolation Proxy", "IPV6_ADVANCED": "IPv6 Advanced", "Z_WORKFLOW_AUTOMATION": "Z Workflow Automation", "Z_DLP_ENDPOINT": "DLP Endpoint", "ZT_BC_CELLULAR_PRE": "Branch Connector - Cellular - Pre", "ZT_CC_CELLULAR_PRE": "Client Connector - Cellular - Pre", "ZS_DP_SAAS_3PA": "AppTotal Third Party", "Z_RA": "Risk360 Advanced", "ZIA_TRAFFIC_CAP_ESS": "Internet Traffic Capture - Essentials", "LARGE_CFW_RULE_SET": "Additional Firewall Rules - 4K", "LARGE_LOCATIONS_VPNCREDS_IPADDR(": "Additional Locations, VPN Creds, IP Addrs -- 64K", "ZIA_TRAFFIC_CAPTURE_ADV": "Internet Traffic Capture", "ZT_IOT_STD": "IoT Policy Control", "ZS_INSP_PRV": "Inspect Private Access Traffic With Internet Access", "BC_DEVICE_CONNECTOR_400": "BC Device Connector 400", "BC_DEVICE_CONNECTOR_600": "BC Device Connector 600", "BC_DEVICE_CONNECTOR_800": "BC Device Connector 800", "WORKLOAD_VDI": "Cloud Connector VDI", "BC_DEVICE_VDI": "Branch Connector VDI", "ZIA_ISO_SAAS_2_0": "BCI ISO SaaS 2.0", "ZDLP_WEB_BASIC": "Data Leak Protection Basic", "Z_CASB_BASIC": "CASB Basic", "ZS_ITDR": "ITDR", "ZS_DSPM_ESS": "DSPM Essentials", "ZS_DP_EMAIL": "Data Protection Email", "ZS_DDCTD_IP": "<PERSON><PERSON><PERSON> Dedicated IP", "ZS_DP_ISO_SAAS_STD": "DP Isolation SAAS STD", "ZS_DP_ISO_SAAS_ADV": "DP Isolation SAAS ADV", "ZS_DP_ISO_SAAS_ADV_PLUS": "DP Isolation SAAS ADV Plus", "ZIA_ISO_ADV": "Isolation Adv", "ZIA_ISO_ADV_PLUS": "Isolation Adv Plus", "ZSEC_WEB_ABA_ADV": "Sandbox Advance Detection", "ZS_SANDBOX_API_FILES": "Sandbox API", "ZS_SANDBOX_API_EXTRA_FILES": "Sandbox API: Extra Files", "ZDX_HOSTED_PROBES": "Digital Experience - Hosted Monitoring Probes", "ZDX_LITE": "Digital Experience Lite - Two Probes Only", "ZSC_ZPA_LOG": "Private Access Nanolog Storage", "ZSC_ZPA_LOG_EXTSUBS": "Private Access Nanolog Extended Storage", "ZS_DINSPECTION": "ATP AI/ML Deep Inspection", "ZS_DINSPECTION_EXTRA_FILES": "ATP AI/ML Deep Inspection Extra Files", "ZS_DP_DATA_TFORM": "Data Transformation Service", "ZS_ZBP": "<PERSON><PERSON><PERSON> Breach Predictor", "ZS_DP_GEN_AI": "Gen AI Security", "ZS_EXT_LOCATIONS": "Zscaler Extranet Locations", "ZS_EXT_PARTNERS": "Zscaler Extranet Partners", "ZIA_ISO_UNCAT": "Isolation of Miscellaneous and Unknown Category", "ZS_EASM": "Zscaler External Attack Surface Management", "ZT_CELL_ESS_EU": "Zero Trust Cellular Edge - Essentials EU", "ZT_CELL_ESS_US": "Zero Trust Cellular Edge - Essentials US", "ZS_DEDICATED_IP": "<PERSON><PERSON><PERSON> Dedicated IP", "Z_RA_STD": "Risk360 - Standard", "CUST_DICTIONARIES_ENGINES": "DLP Dictionaries and Engine", "Z_DP_INSTANCE_DISCOVERY": "Instance <PERSON>", "Z_CLOUD_NSS_ZPA": "Cloud NSS for ZPA", "ZS_DATA_IAAS": "IAAS Security Data Scan", "SSPM_ADVANCED": "SSPM Advanced", "Z_OCR": "Zscaler Optical Character Recognition (OCR)", "ZT_CELL_ESS_APJ": "Zero Trust Cellular Edge - Essentials (APJ)", "AWS_ZTGW_SVC_CREDITS": "Zero Trust for Workloads Gateway (Instance)", "ZS_GEO_IP": "Zscaler Geolocalization", "ZS_DDCTD_GEO_IP": "Dedicated Geo IP", "ZIA_TRAFFIC_CAPTURE_ADVANCE": "Internat Traffic Capture Advance", "ZS_DSPM_ESS_PRE": "DSPM Essentials Pre ", "ZSC_PRIV_LB": "Private LB", "ZIA_SVC_EDGE_3": "SVC Edge 3", "ZIA_SVC_EDGE_5": "SVC Edge 5", "ZIA_SERVER_GB": "SERVER GB", "ZEXT_BW_PREM": "Bandwidth Premium", "ZEXT_BW_PREM_ME": "Bandwidth Premium - Middle East", "ZS_IDP": "IDP", "ZIA_GWIFI_GB": "Inline Guest Wifi", "ZSC_PRIV_NANO_HA": "Private Nanolog Cluster", "ZSC_LOG_EXTSUBS": "Extended Log Storage", "ZIA_SSL": "SSL Inspection", "ZSSL_WEB": "SSL Web", "ZSC_PRI_CAT": "Zscaler Private Categorization", "ZSC_PRIV_ZENHW2": "PRIV ZEN HW2", "ZSC_PRIV_ZENHW3": "PRIV ZENHW3", "ZSC_PRIV_ZENSW": "PRIV ZEN SW", "ZSC_PRIV_ZENHW1": "PRIV ZENHW1", "ZSC_PRIV_ZENHW6": "PRIV-ZENHW6", "ZIA_TEST_TENANT": "Internet Access Test Tenant", "ZSC_PRIV_ZEN_HOST": "Private Zen Hosting", "ZSEC_WEB_WAC": "Web WAC", "ZSC_SIP_LOC": "SIP LOC", "ZIA_ZFED_MODERATE": "Internet Access ZFED Moderate", "ZSC_IR_GWIFI": "IR GWIFI", "ZSC_IR_GWIFI_XL": "IR GWIFI XL", "ZROAD_WAR_SUP": "Road Warrior Sup", "ZSC_PRIV_NANO_HA_5YL": "Private Nano HA 5YL", "ZSC_PRIV_LB_5YL": "Private LB SYL", "ZSC_PRIV_ZENHW6_5YL": "Private ZEN HW6_5YL", "ZSC_PRIV_ZENSW_5YL": "Private Zen SW 5YL", "ZSC_SWITCH_5YL": "Switch 5YL", "ZSC_PRIVPORTAL_5YL": "Private Prortal SYL", "ZIA_ZFED_MODERATE_1000": "Internet Access ZFed Moderate 1000", "ZIA_SERVER_PRE": "Internet Access Server Pre", "ZIA_CASB_ADD_DATA": "CASB Add Data", "ZS_PZEN": "Private Zen", "ZIA_SVC_CHINA_MBPS_PRE": "Internet Access Service China MBPS Pre", "ZIA_CLD_ID_PROXY_PRE": "Internet Access Cloud ID Proxy Free", "ZS_SIPA_EXTRA_GB": "SIPA Extra GB", "ZIA_TRANS_EDITION_LU": "Internet Access Trans Edition LU", "ZIA_PROF_EDITION_LU": "Internet Access Professional Edition LU", "Z_BI": "Zscaler Business Intelligence", "ZIA_TRAFFIC_CAP_EXTRAGB": "Traffic Capture Extra GB", "ZT_CELL_EXTRA_GB": "Zero Trust Cellular Edge - Extra GB", "ZDX_COPILOT": "Digital Experience Co-Pilot", "ZDX_ZSDK_PRE": "Digital Experience For SDK - Pre"}, "menu": {"APIConfiguration": "API Configuration", "AccessControl": "Access Control", "AccessMethods": "Access Methods", "AccountManagement": "Account Management", "AdaptiveAccess": "Adaptive Access", "AdminManagement": "Admin Management", "AdministrativeScope": "Administrative Scope", "AdministratorManagement": "Administrator Management", "Advanced": "Advanced", "AdvancedSettings": "Advanced Settings", "Alerts": "<PERSON><PERSON><PERSON>", "Analytics": "Analytics", "AppConnectors": "App Connectors", "AppSegments": "App Segments", "Application": "Application", "Applications": "Applications", "AssetIntelligence": "Asset Intelligence", "AuditLogs": "<PERSON><PERSON>", "BackupRestore": "Backup & Restore", "BandwidthControl": "Bandwidth Control", "BranchAndCloudConnectors": "Branch And Cloud Connectors", "BranchCloudConnectors": "Branch And Cloud Connectors", "BranchConnector": "Branch Connector", "BrowserIsolation": "Browser Isolation", "BrowserExtension": "Browser Extension", "BusinessContinuity": "Business Continuity", "Client": "Client", "ClientConnector": "Connectors", "ClientConnectorDeployment": "Client Connector Deployment", "ClientConnectorPolicies": "Client Connector Policies", "ClientConnectorServiceEntitlements": "Client Connector Service Entitlements", "Clientless": "Clientless", "Cloud": "Cloud", "CloudAndBranchConnectorResources": "Cloud & Branch Connector Resources", "CloudandEdgeVMDeployment": "Cloud and Edge VM Deployment", "CloudBranchConnectorResources": "Cloud & Branch Connector Resources", "CloudConfiguration": "Cloud Configuration", "CommonConfiguration": "Common Configuration", "CommonResources": "Common Resources", "CompanyProfile": "Company Profile", "Component": "Component", "Configuration": "Configuration", "Connectors": "Connectors", "ContentScanning": "Content Scanning", "Copilot": "Copilot", "CustomApplication": "Custom Application", "Cybersecurity": "Cybersecurity", "DNSControl": "DNS Control", "Dashboard": "Dashboard", "Dashboards": "Dashboards", "DataClassification": "Data Classification", "DataProtection": "Data Protection", "Deployment": "Deployment", "Deployments": "Deployments", "Device": "<PERSON><PERSON>", "DeviceAuthentication": "<PERSON>ce Au<PERSON>cation", "DeviceSegmentation": "Zero Trust Branch", "Digital Experience": "Digital Experience", "DigitalExperience": "Digital Experience", "DigitalExperienceManagement": "Digital Experience Management", "DigitalExperienceMonitoring": "Digital Experience Monitoring", "DisasterRecovery": "Disaster Recovery", "Domains": "Domains", "Edge": "Edge", "Email": "Email", "EndUserNotifications": "End User Notifications", "EndpointControls": "Endpoint Controls", "EnrolledDevices": "Enrolled Devices", "Entitlements": "Entitlements", "Extranet": "Extranet", "FileTypeControl": "File Type Control", "Firewall": "Firewall", "FirewallControl": "Firewall Control", "ForwardingControl": "Forwarding Control", "ForwardingProfiles": "Forwarding Profiles", "GlobalSettings": "Global Settings", "IDPConfiguration": "IDP Configuration", "IPAndFQDNGroups": "IP & FQDN Groups", "IPAssignment": "IP Assignment", "IPFQDNGroups": "IP & FQDN Groups", "IPGroupFQDN": "IP Group & FQDN", "IPS": "IPS", "Identity": "Identity", "IdentityProxy": "Identity Proxy", "IncidentManagement": "Incident Management", "IncidentResponse": "Incident Response", "Infrastructure": "Infrastructure", "InfrastructureCertificates": "Infrastructure Certificates", "InlineControls": "Inline Controls", "InlineSecurity": "Inline Security", "Insights": "Insights", "Integrations": "Integrations", "InternetAuthenticationSettings": "Internet Authentication Settings", "InternetLogStreaming": "Internet Log Streaming", "InternetSaaS": "Internet & SaaS", "Inventory": "Inventory", "Ipv6Configurations": "Ipv6 Configurations", "LegacyAPI": "Legacy API", "Location": "Location", "LocationManagement": "Location Management", "LogStreaming": "Log Streaming", "Logs": "Logs", "Management": "Management", "Microsegmentation": "Microsegmentation", "MonitoringSettings": "Monitoring Settings", "NetworkApplications": "Network Applications", "NetworkServices": "Network Services", "Networking": "Networking", "Operational": "Operational", "NetworkingPolicies": "Networking Policies", "OTHER": "Other", "OneAPI": "OneAPI", "OneAPIConfiguration": "OneAPI Configuration", "Out-of-bandCASB": "Out-of-Band CASB", "OutboundEmailControls": "Outbound Email Controls", "PartnerIntegrations": "Partner Integrations", "PartnerLogin": "Partner Login", "PasswordsAuthentication": "Passwords & Authentication", "PlatformSettings": "Platform Settings", "Policies": "Policies", "Policy": "Policy", "PrivateAccess": "Private Access", "PrivateAppProtection": "Private App Protection", "PrivateApplications": "Private Applications", "PrivateLogStreaming": "Private Log Streaming", "PrivateServiceEdge": "Private Service Edge", "PrivateServiceEdges": "Private Service Edges", "PrivilegedRemoteAccess": "Privileged Remote Access", "Reporting": "Reporting", "Reports": "Reports", "Resources": "Resources", "RoleBasedAccessControl": "Role Based Access Control", "Roles": "Roles", "SCIMConfiguration": "SCIM Configuration", "SCIMEventLogs": "SCIM Event Logs", "SSL/TLSInspection": "SSL/TLS Inspection", "SaaSApplicationControl": "SaaS Application Control", "SaaSApplicationTenants": "SaaS Application Tenants", "SaaSSecurity": "SaaS Security", "SaaSSecurityAPI": "SaaS Security API", "Sandbox": "Sandbox", "SecureBrowsing": "Secure Browsing", "Security": "Security", "Segmentation": "Segmentation", "SelfService": "Self Service", "Servers": "Servers", "Service Entitlements": "Service Entitlements", "ServiceEntitlements": "Service Entitlements", "Settings": "Settings", "SignOn": "Sign On", "Supportability": "Supportability", "ThreatPrevention": "Threat Prevention", "TrafficCapture": "Traffic Capture", "TrafficForwarding": "Traffic Forwarding", "TrafficSteering": "Traffic Steering", "URLControl": "URL Control", "UserManagement": "User Management", "UserPortalConfiguration": "User Portal Configuration", "VPN": "VPN", "ViewAllPolicies": "View All Policies", "Workloads": "Workloads", "ZIdentity": "ZIdentity", "Locations": "Locations", "LocationResources": "Location Resources", "Gateways": "Gateways", "Risk": "Risk"}, "theme": {"dark_mode": "Dark Mode", "dark_mode_subtitle": "(automatically adjust the display based on device’s system settings)"}, "wifi": "WI-FI", "ALL_INTERFACES": "All Interfaces", "CONDITIONAL_ACCESS": "Adaptive Access", "SHARED_SECRET_REQUIRED_MESSAGE": "Shared secret is required", "VPN_ERROR_RESPONSE_SECURE_TRAFFIC": "Custom Application is already in use by App Profile", "ENFORCED": "Enforced", "DHCP": "DHCP", "LINK_STATUS": "Link Status", "APP_CONNECTOR_PROPERTIES": "App Connector Properties", "APPLIANCES": "Appliances", "APPLIANCEGROUPS": "Appliance Groups", "time": {"hours": "Hours", "minutes": "Minutes", "seconds": "Seconds", "days": "Days"}, "vm": {"small": "Small VM", "medium": "Medium VM", "large": "Large VM"}, "vm-product": {"vmware-esxi": "VMware ESXi", "redhat-linux": "RedHat Linux", "microsoft-hyper-v": "Microsoft Hyper V"}, "UNITED_STATES_AMERICA_LOS_ANGELES": "America/Los Angeles", "UNITED_STATES": "United States", "SINGAPORE": "Singapore", "OPTIONAL": "Optional", "ZAA_PROFILES": "Adaptive Access Profiles", "ZAA_CONTEXT_NUGGET_OVERRIDES": "Adaptive Access Overrides", "ZAA_INTEGRATIONS": "Adaptive Access Integrations", "ZAA_SIGNAL_HISTORY": "Adaptive Access Signal History", "inactivity": {"dialog": {"title": "Signed Out Due to Inactivity", "subtext": "For security reasons you have been logged out", "action": "Sign In"}}, "ZTDS": {"SITES": {"COL_SITE_NAME": "Site Name", "COL_TEMPLATE": "Template", "COL_GATEWAY_NAME": "Gateway Name", "COL_STATE": "State", "COL_IP_ADDRESS": "IP Address", "COL_VERSION": "Version"}}, "EDIT_CROWDSTRIKE": "Edit <PERSON>", "EDIT_MICROSOFT_DEFENDER": "Edit Microsoft Defender", "EDIT_SILVERFORT": "<PERSON>", "SSF_CONFIG_ENDPOINT": "SSF Config Endpoint", "EVENT_HUB_INSTANCE": "Event Hub Instance", "EVENT_HUB_HOST": "Event Hub Host", "CONSUMER_GROUP": "Consumer Group", "CONEECTION_STRING": "Connection String", "EVENT_HUB_INSTANCE_REQUIRED_MESSAGE": "Event hub instance is required", "CONNECTION_STRING_REQUIRED_MESSAGE": "Connection string is required", "EVENT_HUB_HOST_REQUIRED_MESSAGE": "Event hub host is required", "CONSUMER_GROUP_REQUIRED_MESSAGE": "Consumer group is required", "BEARER_TOKEN_REQUIRED_MESSAGE": "Bearer token is required", "SSF_CONFIG_ENDPOINT_REQUIRED_MESSAGE": "SSF config endpoint is required", "EDIT_OKTA": "<PERSON>", "SETTINGS": "Settings", "CONFIG_TAKES_EFFECT_FROM_NEXT_LOGIN": "The updated settings will take effect for all admins after their next login.", "navigation": {"analytics": "Analytics", "administration": "Administration", "policies": "Policies", "settings": "Settings", "infrastructure": "Infrastructure", "logs": "Logs"}, "nav": {"administration": {"account-management": "Account Management", "admin-management": "Admin Management", "identity": "Identity", "entitlements": "Entitlements", "api-configuration": "API Configuration", "alerts": "<PERSON><PERSON><PERSON>", "backup-and-restore": "Backup &  Restore"}, "policies": {"access-control": "Access Control", "cybersecurity": "Cybersecurity", "digital-experience-monitoring": "Digital Experience Monitoring", "data-protection": "Data Protection", "common-configuration": "Common Configuration", "dp": {"policy": "Policy", "common-resources": "Common Resources"}}, "infrastructure": {"internet-saas": "Internet & SaaS", "private-access": "Private Access", "locations": "Locations", "connectors": "Connectors", "common-resources": "Common Resources"}, "logs": {"insights": "Insights", "log-streaming": "Log Streaming"}}, "examples": {"blank-page": "This page was intentionally left blank for educational purposes.", "source": "from {{file}}"}, "days": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "timestamp": {"justNow": "Just Now", "minute": "{{value}} min{{plural}} ago", "hour": "{{value}} hour{{plural}} ago", "day": "{{value}} day{{plural}} ago", "week": "{{value}} week{{plural}} ago", "month": "{{value}} month{{plural}} ago"}, "homepage": {"viewHelpDocs": "View Help Docs", "viewHelpDocsDescription": "View help docs to access technical documentation and release notes.", "getATour": "Get a Tour", "getATourDescription": "Get a quick tour of the Experience Center to see what’s new here.", "provideFeedback": "Provide <PERSON>", "provideFeedbackDescription": "Share your thoughts and help us improve your experience.", "viewArticleOnWebsite": "View Article on Website", "discovered": "Discovered", "learnAboutProducts": "Learn About Products", "loadMore": "Load More", "viewMore": "View More", "more": "more", "resetToDefault": "Reset to De<PERSON>ult", "customize": "Customize", "alreadyAdded": "already added", "addCard": "Add Card", "news": "News", "newsFeed": {"cxo_revolutionary": "CXO Revolutionaries", "security_advisory": "Security Advisories", "security_research": "Security Research"}, "recentlyViewed": {"title": "Recently Viewed", "emptyStateHeader": "Your recently viewed pages will show up here", "emptyStateSubTextPrefix": "Check out ", "emptyStateSubTextSuffix": " to get started."}, "banner": {"title": "Experience Center", "description": "Your new unified console brings all of your products together into one place.", "ctaLabel": "Start Tour"}, "analytics": {"zpa_text_header1": "Recent Applications Accessed", "zpa_text_tooltip1": "Recent Applications Accessed displays real time information about the total number of applications requested by users for the selected time frame. The widget always uses the current time for its end time even if you select a custom time range with a different end time.", "zpa_text_header2": "Discovered Applications", "zpa_text_tooltip2": "Discovered Applications displays real time information of the total number of applications that were accessed successfully by users", "zpa_text_header3": "Access Policy Blocks", "zpa_text_tooltip3": "Access Policy Blocks displays the total number of application requests blocked by ZPA over the selected time frame. Data for Access Policy Blocks is updated every 5 minutes", "zpa_text_header4": "Successful Transactions", "zpa_text_tooltip4": "Successful Transactions displays the total number of successful transactions over the selected time frame. Data for Successful Transacations is updated every 5 minutes."}, "videos": {"title1": "Understanding Zscaler's Cyberthreat Protection Solution", "subtext1": "In an era where cybersecurity threats are becoming increasingly sophisticated, Zscaler introduces its Cyberthreat Protection as a modern solution to replace outdated technologies like VPNs and firewalls.", "title2": "Zscaler Internet Access (ZIA): A cloud-native security solution", "subtext2": "Zscaler Internet Access (ZIA) introduces a cloud-native, Zero Trust security model that inspects traffic at scale, enhancing productivity while reducing complexity.", "title3": "Transforming secure access with Zscaler Private Access (ZPA)", "subtext3": "In a world where employees work from various locations, traditional security solutions like VPNs are proving inadequate against modern cyber threats.", "title4": "Understanding Zscaler’s Data Protection Solution", "subtext4": "The video discusses the growing challenges of protecting sensitive data scattered across various platforms, which complicates security efforts for organizations", "title5": "Understanding Data Security Posture Management (DSPM) for Cloud Data Security", "subtext5": "In the face of increasing cloud data breaches, security teams are challenged with securing sensitive data across multiple cloud platforms. The need for visibility and control over data locations, access, and vulnerabilities has never been more critical.", "title6": "Unlocking the Power of Generative AI: Embrace the Future Safely with <PERSON>", "subtext6": "In this video, we explore the exciting potential of Generative AI while safeguarding your company with Zscaler's Zero Trust Exchange. Discover innovative Generative AI applications, protect your data with DLP and Browser Isolation, and learn how to turn data privacy incidents into coaching opportunities.", "title7": "Zscaler Digital Experience Light Board with <PERSON>", "subtext7": "Explore end-to-end visibility, improved user experience, and reduced operational cost with Zscaler Digital Experience.", "title8": "Enhancing Digital Experience with Zscaler Digital Experience (ZDX)", "subtext8": "In the quest for reliable performance of devices, networks, and applications, Zsca<PERSON>'s Digital Experience (ZDX) emerges as a vital solution for IT teams. It addresses the challenges of slow applications and connectivity issues that hinder employee productivity, especially in diverse working environments.", "title9": "Zscaler Digital Experience (ZDX) - Hosted Monitoring", "subtext9": "Zscaler Digital Experience (ZDX) has extended monitoring beyond users to applications and services 24/7 from global vantage points with Hosted Monitoring.", "title10": "Zero Trust in 5 Minutes with <PERSON>", "subtext10": "VPNs will never be zero trust because they typically grant broad network access to users, and well, users on the network are a recipe for disaster. Discover why <PERSON><PERSON><PERSON>'s inside-out zero trust married to identity helps enforce true user-to-application zero trust without exposing an attack surface and keeping the users off the network."}}, "RISK_360": "Risk 360", "RISK": "Risk", "DP_DC_INLINE_TOP_SENSITIVE_DATA_TYPES": "Top Sensitive Data Types", "DP_DC_INLINE_TOTAL_TRANSACTIONS": "Total Transactions", "DP_DC_INLINE": "Inline", "DP_DC_SASS_SECURITY": "SaaS Security", "DP_DC_ENDPOINTS": "Endpoints", "DP_DC_ENDPOINT": "Endpoint", "DP_DC_EMAIL": "Email", "DP_DC_DSPM": "DSPM", "DP_TOTAL_ENDPOINT_INCIDENTS": "Total Endpoint Incidents", "DP_TOP_USERS_ENDPOINT_INCIDENTS": "Top Users with Endpoint Incidents", "DP_DC_TOP_HIGH_APPLICATION_ELIMINATES": "Top High Risk Applications to Eliminate", "DP_DC_TOP_HIGH_APPLICATION_ELIMINATES_DESC": "These are high risk applications that have the lowest usage.", "DP_DC_TOP_HIGH_APPLICATION_SECURE": "Top High Risk Applications to Secure", "DP_DC_TOP_HIGH_APPLICATION_SECURE_DESC": "These are high risk applications that have the most usage.", "DP_DC_TOP_UNSANCTION_INSTANCES": "Top Instances of SaaS Applications", "DP_VIEW_ENDPOINT_ACTIVITY": "View Endpoint Activity", "DP_DC_VIEW_ALL_INSTANCES": "View All Instances", "DP_DC_INLINE_SENSITIVE_GENAI_DATA": "Sensitive GenAI Applications", "DP_DC_VIEW_ALL_GENAI_ACTIVITY": "View All GenAI Activity", "DP_DC_SAAS_INCIDENTS": "SaaS Incidents", "DP_TOP_DOMAINS_SENSITIVE_DATA_BEING_SENT": "Top Domains Sensitive Data is Being Sent To", "DP_VIEW_EMAIL_SECURITY_ACTIVITY": "View Email Security Activity", "DP_TOP_USERS_EMAIL_INCIDENTS": "Top Users with Email Incidents", "DP_CUSTOM_ACTION": "Custom Action", "DP_DC_SAAS_APPLICATION_DATA_EXPOSURE": "SaaS Applications with Data Exposure", "DP_PHI": "PHI", "DP_PII": "PII", "DP_PCI": "PCI", "DP_DC_SAAS_APP_HIGH_RISK_MISCONFIG": "Top SaaS Applications with High Risk Misconfigurations", "DP_BY_CLASSIFICATION": "By Classification", "DP_BY_TYPE": "By Type", "DP_DC_SAAS_TOP_USERS": "Top Users with SaaS Incidents", "DP_CUSTOM_DLP": "Custom DLP", "DP_OFFENSIVE_LANGUAGE": "Offensive Language", "DP_CYBERBULLYING": "Cyberbullying", "DP_DC_SAAS_TOP_DEPARTMENTS": "Top Departments with SaaS Incidents", "DP_PRINTER": "Printer", "DP_BLUETOOTH": "Bluetooth", "DP_AIRDROP": "Airdrop", "DP_TOP_DEPARTMENTS_ENDPOINT_INCIDENTS": "Top Departments with Endpoint Incidents", "DP_USERS_MOST_INCIDENTS": "Users with Most Incidents", "APPLICATION_TENANT_NAME": "Application Tenant Name", "INTERNAL": "Internal", "EXTERNAL": "External", "DP_DASHBOARD_ALL_CHANNELS_DATA": "All Channels Data", "DP_TOP_DEPARTMENTS_EMAIL_INCIDENTS": "Top Departments with Email Incidents", "CHANNEL": "Channel", "AICATEGORY": "AI Category", "DP_DASHBOARD_TOP_SENSITIVE_DATA_AT_REST_TITLE": "Top Sensitive Data At Rest", "DP_TOTAL_TRANSACTIONS_SENSITIVE_DATA": "Total Transactions with Sensitive data", "DP_DASHBOARD_TOP_SENSITIVE_DATA_AT_REST_SUBTITLE": "Data below reflects the most recent insights.", "GENAI_CLASSIFICATION": "GenAI Classification", "GENAI_CATEGORY": "GenAI Category", "DLP_ENGINES": "DLP Engines", "COMING_SOON": "Coming soon", "DP_DASHBOARD_TOP_SENSITIVE_DATA_AT_REST_AI_TYPE": "Exact AI type", "PUBLIC_CLOUDS": "Public Clouds", "SAAS": "Saas", "ONPREM": "On-Prem", "ONPREMISES": "On-Premises", "ENDPOINT": "Endpoint", "FINANCIAL_DOCUMENTS": "Financial Documents", "IMMIGRATION_DOCUMENTS": "Immigration Documents", "INSURANCE_DOCUMENTS": "Insurance Documents", "LEGAL_DOCUMENTS": "Legal Documents", "MEDICAL_DOCUMENTS": "Medical Documents", "REAL_STATE_DOCUMENTS": "Real Estate Documents", "DMV_DOCUMENTS": "DMV Documents", "HR_DOCUMENTS": "HR Documents", "TECHNICAL_DOCUMENTS": "Technical Documents", "IMAGE_DOCUMENTS": "Image Documents", "DP_DASHBOARD_SENSITIVE_FILES": "Sensitive Files", "DP_DATA_CHANNELS_INLINE_SENSITIVE_FILES_HEADING": "Sensitive Files in Top 10 ML Categories", "DP_DATA_CHANNELS_INLINE_SENSITIVE_FILES_DESCRIPTION": "In top 10 ML categories", "DP_DATA_CHANNELS_INLINE_VIEW_ALL_DATA_DISCOVERY": "View All Data Discovery", "DP_DATA_CHANNELS_INLINE_SENSITIVE_FILES_CHART_LABELS": "Total Files", "DP_DC_SAAS_INCIDENTS_PRIVATE_TOOLTIPTEXT": "Compromises the confidentiality of personal information", "DP_DC_SAAS_INCIDENTS_INTERNAL_TOOLTIPTEXT": "Security breach within an organization", "DP_DC_SAAS_INCIDENTS_EXTERNAL_TOOLTIPTEXT": "Events that original from outside the organization"}