import { useTranslation } from "react-i18next";
import { type DeviceStateData } from "./types";
import { getDonutData, getDeviceStateConfig } from "./config";
import { DeviceState } from "./DeviceState";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { usePostMutation } from "@/utils/apiUtils";
import { Operational } from "@/configs/urls/analytics/operational";
import useFilterEffect from "@/hooks/useFilterEffect";
import { generateMAPayload } from "@/components/pages/Analytics/Dashboard/Filters/ModalPillGroupContainer/util";
import { FILTER_DEVICES_CONFIG } from "@/components/pages/Analytics/Dashboard/Filters/ModalPillGroupContainer/config";
import { useProductAccessProvider } from "@/context/ProductAccessProvider";
import { useMAEntitlement } from "@/hooks/useMAEntitlement";
import { getDataTestId } from "@/utils/utils";
import { getSystemTheme } from "@/utils/themeUtils";

const DeviceStateContainer = ({ id }: { id?: string }) => {
  const ID = getDataTestId("device-state", id);
  const { entitlements } = useProductAccessProvider();
  const containerId = "Operational.Devices.DeviceState";
  const { t } = useTranslation();
  const theme = getSystemTheme();
  const deviceStateConfig = getDeviceStateConfig(theme, t);

  const {
    trigger: fetchDeviceState,
    data: response,
    isMutating,
    error,
  } = usePostMutation(Operational.Devices.DeviceState, {
    key: containerId,
  });

  //TODO: Update payload with time filters.
  useFilterEffect(
    fetchDeviceState,
    generateMAPayload,
    entitlements?.zcc,
    {
      user: [0],
      type: [0],
      osId: [0],
    },
    FILTER_DEVICES_CONFIG,
  );

  const transformResponseData = (rawResponse: DeviceStateData[]) => ({
    heading: deviceStateConfig.heading,
    donutChartProps: {
      data: { slice: getDonutData(rawResponse, t) },
      dataKeys: deviceStateConfig.dataKeys,
      config: deviceStateConfig.donutConfig,
      theme,
    },
  });

  const responseData = response as DeviceStateData[];
  const transformedData = transformResponseData(responseData);

  return (
    <WithAnalyticsStates
      loading={isMutating}
      error={error}
      title={deviceStateConfig?.heading}
      noData={!transformedData?.donutChartProps?.data?.slice?.length}
      unsubscribed={useMAEntitlement()}
      id={ID}
    >
      <DeviceState {...transformedData} id={ID} />
    </WithAnalyticsStates>
  );
};

export { DeviceStateContainer };
