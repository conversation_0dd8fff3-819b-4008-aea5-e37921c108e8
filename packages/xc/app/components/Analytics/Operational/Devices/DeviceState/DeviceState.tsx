import { useTranslation } from "react-i18next";
import { Donut } from "@up/components";
import { type DeviceStateProps } from "./types";
import { Card } from "@/components/Analytics/Card";

const DeviceState = ({ heading, donutChartProps, id }: DeviceStateProps) => {
  const { t } = useTranslation();

  return (
    <Card id={id}>
      <Card.Header>
        <Card.Header.Title id={id}>{t(heading)}</Card.Header.Title>
      </Card.Header>
      <Card.Content>
        <Donut {...donutChartProps} chartId={`${id}-donut-chart`} />
      </Card.Content>
    </Card>
  );
};

export { DeviceState };
