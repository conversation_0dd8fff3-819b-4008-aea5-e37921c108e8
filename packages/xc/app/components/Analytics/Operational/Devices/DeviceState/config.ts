import { colors } from "@zs-nimbus/foundations";
import { getDatavizTooltip, type DatavizDonutProps } from "@up/components";
import { type DeviceStateData, type DeviceStateConfigType } from "./types";
import { OPERATIONAL_DEVICE_RESPONSE_STATES } from "@/configs/constants/analytics";
import { capitalizeFirstLetterOnly } from "@/utils/utils";
import { type ThemeTypes } from "@/context/UserPreferenceContext";

export const getDeviceStateConfig = (
  theme: ThemeTypes,
  t: (s: string) => string,
): DeviceStateConfigType => ({
  heading: "DEVICE_STATE",
  dataKeys: ["count", "category"],
  donutConfig: {
    containerClass: "flex h-[240px] mb-rem-160 overflow-hidden",
    chartProps: {
      chartContainer: {
        paddingBottom: 5,
        paddingTop: 5,
      },
    },
    labelProps: {
      centerLabel: {
        labels: ["{valueSum.formatNumber()}", t("TOTAL_DEVICES")],
      },
    },
    legendProps: {
      direction: "bottom",
      legendLabelProps: {
        legendLabelColor: colors[theme].content.base.tertiary,
        fontSize: 12,
      },
      legendMarkerProps: {
        height: 15,
        width: 15,
      },
      legendItemContainerProps: {
        marginTop: 5,
        marginBottom: 5,
      },
    },
    pieSetting: {
      strokeColor: colors[theme].border.inverted.primary,
      strokeWidth: 1,
      tooltipHTML: getDatavizTooltip([
        { label: "{categoryLabel}" },
        {
          data: [
            {
              label: "{value}",
            },
          ],
        },
      ]),
    },
  },
});

export const getDonutData = (
  currentStates: DeviceStateData[],
  t: (s: string) => string,
): DatavizDonutProps["data"]["slice"] =>
  currentStates
    ?.map((curr) => {
      const cat =
        OPERATIONAL_DEVICE_RESPONSE_STATES.find(
          (state) => state.id === curr.regState,
        )?.label ?? "";

      return {
        category: capitalizeFirstLetterOnly(cat),
        categoryLabel: t(cat),
        count: +curr.count,
        color:
          OPERATIONAL_DEVICE_RESPONSE_STATES.find(
            (state) => state.id === curr.regState,
          )?.color ?? "",
      };
    })
    .filter((item) => item.count > 0);
