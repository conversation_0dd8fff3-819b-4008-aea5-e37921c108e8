import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { getDatavizTooltip, type DatavizDonutProps } from "@up/components";
import { type PayloadProps, type TotalIncidentsData } from "./types";
import { type ThemeTypes } from "@/context/UserPreferenceContext";

export const totalIncidentsConfig = () => {
  const baseConfig: DatavizDonutProps["config"] = {
    containerClass: "flex h-[332px]",
    chartProps: {
      chartContainer: {
        paddingBottom: 45,
        paddingTop: 10,
      },
    },
    legendProps: {
      legendMarkerProps: {
        height: 12,
        width: 12,
      },
      legendLabelProps: {
        paddingLeft: 4,
      },
      legendRectangleProps: {
        cornerRadiusTL: 3,
        cornerRadiusTR: 3,
        cornerRadiusBL: 3,
        cornerRadiusBR: 3,
      },
    },
    tooltipProps: {
      tooltipSetting: {
        pointerOrientation: "right",
      },
    },
    pieSetting: {
      tooltipHTML: getDatavizTooltip([
        { label: "{category}" },
        { data: [{ label: "{count.formatNumber()}" }] },
      ]),
    },
  };

  return baseConfig;
};

export const totalIncidentsData = (theme: ThemeTypes): TotalIncidentsData => ({
  totalCountTitle: "TOTAL_THREATS_BLOCKED",
  totalCount: "0",
  donutChartData: {
    data: {
      slice: [
        {
          category: "Inline",
          count: 1664,
          color: datavizColors[theme].dataviz.categorical.primary.chart01,
        },
        {
          category: "Saas Security",
          count: 1149,
          color: datavizColors[theme].dataviz.categorical.primary.chart02,
        },
        {
          category: "Endpoint",
          count: 992,
          color: datavizColors[theme].dataviz.categorical.primary.chart03,
        },
        {
          category: "Email",
          count: 992,
          color: datavizColors[theme].dataviz.categorical.primary.chart04,
        },
      ],
    },
    dataKeys: ["count", "category"],
    config: totalIncidentsConfig(),
    theme,
  },
});

export const TotalIncidentWEBPayload = ({
  startDate,
  endDate,
}: PayloadProps) => ({
  query: `query WEB_TRAFFIC {
    WEB_TRAFFIC {
        no_grouping(
            start_time: ${startDate}
            end_time: "${endDate}
            traffic_unit: SCANS
        ) {
            entries {
                name
                total
            }
        }
    }
}`,
});

export const TotalIncidentCASBPayload = ({
  startDate,
  endDate,
}: PayloadProps) => ({
  query: `query SAAS_SECURITY {
    SAAS_SECURITY {
        casb_no_grouping(start_time: ${startDate}, end_time: ${endDate}) {
            entries {
                name
                total
            }
        }
    }
}
`,
});

export const TotalIncidentEDLPPayload = ({
  startDate,
  endDate,
}: PayloadProps) => ({
  query: `query EDLP {
    EDLP {
        edlp_incidents_with_out_grouping(
            start_time: ${startDate}
            end_time: ${endDate}
        ) {
            entries {
                name
                total
            }
        }
    }
}`,
});
