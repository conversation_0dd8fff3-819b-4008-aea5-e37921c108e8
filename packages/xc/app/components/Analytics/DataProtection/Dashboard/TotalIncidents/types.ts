import { type DatavizDonutProps } from "@up/components";
import { type TimePickerFilter } from "@/app/(post-onboarding)/(with-side-nav)/analytics/AnalyticsFilterContext";

export type TotalIncidentsDataProps = {
  donutChartData: DatavizDonutProps;
  id?: string;
};

export type TotalIncidentsContainerProps = {
  id?: string;
  filter?: TimePickerFilter;
  isMockData: boolean;
};

export type IncidentCategory = {
  total: number;
};

export type TotalIncidentsData = {
  totalCountTitle: string;
  totalCount: string;
  donutChartData: DatavizDonutProps;
};

export type PayloadProps = {
  startDate: number;
  endDate: number;
};
