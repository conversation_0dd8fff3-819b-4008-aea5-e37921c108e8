import { useCallback, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { type DatavizDonutProps } from "@up/components";
import { TotalIncidentsData } from "./TotalIncidentsData";
import {
  type TotalIncidentsContainerProps,
  type IncidentCategory,
} from "./types";
import {
  TotalIncidentCASBPayload,
  TotalIncidentEDLPPayload,
  TotalIncidentWEBPayload,
  totalIncidentsConfig,
} from "./config";
import { TotalIncidentsMockData } from "./mock.data";
import { usePostMutation } from "@/utils/apiUtils";
import { useAnalyticsFilter } from "@/app/(post-onboarding)/(with-side-nav)/analytics/AnalyticsFilterContext";
import { formatNumber, getDataTestId, secToMs } from "@/utils/utils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { getSystemTheme } from "@/utils/themeUtils";
import { useZDXEntitlement } from "@/hooks/useZDXEntitlement";
import { Card } from "@/components/Analytics/Card";
import { DataProtection } from "@/configs/urls/analytics/data-protection";
import { type DataProtectionProps } from "@/components/pages/HomePage/types";
import { API_ENDPOINTS } from "@/utils/apiHelper";

export const TotalIncidentsContainer = ({
  id,
  filter,
  apiResponse,
  setApiResponse,
  isMockData,
}: TotalIncidentsContainerProps & DataProtectionProps) => {
  const { t } = useTranslation();
  const theme = getSystemTheme();
  const containerId = "DataProtection.Dashboard.Totalncidents";
  const HAS_READ_PERMISSION = useZDXEntitlement(containerId);

  const { trigger, data, isMutating, error } = usePostMutation(
    API_ENDPOINTS.Z_INSIGHTS,
    {
      key: containerId,
    },
  );
  const { trigger: triggerCASB } = usePostMutation(API_ENDPOINTS.Z_INSIGHTS, {
    key: containerId,
  });
  const { trigger: triggerEDLP } = usePostMutation(API_ENDPOINTS.Z_INSIGHTS, {
    key: containerId,
  });
  const { trigger: triggerEmailDLP } = usePostMutation(
    DataProtection.Dashboard.TotalIncidentsEmailDLP,
    {
      key: containerId,
    },
  );
  const response = data ?? TotalIncidentsMockData;

  let responseData = {
    response,
    isMutating,
    error,
  };

  if (!apiResponse?.response && responseData.response) {
    setApiResponse?.("totalIncidents", responseData);
  }

  if (apiResponse?.response) {
    responseData = apiResponse;
  }

  const { analyticsFilter } = useAnalyticsFilter();

  const { startDate, endDate } =
    filter?.dateFilter ?? analyticsFilter.dateFilter;
  const { contextualFilters } = analyticsFilter;

  useEffect(() => {
    void trigger(
      TotalIncidentWEBPayload({
        startDate: secToMs(startDate),
        endDate: secToMs(endDate),
      }),
    );
    void triggerCASB(
      TotalIncidentCASBPayload({
        startDate: secToMs(startDate),
        endDate: secToMs(endDate),
      }),
    );
    void triggerEDLP(
      TotalIncidentEDLPPayload({
        startDate: secToMs(startDate),
        endDate: secToMs(endDate),
      }),
    );
    void triggerEmailDLP({
      dataClass: "EMDLP",
      dataType: "USER",
      startTime: startDate,
      endTime: endDate,
      units: "INCIDENTS",
    });
  }, [
    startDate,
    endDate,
    contextualFilters,
    HAS_READ_PERMISSION,
    trigger,
    triggerCASB,
    triggerEDLP,
    triggerEmailDLP,
    apiResponse?.response,
  ]);

  const transformResponseData = useCallback(
    (rawResponse: Record<string, IncidentCategory> | null) => {
      const colorMap: Record<string, string> = {
        Inline: datavizColors[theme].dataviz.categorical.primary.chart01,
        "Saas Security":
          datavizColors[theme].dataviz.categorical.primary.chart02,
        Endpoint: datavizColors[theme].dataviz.categorical.primary.chart03,
        Email: datavizColors[theme].dataviz.categorical.primary.chart04,
      };

      let total = 0;
      const updatedData = rawResponse
        ? Object.entries(rawResponse).map(([category, data]) => {
            const count = Object.values(data).reduce((a, b) => a + b, 0);
            total += count;

            return {
              category: category,
              count: count,
              color: colorMap[category],
            };
          })
        : [];

      const config = totalIncidentsConfig();

      return {
        donutChartData: {
          config: {
            ...config,
            labelProps: {
              ...config.labelProps,
              centerLabel: {
                labels: [`${formatNumber(total)}`, t("INCIDENTS")],
              },
            },
          },
          dataKeys: ["count", "category"],
          data: { slice: updatedData },
          theme,
        } as DatavizDonutProps,
      };
    },
    [t, theme],
  );

  const ID = useMemo(() => getDataTestId("dp-total-incidents", id), [id]);

  const totalIncidentsPropData = useMemo(
    () =>
      transformResponseData(
        responseData.response as Record<string, IncidentCategory> | null,
      ),
    [responseData.response, transformResponseData],
  );

  return (
    <Card id={ID}>
      <Card.Header id={ID}>
        <Card.Header.Title id={ID}>{t("TOTAL_INCIDENTS")}</Card.Header.Title>
      </Card.Header>
      <WithAnalyticsStates
        loading={isMockData ? undefined : isMutating}
        error={isMockData ? undefined : error}
        showBorder={false}
        noData={isMockData ? false : !responseData.response}
        id={ID}
      >
        <Card.Content className="overflow-x-hidden">
          <TotalIncidentsData {...totalIncidentsPropData} id={ID} />
        </Card.Content>
      </WithAnalyticsStates>
    </Card>
  );
};
