import { type DatavizDonutProps } from "@up/components";
import { type SaaSApplicationsDataExposureRowItemProps } from "../types";

export type SaaSApplicationsDataExposureDrawerProps = {
  donutChartData: DatavizDonutProps;
  id?: string;
};

export type SaaSApplicationsDataExposureDrawerContainerProps = {
  id?: string;
  responseData: SaaSApplicationsDataExposureRowItemProps | null;
  handleCloseDrawer: () => void;
};

export type TabTypeProps = "classification" | "type";

export type SegmentControlOptionsProps = {
  label: string;
  value: string;
};

export type SaaSApplicationsDataExposureDrawerDataResponse =
  SaaSApplicationsDataExposureRowItemProps;
