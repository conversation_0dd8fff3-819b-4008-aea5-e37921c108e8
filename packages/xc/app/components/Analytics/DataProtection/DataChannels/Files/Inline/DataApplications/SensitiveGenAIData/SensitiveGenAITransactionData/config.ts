import { colors } from "@zs-nimbus/foundations";
import { getDatavizTooltip, type DatavizDonutProps } from "@up/components";
import { SegmentOptions } from "../config";
import { type SensitiveGenAIDataWidgetProps } from "./types";
import { type ThemeTypes } from "@/context/UserPreferenceContext";
import { type PayloadProps } from "@/components/Analytics/DataProtection/types";

export const sensitiveGenAIDataTransationConfig = (theme: ThemeTypes) => {
  const baseConfig: DatavizDonutProps["config"] = {
    containerClass: "flex h-[290px]",
    chartProps: {
      chartContainer: {
        paddingBottom: 45,
        paddingTop: 10,
      },
    },
    legendProps: {
      legendMarkerProps: {
        height: 12,
        width: 12,
      },
      legendLabelProps: {
        legendLabelColor: colors[theme].content.base.primary,
        paddingLeft: 4,
        fontSize: 13,
        fontWeight: "400",
      },
      legendRectangleProps: {
        cornerRadiusTL: 3,
        cornerRadiusTR: 3,
        cornerRadiusBL: 3,
        cornerRadiusBR: 3,
      },
    },
    tooltipProps: {
      tooltipSetting: {
        pointerOrientation: "right",
      },
    },
    pieSetting: {
      strokeColor: colors[theme].content.inverted.base.primary,
      strokeWidth: 1,
      tooltipPosition: "fixed",
      tooltipHTML: getDatavizTooltip([
        { label: "{category}" },
        { data: [{ label: "{count.formatNumber()}" }] },
      ]),
    },
  };

  return baseConfig;
};

export const sensitiveGenAITransactionsData = (
  theme: ThemeTypes,
): SensitiveGenAIDataWidgetProps => ({
  transactionData: {
    totalCountTitle: "TOTAL_POLICIES_BLOCK",
    totalCount: "0",
    donutChartData: {
      data: {
        slice: [
          {
            category: "Open AI",
            count: 200,
            color: "#194cbb",
          },
          {
            category: "ChatGPT",
            count: 150,
            color: "#25bae2",
          },
          {
            category: "Gemini",
            count: 100,
            color: "#9f46d7",
          },
          {
            category: "Perplexity",
            count: 50,
            color: "#3da592",
          },
          {
            category: "Others",
            count: 100,
            color: "#8590A6",
          },
        ],
      },
      dataKeys: ["count", "category"],
      config: sensitiveGenAIDataTransationConfig(theme),
    } as DatavizDonutProps,
  },
  segmentOptions: [...SegmentOptions],
});

export const SensitiveGenAITransactionPayload = ({
  startDate,
  endDate,
}: PayloadProps) => ({
  query: `query DATA_PROTECTION_GENAI {
    DATA_PROTECTION_GENAI {
        sensitive_data_by_app(
            start_time: ${startDate}
            end_time: ${endDate}
            traffic_unit: BYTES
        ) {
            obfuscated
            entries {
                name
                total
            }
        }
    }
  }`,
});
