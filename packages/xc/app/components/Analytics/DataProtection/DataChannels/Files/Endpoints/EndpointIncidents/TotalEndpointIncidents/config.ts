import { colors } from "@zs-nimbus/foundations";
import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { type DatavizDonutProps } from "@up/components";
import { type PayloadProps } from "./types";
import { type ThemeTypes } from "@/context/UserPreferenceContext";

export const getTotalEndpointIncidentsChartConfig = (theme: ThemeTypes) => {
  const themeColors = colors[theme];
  const baseConfig: DatavizDonutProps["config"] = {
    containerClass: "flex h-[340px]",
    chartProps: {
      chartContainer: {
        paddingBottom: 45,
        paddingTop: 10,
      },
    },
    legendProps: {
      gridLayout: true,
      legendMarkerProps: {
        height: 12,
        width: 12,
      },
      legendLabelProps: {
        legendLabelColor: themeColors.content.base.primary,
        fontSize: 13,
        fontWeight: "400",
      },
      legendRectangleProps: {
        cornerRadiusTL: 2,
        cornerRadiusTR: 2,
        cornerRadiusBL: 2,
        cornerRadiusBR: 2,
      },
    },
    tooltipProps: {
      tooltipSetting: {
        pointerOrientation: "right",
      },
    },
  };

  return baseConfig;
};

export const TotalEndpointIncidentsPayload = ({
  startDate,
  endDate,
}: PayloadProps) => ({
  query: `query fetchLocation {
  EDLP {
    edlp_incidents_with_out_grouping(
      start_time: ${startDate}
      end_time: ${endDate}
    ) {
      entries{
        name
        total
      }
    }
  }
}`,
});

export const TotalEndpointIncidentsChartLegends = (theme: ThemeTypes) => [
  {
    category: "TOTAL",
    iconColor: datavizColors[theme].dataviz.sequential.blue.scale05,
    label: "TOTAL",
  },
  {
    category: "USB",
    iconColor: datavizColors[theme].dataviz.sequential.blue.scale05,
    label: "USB",
  },
  {
    category: "Printer",
    iconColor: datavizColors[theme].dataviz.sequential.blue.scale03,
    label: "Printer",
  },
  {
    category: "Bluetooth",
    iconColor: datavizColors[theme].dataviz.sequential.blue.scale02,
    label: "Bluetooth",
  },
  {
    category: "Airdrop",
    iconColor: datavizColors[theme].dataviz.sequential.blue.scale01,
    label: "Airdrop",
  },
];
