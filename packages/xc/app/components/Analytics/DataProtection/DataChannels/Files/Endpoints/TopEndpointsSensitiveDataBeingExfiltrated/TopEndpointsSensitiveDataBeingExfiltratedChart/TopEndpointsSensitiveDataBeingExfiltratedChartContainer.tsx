import { useTranslation } from "react-i18next";
import { useContext, useEffect } from "react";
import { type VerticalBarDefaultConfigProps } from "@up/components";
import { TopEndpointsSensitiveDataBeingExfiltratedConfig } from "../config";
import {
  getTooltipData,
  getTopEndpointsSensitiveDataBeingExfiltratedChartConfig,
  TopEndpointsSensitiveDataBeingExfiltratedChartPayload,
} from "./config";
import { TopEndpointsSensitiveDataBeingExfiltratedChart } from "./TopEndpointsSensitiveDataBeingExfiltratedChart";
import {
  type Props,
  type TopEndpointsSensitiveDataBeingExfiltratedChartResponse,
} from "./types";
import { TopEndpointsSensitiveDataBeingExfiltratedChartMockData } from "./mock.data";
import { getDataTestId, secToMs } from "@/utils/utils";
import { usePostMutation } from "@/utils/apiUtils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { getSystemTheme } from "@/utils/themeUtils";
import { AnalyticsFilterContext } from "@/app/(post-onboarding)/(with-side-nav)/analytics/AnalyticsFilterContext";
import { API_ENDPOINTS } from "@/utils/apiHelper";
import { getBarStackColors } from "@/utils/unifiedAnalytics/utils";
import { getStackBarData } from "@/components/Analytics/Charts/VerticalBarChart/utils";

export const toolTipColors: Record<string, string> = {
  pci: "text-dataviz-categorical-primary-chart01",
  phi: "text-dataviz-categorical-primary-chart02",
  pii: "text-dataviz-categorical-primary-chart03",
};

export const TopEndpointsSensitiveDataBeingExfiltratedChartContainer = ({
  id,
  isMockData,
}: Props) => {
  const ID = getDataTestId("app-exp-trend", id);
  const containerId =
    "DataProtection.DataChannels.Endpoints.TopEndpointsSensitiveDataBeingExfiltratedChart";
  const { t } = useTranslation();
  const theme = getSystemTheme();
  const TopEndpointsSensitiveDataBeingExfiltratedChartConfig =
    getTopEndpointsSensitiveDataBeingExfiltratedChartConfig();
  const {
    analyticsFilter: {
      dateFilter: { startDate, endDate },
    },
  } = useContext(AnalyticsFilterContext);
  const { trigger, data, isMutating, error } = usePostMutation(
    API_ENDPOINTS.Z_INSIGHTS,
    {
      key: containerId,
    },
  );

  useEffect(() => {
    trigger(
      TopEndpointsSensitiveDataBeingExfiltratedChartPayload({
        startDate: secToMs(startDate),
        endDate: secToMs(endDate),
      }),
    );
  }, [startDate, endDate, trigger]);

  const responseData =
    data as TopEndpointsSensitiveDataBeingExfiltratedChartResponse;

  const config: VerticalBarDefaultConfigProps = {
    ...TopEndpointsSensitiveDataBeingExfiltratedChartConfig,
    axisProps: {
      ...TopEndpointsSensitiveDataBeingExfiltratedChartConfig?.axisProps,
      xAxisProps: {
        ...TopEndpointsSensitiveDataBeingExfiltratedChartConfig?.axisProps
          ?.xAxisProps,
      },
      yAxisTitleSetting: {
        ...TopEndpointsSensitiveDataBeingExfiltratedChartConfig?.axisProps
          ?.yAxisTitleSetting,
        text: t("DP_TOTAL_TRANSACTIONS_SENSITIVE_DATA"),
      },
    },
    columnProps: {
      ...TopEndpointsSensitiveDataBeingExfiltratedChartConfig?.columnProps,
    },
  };

  const tdata = [
    ...getStackBarData(
      TopEndpointsSensitiveDataBeingExfiltratedChartMockData,
      getBarStackColors(theme),
      toolTipColors,
      getTooltipData(t),
    ),
  ];

  return (
    <WithAnalyticsStates
      loading={isMockData ? undefined : isMutating}
      error={isMockData ? undefined : error}
      noData={
        isMockData
          ? false
          : !responseData?.data?.EDLP?.edlp_incidents_by_dlp_engine?.entries
              ?.length
      }
      id={ID}
      showBorder={false}
    >
      <TopEndpointsSensitiveDataBeingExfiltratedChart
        heading={t(TopEndpointsSensitiveDataBeingExfiltratedConfig.heading)}
        id={ID}
        chartData={{
          data: isMockData ? tdata : [],
          config,
          axisKeys: ["id", "value"],
          stacked: true,
          showScrollbar: false,
          theme,
        }}
      />
    </WithAnalyticsStates>
  );
};
