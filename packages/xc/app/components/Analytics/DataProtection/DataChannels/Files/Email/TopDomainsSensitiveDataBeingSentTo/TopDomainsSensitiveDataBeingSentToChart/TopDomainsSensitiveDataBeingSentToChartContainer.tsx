import { useTranslation } from "react-i18next";
import { useContext, useEffect } from "react";
import { type VerticalBarDefaultConfigProps } from "@up/components";
import {
  getTooltipData,
  getTopDomainsSensitiveDataBeingSentToChartConfig,
  toolTipColors,
  TopDomainsSensitiveDataBeingSentToChartPayload,
} from "./config";
import { TopDomainsSensitiveDataBeingSentToChart } from "./TopDomainsSensitiveDataBeingSentToChart";
import {
  type TopDomainsSensitiveDataBeingSentToChartResponse,
  type Props,
} from "./types";
import { TopDomainsSensitiveDataBeingSentToChartMockData } from "./mock.data";
import { getDataTestId, secToMs } from "@/utils/utils";
import { usePostMutation } from "@/utils/apiUtils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { getSystemTheme } from "@/utils/themeUtils";
import { API_ENDPOINTS } from "@/utils/apiHelper";
import { AnalyticsFilterContext } from "@/app/(post-onboarding)/(with-side-nav)/analytics/AnalyticsFilterContext";
import { getBarStackColors } from "@/utils/unifiedAnalytics/utils";
import { getStackBarData } from "@/components/Analytics/Charts/VerticalBarChart/utils";

export const TopDomainsSensitiveDataBeingSentToChartContainer = ({
  id,
  isMockData,
}: Props) => {
  const ID = getDataTestId("app-exp-trend", id);
  const containerId =
    "DataProtection.DataChannels.Endpoints.TopDomainsSensitiveDataBeingSentToChart";
  const { t } = useTranslation();
  const theme = getSystemTheme();
  const TopDomainsSensitiveDataBeingSentToChartConfig =
    getTopDomainsSensitiveDataBeingSentToChartConfig();
  const { trigger, data, isMutating, error } = usePostMutation(
    API_ENDPOINTS.Z_INSIGHTS,
    {
      key: containerId,
    },
  );
  const {
    analyticsFilter: {
      dateFilter: { startDate, endDate },
    },
  } = useContext(AnalyticsFilterContext);

  useEffect(() => {
    trigger(
      TopDomainsSensitiveDataBeingSentToChartPayload({
        startDate: secToMs(startDate),
        endDate: secToMs(endDate),
      }),
    );
  }, [startDate, endDate, trigger]);

  const responseData = data as TopDomainsSensitiveDataBeingSentToChartResponse;

  const config: VerticalBarDefaultConfigProps = {
    ...TopDomainsSensitiveDataBeingSentToChartConfig,
    axisProps: {
      ...TopDomainsSensitiveDataBeingSentToChartConfig?.axisProps,
      xAxisProps: {
        ...TopDomainsSensitiveDataBeingSentToChartConfig?.axisProps?.xAxisProps,
      },
      yAxisTitleSetting: {
        ...TopDomainsSensitiveDataBeingSentToChartConfig?.axisProps
          ?.yAxisTitleSetting,
        text: t("DP_TOTAL_TRANSACTIONS_SENSITIVE_DATA"),
      },
    },
    columnProps: {
      ...TopDomainsSensitiveDataBeingSentToChartConfig?.columnProps,
    },
  };

  const tdata = [
    ...getStackBarData(
      TopDomainsSensitiveDataBeingSentToChartMockData,
      getBarStackColors(theme),
      toolTipColors,
      getTooltipData(t),
    ),
  ];

  return (
    <WithAnalyticsStates
      loading={isMockData ? undefined : isMutating}
      error={isMockData ? undefined : error}
      noData={
        isMockData
          ? false
          : !responseData?.data?.DATA_PROTECTION_EMAIL
              ?.domains_for_sensitive_data?.entries?.length
      }
      id={ID}
      showBorder={false}
    >
      <TopDomainsSensitiveDataBeingSentToChart
        id={ID}
        chartData={{
          data: isMockData ? tdata : [],
          axisKeys: ["id", "value"],
          stacked: true,
          showScrollbar: false,
          config,
          theme,
        }}
      />
    </WithAnalyticsStates>
  );
};
