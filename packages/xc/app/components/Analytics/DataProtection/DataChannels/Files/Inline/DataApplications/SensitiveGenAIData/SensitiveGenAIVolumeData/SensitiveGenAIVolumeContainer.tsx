import { useContext, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { toMilliseconds } from "@up/std";
import { type DatavizDonutProps } from "@up/components";
import { SensitiveGenAIVolumeChart } from "./SensitiveGenAIVolumeChart";
import {
  type SensitiveGenAIResponse,
  type SensitiveGenAIDataContainerProps,
  type SensitiveGenAIVolumeMockDataProps,
} from "./types";
import {
  SensitiveGenAIVolumePayload,
  sensitiveGenAIDataVolumeConfig,
} from "./config";
import { SensitiveGenAIVolumeMockData } from "./mock.data";
import { usePostMutation } from "@/utils/apiUtils";
import { AnalyticsFilterContext } from "@/app/(post-onboarding)/(with-side-nav)/analytics/AnalyticsFilterContext";
import { formatNumber, getDataTestId, getDurationValue } from "@/utils/utils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { getSystemTheme } from "@/utils/themeUtils";
import useFetchETime from "@/hooks/useFetchETime";
import { useZIAEntitlement } from "@/hooks/useZIAEntitlement";
import { API_ENDPOINTS } from "@/utils/apiHelper";

export const SensitiveGenAIVolumeContainer = ({
  id,
  isMockData,
}: SensitiveGenAIDataContainerProps) => {
  const { t } = useTranslation();
  const theme = getSystemTheme();
  const containerId = "DataProtection.DataChannels.Inline.SensitiveGenAIVolume";
  const entitlementError = useZIAEntitlement(containerId);

  const {
    trigger: fetchSensitiveGenAIVolumeData,
    data,
    isMutating,
    error,
  } = usePostMutation(API_ENDPOINTS.Z_INSIGHTS, {
    key: containerId,
  });
  const responseData = data as SensitiveGenAIResponse;

  const {
    analyticsFilter: {
      dateFilter: {
        startDate,
        endDate,
        timeRangeSelected: { value },
      },
    },
  } = useContext(AnalyticsFilterContext);

  const [eTime, isLoading, eTimeError] = useFetchETime(
    getDurationValue(value).toString(),
    !entitlementError,
  );

  useEffect(() => {
    void fetchSensitiveGenAIVolumeData(
      SensitiveGenAIVolumePayload({
        startDate: toMilliseconds(startDate),
        endDate: toMilliseconds(endDate),
      }),
    );
  }, [startDate, endDate, eTime, fetchSensitiveGenAIVolumeData]);

  const transformVolumeResponseData = (
    rawResponse: Record<string, SensitiveGenAIVolumeMockDataProps> | null,
  ) => {
    const colorMap: Record<string, string> = {
      "Open AI": datavizColors[theme].dataviz.categorical.primary.chart01,
      ChatGPT: datavizColors[theme].dataviz.categorical.primary.chart02,
      Gemini: datavizColors[theme].dataviz.categorical.primary.chart03,
      Perplexity: datavizColors[theme].dataviz.categorical.primary.chart04,
      Others: datavizColors[theme].dataviz.neutrals.others,
    };

    let total = 0;
    const updatedData = rawResponse
      ? Object.entries(rawResponse)?.map(([category, data]) => {
          const count = Object.values(data).reduce((a, b) => a + b, 0);
          total += count;

          return {
            category: category,
            count: count,
            color: colorMap[category],
          };
        })
      : [];

    const config = sensitiveGenAIDataVolumeConfig();

    return {
      donutChartData: {
        config: {
          ...config,
          labelProps: {
            ...config.labelProps,
            centerLabel: {
              ...config.labelProps?.centerLabel,
              labels: [`${formatNumber(total)}`, t("TOTAL_VOLUME")],
            },
          },
        },
        dataKeys: ["count", "category"],
        data: { slice: updatedData },
        theme,
      } as DatavizDonutProps,
    };
  };

  const ID = getDataTestId("sensitive-gen-ai-volume", id);

  const sensitiveGenAIDataPropData = transformVolumeResponseData(
    SensitiveGenAIVolumeMockData,
  );

  return (
    <WithAnalyticsStates
      loading={isMockData ? undefined : (isLoading ?? isMutating)}
      error={isMockData ? undefined : (eTimeError ?? error)}
      noData={
        isMockData
          ? false
          : !responseData?.data?.DATA_PROTECTION_GENAI?.sensitive_data_by_app
              ?.entries?.length
      }
      showBorder={false}
      id={ID}
    >
      <SensitiveGenAIVolumeChart {...sensitiveGenAIDataPropData} id={ID} />
    </WithAnalyticsStates>
  );
};
