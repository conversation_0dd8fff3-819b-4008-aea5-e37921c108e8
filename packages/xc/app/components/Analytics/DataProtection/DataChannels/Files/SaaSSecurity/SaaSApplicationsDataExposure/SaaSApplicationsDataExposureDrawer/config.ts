import { getDatavizTooltip, type DatavizDonutProps } from "@up/components";
import { type SegmentControlOptionsProps } from "./types";
import { type ThemeTypes } from "@/context/UserPreferenceContext";

export const saaSApplicationsDataExposureConfig = () => {
  const baseConfig: DatavizDonutProps["config"] = {
    containerClass: "flex h-[400px]",
    chartProps: {
      chartContainer: {
        paddingBottom: 45,
        paddingTop: 10,
      },
    },
    legendProps: {
      legendMarkerProps: {
        height: 12,
        width: 12,
      },
      legendLabelProps: {
        paddingLeft: 4,
      },
      legendRectangleProps: {
        cornerRadiusTL: 3,
        cornerRadiusTR: 3,
        cornerRadiusBL: 3,
        cornerRadiusBR: 3,
      },
    },
    pieSetting: {
      strokeWidth: 1,
      tooltipPosition: "fixed",
      tooltipHTML: getDatavizTooltip([
        { label: "{category}" },
        { data: [{ label: "{count.formatNumber('#.# a')}" }] },
      ]),
    },
  };

  return baseConfig;
};

export const SegmentOptions = [
  {
    label: "DP_BY_CLASSIFICATION",
    value: "classification",
  },
  {
    label: "DP_BY_TYPE",
    value: "type",
  },
] as SegmentControlOptionsProps[];

export const saaSApplicationsDataExposureData = (theme: ThemeTypes) => ({
  classification: {
    total: 3805,
    donutChartData: {
      data: {
        slice: [
          {
            category: "Private",
            count: 1664,
            color: "#194cbb",
          },
          {
            category: "Internal",
            count: 1149,
            color: "#25bae2",
          },
          {
            category: "External",
            count: 992,
            color: "#9f46d7",
          },
        ],
      },
      dataKeys: ["count", "category"],
      config: saaSApplicationsDataExposureConfig(),
      theme,
    } as DatavizDonutProps,
  },
  type: {
    total: 5980,
    donutChartData: {
      data: {
        slice: [
          {
            category: "PCI",
            count: 1600,
            color: "#194cbb",
          },
          {
            category: "PHI",
            count: 1326,
            color: "#25bae2",
          },
          {
            category: "PII",
            count: 570,
            color: "#9f46d7",
          },
          {
            category: "Source Code",
            count: 884,
            color: "#3da592",
          },
          {
            category: "Others",
            count: 1600,
            color: "#bac2cf",
          },
        ],
      },
      dataKeys: ["count", "category"],
      config: saaSApplicationsDataExposureConfig(),
      theme,
    } as DatavizDonutProps,
  },
  segmentOptions: [...SegmentOptions],
});
