import { useContext, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { getDatavizTooltip, type DatavizDonutProps } from "@up/components";
import {
  type TotalEndpointIncidentsProps,
  type TotalEndpointIncidentsResponse,
} from "./types";
import {
  TotalEndpointIncidentsChartLegends,
  TotalEndpointIncidentsPayload,
  getTotalEndpointIncidentsChartConfig,
} from "./config";
import { TotalEndpointIncidents } from "./TotalEndpointIncidents";
import { TotalEndpointIncidentsMockData } from "./mock.data";
import { usePostMutation } from "@/utils/apiUtils";
import { formatNumber, getDataTestId, secToMs } from "@/utils/utils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { AnalyticsFilterContext } from "@/app/(post-onboarding)/(with-side-nav)/analytics/AnalyticsFilterContext";
import { getSystemTheme } from "@/utils/themeUtils";
import { API_ENDPOINTS } from "@/utils/apiHelper";

export const TotalEndpointIncidentsContainer = ({
  id,
  isMockData,
}: {
  id?: string;
  isMockData: boolean;
}) => {
  const ID = getDataTestId("dp-total-endpoint-incidents", id);
  const containerId =
    "DataProtection.DataChannels.Endpoints.TotalEndpointIncidents";
  const { t } = useTranslation();
  const theme = getSystemTheme();
  const {
    analyticsFilter: {
      dateFilter: { startDate, endDate },
    },
  } = useContext(AnalyticsFilterContext);
  const { trigger, data, isMutating, error } = usePostMutation(
    API_ENDPOINTS.Z_INSIGHTS,
    {
      key: containerId,
    },
  );
  const responseData = data as TotalEndpointIncidentsResponse;

  useEffect(() => {
    trigger(
      TotalEndpointIncidentsPayload({
        startDate: secToMs(startDate),
        endDate: secToMs(endDate),
      }),
    );
  }, [startDate, endDate, trigger]);

  const transformResponseData = (
    rawResponse: TotalEndpointIncidentsResponse,
  ): TotalEndpointIncidentsProps => {
    const payload =
      rawResponse?.data?.EDLP?.edlp_incidents_with_out_grouping?.entries ?? [];
    let totalThroughput = 0;
    rawResponse?.data?.EDLP?.edlp_incidents_with_out_grouping?.entries?.forEach(
      (i) => {
        totalThroughput += i.total;
      },
    );
    const updatedPayload = TotalEndpointIncidentsChartLegends(theme)
      .map((pie) => {
        const total = payload.find((a) => a.name === pie.category)?.total ?? 0;
        const val = total ? total / 1000 : 0;

        return {
          ...pie,
          count: val,
          category: t(pie.label),
          color: pie.iconColor,
        };
      })
      .filter((item) => item.count > 0);
    const total = +totalThroughput;

    const isEmpty = !totalThroughput;
    const config = getTotalEndpointIncidentsChartConfig(theme);

    return {
      donutChartData: {
        config: {
          ...config,
          labelProps: {
            ...config.labelProps,
            centerLabel: {
              ...config.labelProps?.centerLabel,
              labels: [formatNumber(total).toString(), t("TOTAL_INCIDENTS")],
            },
          },
          pieSetting: {
            ...config.pieSetting,
            tooltipHTML: getDatavizTooltip([
              {
                label: "{category}",
              },
              {
                data: [
                  {
                    label: `{count.formatNumber("#.a")} ${t("INCIDENTS")}`,
                  },
                ],
              },
            ]),
          },
          emptyProp: { showEmpty: isEmpty },
        },
        dataKeys: ["count", "category"],
        data: { slice: updatedPayload },
        theme,
      } as DatavizDonutProps,
    };
  };
  const heading = t("DP_TOTAL_ENDPOINT_INCIDENTS");

  return (
    <WithAnalyticsStates
      id={ID}
      title={heading}
      loading={isMockData ? undefined : isMutating}
      error={isMockData ? undefined : error}
      noData={
        isMockData
          ? false
          : !responseData?.data?.EDLP?.edlp_incidents_with_out_grouping?.entries
              ?.length
      }
    >
      {isMockData ? (
        <TotalEndpointIncidents
          id={ID}
          heading={heading}
          {...transformResponseData(TotalEndpointIncidentsMockData)}
        />
      ) : (
        <TotalEndpointIncidents
          heading={heading}
          donutChartData={{
            data: { slice: [] },
            theme,
            dataKeys:
              transformResponseData(responseData).donutChartData.dataKeys,
            config: transformResponseData(responseData).donutChartData.config,
            chartId: transformResponseData(responseData).donutChartData.chartId,
          }}
        />
      )}
    </WithAnalyticsStates>
  );
};
