import { type DatavizDonutProps } from "@up/components";

export type TotalEndpointIncidentsProps = {
  donutChartData: DatavizDonutProps;
  id?: string;
  heading?: string;
};

type EntriesProps = {
  name: string;
  total: number;
};

export type TotalEndpointIncidentsResponse = {
  data: {
    EDLP: {
      edlp_incidents_with_out_grouping: {
        entries: EntriesProps[];
      };
    };
  };
};

export type StatusResponseType = {
  deploymentStatus: {
    DISABLED: number;
    TOTAL: number;
    NOT_DEPLOYED: number;
    DEPLOYED: number;
  };
  statusDetails: Array<{
    lastModifiedTime: number;
    ecId: number;
    vmDisabled: boolean;
    autoScale: boolean;
    vmSize: string;
    errorCodes: string[];
    status: string;
    useWanDns: boolean;
    lastTemplateConfigPushFailed: boolean;
  }>;
};

export type PayloadProps = {
  startDate: number;
  endDate: number;
};
