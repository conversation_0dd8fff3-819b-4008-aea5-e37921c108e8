import { Donut } from "@up/components";
import { type TotalEndpointIncidentsProps } from "./types";
import { Card } from "@/components/Analytics/Card";

const TotalEndpointIncidents = ({
  donutChartData,
  heading,
  id,
}: TotalEndpointIncidentsProps) => (
  <Card id={id}>
    <Card.Header>
      <Card.Header.Title id={id}>{heading}</Card.Header.Title>
    </Card.Header>
    <Card.Content className="overflow-x-hidden">
      <Donut {...donutChartData} chartId={`${id}-donut-chart`} />
    </Card.Content>
  </Card>
);

export { TotalEndpointIncidents };
