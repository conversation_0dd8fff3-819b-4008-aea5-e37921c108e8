import { useContext, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { toMilliseconds } from "@up/std";
import { type DatavizDonutProps } from "@up/components";
import {
  type SensitiveGenAITransactionResponse,
  type SensitiveGenAIDataContainerProps,
  type SensitiveGenAITransactionMockDataProps,
} from "./types";
import {
  SensitiveGenAITransactionPayload,
  sensitiveGenAIDataTransationConfig,
} from "./config";
import { SensitiveGenAITransactionMockData } from "./mock.data";
import { SensitiveGenAITransactionChart } from "./SensitiveGenAITransactionChart";
import { usePostMutation } from "@/utils/apiUtils";
import { AnalyticsFilterContext } from "@/app/(post-onboarding)/(with-side-nav)/analytics/AnalyticsFilterContext";
import { formatNumber, getDataTestId, getDurationValue } from "@/utils/utils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { getSystemTheme } from "@/utils/themeUtils";
import useFetchETime from "@/hooks/useFetchETime";
import { useZIAEntitlement } from "@/hooks/useZIAEntitlement";
import { API_ENDPOINTS } from "@/utils/apiHelper";

export const SensitiveGenAITransactionContainer = ({
  id,
  isMockData,
}: SensitiveGenAIDataContainerProps) => {
  const { t } = useTranslation();
  const theme = getSystemTheme();
  const containerId =
    "DataProtection.DataChannels.Inline.SensitiveGenAITransaction";
  const entitlementError = useZIAEntitlement(containerId);

  const {
    trigger: fetchSensitiveGenAITransactionData,
    data,
    isMutating,
    error,
  } = usePostMutation(API_ENDPOINTS.Z_INSIGHTS, {
    key: containerId,
  });
  const responseData = data as SensitiveGenAITransactionResponse;

  const {
    analyticsFilter: {
      dateFilter: {
        startDate,
        endDate,
        timeRangeSelected: { value },
      },
    },
  } = useContext(AnalyticsFilterContext);

  const [eTime, isLoading, eTimeError] = useFetchETime(
    getDurationValue(value).toString(),
    !entitlementError,
  );

  useEffect(() => {
    void fetchSensitiveGenAITransactionData(
      SensitiveGenAITransactionPayload({
        startDate: toMilliseconds(startDate),
        endDate: toMilliseconds(endDate),
      }),
    );
  }, [startDate, endDate, eTime, fetchSensitiveGenAITransactionData]);

  const transformTransactionResponseData = (
    rawResponse: Record<string, SensitiveGenAITransactionMockDataProps> | null,
  ) => {
    const colorMap: Record<string, string> = {
      "Open AI": datavizColors[theme].dataviz.categorical.primary.chart01,
      ChatGPT: datavizColors[theme].dataviz.categorical.primary.chart02,
      Gemini: datavizColors[theme].dataviz.categorical.primary.chart03,
      Perplexity: datavizColors[theme].dataviz.categorical.primary.chart04,
      Others: datavizColors[theme].dataviz.neutrals.others,
    };

    let total = 0;
    const updatedData = rawResponse
      ? Object.entries(rawResponse)?.map(([category, data]) => {
          const count = Object.values(data).reduce((a, b) => a + b, 0);
          total += count;

          return {
            category: category,
            count: count,
            color: colorMap[category],
          };
        })
      : [];

    const config = sensitiveGenAIDataTransationConfig(theme);

    return {
      donutChartData: {
        config: {
          ...config,
          labelProps: {
            ...config.labelProps,
            centerLabel: {
              ...config.labelProps?.centerLabel,
              labels: [`${formatNumber(total)}`, t("TOTAL_TRANSACTIONS")],
            },
          },
        },
        dataKeys: ["count", "category"],
        data: { slice: updatedData },
        theme,
      } as DatavizDonutProps,
    };
  };

  const ID = getDataTestId("sensitive-gen-ai-trans", id);

  const sensitiveGenAIDataPropData = transformTransactionResponseData(
    SensitiveGenAITransactionMockData,
  );

  return (
    <WithAnalyticsStates
      loading={isMockData ? undefined : (isLoading ?? isMutating)}
      error={isMockData ? undefined : (eTimeError ?? error)}
      noData={
        isMockData
          ? false
          : !responseData?.data?.DATA_PROTECTION_GENAI?.sensitive_data_by_app
              ?.entries?.length
      }
      showBorder={false}
      id={ID}
    >
      <SensitiveGenAITransactionChart {...sensitiveGenAIDataPropData} id={ID} />
    </WithAnalyticsStates>
  );
};
