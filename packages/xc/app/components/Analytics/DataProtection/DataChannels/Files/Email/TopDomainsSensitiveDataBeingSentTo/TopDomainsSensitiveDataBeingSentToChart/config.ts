import {
  type DatavizTooltipDataType,
  type DatavizVerticalBarProps,
} from "@up/components";
import { type PayloadProps } from "./types";
import { formatNumber, upperCaseFirst } from "@/utils/utils";
import { type TooltipDataProp } from "@/components/Analytics/Charts/ChartUtils/TooltipUtils/types";

export const toolTipColors: Record<string, string> = {
  pci: "text-dataviz-categorical-primary-chart01",
  phi: "text-dataviz-categorical-primary-chart02",
  pii: "text-dataviz-categorical-primary-chart03",
};

export const getTopDomainsSensitiveDataBeingSentToChartConfig =
  (): DatavizVerticalBarProps["config"] => ({
    numberFormatter: {
      numberFormat: "#.#a",
      bigNumberPrefixes: [
        { number: 1e3, suffix: "K" },
        { number: 1e6, suffix: "M" },
        { number: 1e9, suffix: "B" },
      ],
    },
    containerClass: "flex h-[340px]",
    legendsProps: {
      showLegend: true,
      customLegend: true,
      legendsData: [
        {
          name: `
            <div class="flex items-center">
              <i class="fa-solid fa-square text-dataviz-categorical-primary-chart01 text-[12px] pr-xs"></i>  <span class="typography-paragraph1-strong text-semantic-content-base-primary">PCI</span>
            <div>
          `,
        },
        {
          name: `
            <div class="flex items-center">
              <i class="fa-solid fa-square text-dataviz-categorical-primary-chart02 text-[12px] pr-xs"></i>  <span class="typography-paragraph1-strong text-semantic-content-base-primary">PHI</span>
            <div>
          `,
        },
        {
          name: `
            <div class="flex items-center">
              <i class="fa-solid fa-square text-dataviz-categorical-primary-chart03 text-[12px] pr-xs"></i>  <span class="typography-paragraph1-strong text-semantic-content-base-primary">PII</span>
            <div>
          `,
        },
      ],
      legendSetting: {
        clickTarget: "none",
      },
      legendMarkerProps: {
        width: 12,
        height: 12,
      },
    },
    axisProps: {
      xLabelProps: {
        paddingTop: 8,
      },
      yAxisSetting: {
        maxPrecision: 0,
      },
      yAxisProps: {
        visible: true,
        minGridDistance: 25,
      },
      yAxisTitleSetting: {
        text: "Total Transactions",
        rotation: 270,
        labelColor: "#767676",
        fontWeight: "400",
        fontStyle: "normal",
        fontSize: "14px",
      },
    },
    roundedColumnProps: {
      cornerRadiusTL: 0,
      cornerRadiusTR: 0,
    },
    columnProps: {
      width: 30,
      cursorOverStyle: "pointer",
    },
    tooltipProps: {
      tooltipSetting: {
        pointerOrientation: "left",
      },
    },
  });

export function getTopDomainsSensitiveDataBeingSentToTooltipData(
  t: (s: string) => string,
) {
  return (nums: TooltipDataProp, toolTipColors: Record<string, string>) => [
    {
      key: "heading",
      title: `${nums.tooltipHeading}`,
      classes: " text-semantic-content-base-primary",
      data: [
        {
          key: "data1",
          classes: "",
          data: Object.keys(nums.data).map((key) => {
            const value = nums.data[key];

            return {
              iconClass: `fa-solid fa-circle ${toolTipColors[key]} text-[10px] pr-xs`,
              title: upperCaseFirst(t(key.toUpperCase())),
              titleClass: "text-semantic-content-base-primary",
              value: value,
              valueClass: "pr-xs text-semantic-content-base-tertiary",
              text: upperCaseFirst(t(key.toUpperCase())),
              textClass:
                "typography-paragraph2-strong text-semantic-content-base-tertiary content-end pb-xs",
            };
          }),
        },
      ],
    },
  ];
}

export const TopDomainsSensitiveDataBeingSentToChartPayload = ({
  startDate,
  endDate,
}: PayloadProps) => ({
  query: `query DATA_PROTECTION_EMAIL {
    DATA_PROTECTION_EMAIL {
        domains_for_sensitive_data(
            end_time: ${endDate}
            start_time: ${startDate}
            traffic_unit: TRANSACTIONS
        ) {
            entries {
                domain
                total
                name
            }
        }
    }
}`,
});

export function getTooltipData(t: (s: string) => string) {
  return (
    nums: TooltipDataProp,
    toolTipColors: Record<string, string>,
  ): DatavizTooltipDataType[] => [
    {
      label: `<span class=" typography-paragraph2-strong text-semantic-content-base-primary pb-xs">${formatNumber(Number(nums.total)) ?? 0} ${t("TOTAL_TRANSACTIONS")}</span>`,
      data: Object.keys(nums.data).map((key) => ({
        label: `<span class=""> 
                 <i class='fa-solid fa-circle ${toolTipColors[key]} accent-red-content-icon text-[10px] pr-xs' >
                 </i> ${t(key.toUpperCase())}
                 </span>`,
        value: `${nums.data[key]}`,
      })),
    },
  ];
}
