import { useState } from "react";
import { useTranslation } from "react-i18next";
import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faXmark } from "@fortawesome/pro-solid-svg-icons";
import { type DatavizDonutProps } from "@up/components";
import {
  type DataCategoryClassificationProps,
  type DataCategoryTypeProps,
} from "../types";
import {
  type SaaSApplicationsDataExposureDrawerContainerProps,
  type SaaSApplicationsDataExposureDrawerDataResponse,
  type TabTypeProps,
} from "./types";
import { saaSApplicationsDataExposureConfig, SegmentOptions } from "./config";
import { SaaSApplicationsDataExposureDrawer } from "./SaaSApplicationsDataExposureDrawer";
import { formatNumber, getDataTestId, handleKeyboardDown } from "@/utils/utils";
import { CLASSIFICATION } from "@/configs/constants/analytics";
import { getSystemTheme } from "@/utils/themeUtils";
import SegmentControl from "@/components/Analytics/SegmentedControl/SegmentedControl";

export const SaaSApplicationsDataExposureDrawerContainer = ({
  id,
  responseData,
  handleCloseDrawer,
}: SaaSApplicationsDataExposureDrawerContainerProps) => {
  const { t } = useTranslation();
  const theme = getSystemTheme();
  const [selectedTab, setSelectedTab] = useState<TabTypeProps>(CLASSIFICATION);

  const transformClassificationResponseData = (
    rawResponse: SaaSApplicationsDataExposureDrawerDataResponse | null,
  ) => {
    const colorMap: Record<string, string> = {
      Private: datavizColors[theme].dataviz.categorical.primary.chart01,
      Internal: datavizColors[theme].dataviz.categorical.primary.chart02,
      External: datavizColors[theme].dataviz.categorical.primary.chart03,
    };
    let total = 0;
    const updatedData = rawResponse?.classification
      ? Object.entries(rawResponse?.classification).map(([category, data]) => {
          const count = Object.values(data).reduce((a, b) => a + b, 0);
          total += count;

          return {
            category: category,
            count: count,
            color: colorMap[category],
          };
        })
      : [];

    const config = saaSApplicationsDataExposureConfig();

    return {
      donutChartData: {
        config: {
          ...config,
          labelProps: {
            ...config.labelProps,
            centerLabel: {
              ...config.labelProps?.centerLabel,
              labels: [`${formatNumber(total)}`, t("TOTAL_INCIDENTS")],
            },
          },
        },
        dataKeys: ["count", "category"],
        data: { slice: updatedData },
        theme,
      } as DatavizDonutProps,
    };
  };

  const transformTypeResponseData = (
    rawResponse: SaaSApplicationsDataExposureDrawerDataResponse | null,
  ) => {
    const colorMap: Record<string, string> = {
      PCI: datavizColors[theme].dataviz.categorical.primary.chart01,
      PHI: datavizColors[theme].dataviz.categorical.primary.chart02,
      PII: datavizColors[theme].dataviz.categorical.primary.chart03,
      "Source Code": datavizColors[theme].dataviz.categorical.primary.chart04,
      Others: datavizColors[theme].dataviz.neutrals.others,
    };

    let total = 0;
    const updatedData = rawResponse?.type
      ? Object.entries(rawResponse?.type).map(([category, data]) => {
          const count = Object.values(data).reduce((a, b) => a + b, 0);
          total += count;

          return {
            category: category,
            count: count,
            color: colorMap[category],
          };
        })
      : [];

    const config = saaSApplicationsDataExposureConfig();

    return {
      donutChartData: {
        config: {
          ...config,
          labelProps: {
            ...config.labelProps,
            centerLabel: {
              ...config.labelProps?.centerLabel,
              labels: [`${formatNumber(total)}`, t("TOTAL_INCIDENTS")],
            },
          },
        },
        dataKeys: ["count", "category"],
        data: { slice: updatedData },
        theme,
      } as DatavizDonutProps,
    };
  };

  const ID = getDataTestId(selectedTab, id);

  const SaaSApplicationsDataExposureData =
    selectedTab === CLASSIFICATION
      ? transformClassificationResponseData(responseData)
      : transformTypeResponseData(responseData);
  const selectedTabData =
    selectedTab === CLASSIFICATION
      ? responseData?.classification
      : responseData?.type;

  return (
    <div
      className="flex flex-col h-full justify-between p-rem-240 gap-xxl bg-semantic-surface-inverted-base-primary"
      data-testid={ID}
    >
      <div className="flex flex-col gap-rem-80">
        <div className="flex flex-row justify-between items-center">
          <div className="inline-flex gap-rem-80 typography-header5 text-semantic-content-base-primary">
            {responseData?.CustomIcon && <responseData.CustomIcon />}
            {responseData?.applicationTenantName}
          </div>
          <FontAwesomeIcon
            icon={faXmark}
            aria-label={t("CLOSE_ICON")}
            role="button"
            className="text-semantic-brand-default cursor-pointer focus-visible-default block"
            onClick={handleCloseDrawer}
            onKeyDown={handleKeyboardDown(handleCloseDrawer)}
            tabIndex={0}
          />
        </div>
        <div className="typography-paragraph2 text-semantic-content-base-tertiary">
          {responseData?.totalIncidents} {t("TOTAL_INCIDENTS")}
        </div>
      </div>
      <div className="flex flex-col mb-rem-120">
        <div className="flex justify-center" id={ID}>
          <SegmentControl
            value={selectedTab}
            onChange={(value) => setSelectedTab(value as TabTypeProps)}
            options={SegmentOptions}
            customClass="flex h-full"
            id={ID}
          />
        </div>
        <div className="flex flex-col gap-rem-160 mt-rem-200">
          <SaaSApplicationsDataExposureDrawer
            {...SaaSApplicationsDataExposureData}
            id={ID}
          />

          {selectedTabData &&
            Object?.entries(selectedTabData)?.map(([category, data], index) => (
              <div
                className="flex flex-col border-t border-semantic-border-base-primary"
                key={index}
              >
                <div className="flex flex-col gap-rem-80 mt-rem-160">
                  <div className="typography-paragraph1-strong text-semantic-content-base-primary">
                    {category}
                  </div>
                  {Object?.entries(
                    data as
                      | Record<string, DataCategoryClassificationProps>
                      | Record<string, DataCategoryTypeProps>,
                  )?.map(([category, data], index) => (
                    <div
                      className="inline-flex justify-start mb-rem gap-rem-240"
                      key={index}
                    >
                      <div className="typography-paragraph2 text-semantic-content-base-tertiary w-[50%] break-all">
                        {category}
                      </div>

                      <div className="typography-paragraph2 text-semantic-content-base-primary">
                        {typeof data === "number" && data}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};
