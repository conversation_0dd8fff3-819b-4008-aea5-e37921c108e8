import {
  type VerticalBarDefaultConfigProps,
  type DatavizTooltipDataType,
} from "@up/components";
import { type PayloadProps } from "./types";
import { type TooltipDataProp } from "@/components/Analytics/Charts/ChartUtils/TooltipUtils/types";

export const toolTipColors: Record<string, string> = {
  pci: "text-dataviz-categorical-primary-chart01",
  phi: "text-dataviz-categorical-primary-chart02",
  pii: "text-dataviz-categorical-primary-chart03",
};

export const getTopEndpointsSensitiveDataBeingExfiltratedChartConfig =
  (): VerticalBarDefaultConfigProps => ({
    containerClass: "flex h-[340px]",
    numberFormatter: {
      numberFormat: "#.#a",
      bigNumberPrefixes: [
        { number: 1e3, suffix: "K" },
        { number: 1e6, suffix: "M" },
        { number: 1e9, suffix: "B" },
      ],
    },
    legendsProps: {
      showLegend: true,
      customLegend: true,
      legendsData: [
        {
          name: `
            <div class="flex items-center">
              <i class="fa-solid fa-square text-dataviz-categorical-primary-chart01 text-[12px] pr-xs"></i>  <span class="typography-paragraph1-strong text-semantic-content-base-primary">PCI</span>
            <div>
          `,
        },
        {
          name: `
            <div class="flex items-center">
              <i class="fa-solid fa-square text-dataviz-categorical-primary-chart02 text-[12px] pr-xs"></i>  <span class="typography-paragraph1-strong text-semantic-content-base-primary">PHI</span>
            <div>
          `,
        },
        {
          name: `
            <div class="flex items-center">
              <i class="fa-solid fa-square text-dataviz-categorical-primary-chart03 text-[12px] pr-xs"></i>  <span class="typography-paragraph1-strong text-semantic-content-base-primary">PII</span>
            <div>
          `,
        },
      ],
      legendSetting: {
        clickTarget: "none",
      },
      legendMarkerProps: {
        width: 12,
        height: 12,
      },
    },
    axisProps: {
      xLabelProps: {
        paddingTop: 8,
      },
      xGridProps: { visible: false },
      yAxisSetting: {
        maxPrecision: 0,
      },
      yAxisProps: {
        visible: true,
        minGridDistance: 25,
      },
      yAxisTitleSetting: {
        text: "Total Transactions",
        rotation: 270,
        labelColor: "#767676",
        fontWeight: "400",
        fontStyle: "normal",
        fontSize: "14px",
      },
    },
    roundedColumnProps: {
      cornerRadiusTL: 0,
      cornerRadiusTR: 0,
    },
    columnProps: {
      width: 30,
      cursorOverStyle: "pointer",
    },
    tooltipProps: {
      tooltipSetting: {
        pointerOrientation: "left",
      },
    },
  });

export const TopEndpointsSensitiveDataBeingExfiltratedChartPayload = ({
  startDate,
  endDate,
}: PayloadProps) => ({
  query: `query fetchLocation {
  EDLP {
    edlp_incidents_by_dlp_engine(
      start_time: ${startDate}
      end_time: ${endDate}
    ) {
      entries{
        name
        total
      }
    }
  }
}`,
});

export function getTooltipData(t: (s: string) => string) {
  return (
    nums: TooltipDataProp,
    toolTipColors: Record<string, string>,
  ): DatavizTooltipDataType[] => [
    {
      label: `<span class=" typography-paragraph2-strong text-semantic-content-base-primary pb-xs">${nums.total} ${t("TOTAL_TRANSACTIONS")}</span>`,
      data: Object.keys(nums.data).map((key) => ({
        label: `<span class=""> 
                 <i class='fa-solid fa-circle ${toolTipColors[key]} accent-red-content-icon text-default' ></i>
                  <span class="ml-default"> 
                  ${t(key.toUpperCase())}
                  </span>
                 </span>
                 `,
        value: `${nums.data[key]}`,
      })),
    },
  ];
}
