import { type DatavizDonutProps } from "@up/components";

export type SensitiveGenAITransactionChartProps = {
  donutChartData: DatavizDonutProps;
  id?: string;
};

export type SegmentControlOptionsProps = {
  label: string;
  value: string;
};

export type SensitiveGenAIDataContainerProps = {
  id?: string;
  isMockData: boolean;
};

export type SegmentControlProps = {
  totalCountTitle: string;
  totalCount: string;
  donutChartData: DatavizDonutProps;
};

export type SensitiveGenAIDataWidgetProps = {
  transactionData: SegmentControlProps;
  segmentOptions: SegmentControlOptionsProps[];
};

export type PayloadProps = {
  eTime: number;
};

export type SensitiveGenAITransactionMockDataProps = {
  total: number;
};

type EntriesProps = {
  name: string;
  total: number;
};

export type SensitiveGenAITransactionResponse = {
  data: {
    DATA_PROTECTION_GENAI: {
      sensitive_data_by_app: {
        entries: EntriesProps[];
      };
    };
  };
};
