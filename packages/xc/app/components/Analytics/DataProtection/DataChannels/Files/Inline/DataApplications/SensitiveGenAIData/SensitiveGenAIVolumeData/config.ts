import { getDatavizTooltip, type DatavizDonutProps } from "@up/components";
import { SegmentOptions } from "../config";
import { type SensitiveGenAIDataWidgetProps } from "./types";
import { type PayloadProps } from "@/components/Analytics/DataProtection/types";

export const sensitiveGenAIDataVolumeConfig = () => {
  const baseConfig: DatavizDonutProps["config"] = {
    containerClass: "flex h-[290px]",
    chartProps: {
      chartContainer: {
        paddingBottom: 45,
        paddingTop: 10,
      },
    },
    legendProps: {
      legendMarkerProps: {
        height: 12,
        width: 12,
      },
      legendLabelProps: {
        paddingLeft: 4,
      },
      legendRectangleProps: {
        cornerRadiusTL: 3,
        cornerRadiusTR: 3,
        cornerRadiusBL: 3,
        cornerRadiusBR: 3,
      },
    },
    pieSetting: {
      tooltipHTML: getDatavizTooltip([
        { label: "{category}" },
        { data: [{ label: "{count.formatNumber()}" }] },
      ]),
    },
  };

  return baseConfig;
};

export const sensitiveGenAIVolumeData = (): SensitiveGenAIDataWidgetProps => ({
  volumeData: {
    totalCountTitle: "TOTAL_THREATS_BLOCKED",
    totalCount: "0",
    donutChartData: {
      data: {
        slice: [
          {
            category: "Open AI",
            count: 500,
            color: "#194cbb",
          },
          {
            category: "ChatGPT",
            count: 400,
            color: "#25bae2",
          },
          {
            category: "Gemini",
            count: 200,
            color: "#9f46d7",
          },
          {
            category: "Perplexity",
            count: 100,
            color: "#3da592",
          },
          {
            category: "Others",
            count: 100,
            color: "#8590A6",
          },
        ],
      },
      dataKeys: ["count", "category"],
      config: sensitiveGenAIDataVolumeConfig(),
    } as DatavizDonutProps,
  },
  segmentOptions: [...SegmentOptions],
});

export const SensitiveGenAIVolumePayload = ({
  startDate,
  endDate,
}: PayloadProps) => ({
  query: `query DATA_PROTECTION_GENAI {
    DATA_PROTECTION_GENAI {
        sensitive_data_by_app(
            start_time: ${startDate}
            end_time: ${endDate}
            traffic_unit: BYTES
        ) {
            obfuscated
            entries {
                name
                total
            }
        }
    }
  }`,
});
