import { t } from "i18next";
import {
  type DatavizTooltipDataType,
  type DatavizVerticalBarProps,
} from "@up/components";
import { generateSessionId, upperCaseFirst } from "@/utils/utils";
import { type TooltipDataProp } from "@/components/Analytics/Charts/ChartUtils/TooltipUtils/types";

export const toolTipColors: Record<string, string> = {
  good: "text-dataviz-severity-primary-lowest",
  okay: "text-dataviz-severity-primary-medium",
  poor: "text-dataviz-severity-primary-high",
};

export const applicationsExperienceTrendCardConfig = {
  heading: "APPLICATION_EXPERIENCE_TREND",
};

export const seriesDXData = (isDarkMode = false) => [
  {
    label: t("POOR"),
    propertyName: "poor",
    colorCode: isDarkMode ? "#BF392F" : "#DC362E",
  },
  {
    label: t("OKAY"),
    propertyName: "okay",
    colorCode: isDarkMode ? "#D78320" : "#F19325",
  },
  {
    label: t("GOOD"),
    propertyName: "good",
    colorCode: isDarkMode ? "#389685" : "#3DA592",
  },
];

export const getApplicationsExperienceTrendConfig = (): Omit<
  DatavizVerticalBarProps,
  "data"
> => ({
  axisKeys: ["id", "value"],
  stacked: true,
  showScrollbar: true,
  config: {
    containerClass: "flex h-[400px]",
    legendsProps: {
      showLegend: true,
      customLegend: true,
      legendsData: [
        {
          name: `
            <div class="flex items-center" >
              <i class="fa-solid fa-circle text-dataviz-severity-primary-high accent-red-content-icon text-[10px] pr-xs"></i>  <span class="text-semantic-content-base-primary">Poor (0-33)</span>
            <div>
          `,
        },
        {
          name: `
            <div class="flex items-center" >
              <i class="fa-solid fa-circle text-dataviz-severity-primary-medium accent-red-content-icon text-[10px] pr-xs"></i>  <span class="text-semantic-content-base-primary">Okay (34-65)</span>
            <div>
          `,
        },
        {
          name: `
            <div class="flex items-center" >
              <i class="fa-solid fa-circle text-dataviz-severity-primary-lowest accent-red-content-icon text-[10px] pr-xs"></i>  <span class="text-semantic-content-base-primary">Good (66-100)</span>
            <div>
          `,
        },
      ],
      legendSetting: {
        clickTarget: "none",
      },
      legendMarkerProps: {
        width: 12,
        height: 12,
      },
    },
    axisProps: {
      xLabelProps: {
        paddingTop: 8,
      },
      yAxisSetting: {
        maxPrecision: 0,
      },
      yAxisProps: {
        visible: true,
        minGridDistance: 25,
      },
      yAxisTitleSetting: {
        text: "APPLICATIONS",
        rotation: 270,
        labelColor: "#767676",
        fontWeight: "400",
        fontStyle: "normal",
        fontSize: "14px",
      },
    },
    roundedColumnProps: {
      cornerRadiusTL: 0,
      cornerRadiusTR: 0,
    },
    columnProps: {
      width: 30,
      tooltipHTML: ` `,
      cursorOverStyle: "pointer",
    },
    tooltipProps: {
      tooltipSetting: {
        pointerOrientation: "left",
      },
    },
  },
});

export function getDigitalExperienceVerticalBarTooltipData(
  t: (s: string) => string,
) {
  return (
    nums: TooltipDataProp,
    toolTipColors: Record<string, string>,
  ): DatavizTooltipDataType[] => [
    {
      label: `<span class=" typography-paragraph1-strong text-semantic-content-base-primary  pb-xs">${nums.id}</span>`,
      data: Object.keys(nums.data).map((key) => ({
        label: `<span class=""> 
                 <i class='fa-solid fa-circle ${toolTipColors[key]} accent-red-content-icon text-[10px] pr-xs' ></i> 
                 <span class="typography-paragraph1">
                    ${upperCaseFirst(t(key.toUpperCase() + "_LABEL"))}
                 </span>
                 </span>`,
        value: `${nums.data[key]}`,
      })),
    },
  ];
}

export const appDashboardPayload = {
  pageid: "APPS_DASHBOARD",
  widgetid: "APPS_OVERVIEW",
  sessionId: generateSessionId(),
  top: [
    {
      view: "web_union_cqm_view",
      dimension: ["app_id", "loc_id"],
      aggregate: [{ metric: "upm_app_score", agg: "avg" }],
      orderby: [
        { metric: "upm_app_score", order: "asc" },
        { metric: "loc_id", order: "asc" },
      ],
      limit: 1,
    },
    {
      view: "web_union_cqm_view",
      dimension: ["app_id", "dept_id"],
      aggregate: [{ metric: "upm_app_score", agg: "avg" }],
      orderby: [
        { metric: "upm_app_score", order: "asc" },
        { metric: "dept_id", order: "asc" },
      ],
      limit: 1,
    },
    {
      view: "web_union_cqm_view",
      dimension: ["app_id", "rgn_code", "rgn_region", "rgn_category"],
      aggregate: [{ metric: "upm_app_score" }],
      orderby: [
        { metric: "upm_app_score", order: "asc" },
        { metric: "rgn_region", order: "asc" },
      ],
      limit: 1,
    },
    {
      view: "web_union_cqm_view",
      dimension: ["app_id"],
      aggregate: [{ metric: "upm_app_score", agg: "avg" }],
      orderby: [
        { metric: "upm_app_score", order: "asc" },
        { metric: "app_id", order: "asc" },
      ],
      limit: 1,
    },
  ],
  trend: [
    {
      view: "web_union_cqm_view",
      aggregate: [
        {
          metric: "poor_app_count",
        },
        {
          metric: "ok_app_count",
        },
        {
          metric: "good_app_count",
        },
      ],
      interval: "240m",
    },
  ],
};
