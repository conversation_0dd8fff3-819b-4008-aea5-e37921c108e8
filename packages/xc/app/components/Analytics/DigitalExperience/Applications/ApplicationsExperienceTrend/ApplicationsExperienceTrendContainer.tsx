import { useTranslation } from "react-i18next";
import { type DatavizVerticalBarProps } from "@up/components";
import { applicationTablePayload } from "../ApplicationsTable/config";
import { type ApplicationsTableResponse } from "../ApplicationsTable/types";
import {
  applicationsExperienceTrendCardConfig,
  getApplicationsExperienceTrendConfig,
  toolTipColors,
  getDigitalExperienceVerticalBarTooltipData,
} from "./config";
import { ApplicationsExperienceTrend } from "./ApplicationsExperienceTrend";
import { type ScoreValProps, type ScoreProps, type TDataProps } from "./types";
import {
  formatXAxisDateTime,
  getDataTestId,
  getGridConfigByDataLength,
  parseDateWithYearAndLabelShortFormat,
} from "@/utils/utils";
import { DigitalExperience } from "@/configs/urls/analytics/digital-experience";
import { usePostMutation } from "@/utils/apiUtils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import useAnalyticsFilterEffect from "@/hooks/useAnalyticsFilterEffect";
import { FILTER_APPLICATION_CONFIG } from "@/components/pages/Analytics/Dashboard/Filters/ModalPillGroupContainer/config";
import { BAR_GRAPH, ZDX_SCORE_LOOKUP } from "@/configs/constants/analytics";
import { useZDXEntitlement } from "@/hooks/useZDXEntitlement";
import { useZDXInterval } from "@/hooks/useZDXInterval";
import { type VStackBarChartDataType } from "@/components/Analytics/Charts/VerticalBarChart/types";
import { getStackBarData } from "@/components/Analytics/Charts/VerticalBarChart/utils";
import { getStackColors } from "@/utils/unifiedAnalytics/utils";
import { getSystemTheme } from "@/utils/themeUtils";

/**
 * when number of bars is greater than 30, i'll show 0 to 15 bars in initial scrollbar
 * */
const THRESHOLD_BARS_TO_SHOW_PRE_ZOOM = 30;
const FIRST_CATEGORY_INDEX_IN_PRE_ZOOM = 0;
const LAST_CATEGORY_INDEX_IN_PRE_ZOOM = 15;
const THRESHOLD_BAR_TO_SHOW_SCROLLBAR = 15;

type Props = {
  id?: string;
};

export const ApplicationsExperienceTrendContainer = ({ id }: Props) => {
  const ID = getDataTestId("app-exp-trend", id);
  const containerId = "DigitalExperience.Applications.ApplicationsTable";
  const HAS_READ_PERMISSION = useZDXEntitlement(containerId);
  const { t } = useTranslation();
  const theme = getSystemTheme();
  const applicationsExperienceTrendConfig =
    getApplicationsExperienceTrendConfig();
  const { trigger, data, isMutating, error } = usePostMutation(
    DigitalExperience.Applications.ApplicationsTable,
    {
      key: containerId,
    },
  );
  const interval = useZDXInterval(BAR_GRAPH);

  useAnalyticsFilterEffect(
    trigger,
    HAS_READ_PERMISSION === "",
    applicationTablePayload(interval),
    FILTER_APPLICATION_CONFIG,
    [],
    {},
    BAR_GRAPH,
  );

  const transformResponseData = (
    rawResponse: ApplicationsTableResponse,
  ): DatavizVerticalBarProps => {
    let tData: TDataProps = {};
    rawResponse?.rows?.map((row) => {
      row?.trendPayload?.datapoints?.upm_app_score?.map((score) => {
        const value = score[0];
        const timestamp = score[1];
        if (value > -1) {
          if (timestamp in tData) {
            const valData = tData[timestamp] as unknown as ScoreProps;
            if (
              value >= ZDX_SCORE_LOOKUP.Poor.range[0] &&
              value <= ZDX_SCORE_LOOKUP.Poor.range[1]
            ) {
              valData.poor = (valData.poor ?? 0) + 1;
            } else if (
              value >= ZDX_SCORE_LOOKUP.Okay.range[0] &&
              value <= ZDX_SCORE_LOOKUP.Okay.range[1]
            ) {
              valData.okay = (valData.okay ?? 0) + 1;
            } else if (
              value >= ZDX_SCORE_LOOKUP.Good.range[0] &&
              value <= ZDX_SCORE_LOOKUP.Good.range[1]
            ) {
              valData.good = (valData.good ?? 0) + 1;
            }
          } else {
            const val: ScoreValProps = {
              [timestamp]: {
                date: timestamp,
              },
            };
            if (
              value >= ZDX_SCORE_LOOKUP.Poor.range[0] &&
              value <= ZDX_SCORE_LOOKUP.Poor.range[1]
            ) {
              val[timestamp].poor = 1;
            } else if (
              value >= ZDX_SCORE_LOOKUP.Okay.range[0] &&
              value <= ZDX_SCORE_LOOKUP.Okay.range[1]
            ) {
              val[timestamp].okay = 1;
            } else if (
              value >= ZDX_SCORE_LOOKUP.Good.range[0] &&
              value <= ZDX_SCORE_LOOKUP.Good.range[1]
            ) {
              val[timestamp].good = 1;
            }
            tData = { ...tData, ...val } as TDataProps;
          }
        }
      });
    });

    const resData: VStackBarChartDataType[] = [];

    for (const scoreData in tData) {
      const { date, ...rest } = tData[scoreData] as unknown as ScoreProps;
      resData.push({
        id: formatXAxisDateTime(date),
        data: rest,
        tooltipHeading: parseDateWithYearAndLabelShortFormat(date),
      });
    }

    const { minGridDistance, width } = getGridConfigByDataLength(resData);

    return {
      ...applicationsExperienceTrendConfig,
      showScrollbar: resData.length > THRESHOLD_BAR_TO_SHOW_SCROLLBAR,
      config: {
        ...applicationsExperienceTrendConfig.config,
        axisProps: {
          ...applicationsExperienceTrendConfig.config?.axisProps,
          xAxisProps: {
            ...applicationsExperienceTrendConfig.config?.axisProps?.xAxisProps,
            minGridDistance,
          },
          yAxisTitleSetting: {
            ...applicationsExperienceTrendConfig.config?.axisProps
              ?.yAxisTitleSetting,
            text: t("APPLICATIONS"),
          },
          ...(resData.length > THRESHOLD_BARS_TO_SHOW_PRE_ZOOM && {
            zoomProps: {
              preZoomAxisProps: {
                preZoom: true,
                firstCategory: resData[FIRST_CATEGORY_INDEX_IN_PRE_ZOOM].id,
                secondCategory: resData[LAST_CATEGORY_INDEX_IN_PRE_ZOOM].id,
              },
            },
          }),
        },
        columnProps: {
          ...applicationsExperienceTrendConfig.config?.columnProps,
          width,
        },
      },
      data: [
        ...getStackBarData(
          resData,
          getStackColors(theme),
          toolTipColors,
          getDigitalExperienceVerticalBarTooltipData(t),
        ),
      ],
      theme,
    };
  };

  const responseData = data as ApplicationsTableResponse;
  const transformedResponse = transformResponseData(responseData);

  return (
    <WithAnalyticsStates
      loading={isMutating}
      error={error}
      title={t(applicationsExperienceTrendCardConfig.heading)}
      noData={!responseData?.rows?.length}
      unsubscribed={HAS_READ_PERMISSION}
      id={ID}
    >
      <ApplicationsExperienceTrend
        heading={t(applicationsExperienceTrendCardConfig.heading)}
        chartData={transformedResponse}
        id={ID}
      />
    </WithAnalyticsStates>
  );
};
