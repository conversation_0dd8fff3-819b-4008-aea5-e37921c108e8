import { t } from "i18next";
import { type DatavizVerticalBarProps } from "@up/components";

export const overallDigitalExperienceConfig = {
  heading: "DXP_OVERALL_EXPERIENCE",
  description: "DXP_OVERALL_EXPERIENCE_DESCRIPTION",
};

export const toolTipColors: Record<string, string> = {
  good: "text-dataviz-severity-primary-lowest",
  okay: "text-dataviz-severity-primary-medium",
  poor: "text-dataviz-severity-primary-high",
};

export const seriesDXData = (isDarkMode = false) => [
  {
    label: t("POOR"),
    propertyName: "poor",
    colorCode: isDarkMode ? "#BF392F" : "#DC362E",
  },
  {
    label: t("OKAY"),
    propertyName: "okay",
    colorCode: isDarkMode ? "#D78320" : "#F19325",
  },
  {
    label: t("GOOD"),
    propertyName: "good",
    colorCode: isDarkMode ? "#389685" : "#3DA592",
  },
];

export const getOverallDigitalExperienceStackedVerticalBarChartConfig =
  (): Omit<DatavizVerticalBarProps, "data"> => ({
    axisKeys: ["id", "value"],
    stacked: true,
    showScrollbar: true,
    config: {
      containerClass: "flex h-[400px]",
      legendsProps: {
        showLegend: true,
        customLegend: true,
        legendsData: [
          {
            name: `
          <div class="flex items-center" >
            <i class="fa-solid fa-circle text-dataviz-severity-primary-high accent-red-content-icon text-[10px] pr-xs"></i>  <span class="text-semantic-content-base-primary typography-paragraph2">${t("POOR")}</span>
          <div>
          `,
          },
          {
            name: `
          <div class="flex items-center" >
            <i class="fa-solid fa-circle text-dataviz-severity-primary-medium accent-red-content-icon text-[10px] pr-xs"></i>  <span class="text-semantic-content-base-primary typography-paragraph2">${t("OKAY")}</span>
          <div>
            `,
          },
          {
            name: `
          <div class="flex items-center" >
            <i class="fa-solid fa-circle text-dataviz-severity-primary-lowest accent-red-content-icon text-[10px] pr-xs"></i>  <span class="text-semantic-content-base-primary typography-paragraph2">${t("GOOD")}</span>
          <div>
           `,
          },
        ],
        legendSetting: {
          clickTarget: "none",
        },
        legendMarkerProps: {
          width: 12,
          height: 12,
        },
      },
      axisProps: {
        xLabelProps: {
          paddingTop: 8,
        },
        yAxisSetting: {
          maxPrecision: 0,
        },
        yAxisProps: {
          visible: true,
          minGridDistance: 25,
        },
        yAxisTitleSetting: {
          rotation: 270,
          labelColor: "#767676",
          fontWeight: "400",
          fontStyle: "normal",
          fontSize: "14px",
        },
      },
      roundedColumnProps: {
        cornerRadiusTL: 0,
        cornerRadiusTR: 0,
      },
      columnProps: {
        width: 30,
        tooltipHTML: `<div>
          <div class="font-medium text-[14px] text-grey-900 mb-[4px]">{name}</div>
          <div class="font-light text-[13px]">{valueY} Applications</div>
        </div>`,
        cursorOverStyle: "pointer",
      },
      tooltipProps: {
        tooltipSetting: {
          pointerOrientation: "left",
        },
      },
    },
  });
