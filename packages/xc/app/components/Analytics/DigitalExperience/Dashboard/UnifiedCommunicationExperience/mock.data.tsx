import { type DatavizDonutProps } from "@up/components";
import { getUnifiedCommunicationExperienceConfig } from "./config";
import { type DonutProps } from "@/components/Analytics/Charts/DonutChart/types";
import { TeamsIcon, WebexIcon, ZoomIcon } from "@/components/Analytics/Icons";
import { THEME } from "@/context/UserPreferenceContext";

export const unifiedCommunicationExperienceData = {
  tableData: {
    tableRowData: [
      {
        CustomIcon: ZoomIcon,
        applicationName: "Zoom Call Quality",
        score: 66,
        meetings: 564,
      },
      {
        CustomIcon: TeamsIcon,
        applicationName: "Microsoft Teams Call Quality",
        score: 61,
        meetings: 241,
      },
      {
        CustomIcon: WebexIcon,
        applicationName: "Webex Call Quality",
        score: 60,
        meetings: 114,
      },
    ],
  },
};

export const unifiedCommunicationExperienceMockData = {
  top: [
    {
      payload: [
        {
          isTimeshift: false,
          rows: [
            {
              meet_host_type: ["managed_no_zdx"],
              isRateLimit: false,
              meet_host_id_name: [
                "nastya kor (<EMAIL>)",
              ],
              meet_subject:
                "IqlUhPYK7zJwyAkLME/GyXol66+dDyz9B6uK8lfX5e95MzR7LxSuoyM2PmTmZ3Vf",
              meet_mos: 3.0,
              geo_long: -190.0,
              meet_ended_on: 0,
              meet_started_on: 1714161624,
              lastUpdated: "2024-04-26T23:11:39.6613029Z",
              meet_participants_count: 2,
              meet_id:
                "WEBEX-60656bc3b9b244d684e52102aa9a8f9b_I_291700553901149399",
              geo_lat: -190.0,
              meet_host_id: [70725],
              upm_ux_score: 92,
              application_name: "Zoom Call Quality",
            },
            {
              meet_host_type: ["managed_no_zdx"],
              isRateLimit: false,
              meet_host_id_name: [
                "nastya kor (<EMAIL>)",
              ],
              meet_subject:
                "IqlUhPYK7zJwyAkLME/GyXol66+dDyz9B6uK8lfX5e95MzR7LxSuoyM2PmTmZ3Vf",
              meet_mos: 5.0,
              geo_long: -190.0,
              meet_ended_on: 0,
              meet_started_on: 1714161624,
              lastUpdated: "2024-04-26T23:11:39.6613029Z",
              meet_participants_count: 2,
              meet_id:
                "WEBEX-60656bc3b9b244d684e52102aa9a8f9b_I_291700553901149399",
              geo_lat: -190.0,
              meet_host_id: [70725],
              upm_ux_score: 65,
              application_name: "Microsoft Teams Call Quality",
            },
            {
              meet_host_type: ["managed_no_zdx"],
              isRateLimit: false,
              meet_host_id_name: [
                "nastya kor (<EMAIL>)",
              ],
              meet_subject:
                "IqlUhPYK7zJwyAkLME/GyXol66+dDyz9B6uK8lfX5e95MzR7LxSuoyM2PmTmZ3Vf",
              meet_mos: 4.0,
              geo_long: -190.0,
              meet_ended_on: 0,
              meet_started_on: 1714161624,
              lastUpdated: "2024-04-26T23:11:39.6613029Z",
              meet_participants_count: 2,
              meet_id:
                "WEBEX-60656bc3b9b244d684e52102aa9a8f9b_I_291700553901149399",
              geo_lat: -190.0,
              meet_host_id: [70725],
              upm_ux_score: 20,
              application_name: "Webex Call Quality",
            },
          ],
        },
      ],
      limit: 0,
      unknownFields: {},
      queryId: 0,
    },
  ],
  widgetid: "CQM_MEETING_SUMMARY_T1",
  qsdkTimeTaken: 947,
  qsdkVersion: "2.1.0-2024-04-25T04:54:06Z",
  sessionId: "Rge13gYsLQwuXy1Xl6afW",
  pageid: "MEETINGS_DASHBOARD",
};

const donutMockStorybookData: DonutProps["data"] = [
  {
    category: "Zoom Call Quality",
    count: 69,
    color: "#194CBB",
  },
  {
    category: "Microsoft Teams Call Quality",
    count: 40,
    color: "#25BAE2",
  },
  {
    category: "Webex Call Quality",
    count: 10,
    color: "#9F46D7",
  },
];

export const UnifiedCommunicationDonutChartStorybookData: DatavizDonutProps = {
  data: { slice: donutMockStorybookData },
  dataKeys: ["count", "category"],
  config: getUnifiedCommunicationExperienceConfig(THEME.light).donutChartData,
  theme: "light",
};
