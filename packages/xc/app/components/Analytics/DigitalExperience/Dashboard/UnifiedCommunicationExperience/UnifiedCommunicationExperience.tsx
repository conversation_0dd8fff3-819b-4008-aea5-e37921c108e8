import { usePathname } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Card } from "@zs-nimbus/core";
import { Donut } from "@up/components";
import { getUnifiedCommunicationExperienceConfig } from "./config";
import { type UnifiedCommunicationExperienceProps } from "./types";
import DataTable from "@/components/Analytics/DataTable/DataTable";
import { getDataTestId } from "@/utils/utils";
import ZButtonLink from "@/components/ZLink/ZButtonLink";
import { getSystemTheme } from "@/utils/themeUtils";

export const UnifiedCommunicationExperience = ({
  donutChartData,
  tableData,
  footerLink,
  id,
}: UnifiedCommunicationExperienceProps) => {
  const baseURL = usePathname();
  const { t } = useTranslation();
  const theme = getSystemTheme();

  return (
    <Card.Root
      className="!justify-normal bg-semantic-surface-base-primary"
      data-testid={getDataTestId(`card-container`, id)}
    >
      <Card.Header>
        <div
          data-testid={getDataTestId(`card-title`, id)}
          className="text-semantic-content-base-primary typography-header5"
        >
          {t(getUnifiedCommunicationExperienceConfig(theme).heading)}
        </div>
        <div
          data-testid={getDataTestId(`card-description`, id)}
          className="text-semantic-content-base-secondary typography-paragraph2"
        >
          {t(getUnifiedCommunicationExperienceConfig(theme).description)}
        </div>
      </Card.Header>
      <Card.Body>
        <div className="overflow-hidden">
          <Donut {...donutChartData} chartId={`${id}-donut-chart`} />
        </div>
        <div className="h-auto">
          <DataTable {...tableData} id={id} />
        </div>
      </Card.Body>
      <Card.Footer className="flex justify-end">
        <ZButtonLink
          href={`${baseURL}${footerLink}`}
          type="Breadcrumb"
          id={getDataTestId(`footer-navLink`, id)}
        >
          {t("VIEW_ALL_MEETINGS")} {">"}
        </ZButtonLink>
      </Card.Footer>
    </Card.Root>
  );
};
