import { type FC } from "react";
import { type DatavizDonutProps } from "@up/components";
import { type TableProps } from "@/components/Analytics/DataTable/types";
import { type IconProps } from "@/components/Analytics/Icons";

export type UnifiedCommunicationExperienceTableRowItemProps = {
  score?: number;
  applicationName?: string;
  CustomIcon?: FC<IconProps>;
  meetings?: number;
};

export type UnifiedCommunicationExperienceProps = {
  donutChartData: DatavizDonutProps;
  tableData: TableProps;
  footerLink: string;
  id?: string;
};

type RowsProps = {
  meet_host_type: [string];
  isRateLimit: boolean;
  meet_host_id_name: [string];
  meet_subject: string;
  meet_mos: number;
  geo_long: number;
  meet_ended_on: number;
  meet_started_on: number;
  lastUpdated: string;
  meet_participants_count: number;
  meet_id: string;
  geo_lat: number;
  meet_host_id: [number];
  upm_ux_score: number;
  application_name: string;
};

type PayloadProps = {
  isTimeshift: boolean;
  rows: RowsProps[];
};

type TopProps = {
  limit: number;
  unknownFields: object;
  queryId: number;
  payload: PayloadProps[];
};

export type UnifiedCommunicationExperienceItemProps = {
  widgetid: string;
  qsdkTimeTaken: number;
  qsdkVersion: string;
  sessionId: string;
  pageid: string;
  error?: string;
  top: TopProps[];
};

export type CQMApplicationProps = {
  applicationName: string;
  CustomIcon: React.ComponentType;
  color: string;
};

export type ExtendedRowsProps = CQMApplicationProps & {
  meetings: number;
  score: number;
};
