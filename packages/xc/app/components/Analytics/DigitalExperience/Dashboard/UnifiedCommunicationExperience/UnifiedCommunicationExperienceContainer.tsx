import { useTranslation } from "react-i18next";
import { type DatavizDonutDataType } from "@up/components";
import {
  UnifiedCommunicationExperience,
  MEETING_PLATFORM_DETAILS,
  getUnifiedCommunicationExperienceConfig,
} from "..";

import {
  type CQMApplicationProps,
  type ExtendedRowsProps,
  type UnifiedCommunicationExperienceItemProps,
} from "./types";
import { CQM_APPLICATION_IDS } from "@/configs/constants/analytics";
import { generateSessionId, getDataTestId } from "@/utils/utils";
import { DigitalExperience } from "@/configs/urls/analytics/digital-experience";

import { usePostMutation } from "@/utils/apiUtils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { FILTER_DXP_DASHBOARD_CONFIG } from "@/components/pages/Analytics/Dashboard/Filters/ModalPillGroupContainer/config";
import useAnalyticsFilterEffect from "@/hooks/useAnalyticsFilterEffect";
import { useZDXEntitlement } from "@/hooks/useZDXEntitlement";
import { getSystemTheme } from "@/utils/themeUtils";

export const UnifiedCommunicationExperienceContainer = ({
  id,
}: {
  id?: string;
}) => {
  const theme = getSystemTheme();
  const unifiedCommunicationExperienceConfig =
    getUnifiedCommunicationExperienceConfig(theme);
  const { t } = useTranslation();
  const ID = getDataTestId("unified-communication-experience", id);
  const containerId =
    "DigitalExperience.Dashboard.UnifiedCommunicationExperience";
  const HAS_READ_PERMISSION = useZDXEntitlement(containerId);
  const { trigger, data, isMutating, error } = usePostMutation(
    DigitalExperience.Dashboard.UnifiedCommunicationExperience,
    {
      key: containerId,
    },
  );
  const responseData = data as UnifiedCommunicationExperienceItemProps;

  useAnalyticsFilterEffect(
    trigger,
    HAS_READ_PERMISSION === "",
    {
      sessionId: generateSessionId(),
      pageid: "MEETINGS_DASHBOARD",
      widgetid: "CQM_MEETING_SUMMARY_T1",
      top: [
        {
          view: "cqm_meet_records",
          dimension: ["meet_id", "meet_subject"],
          aggregate: [
            {
              metric: "meet_started_on",
              agg: "min",
            },
            {
              metric: "meet_ended_on",
              agg: "max",
            },
            {
              metric: "upm_ux_score",
              agg: "avg",
            },
            {
              metric: "meet_mos",
              agg: "avg",
            },
            {
              metric: "meet_participants_count",
              agg: "max",
            },
            {
              metric: "meet_host_id",
              agg: "make_set",
            },
          ],
          orderby: [
            {
              metric: "meet_started_on",
              order: "desc",
            },
            {
              metric: "upm_ux_score",
              order: "asc",
            },
          ],
          limit: 10000,
        },
      ],
    },
    FILTER_DXP_DASHBOARD_CONFIG,
    [],
    { app_id: { in: CQM_APPLICATION_IDS } },
  );

  const transformResponseData = (
    rawResponse: UnifiedCommunicationExperienceItemProps,
  ) => {
    const payload = rawResponse?.top?.[0]?.payload?.[0] ?? [];
    const response = (payload.rows || []).reduce(
      (accumulator, meeting) => {
        // meeting type could be Zoom / Teams / Webex
        const CQM_TYPE = meeting?.meet_id?.split("-")[0].toLowerCase() ?? "";
        if (CQM_TYPE) {
          if (!accumulator[CQM_TYPE]) {
            accumulator[CQM_TYPE] = {
              meetings: 0,
              score: 0,
              ...MEETING_PLATFORM_DETAILS[CQM_TYPE],
            };
          }
          accumulator[CQM_TYPE].meetings += 1;
          accumulator[CQM_TYPE].score +=
            meeting.upm_ux_score === -1 ? 0 : meeting.upm_ux_score;
        }

        return accumulator;
      },
      {} as Record<string, ExtendedRowsProps>,
    );

    const updatedTableData = Object.values(response).map((item) => ({
      ...item,
      score: Math.round(item.score / item.meetings),
    }));
    const updatedDonutChartData = (payload.rows || []).reduce(
      (accumulator, currentObject) => {
        const { meet_id } = currentObject;
        const CQM_TYPE = meet_id?.split("-")[0].toLowerCase();
        const cqmApplicationProps: CQMApplicationProps =
          MEETING_PLATFORM_DETAILS[CQM_TYPE];

        if (!cqmApplicationProps) {
          return accumulator;
        }

        if (!accumulator[CQM_TYPE]) {
          accumulator[CQM_TYPE] = {
            category: t(cqmApplicationProps.applicationName),
            count: 0,
            color: cqmApplicationProps.color,
          };
        }

        accumulator[CQM_TYPE].count += 1;

        return accumulator;
      },
      {} as Record<string, DatavizDonutDataType>,
    );

    return {
      tableData: updatedTableData,
      donutChartData: Object.values(updatedDonutChartData),
    };
  };

  const updatedTableData = transformResponseData(responseData);

  const totalCount = updatedTableData?.donutChartData?.reduce(
    (acc, ele) => ele.count + acc,
    0,
  );

  const config = unifiedCommunicationExperienceConfig.donutChartData;

  const updatedConfig = {
    ...config,
    labelProps: {
      ...config.labelProps,
      centerLabel: {
        ...config.labelProps?.centerLabel,
        labels: [`${totalCount}`, t("MEETINGS")],
      },
    },
  };

  return (
    <WithAnalyticsStates
      loading={isMutating}
      error={error}
      title={t(unifiedCommunicationExperienceConfig.heading)}
      noData={!updatedTableData?.tableData?.length}
      unsubscribed={HAS_READ_PERMISSION}
      id={ID}
    >
      <UnifiedCommunicationExperience
        id={ID}
        donutChartData={{
          config: updatedConfig,
          dataKeys: ["count", "category"],
          data: { slice: updatedTableData?.donutChartData },
          theme,
        }}
        tableData={{
          tableColumnFn:
            unifiedCommunicationExperienceConfig.tableData.tableColumnFn,
          tablePaginationData:
            unifiedCommunicationExperienceConfig.tableData.tablePaginationData,
          tableRowData: updatedTableData?.tableData as [],
        }}
        footerLink={unifiedCommunicationExperienceConfig.footerLink}
      />
    </WithAnalyticsStates>
  );
};
