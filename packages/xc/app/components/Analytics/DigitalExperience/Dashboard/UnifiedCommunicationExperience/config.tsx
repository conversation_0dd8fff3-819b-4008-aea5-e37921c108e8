import { t } from "i18next";
import { colors } from "@zs-nimbus/foundations";
import { getDatavizTooltip, type DatavizDonutProps } from "@up/components";
import {
  type CQMApplicationProps,
  type UnifiedCommunicationExperienceTableRowItemProps,
} from "./types";
import { type TableRenderItem } from "@/components/Analytics/DataTable/types";
import { TeamsIcon, WebexIcon, ZoomIcon } from "@/components/Analytics/Icons";
import { ProgressBar } from "@/components/Analytics/ProgressBar/ProgressBar";
import { getProgressBarColor } from "@/utils/utils";
import { type ThemeTypes } from "@/context/UserPreferenceContext";

export const getUnifiedCommunicationExperienceConfig = (theme: ThemeTypes) => ({
  heading: "UNIFIED_COMMUNICATION_EXP",
  description: "UNIFIED_COMMUNICATION_EXP_DESC",
  tableData: {
    tableColumnFn: () => [
      {
        id: "applicationName",
        name: t("APPLICATION_NAME"),
        isSortable: true,
        isHidden: false,
        width: "50%",
        renderItem: (row: TableRenderItem) => {
          const { applicationName, CustomIcon } =
            row.item as UnifiedCommunicationExperienceTableRowItemProps;

          return (
            <>
              {CustomIcon && <CustomIcon />}
              <span className="ml-m">{t(applicationName ?? "")}</span>
            </>
          );
        },
      },
      {
        id: "meetings",
        name: t("MEETINGS"),
        isSortable: true,
        isHidden: false,
        width: "20%",
        isPinned: true,
        pinnedDirection: "right",
      },
      {
        id: "score",
        name: t("SCORE"),
        isSortable: true,
        isHidden: false,
        width: "30%",
        renderItem: (row: TableRenderItem) => {
          const { score } =
            row.item as UnifiedCommunicationExperienceTableRowItemProps;

          return (
            <>
              <span className="mr-m">{score}</span>
              <ProgressBar
                size="md"
                color={getProgressBarColor(score ?? 0)}
                value={score ?? 0}
                thickness="sm"
                showCircularDelimiter={false}
              />
            </>
          );
        },
      },
    ],
    tableRowData: [
      {
        CustomIcon: ZoomIcon,
        applicationName: t("ZOOM_CALL_QUALITY"),
        score: 66,
        meetings: 564,
      },
      {
        CustomIcon: TeamsIcon,
        applicationName: t("MICROSOFT_TEAMS_CALL_QUALITY"),
        score: 61,
        meetings: 241,
      },
      {
        CustomIcon: WebexIcon,
        applicationName: t("WEBEX_CALL_QUALITY"),
        score: 60,
        meetings: 114,
      },
    ],
    tablePaginationData: {
      enabled: false,
      pageSize: 50,
      currentPage: 1,
      allowedPageSizes: [50, 100, 150, 200],
      hideSummary: false,
    },
  },
  donutChartData: {
    containerClass: "flex h-[220px] mb-rem-160",
    chartProps: {
      chartContainer: {
        paddingBottom: 20,
        paddingTop: 20,
      },
    },
    legendProps: {
      direction: "right",
      legendLabelProps: {
        legendLabelColor: colors[theme].content.base.tertiary,
        fontSize: 12,
      },
      legendMarkerProps: {
        height: 15,
        width: 15,
      },
      legendItemContainerProps: {
        marginTop: 5,
        marginBottom: 5,
      },
      legendRectangleProps: {
        cornerRadiusTL: 4,
        cornerRadiusTR: 4,
        cornerRadiusBL: 4,
        cornerRadiusBR: 4,
      },
    },
    pieSetting: {
      strokeColor: colors[theme].content.inverted.base.primary,
      strokeWidth: 1,
      tooltipHTML: getDatavizTooltip([
        { label: "{category}" },
        { label: `{count.formatNumber()}` },
      ]),
    },
  } as DatavizDonutProps["config"],
  footerLink: "/meetings",
});

export const MEETING_PLATFORM_DETAILS: Record<string, CQMApplicationProps> = {
  zoom: {
    applicationName: "ZOOM_CALL_QUALITY",
    CustomIcon: ZoomIcon,
    color: "#194CBB",
  },
  teams: {
    applicationName: "MICROSOFT_TEAMS_CALL_QUALITY",
    CustomIcon: TeamsIcon,
    color: "#25BAE2",
  },
  webex: {
    applicationName: "WEBEX_CALL_QUALITY",
    CustomIcon: WebexIcon,
    color: "#9F46D7",
  },
};
