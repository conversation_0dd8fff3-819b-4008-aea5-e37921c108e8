import { useContext, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { type DatavizDonutProps } from "@up/components";
import { ThreatBlockedBySSLInspection } from "./ThreatBlockedBySSLInspection";
import {
  getThreatsBlockedBySSLDonutConfig,
  threatsBlockedBySSLCardConfig,
  threatsBlockedBySSLDonutColorConfig,
  getThreatsBlockedBySSLTrendConfig,
} from "./config";
import {
  type ThreatBlockedBySSLInspectionDonutResponse,
  type ThreatBlockedBySSLInspectionTrendEntry,
  type ThreatBlockedBySSLInspectionTrendResponse,
} from "./types";
import { Cybersecurity } from "@/configs/urls/analytics/cybersecurity";
import { type VerticalBarChartProps } from "@/components/Analytics/Charts/VerticalBarChart/types";
import {
  formatDonutResponse,
  formatNumber,
  formatXAxisDateTime,
  getDataTestId,
  parseDateWithYearAndLabelShortFormat,
} from "@/utils/utils";
import { usePostMutation } from "@/utils/apiUtils";
import { AnalyticsFilterContext } from "@/app/(post-onboarding)/(with-side-nav)/analytics/AnalyticsFilterContext";
import {
  ACTION_FILTER,
  DATA_CLASS,
  DATA_TYPE,
  TREND_INTERVAL,
  UNITS,
} from "@/configs/constants/analytics";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { useZIAEntitlement } from "@/hooks/useZIAEntitlement";
import { getNanoLogTimestampsForRange } from "@/utils/unifiedAnalytics/utils";
import { getSystemTheme } from "@/utils/themeUtils";

type Props = {
  id?: string;
};

const ThreatBlockedBySSLInspectionContainer = ({ id }: Props) => {
  const theme = getSystemTheme();
  const { t } = useTranslation();
  const ID = getDataTestId("threat-blocked-ssl", id);
  const containerId =
    "Cybersecurity.SSLInspection.ThreatsBlockedBySSLInspectionDonut";
  const entitlementError = useZIAEntitlement(containerId);
  const {
    analyticsFilter: {
      dateFilter: {
        startDate,
        endDate,
        timeRangeSelected: { value },
      },
    },
  } = useContext(AnalyticsFilterContext);

  const threatsBlockedBySSLDonutConfig = getThreatsBlockedBySSLDonutConfig(
    theme,
    t,
  );

  const {
    trigger: fetchThreatBlockedBySSLInspection,
    data: donutResponse,
    isMutating: donutIsMutating,
    error: donutError,
  } = usePostMutation(
    Cybersecurity.SSLInspection.ThreatBlockedBySSLInspectionDonut,
    {
      key: containerId,
    },
    true,
  );

  useEffect(() => {
    if (!entitlementError) {
      const { startEpochTime, endEpochTime } = getNanoLogTimestampsForRange(
        Number(value),
      );
      void fetchThreatBlockedBySSLInspection({
        dataClass: DATA_CLASS.WEB,
        dataType: DATA_TYPE.WEB_APP,
        units: UNITS.TRANSACTIONS,
        startTime: startEpochTime,
        endTime: endEpochTime,
        actionFilter: ACTION_FILTER.BLOCK,
        protocols: ["DNSOVERHTTPS", "HTTPS"],
      });
    }
  }, [
    endDate,
    startDate,
    fetchThreatBlockedBySSLInspection,
    entitlementError,
    value,
  ]);

  const {
    trigger: fetchThreatBlockedBySSLInspectionTrend,
    data: trendResponse,
    isMutating: trendIsMutating,
    error: trendError,
  } = usePostMutation(
    Cybersecurity.SSLInspection.ThreatBlockedBySSLInspectionTrend,
    {
      key: "Cybersecurity.SSLInspection.ThreatsBlockedBySSLInspectionTrend",
    },
  );

  useEffect(() => {
    if (!entitlementError) {
      const { startEpochTime, endEpochTime } = getNanoLogTimestampsForRange(
        Number(value),
      );
      void fetchThreatBlockedBySSLInspectionTrend({
        dataClass: DATA_CLASS.WEB,
        dataType: DATA_TYPE.WEB_APP,
        units: UNITS.TRANSACTIONS,
        startTime: startEpochTime,
        endTime: endEpochTime,
        actionFilter: ACTION_FILTER.BLOCK,
        protocols: ["DNSOVERHTTPS", "HTTPS"],
        trendInterval: value !== "1" ? TREND_INTERVAL.DAY : TREND_INTERVAL.HOUR,
        includeTrend: true,
      });
    }
  }, [
    endDate,
    startDate,
    fetchThreatBlockedBySSLInspectionTrend,
    value,
    entitlementError,
  ]);

  const donutResponseData =
    donutResponse as ThreatBlockedBySSLInspectionDonutResponse[];
  const trendResponseData =
    trendResponse as ThreatBlockedBySSLInspectionTrendResponse[];

  const formatTrendResponse = (
    res: ThreatBlockedBySSLInspectionTrendEntry[],
  ) => {
    if (!res?.length) return [];

    const { trendStartTime = 0, trendInterval = 0 } = res?.[0]?.trend || {};
    const resultArray: Record<number, number> = {};

    res.forEach(({ trend: { trendValues } }) => {
      trendValues.forEach((value, index) => {
        const timestamp = trendStartTime + index * trendInterval;
        resultArray[timestamp] = (resultArray[timestamp] || 0) + value;
      });
    });

    return Object.keys(resultArray).map((timestamp) => {
      const dateValue = Number(timestamp) / 1000;

      return {
        dateTooltip: parseDateWithYearAndLabelShortFormat(dateValue),
        date: formatXAxisDateTime(dateValue),
        value: resultArray[Number(timestamp)],
      };
    });
  };

  const data = formatDonutResponse(
    donutResponseData?.[0]?.entries,
    threatsBlockedBySSLDonutColorConfig,
  )?.map((item) =>
    item.category === "OTHERS" ? { ...item, category: t("OTHER") } : item,
  );

  const sumTotal = data?.reduce((a, b) => a + b.count, 0);

  const transformDonutResponseData = () =>
    ({
      config: {
        ...threatsBlockedBySSLDonutConfig,
        labelProps: {
          ...threatsBlockedBySSLDonutConfig.labelProps,
          centerLabel: {
            ...threatsBlockedBySSLDonutConfig.labelProps?.centerLabel,
            labels: [formatNumber(sumTotal), t("THREATS_BLOCKED")],
          },
        },
      },
      dataKeys: ["count", "category"],
      data: {
        slice: data,
      },
      theme,
    }) as DatavizDonutProps;

  const transformTrendResponseData = () =>
    ({
      ...getThreatsBlockedBySSLTrendConfig(theme),
      data: formatTrendResponse(trendResponseData?.[0]?.entries),
    }) as VerticalBarChartProps;

  return (
    <WithAnalyticsStates
      loading={donutIsMutating || trendIsMutating}
      error={donutError || trendError}
      noData={
        !donutResponseData?.[0]?.entries?.length ||
        !trendResponseData?.[0]?.entries?.length
      }
      title={threatsBlockedBySSLCardConfig.heading}
      unsubscribed={entitlementError}
      id={ID}
    >
      <ThreatBlockedBySSLInspection
        donutChartData={transformDonutResponseData()}
        verticalGraphData={transformTrendResponseData()}
        heading={threatsBlockedBySSLCardConfig.heading}
        description={threatsBlockedBySSLCardConfig.description}
        id={ID}
      />
    </WithAnalyticsStates>
  );
};

export { ThreatBlockedBySSLInspectionContainer };
