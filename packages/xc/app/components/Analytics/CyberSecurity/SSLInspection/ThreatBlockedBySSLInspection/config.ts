import { colors } from "@zs-nimbus/foundations";
import * as am5 from "@amcharts/amcharts5";
import { getDatavizTooltip, type DatavizDonutProps } from "@up/components";
import { type VerticalBarChartProps } from "@/components/Analytics/Charts/VerticalBarChart/types";
import { type ThemeTypes } from "@/context/UserPreferenceContext";

export const threatsBlockedBySSLCardConfig = {
  heading: "THREATS_BLOCKED_BY_SSL_INSPECTION",
  description: "THREATS_BLOCKED_BY_SSL_INSPECTION_DESC",
};

export const getThreatsBlockedBySSLDonutConfig = (
  theme: ThemeTypes,
  t: (s: string) => string,
) => {
  const themeColors = colors[theme];
  const baseConfig: DatavizDonutProps["config"] = {
    containerClass: "flex h-[300px]",
    numberFormatter: {
      numberFormat: "#.#a",
      bigNumberPrefixes: [
        { number: 1e3, suffix: "K" },
        { number: 1e6, suffix: "M" },
        { number: 1e9, suffix: "B" },
      ],
    },
    labelProps: {
      labelSetting: {
        visible: false,
      },
    },
    chartProps: {
      chartContainer: {
        paddingBottom: 45,
        paddingTop: 10,
      },
    },
    legendProps: {
      legendMarkerProps: {
        height: 12,
        width: 12,
      },
      legendLabelProps: {
        legendLabelColor: themeColors.content.base.primary,
        fontSize: 13,
        fontWeight: "400",
        maxWidth: 150,
        oversizedBehavior: "truncate",
      },
      legendRectangleProps: {
        cornerRadiusTL: 15,
        cornerRadiusTR: 15,
        cornerRadiusBL: 15,
        cornerRadiusBR: 15,
      },
    },
    tooltipProps: {
      showTooltip: true,
      tooltipBackgroundSetting: {
        fill: am5.color(themeColors.surface.elevated.low10),
      },
      tooltipSetting: {
        pointerOrientation: "up",
      },
    },
    pieSetting: {
      strokeColor: themeColors.border.inverted.primary,
      strokeWidth: 1,
      tooltipPosition: "fixed",
      tooltipHTML: getDatavizTooltip([
        { label: "{category}" },
        {
          data: [
            {
              label: `{count.formatNumber("#.#a")} ${t("THREATS")}`,
            },
          ],
        },
      ]),
    },
  };

  return baseConfig;
};

export const threatsBlockedBySSLDonutColorConfig = [
  {
    color: "#194CBB",
  },
  {
    color: "#9F46D7",
  },
  {
    color: "#3DA592",
  },
  {
    color: "#DD3BB2",
  },
  {
    color: "#A10000",
  },
  {
    color: "#25BAE2",
  },
];

export const getThreatsBlockedBySSLTrendConfig = (
  theme: ThemeTypes,
): Omit<VerticalBarChartProps, "data"> => {
  const themeColors = colors[theme];

  return {
    axisKeys: ["date", "value"],
    config: {
      containerClass: "h-[315px]",
      numberFormatter: {
        numberFormat: "#.#a",
        bigNumberPrefixes: [
          { number: 1e3, suffix: "K" },
          { number: 1e6, suffix: "M" },
          { number: 1e9, suffix: "B" },
        ],
      },
      axisProps: {
        xAxisProps: {
          minorGridEnabled: true,
        },
        yAxisSetting: {
          numberFormat: "#.#a",
          min: 0,
          strictMinMax: true,
          maxPrecision: 0,
        },
        xAxisSetting: {
          startLocation: 0.2,
          endLocation: 0.8,
        },
        yGridProps: {
          visible: true,
          stroke: am5.color(colors[theme].content.base.primary),
        },
        yLabelProps: {
          labelColor: themeColors.content.base.tertiary,
          showCustomLabel: true,
          labelFormatter: (text?: string) => (text ? text?.toUpperCase() : ""),
        },
        yAxisProps: {
          minorGridEnabled: false,
        },
      },
      tooltipProps: {
        tooltipBackgroundSetting: {
          fill: am5.color(themeColors.surface.elevated.low10),
        },
        tooltipData: {
          key: "TOTAL_THREATS",
          title: "dateTooltip",
        },
        tooltipSetting: {
          pointerOrientation: "up",
        },
      },
      gradientProps: {
        showGradient: true,
        stops: [
          {
            color: "rgba(61, 165, 146, 1)",
            opacity: 1,
          },
          {
            color: "rgba(61, 165, 146, 0.5)",
            opacity: 0.8,
          },
        ],
        rotation: 90,
      },
    },
  };
};
