import { useTranslation } from "react-i18next";
import { Donut } from "@up/components";
import { type ThreatBlockedBySSLInspectionProps } from "./types";
import { Card } from "@/components/Analytics/Card";
import VerticalBarChart from "@/components/Analytics/Charts/VerticalBarChart/VerticalBarChart";

const ThreatBlockedBySSLInspection = ({
  heading,
  description,
  donutChartData,
  verticalGraphData,
  id,
}: ThreatBlockedBySSLInspectionProps) => {
  const { t } = useTranslation();

  return (
    <Card id={id}>
      <Card.Header>
        <Card.Header.Title id={id}>{t(heading)}</Card.Header.Title>
        <Card.Header.Description id={id}>
          {t(description)}
        </Card.Header.Description>
      </Card.Header>
      <Card.Content>
        <Card.SplitSection>
          <Card.SplitSection.Section size="sm" showDivider={true}>
            <div className="z-10 relative">
              <Donut {...donutChartData} chartId={`${id}-donut-chart`} />
            </div>
          </Card.SplitSection.Section>
          <Card.SplitSection.Section size="lg">
            <VerticalBarChart {...verticalGraphData} chartId={id} />
          </Card.SplitSection.Section>
        </Card.SplitSection>
      </Card.Content>
    </Card>
  );
};

export { ThreatBlockedBySSLInspection };
