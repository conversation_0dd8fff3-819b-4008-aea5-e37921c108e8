import { type DatavizDonutProps } from "@up/components";
import { type VerticalBarChartProps } from "@/components/Analytics/Charts/VerticalBarChart/types";

export type ThreatBlockedBySSLInspectionProps = {
  heading: string;
  description: string;
  donutChartData: DatavizDonutProps;
  verticalGraphData: VerticalBarChartProps;
  id?: string;
};

type ThreatBlockedBySSLInspectionDonutEntry = {
  name: string;
  total: number;
};

export type ThreatBlockedBySSLInspectionDonutResponse = {
  entries: ThreatBlockedBySSLInspectionDonutEntry[];
  obfuscated: boolean;
  weblogTime: number;
  entryNameL10n: string;
  permissionError?: string;
};

export type ThreatBlockedBySSLInspectionTrendEntry = {
  name: string;
  total: number;
  trend: {
    trendStartTime: number;
    trendInterval: number;
    trendValues: number[];
  };
};

export type ThreatBlockedBySSLInspectionTrendResponse = {
  entries: ThreatBlockedBySSLInspectionTrendEntry[];
  obfuscated: boolean;
  weblogTime: number;
  entryNameL10n: string;
  permissionError?: string;
};
