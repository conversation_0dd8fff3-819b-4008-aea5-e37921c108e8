import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { type DatavizDonutProps } from "@up/components";
import { SSLInspectionView as component } from "./SSLInspectionView";
import { topHighVolumeApplicationCardConfig } from "@/components/Analytics/CyberSecurity/SSLInspection/TopHighVolumeApplications/config";
import { topLocationsCardConfig } from "@/components/Analytics/CyberSecurity/SSLInspection/TopLocations/config";
import { topUrlCategoriesCardConfig } from "@/components/Analytics/CyberSecurity/SSLInspection/TopURLCategories/config";
import { type VerticalBarChartProps } from "@/components/Analytics/Charts/VerticalBarChart/types";
import {
  getThreatsBlockedBySSLTrendConfig,
  threatsBlockedBySSLCardConfig,
} from "@/components/Analytics/CyberSecurity/SSLInspection/ThreatBlockedBySSLInspection/config";
import { DonutData } from "@/components/Analytics/Charts/DonutChart/Donut.Data";
import { trendStoryBookMockData } from "@/components/Analytics/CyberSecurity/SSLInspection/ThreatBlockedBySSLInspection/mock.data";
import { TopHighVolumeApplicationMockData } from "@/components/Analytics/CyberSecurity/SSLInspection/TopHighVolumeApplications/mock.data";
import { TopLocationsMockData } from "@/components/Analytics/CyberSecurity/SSLInspection/TopLocations/mock.data";
import { TopUrlCategoriesMockData } from "@/components/Analytics/CyberSecurity/SSLInspection/TopURLCategories/mock.data";
import { THEME } from "@/context/UserPreferenceContext";

const meta = {
  title: "pages/Analytics/Dashboard/CyberSecurity/SSLInspection",
  component,
  parameters: {
    layout: "fullscreen",
    design: {
      type: "figma",
      url: "https://www.figma.com/file/wLxNEe788V2qdOKQDy31Of/CXO---Desktop-(Analytics)?type=design&node-id=4945-44419&mode=design&t=7IDd5OO8nvyNx5s7-4",
    },
  },
} satisfies Meta<typeof component>;

export default meta;

const ThreatsBlockedDonutChartMockData: DatavizDonutProps = {
  data: { slice: DonutData },
  dataKeys: ["count", "category"],
  config: {
    containerClass: "flex h-[300px]",
  },
  theme: "light",
};

const ThreatsBlockedSSLVerticalGraphMockData: VerticalBarChartProps = {
  data: trendStoryBookMockData,
  ...getThreatsBlockedBySSLTrendConfig(THEME.light),
};

export const SSLInspection: StoryObj<typeof meta> = {
  args: {
    topHighVolumeApplication: {
      chartData: TopHighVolumeApplicationMockData,
      heading: topHighVolumeApplicationCardConfig.heading,
    },
    topLocations: {
      chartData: TopLocationsMockData,
      heading: topLocationsCardConfig.heading,
    },
    topURLCategories: {
      chartData: TopUrlCategoriesMockData,
      heading: topUrlCategoriesCardConfig.heading,
    },
    threatsBlockedBySSL: {
      heading: threatsBlockedBySSLCardConfig.heading,
      description: threatsBlockedBySSLCardConfig.description,
      donutChartData: ThreatsBlockedDonutChartMockData,
      verticalGraphData: ThreatsBlockedSSLVerticalGraphMockData,
    },
  },
};
