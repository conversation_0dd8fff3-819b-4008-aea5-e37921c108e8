/* eslint-disable @up/unified-platform/max-file-lines */
import { t } from "i18next";
import { type Product } from "@/context/ProductAccessProvider";

export const VM_PRODUCT_MAP = {
  VMWARE_ESXI: "vm-product.vmware-esxi",
  REDHAT_LINUX: "vm-product.redhat-linux",
  MICROSOFT_HYPER_V: "vm-product.microsoft-hyper-v",
};

export const VM_SIZE_MAP = {
  SMALL: "vm.small",
  MEDIUM: "vm.medium",
  LARGE: "vm.large",
};

export const COUNTRIES = {
  AFGHANISTAN: "Afghanistan",
  IVORY_COAST: "Africa/Abidjan",
  THE_GAMBIA: "Africa/Banjul",
  CONGO_REPUBLIC: "Africa/Brazzaville",
  DR_CONGO: "Africa/Kinshasa",
  ALBANIA: "Albania",
  ALGERIA: "Algeria",
  ST_KITTS_AND_NEVIS: "America/St_Kitts",
  US_VIRGIN_ISLANDS: "America/St_Thomas",
  ST_VINCENT_AND_THE_GRENADINES: "America/St_Vincent",
  BRITISH_VIRGIN_ISLANDS: "America/Tortola",
  AMERICAN_SAMOA: "American Samoa",
  ANDORRA: "Andorra",
  ANGOLA: "Angola",
  ANGUILLA: "Anguilla",
  ANTARCTICA: "Antarctica",
  ANTIGUA_AND_BARBUDA: "Antigua and Barbuda",
  ARGENTINA: "Argentina",
  ARMENIA: "Armenia",
  BRUNEI: "Asia/Brunei",
  PALESTINE: "Asia/Gaza",
  CABO_VERDE: "Atlantic/Cape_Verde",
  SOUTH_GEORGIA_AND_SOUTH_SANDWICH_ISLANDS: "Atlantic/South_Georgia",
  AUSTRALIA: "Australia",
  AUSTRIA: "Austria",
  AZERBAIJAN: "Azerbaijan",
  BAHAMAS: "Bahamas",
  BAHRAIN: "Bahrain",
  BANGLADESH: "Bangladesh",
  BARBADOS: "Barbados",
  BELARUS: "Belarus",
  BELGIUM: "Belgium",
  BELIZE: "Belize",
  BENIN: "Benin",
  BERMUDA: "Bermuda",
  BHUTAN: "Bhutan",
  BOLIVIA: "Bolivia",
  BOSNIA_AND_HERZEGOVINA: "Bosnia and Herzegovina",
  BOTSWANA: "Botswana",
  BRAZIL: "Brazil",
  BRITISH_INDIAN_OCEAN_TERRITORY: "British Indian Ocean Territory",
  BULGARIA: "Bulgaria",
  BURKINA_FASO: "Burkina Faso",
  BURUNDI: "Burundi",
  CAMBODIA: "Cambodia",
  CAMEROON: "Cameroon",
  CANADA: "Canada",
  CAYMAN_ISLANDS: "Cayman Islands",
  CENTRAL_AFRICAN_REPUBLIC: "Central African Republic",
  CHAD: "Chad",
  CHILE: "Chile",
  CHINA: "China",
  CHRISTMAS_ISLAND: "Christmas Island",
  COCOS_KEELING_ISLANDS: "Cocos (Keeling) Islands",
  COLOMBIA: "Colombia",
  COMOROS: "Comoros",
  COOK_ISLANDS: "Cook Islands",
  COSTA_RICA: "Costa Rica",
  CROATIA: "Croatia",
  CUBA: "Cuba",
  CYPRUS: "Cyprus",
  CZECHIA: "Czechia",
  DENMARK: "Denmark",
  DJIBOUTI: "Djibouti",
  DOMINICA: "Dominica",
  DOMINICAN_REPUBLIC: "Dominican Republic",
  ECUADOR: "Ecuador",
  EGYPT: "Egypt",
  EL_SALVADOR: "El Salvador",
  EQUATORIAL_GUINEA: "Equatorial Guinea",
  ERITREA: "Eritrea",
  ESTONIA: "Estonia",
  ETHIOPIA: "Ethiopia",
  ALAND: "Europe/Mariehamn",
  NORTH_MACEDONIA: "Europe/Skopje",
  VATICAN_CITY: "Europe/Vatican",
  FALKLAND_ISLANDS: "Falkland Islands",
  FAROE_ISLANDS: "Faroe Islands",
  FIJI: "Fiji",
  FINLAND: "Finland",
  FRANCE: "France",
  FRENCH_GUIANA: "French Guiana",
  FRENCH_POLYNESIA: "French Polynesia",
  FRENCH_SOUTHERN_TERRITORIES: "French Southern Territories",
  GABON: "Gabon",
  GEORGIA: "Georgia",
  GERMANY: "Germany",
  GHANA: "Ghana",
  GIBRALTAR: "Gibraltar",
  GREECE: "Greece",
  GREENLAND: "Greenland",
  GRENADA: "Grenada",
  GUADELOUPE: "Guadeloupe",
  GUAM: "Guam",
  GUATEMALA: "Guatemala",
  GUERNSEY: "Guernsey",
  GUINEA: "Guinea",
  GUINEA_BISSAU: "Guinea-Bissau",
  GUYANA: "Guyana",
  HAITI: "Haiti",
  HONDURAS: "Honduras",
  HONG_KONG: "Hong Kong",
  HUNGARY: "Hungary",
  ICELAND: "Iceland",
  INDIA: "India",
  INDONESIA: "Indonesia",
  IRAN: "Iran",
  IRAQ: "Iraq",
  IRELAND: "Ireland",
  ISLE_OF_MAN: "Isle of Man",
  ISRAEL: "Israel",
  ITALY: "Italy",
  JAMAICA: "Jamaica",
  JAPAN: "Japan",
  JERSEY: "Jersey",
  JORDAN: "Jordan",
  KAZAKHSTAN: "Kazakhstan",
  KENYA: "Kenya",
  KIRIBATI: "Kiribati",
  KUWAIT: "Kuwait",
  KYRGYZSTAN: "Kyrgyzstan",
  LAOS: "Laos",
  LATVIA: "Latvia",
  LEBANON: "Lebanon",
  LESOTHO: "Lesotho",
  LIBERIA: "Liberia",
  LIBYA: "Libya",
  LIECHTENSTEIN: "Liechtenstein",
  LITHUANIA: "Lithuania",
  LUXEMBOURG: "Luxembourg",
  MACAO: "Macao",
  MADAGASCAR: "Madagascar",
  MALAWI: "Malawi",
  MALAYSIA: "Malaysia",
  MALDIVES: "Maldives",
  MALI: "Mali",
  MALTA: "Malta",
  MARSHALL_ISLANDS: "Marshall Islands",
  MARTINIQUE: "Martinique",
  MAURITANIA: "Mauritania",
  MAURITIUS: "Mauritius",
  MAYOTTE: "Mayotte",
  MEXICO: "Mexico",
  MOLDOVA: "Moldova",
  MONACO: "Monaco",
  MONGOLIA: "Mongolia",
  MONTENEGRO: "Montenegro",
  MONTSERRAT: "Montserrat",
  MOROCCO: "Morocco",
  MOZAMBIQUE: "Mozambique",
  MYANMAR: "Myanmar",
  NAMIBIA: "Namibia",
  NAURU: "Nauru",
  NEPAL: "Nepal",
  NETHERLANDS_ANTILLES: "Netherlands Antilles",
  NEW_CALEDONIA: "New Caledonia",
  NEW_ZEALAND: "New Zealand",
  NICARAGUA: "Nicaragua",
  NIGER: "Niger",
  NIGERIA: "Nigeria",
  NIUE: "Niue",
  NONE: "None",
  NORFOLK_ISLAND: "Norfolk Island",
  NORTH_KOREA: "North Korea",
  NORTHERN_MARIANA_ISLANDS: "Northern Mariana Islands",
  NORWAY: "Norway",
  OMAN: "Oman",
  US_OUTLYING_ISLANDS: "Pacific/Johnston",
  MICRONESIA: "Pacific/Kosrae",
  PAKISTAN: "Pakistan",
  PALAU: "Palau",
  PANAMA: "Panama",
  PAPUA_NEW_GUINEA: "Papua New Guinea",
  PARAGUAY: "Paraguay",
  PERU: "Peru",
  PHILIPPINES: "Philippines",
  PITCAIRN_ISLANDS: "Pitcairn Islands",
  POLAND: "Poland",
  PORTUGAL: "Portugal",
  PUERTO_RICO: "Puerto Rico",
  QATAR: "Qatar",
  REUNION: "Reunion",
  ROMANIA: "Romania",
  RUSSIA: "Russia",
  RWANDA: "Rwanda",
  SAINT_BARTHELEMY: "Saint Barthelemy",
  SAINT_HELENA: "Saint Helena",
  SAINT_LUCIA: "Saint Lucia",
  SAINT_MARTIN: "Saint Martin",
  SAINT_PIERRE_AND_MIQUELON: "Saint Pierre and Miquelon",
  SAMOA: "Samoa",
  SAN_MARINO: "San Marino",
  SAO_TOME_AND_PRINCIPE: "Sao Tome and Principe",
  SAUDI_ARABIA: "Saudi Arabia",
  SENEGAL: "Senegal",
  SERBIA: "Serbia",
  SEYCHELLES: "Seychelles",
  SIERRA_LEONE: "Sierra Leone",
  SINGAPORE: "Singapore",
  SLOVAKIA: "Slovakia",
  SLOVENIA: "Slovenia",
  SOLOMON_ISLANDS: "Solomon Islands",
  SOMALIA: "Somalia",
  SOUTH_AFRICA: "South Africa",
  SOUTH_KOREA: "South Korea",
  SOUTH_SUDAN: "South Sudan",
  SPAIN: "Spain",
  SRI_LANKA: "Sri Lanka",
  SUDAN: "Sudan",
  SURINAME: "Suriname",
  SVALBARD_AND_JAN_MAYEN: "Svalbard and Jan Mayen",
  SWAZILAND: "Swaziland",
  SWEDEN: "Sweden",
  SWITZERLAND: "Switzerland",
  SYRIA: "Syria",
  TAIWAN: "Taiwan",
  TAJIKISTAN: "Tajikistan",
  TANZANIA: "Tanzania",
  THAILAND: "Thailand",
  THE_NETHERLANDS: "The Netherlands",
  TIMOR_LESTE: "Timor-Leste",
  TOGO: "Togo",
  TOKELAU: "Tokelau",
  TONGA: "Tonga",
  TRINIDAD_AND_TOBAGO: "Trinidad and Tobago",
  TUNISIA: "Tunisia",
  TURKEY: "Turkey",
  TURKMENISTAN: "Turkmenistan",
  TURKS_AND_CAICOS_ISLANDS: "Turks and Caicos Islands",
  TUVALU: "Tuvalu",
  UGANDA: "Uganda",
  UKRAINE: "Ukraine",
  UNITED_ARAB_EMIRATES: "United Arab Emirates",
  UNITED_KINGDOM: "United Kingdom",
  UNITED_STATES: "United States",
  URUGUAY: "Uruguay",
  UZBEKISTAN: "Uzbekistan",
  VANUATU: "Vanuatu",
  VENEZUELA: "Venezuela",
  VIETNAM: "Vietnam",
  WALLIS_AND_FUTUNA: "Wallis and Futuna",
  WESTERN_SAHARA: "Western Sahara",
  YEMEN: "Yemen",
  ZAMBIA: "Zambia",
  ZIMBABWE: "Zimbabwe",
};

export const TIMEZONES = {
  IVORY_COAST_AFRICA_ABIDJAN: "Africa/Abidjan",
  GHANA_AFRICA_ACCRA: "Africa/Accra",
  ETHIOPIA_AFRICA_ADDIS_ABABA: "Africa/Addis Ababa",
  ALGERIA_AFRICA_ALGIERS: "Africa/Algiers",
  ERITREA_AFRICA_ASMARA: "Africa/Asmara",
  MALI_AFRICA_BAMAKO: "Africa/Bamako",
  CENTRAL_AFRICAN_REPUBLIC_AFRICA_BANGUI: "Africa/Bangui",
  THE_GAMBIA_AFRICA_BANJUL: "Africa/Banjul",
  GUINEA_BISSAU_AFRICA_BISSAU: "Africa/Bissau",
  MALAWI_AFRICA_BLANTYRE: "Africa/Blantyre",
  CONGO_REPUBLIC_AFRICA_BRAZZAVILLE: "Africa/Brazzaville",
  BURUNDI_AFRICA_BUJUMBURA: "Africa/Bujumbura",
  EGYPT_AFRICA_CAIRO: "Africa/Cairo",
  MOROCCO_AFRICA_CASABLANCA: "Africa/Casablanca",
  SPAIN_AFRICA_CEUTA: "Africa/Ceuta",
  GUINEA_AFRICA_CONAKRY: "Africa/Conakry",
  SENEGAL_AFRICA_DAKAR: "Africa/Dakar",
  TANZANIA_AFRICA_DAR_ES_SALAAM: "Africa/Dar es Salaam",
  DJIBOUTI_AFRICA_DJIBOUTI: "Africa/Djibouti",
  CAMEROON_AFRICA_DOUALA: "Africa/Douala",
  WESTERN_SAHARA_AFRICA_EL_AAIUN: "Africa/El Aaiun",
  SIERRA_LEONE_AFRICA_FREETOWN: "Africa/Freetown",
  BOTSWANA_AFRICA_GABORONE: "Africa/Gaborone",
  ZIMBABWE_AFRICA_HARARE: "Africa/Harare",
  SOUTH_AFRICA_AFRICA_JOHANNESBURG: "Africa/Johannesburg",
  UGANDA_AFRICA_KAMPALA: "Africa/Kampala",
  SUDAN_AFRICA_KHARTOUM: "Africa/Khartoum",
  RWANDA_AFRICA_KIGALI: "Africa/Kigali",
  DR_CONGO_AFRICA_KINSHASA: "Africa/Kinshasa",
  NIGERIA_AFRICA_LAGOS: "Africa/Lagos",
  GABON_AFRICA_LIBREVILLE: "Africa/Libreville",
  TOGO_AFRICA_LOME: "Africa/Lome",
  ANGOLA_AFRICA_LUANDA: "Africa/Luanda",
  DR_CONGO_AFRICA_LUBUMBASHI: "Africa/Lubumbashi",
  ZAMBIA_AFRICA_LUSAKA: "Africa/Lusaka",
  EQUATORIAL_GUINEA_AFRICA_MALABO: "Africa/Malabo",
  MOZAMBIQUE_AFRICA_MAPUTO: "Africa/Maputo",
  LESOTHO_AFRICA_MASERU: "Africa/Maseru",
  SWAZILAND_AFRICA_MBABANE: "Africa/Mbabane",
  SOMALIA_AFRICA_MOGADISHU: "Africa/Mogadishu",
  LIBERIA_AFRICA_MONROVIA: "Africa/Monrovia",
  KENYA_AFRICA_NAIROBI: "Africa/Nairobi",
  CHAD_AFRICA_NDJAMENA: "Africa/Ndjamena",
  NIGER_AFRICA_NIAMEY: "Africa/Niamey",
  MAURITANIA_AFRICA_NOUAKCHOTT: "Africa/Nouakchott",
  BURKINA_FASO_AFRICA_OUAGADOUGOU: "Africa/Ouagadougou",
  BENIN_AFRICA_PORTO_NOVO: "Africa/Porto-Novo",
  SAO_TOME_AND_PRINCIPE_AFRICA_SAO_TOME: "Africa/Sao Tome",
  LIBYA_AFRICA_TRIPOLI: "Africa/Tripoli",
  TUNISIA_AFRICA_TUNIS: "Africa/Tunis",
  NAMIBIA_AFRICA_WINDHOEK: "Africa/Windhoek",
  UNITED_STATES_AMERICA_ADAK: "America/Adak",
  UNITED_STATES_AMERICA_ANCHORAGE: "America/Anchorage",
  ANGUILLA_AMERICA_ANGUILLA: "America/Anguilla",
  ANTIGUA_AND_BARBUDA_AMERICA_ANTIGUA: "America/Antigua",
  BRAZIL_AMERICA_ARAGUAINA: "America/Araguaina",
  ARGENTINA_AMERICA_ARGENTINA_BUENOS_AIRES: "America/Argentina/Buenos Aires",
  ARGENTINA_AMERICA_ARGENTINA_CATAMARCA: "America/Argentina/Catamarca",
  ARGENTINA_AMERICA_ARGENTINA_CORDOBA: "America/Argentina/Cordoba",
  ARGENTINA_AMERICA_ARGENTINA_JUJUY: "America/Argentina/Jujuy",
  ARGENTINA_AMERICA_ARGENTINA_LA_RIOJA: "America/Argentina/La Rioja",
  ARGENTINA_AMERICA_ARGENTINA_MENDOZA: "America/Argentina/Mendoza",
  ARGENTINA_AMERICA_ARGENTINA_RIO_GALLEGOS: "America/Argentina/Rio Gallegos",
  ARGENTINA_AMERICA_ARGENTINA_SAN_JUAN: "America/Argentina/San Juan",
  ARGENTINA_AMERICA_ARGENTINA_TUCUMAN: "America/Argentina/Tucuman",
  ARGENTINA_AMERICA_ARGENTINA_USHUAIA: "America/Argentina/Ushuaia",
  ARUBA_AMERICA_ARUBA: "America/Aruba",
  PARAGUAY_AMERICA_ASUNCION: "America/Asuncion",
  CANADA_AMERICA_ATIKOKAN: "America/Atikokan",
  BRAZIL_AMERICA_BAHIA: "America/Bahia",
  BARBADOS_AMERICA_BARBADOS: "America/Barbados",
  BRAZIL_AMERICA_BELEM: "America/Belem",
  BELIZE_AMERICA_BELIZE: "America/Belize",
  CANADA_AMERICA_BLANC_SABLON: "America/Blanc-Sablon",
  BRAZIL_AMERICA_BOA_VISTA: "America/Boa Vista",
  COLOMBIA_AMERICA_BOGOTA: "America/Bogota",
  UNITED_STATES_AMERICA_BOISE: "America/Boise",
  CANADA_AMERICA_CAMBRIDGE_BAY: "America/Cambridge Bay",
  BRAZIL_AMERICA_CAMPO_GRANDE: "America/Campo Grande",
  MEXICO_AMERICA_CANCUN: "America/Cancun",
  VENEZUELA_AMERICA_CARACAS: "America/Caracas",
  FRENCH_GUIANA_AMERICA_CAYENNE: "America/Cayenne",
  CAYMAN_ISLANDS_AMERICA_CAYMAN: "America/Cayman",
  UNITED_STATES_AMERICA_CHICAGO: "America/Chicago",
  MEXICO_AMERICA_CHIHUAHUA: "America/Chihuahua",
  COSTA_RICA_AMERICA_COSTA_RICA: "America/Costa Rica",
  BRAZIL_AMERICA_CUIABA: "America/Cuiaba",
  NETHERLANDS_ANTILLES_AMERICA_CURACAO: "America/Curacao",
  GREENLAND_AMERICA_DANMARKSHAVN: "America/Danmarkshavn",
  CANADA_AMERICA_DAWSON: "America/Dawson",
  CANADA_AMERICA_DAWSON_CREEK: "America/Dawson Creek",
  UNITED_STATES_AMERICA_DENVER: "America/Denver",
  UNITED_STATES_AMERICA_DETROIT: "America/Detroit",
  DOMINICA_AMERICA_DOMINICA: "America/Dominica",
  CANADA_AMERICA_EDMONTON: "America/Edmonton",
  BRAZIL_AMERICA_EIRUNEPE: "America/Eirunepe",
  EL_SALVADOR_AMERICA_EL_SALVADOR: "America/El Salvador",
  BRAZIL_AMERICA_FORTALEZA: "America/Fortaleza",
  CANADA_AMERICA_GLACE_BAY: "America/Glace Bay",
  GREENLAND_AMERICA_GODTHAB: "America/Godthab",
  CANADA_AMERICA_GOOSE_BAY: "America/Goose Bay",
  TURKS_AND_CAICOS_ISLANDS_AMERICA_GRAND_TURK: "America/Grand Turk",
  GRENADA_AMERICA_GRENADA: "America/Grenada",
  GUADELOUPE_AMERICA_GUADELOUPE: "America/Guadeloupe",
  GUATEMALA_AMERICA_GUATEMALA: "America/Guatemala",
  ECUADOR_AMERICA_GUAYAQUIL: "America/Guayaquil",
  GUYANA_AMERICA_GUYANA: "America/Guyana",
  CANADA_AMERICA_HALIFAX: "America/Halifax",
  CUBA_AMERICA_HAVANA: "America/Havana",
  MEXICO_AMERICA_HERMOSILLO: "America/Hermosillo",
  UNITED_STATES_AMERICA_INDIANA_INDIANAPOLIS: "America/Indiana/Indianapolis",
  UNITED_STATES_AMERICA_INDIANA_KNOX: "America/Indiana/Knox",
  UNITED_STATES_AMERICA_INDIANA_MARENGO: "America/Indiana/Marengo",
  UNITED_STATES_AMERICA_INDIANA_PETERSBURG: "America/Indiana/Petersburg",
  UNITED_STATES_AMERICA_INDIANA_TELL_CITY: "America/Indiana/Tell City",
  UNITED_STATES_AMERICA_INDIANA_VEVAY: "America/Indiana/Vevay",
  UNITED_STATES_AMERICA_INDIANA_VINCENNES: "America/Indiana/Vincennes",
  UNITED_STATES_AMERICA_INDIANA_WINAMAC: "America/Indiana/Winamac",
  CANADA_AMERICA_INUVIK: "America/Inuvik",
  CANADA_AMERICA_IQALUIT: "America/Iqaluit",
  JAMAICA_AMERICA_JAMAICA: "America/Jamaica",
  UNITED_STATES_AMERICA_JUNEAU: "America/Juneau",
  UNITED_STATES_AMERICA_KENTUCKY_LOUISVILLE: "America/Kentucky/Louisville",
  UNITED_STATES_AMERICA_KENTUCKY_MONTICELLO: "America/Kentucky/Monticello",
  BOLIVIA_AMERICA_LA_PAZ: "America/La Paz",
  PERU_AMERICA_LIMA: "America/Lima",
  UNITED_STATES_AMERICA_LOS_ANGELES: "America/Los Angeles",
  BRAZIL_AMERICA_MACEIO: "America/Maceio",
  NICARAGUA_AMERICA_MANAGUA: "America/Managua",
  BRAZIL_AMERICA_MANAUS: "America/Manaus",
  SAINT_MARTIN_AMERICA_MARIGOT: "America/Marigot",
  MARTINIQUE_AMERICA_MARTINIQUE: "America/Martinique",
  MEXICO_AMERICA_MAZATLAN: "America/Mazatlan",
  UNITED_STATES_AMERICA_MENOMINEE: "America/Menominee",
  MEXICO_AMERICA_MERIDA: "America/Merida",
  MEXICO_AMERICA_MEXICO_CITY: "America/Mexico City",
  SAINT_PIERRE_AND_MIQUELON_AMERICA_MIQUELON: "America/Miquelon",
  CANADA_AMERICA_MONCTON: "America/Moncton",
  MEXICO_AMERICA_MONTERREY: "America/Monterrey",
  URUGUAY_AMERICA_MONTEVIDEO: "America/Montevideo",
  CANADA_AMERICA_MONTREAL: "America/Montreal",
  MONTSERRAT_AMERICA_MONTSERRAT: "America/Montserrat",
  BAHAMAS_AMERICA_NASSAU: "America/Nassau",
  UNITED_STATES_AMERICA_NEW_YORK: "America/New York",
  CANADA_AMERICA_NIPIGON: "America/Nipigon",
  UNITED_STATES_AMERICA_NOME: "America/Nome",
  BRAZIL_AMERICA_NORONHA: "America/Noronha",
  UNITED_STATES_AMERICA_NORTH_DAKOTA_CENTER: "America/North Dakota/Center",
  UNITED_STATES_AMERICA_NORTH_DAKOTA_NEW_SALEM:
    "America/North Dakota/New Salem",
  PANAMA_AMERICA_PANAMA: "America/Panama",
  CANADA_AMERICA_PANGNIRTUNG: "America/Pangnirtung",
  SURINAME_AMERICA_PARAMARIBO: "America/Paramaribo",
  UNITED_STATES_AMERICA_PHOENIX: "America/Phoenix",
  TRINIDAD_AND_TOBAGO_AMERICA_PORT_OF_SPAIN: "America/Port of Spain",
  HAITI_AMERICA_PORT_AU_PRINCE: "America/Port-au-Prince",
  BRAZIL_AMERICA_PORTO_VELHO: "America/Porto Velho",
  PUERTO_RICO_AMERICA_PUERTO_RICO: "America/Puerto Rico",
  CANADA_AMERICA_RAINY_RIVER: "America/Rainy River",
  CANADA_AMERICA_RANKIN_INLET: "America/Rankin Inlet",
  BRAZIL_AMERICA_RECIFE: "America/Recife",
  CANADA_AMERICA_REGINA: "America/Regina",
  CANADA_AMERICA_RESOLUTE: "America/Resolute",
  BRAZIL_AMERICA_RIO_BRANCO: "America/Rio Branco",
  CHILE_AMERICA_SANTIAGO: "America/Santiago",
  DOMINICAN_REPUBLIC_AMERICA_SANTO_DOMINGO: "America/Santo Domingo",
  BRAZIL_AMERICA_SAO_PAULO: "America/Sao Paulo",
  GREENLAND_AMERICA_SCORESBYSUND: "America/Scoresbysund",
  UNITED_STATES_AMERICA_SHIPROCK: "America/Shiprock",
  ST_KITTS_AND_NEVIS_AMERICA_ST_KITTS: "America/St Kitts",
  US_VIRGIN_ISLANDS_AMERICA_ST_THOMAS: "America/St Thomas",
  ST_VINCENT_AND_THE_GRENADINES_AMERICA_ST_VINCENT: "America/St Vincent",
  SAINT_BARTHELEMY_AMERICA_ST_BARTHELEMY: "America/St. Barthelemy",
  CANADA_AMERICA_ST_JOHNS: "America/St. Johns",
  SAINT_LUCIA_AMERICA_ST_LUCIA: "America/St. Lucia",
  CANADA_AMERICA_SWIFT_CURRENT: "America/Swift Current",
  HONDURAS_AMERICA_TEGUCIGALPA: "America/Tegucigalpa",
  GREENLAND_AMERICA_THULE: "America/Thule",
  CANADA_AMERICA_THUNDER_BAY: "America/Thunder Bay",
  MEXICO_AMERICA_TIJUANA: "America/Tijuana",
  CANADA_AMERICA_TORONTO: "America/Toronto",
  BRITISH_VIRGIN_ISLANDS_AMERICA_TORTOLA: "America/Tortola",
  CANADA_AMERICA_VANCOUVER: "America/Vancouver",
  CANADA_AMERICA_WHITEHORSE: "America/Whitehorse",
  CANADA_AMERICA_WINNIPEG: "America/Winnipeg",
  UNITED_STATES_AMERICA_YAKUTAT: "America/Yakutat",
  CANADA_AMERICA_YELLOWKNIFE: "America/Yellowknife",
  ANTARCTICA_CASEY: "Antarctica/Casey",
  ANTARCTICA_DAVIS: "Antarctica/Davis",
  ANTARCTICA_DUMONTDURVILLE: "Antarctica/DumontDUrville",
  ANTARCTICA_MAWSON: "Antarctica/Mawson",
  ANTARCTICA_MCMURDO: "Antarctica/McMurdo",
  ANTARCTICA_PALMER: "Antarctica/Palmer",
  ANTARCTICA_ROTHERA: "Antarctica/Rothera",
  ANTARCTICA_SOUTH_POLE: "Antarctica/South Pole",
  ANTARCTICA_SYOWA: "Antarctica/Syowa",
  ANTARCTICA_VOSTOK: "Antarctica/Vostok",
  SVALBARD_AND_JAN_MAYEN_ARCTIC_LONGYEARBYEN: "Arctic/Longyearbyen",
  YEMEN_ASIA_ADEN: "Asia/Aden",
  KAZAKHSTAN_ASIA_ALMATY: "Asia/Almaty",
  JORDAN_ASIA_AMMAN: "Asia/Amman",
  RUSSIA_ASIA_ANADYR: "Asia/Anadyr",
  KAZAKHSTAN_ASIA_AQTAU: "Asia/Aqtau",
  KAZAKHSTAN_ASIA_AQTOBE: "Asia/Aqtobe",
  TURKMENISTAN_ASIA_ASHGABAT: "Asia/Ashgabat",
  IRAQ_ASIA_BAGHDAD: "Asia/Baghdad",
  BAHRAIN_ASIA_BAHRAIN: "Asia/Bahrain",
  AZERBAIJAN_ASIA_BAKU: "Asia/Baku",
  THAILAND_ASIA_BANGKOK: "Asia/Bangkok",
  LEBANON_ASIA_BEIRUT: "Asia/Beirut",
  KYRGYZSTAN_ASIA_BISHKEK: "Asia/Bishkek",
  BRUNEI_ASIA_BRUNEI: "Asia/Brunei",
  MONGOLIA_ASIA_CHOIBALSAN: "Asia/Choibalsan",
  CHINA_ASIA_CHONGQING: "Asia/Chongqing",
  SRI_LANKA_ASIA_COLOMBO: "Asia/Colombo",
  SYRIA_ASIA_DAMASCUS: "Asia/Damascus",
  BANGLADESH_ASIA_DHAKA: "Asia/Dhaka",
  TIMOR_LESTE_ASIA_DILI: "Asia/Dili",
  UNITED_ARAB_EMIRATES_ASIA_DUBAI: "Asia/Dubai",
  TAJIKISTAN_ASIA_DUSHANBE: "Asia/Dushanbe",
  PALESTINE_ASIA_GAZA: "Asia/Gaza",
  CHINA_ASIA_HARBIN: "Asia/Harbin",
  HONG_KONG_ASIA_HONG_KONG: "Asia/Hong Kong",
  MONGOLIA_ASIA_HOVD: "Asia/Hovd",
  RUSSIA_ASIA_IRKUTSK: "Asia/Irkutsk",
  INDONESIA_ASIA_JAKARTA: "Asia/Jakarta",
  INDONESIA_ASIA_JAYAPURA: "Asia/Jayapura",
  ISRAEL_ASIA_JERUSALEM: "Asia/Jerusalem",
  AFGHANISTAN_ASIA_KABUL: "Asia/Kabul",
  RUSSIA_ASIA_KAMCHATKA: "Asia/Kamchatka",
  PAKISTAN_ASIA_KARACHI: "Asia/Karachi",
  CHINA_ASIA_KASHGAR: "Asia/Kashgar",
  NEPAL_ASIA_KATMANDU: "Asia/Katmandu",
  INDIA_ASIA_KOLKATA: "Asia/Kolkata",
  RUSSIA_ASIA_KRASNOYARSK: "Asia/Krasnoyarsk",
  MALAYSIA_ASIA_KUALA_LUMPUR: "Asia/Kuala Lumpur",
  MALAYSIA_ASIA_KUCHING: "Asia/Kuching",
  KUWAIT_ASIA_KUWAIT: "Asia/Kuwait",
  MACAO_ASIA_MACAU: "Asia/Macau",
  RUSSIA_ASIA_MAGADAN: "Asia/Magadan",
  INDONESIA_ASIA_MAKASSAR: "Asia/Makassar",
  PHILIPPINES_ASIA_MANILA: "Asia/Manila",
  OMAN_ASIA_MUSCAT: "Asia/Muscat",
  CYPRUS_ASIA_NICOSIA: "Asia/Nicosia",
  RUSSIA_ASIA_NOVOSIBIRSK: "Asia/Novosibirsk",
  RUSSIA_ASIA_OMSK: "Asia/Omsk",
  KAZAKHSTAN_ASIA_ORAL: "Asia/Oral",
  CAMBODIA_ASIA_PHNOM_PENH: "Asia/Phnom Penh",
  INDONESIA_ASIA_PONTIANAK: "Asia/Pontianak",
  NORTH_KOREA_ASIA_PYONGYANG: "Asia/Pyongyang",
  QATAR_ASIA_QATAR: "Asia/Qatar",
  KAZAKHSTAN_ASIA_QYZYLORDA: "Asia/Qyzylorda",
  MYANMAR_ASIA_RANGOON: "Asia/Rangoon",
  SAUDI_ARABIA_ASIA_RIYADH: "Asia/Riyadh",
  VIETNAM_ASIA_SAIGON: "Asia/Saigon",
  RUSSIA_ASIA_SAKHALIN: "Asia/Sakhalin",
  UZBEKISTAN_ASIA_SAMARKAND: "Asia/Samarkand",
  SOUTH_KOREA_ASIA_SEOUL: "Asia/Seoul",
  CHINA_ASIA_SHANGHAI: "Asia/Shanghai",
  SINGAPORE_ASIA_SINGAPORE: "Asia/Singapore",
  TAIWAN_ASIA_TAIPEI: "Asia/Taipei",
  UZBEKISTAN_ASIA_TASHKENT: "Asia/Tashkent",
  GEORGIA_ASIA_TBILISI: "Asia/Tbilisi",
  IRAN_ASIA_TEHRAN: "Asia/Tehran",
  BHUTAN_ASIA_THIMPHU: "Asia/Thimphu",
  JAPAN_ASIA_TOKYO: "Asia/Tokyo",
  MONGOLIA_ASIA_ULAANBAATAR: "Asia/Ulaanbaatar",
  CHINA_ASIA_URUMQI: "Asia/Urumqi",
  LAOS_ASIA_VIENTIANE: "Asia/Vientiane",
  RUSSIA_ASIA_VLADIVOSTOK: "Asia/Vladivostok",
  RUSSIA_ASIA_YAKUTSK: "Asia/Yakutsk",
  RUSSIA_ASIA_YEKATERINBURG: "Asia/Yekaterinburg",
  ARMENIA_ASIA_YEREVAN: "Asia/Yerevan",
  PORTUGAL_ATLANTIC_AZORES: "Atlantic/Azores",
  BERMUDA_ATLANTIC_BERMUDA: "Atlantic/Bermuda",
  SPAIN_ATLANTIC_CANARY: "Atlantic/Canary",
  CABO_VERDE_ATLANTIC_CAPE_VERDE: "Atlantic/Cape Verde",
  FAROE_ISLANDS_ATLANTIC_FAROE: "Atlantic/Faroe",
  PORTUGAL_ATLANTIC_MADEIRA: "Atlantic/Madeira",
  ICELAND_ATLANTIC_REYKJAVIK: "Atlantic/Reykjavik",
  SOUTH_GEORGIA_AND_SOUTH_SANDWICH_ISLANDS_ATLANTIC_SOUTH_GEORGIA:
    "Atlantic/South Georgia",
  SAINT_HELENA_ATLANTIC_ST_HELENA: "Atlantic/St Helena",
  FALKLAND_ISLANDS_ATLANTIC_STANLEY: "Atlantic/Stanley",
  AUSTRALIA_ADELAIDE: "Australia/Adelaide",
  AUSTRALIA_BRISBANE: "Australia/Brisbane",
  AUSTRALIA_BROKEN_HILL: "Australia/Broken Hill",
  AUSTRALIA_CURRIE: "Australia/Currie",
  AUSTRALIA_DARWIN: "Australia/Darwin",
  AUSTRALIA_EUCLA: "Australia/Eucla",
  AUSTRALIA_HOBART: "Australia/Hobart",
  AUSTRALIA_LINDEMAN: "Australia/Lindeman",
  AUSTRALIA_LORD_HOWE: "Australia/LordHowe",
  AUSTRALIA_MELBOURNE: "Australia/Melbourne",
  AUSTRALIA_PERTH: "Australia/Perth",
  AUSTRALIA_SYDNEY: "Australia/Sydney",
  THE_NETHERLANDS_EUROPE_AMSTERDAM: "Europe/Amsterdam",
  ANDORRA_EUROPE_ANDORRA: "Europe/Andorra",
  GREECE_EUROPE_ATHENS: "Europe/Athens",
  SERBIA_EUROPE_BELGRADE: "Europe/Belgrade",
  GERMANY_EUROPE_BERLIN: "Europe/Berlin",
  SLOVAKIA_EUROPE_BRATISLAVA: "Europe/Bratislava",
  BELGIUM_EUROPE_BRUSSELS: "Europe/Brussels",
  ROMANIA_EUROPE_BUCHAREST: "Europe/Bucharest",
  HUNGARY_EUROPE_BUDAPEST: "Europe/Budapest",
  MOLDOVA_EUROPE_CHISINAU: "Europe/Chisinau",
  DENMARK_EUROPE_COPENHAGEN: "Europe/Copenhagen",
  IRELAND_EUROPE_DUBLIN: "Europe/Dublin",
  GIBRALTAR_EUROPE_GIBRALTAR: "Europe/Gibraltar",
  GUERNSEY_EUROPE_GUERNSEY: "Europe/Guernsey",
  FINLAND_EUROPE_HELSINKI: "Europe/Helsinki",
  ISLE_OF_MAN_EUROPE_ISLE_OF_MAN: "Europe/Isle of Man",
  TURKEY_EUROPE_ISTANBUL: "Europe/Istanbul",
  JERSEY_EUROPE_JERSEY: "Europe/Jersey",
  RUSSIA_EUROPE_KALININGRAD: "Europe/Kaliningrad",
  UKRAINE_EUROPE_KIEV: "Europe/Kiev",
  PORTUGAL_EUROPE_LISBON: "Europe/Lisbon",
  SLOVENIA_EUROPE_LJUBLJANA: "Europe/Ljubljana",
  UNITED_KINGDOM_EUROPE_LONDON: "Europe/London",
  LUXEMBOURG_EUROPE_LUXEMBOURG: "Europe/Luxembourg",
  SPAIN_EUROPE_MADRID: "Europe/Madrid",
  MALTA_EUROPE_MALTA: "Europe/Malta",
  ALAND_EUROPE_MARIEHAMN: "Europe/Mariehamn",
  BELARUS_EUROPE_MINSK: "Europe/Minsk",
  MONACO_EUROPE_MONACO: "Europe/Monaco",
  RUSSIA_EUROPE_MOSCOW: "Europe/Moscow",
  NORWAY_EUROPE_OSLO: "Europe/Oslo",
  FRANCE_EUROPE_PARIS: "Europe/Paris",
  MONTENEGRO_EUROPE_PODGORICA: "Europe/Podgorica",
  CZECHIA_EUROPE_PRAGUE: "Europe/Prague",
  LATVIA_EUROPE_RIGA: "Europe/Riga",
  ITALY_EUROPE_ROME: "Europe/Rome",
  RUSSIA_EUROPE_SAMARA: "Europe/Samara",
  SAN_MARINO_EUROPE_SAN_MARINO: "Europe/San Marino",
  BOSNIA_AND_HERZEGOVINA_EUROPE_SARAJEVO: "Europe/Sarajevo",
  UKRAINE_EUROPE_SIMFEROPOL: "Europe/Simferopol",
  NORTH_MACEDONIA_EUROPE_SKOPJE: "Europe/Skopje",
  BULGARIA_EUROPE_SOFIA: "Europe/Sofia",
  SWEDEN_EUROPE_STOCKHOLM: "Europe/Stockholm",
  ESTONIA_EUROPE_TALLINN: "Europe/Tallinn",
  ALBANIA_EUROPE_TIRANE: "Europe/Tirane",
  UKRAINE_EUROPE_UZHGOROD: "Europe/Uzhgorod",
  LIECHTENSTEIN_EUROPE_VADUZ: "Europe/Vaduz",
  VATICAN_CITY_EUROPE_VATICAN: "Europe/Vatican",
  AUSTRIA_EUROPE_VIENNA: "Europe/Vienna",
  LITHUANIA_EUROPE_VILNIUS: "Europe/Vilnius",
  RUSSIA_EUROPE_VOLGOGRAD: "Europe/Volgograd",
  POLAND_EUROPE_WARSAW: "Europe/Warsaw",
  CROATIA_EUROPE_ZAGREB: "Europe/Zagreb",
  UKRAINE_EUROPE_ZAPOROZHYE: "Europe/Zaporozhye",
  SWITZERLAND_EUROPE_ZURICH: "Europe/Zurich",
  GMT: "GMT",
  GMT_01_00_AZORES: "GMT-01:00",
  GMT_02_00_MID_ATLANTIC: "GMT-02:00",
  GMT_03_00_BRAZIL: "GMT-03:00",
  GMT_03_00_ARGENTINA: "GMT-03:00",
  GMT_03_30_NEWFOUNDLAND_CANADA: "GMT-03:30",
  GMT_04_00_ATLANTIC_TIME: "GMT-04:00",
  GMT_05_00_COLUMBIA_PERU_SOUTH_AMERICA: "GMT-05:00",
  GMT_05_00_US_EASTERN_TIME_INDIANA: "GMT-05:00",
  GMT_05_00_US_EASTERN_TIME: "GMT-05:00",
  GMT_06_00_MEXICO: "GMT-06:00",
  GMT_06_00_US_CENTRAL_TIME: "GMT-06:00",
  GMT_07_00_US_MOUNTAIN_TIME_ARIZONA: "GMT-07:00",
  GMT_07_00_US_MOUNTAIN_TIME: "GMT-07:00",
  GMT_08_00_PACIFIC_TIME: "GMT-08:00",
  GMT_08_30_PITCARN: "GMT-08:30",
  GMT_09_00_US_ALASKA_TIME: "GMT-09:00",
  GMT_09_30_MARQUESAS: "GMT-09:30",
  GMT_10_00_US_HAWAIIAN_TIME: "GMT-10:00",
  GMT_11_00_SAMOA: "GMT-11:00",
  GMT_12_00_DATELINE: "GMT-12:00",
  GMT_01_00_WESTERN_EUROPE_GMT_01_00: "GMT+01:00",
  GMT_02_00_ISRAEL_GMT_02_00: "GMT+02:00",
  GMT_02_00_EGYPT_GMT_02_00: "GMT+02:00",
  GMT_02_00_EASTERN_EUROPE_GMT_02_00: "GMT+02:00",
  GMT_03_00_SAUDI_ARABIA_GMT_03_00: "GMT+03:00",
  GMT_03_00_RUSSIA_GMT_03_00: "GMT+03:00",
  GMT_03_30_IRAN_GMT_03_30: "GMT+03:30",
  GMT_04_00_ARABIAN_GMT_04_00: "GMT+04:00",
  GMT_04_30_AFGHANISTAN_GMT_04_30: "GMT+04:30",
  GMT_05_00_PAKISTAN_WEST_ASIA_GMT_05_00: "GMT+05:00",
  GMT_05_30_INDIA_GMT_05_30: "GMT+05:30",
  GMT_06_00_BANGLADESH_CENTRAL_ASIA_GMT_06_00: "GMT+06:00",
  GMT_06_30_BURMA_GMT_06_30: "GMT+06:30",
  GMT_07_00_BANGKOK_HANOI_JAKARTA_GMT_07_00: "GMT+07:00",
  GMT_08_00_AUSTRALIA_WT_GMT_08_00: "GMT+08:00",
  GMT_08_00_SINGAPORE_GMT_08_00: "GMT+08:00",
  GMT_08_00_CHINA_TAIWAN_GMT_08_00: "GMT+08:00",
  GMT_09_00_KOREA_GMT_09_00: "GMT+09:00",
  GMT_09_00_JAPAN_GMT_09_00: "GMT+09:00",
  GMT_09_30_AUSTRALIA_CT_GMT_09_30: "GMT+09:30",
  GMT_10_00_AUSTRALIA_ET_GMT_10_00: "GMT+10:00",
  GMT_10_30_AUSTRALIA_LORD_HOWE_GMT_10_30: "GMT+10:30",
  GMT_11_00_CENTRAL_PACIFIC_GMT_11_00: "GMT+11:00",
  GMT_11_30_NORFOLK_ISLANDS_GMT_11_30: "GMT+11:30",
  GMT_12_00_FIJI_NEW_ZEALAND_GMT_12_00: "GMT+12:00",
  MADAGASCAR_INDIAN_ANTANANARIVO: "Indian/Antananarivo",
  BRITISH_INDIAN_OCEAN_TERRITORY_INDIAN_CHAGOS: "Indian/Chagos",
  CHRISTMAS_ISLAND_INDIAN_CHRISTMAS: "Indian/Christmas",
  COCOS_KEELING_ISLANDS_INDIAN_COCOS: "Indian/Cocos",
  COMOROS_INDIAN_COMORO: "Indian/Comoro",
  FRENCH_SOUTHERN_TERRITORIES_INDIAN_KERGUELEN: "Indian/Kerguelen",
  SEYCHELLES_INDIAN_MAHE: "Indian/Mahe",
  MALDIVES_INDIAN_MALDIVES: "Indian/Maldives",
  MAURITIUS_INDIAN_MAURITIUS: "Indian/Mauritius",
  MAYOTTE_INDIAN_MAYOTTE: "Indian/Mayotte",
  REUNION_INDIAN_REUNION: "Indian/Reunion",
  NOT_SPECIFIED: "Not Specified",
  SAMOA_PACIFIC_APIA: "Pacific/Apia",
  NEW_ZEALAND_PACIFIC_AUCKLAND: "Pacific/Auckland",
  NEW_ZEALAND_PACIFIC_CHATHAM: "Pacific/Chatham",
  CHILE_PACIFIC_EASTER: "Pacific/Easter",
  VANUATU_PACIFIC_EFATE: "Pacific/Efate",
  KIRIBATI_PACIFIC_ENDERBURY: "Pacific/Enderbury",
  TOKELAU_PACIFIC_FAKAOFO: "Pacific/Fakaofo",
  FIJI_PACIFIC_FIJI: "Pacific/Fiji",
  TUVALU_PACIFIC_FUNAFUTI: "Pacific/Funafuti",
  ECUADOR_PACIFIC_GALAPAGOS: "Pacific/Galapagos",
  FRENCH_POLYNESIA_PACIFIC_GAMBIER: "Pacific/Gambier",
  SOLOMON_ISLANDS_PACIFIC_GUADALCANAL: "Pacific/Guadalcanal",
  GUAM_PACIFIC_GUAM: "Pacific/Guam",
  UNITED_STATES_PACIFIC_HONOLULU: "Pacific/Honolulu",
  US_OUTLYING_ISLANDS_PACIFIC_JOHNSTON: "Pacific/Johnston",
  KIRIBATI_PACIFIC_KIRITIMATI: "Pacific/Kiritimati",
  MICRONESIA_PACIFIC_KOSRAE: "Pacific/Kosrae",
  MARSHALL_ISLANDS_PACIFIC_KWAJALEIN: "Pacific/Kwajalein",
  MARSHALL_ISLANDS_PACIFIC_MAJURO: "Pacific/Majuro",
  FRENCH_POLYNESIA_PACIFIC_MARQUESAS: "Pacific/Marquesas",
  US_OUTLYING_ISLANDS_PACIFIC_MIDWAY: "Pacific/Midway",
  NAURU_PACIFIC_NAURU: "Pacific/Nauru",
  NIUE_PACIFIC_NIUE: "Pacific/Niue",
  NORFOLK_ISLAND_PACIFIC_NORFOLK: "Pacific/Norfolk",
  NEW_CALEDONIA_PACIFIC_NOUMEA: "Pacific/Noumea",
  AMERICAN_SAMOA_PACIFIC_PAGO_PAGO: "Pacific/PagoPago",
  PALAU_PACIFIC_PALAU: "Pacific/Palau",
  PITCAIRN_ISLANDS_PACIFIC_PITCAIRN: "Pacific/Pitcairn",
  MICRONESIA_PACIFIC_PONAPE: "Pacific/Ponape",
  PAPUA_NEW_GUINEA_PACIFIC_PORT_MORESBY: "Pacific/Port Moresby",
  COOK_ISLANDS_PACIFIC_RAROTONGA: "Pacific/Rarotonga",
  NORTHERN_MARIANA_ISLANDS_PACIFIC_SAIPAN: "Pacific/Saipan",
  FRENCH_POLYNESIA_PACIFIC_TAHITI: "Pacific/Tahiti",
  KIRIBATI_PACIFIC_TARAWA: "Pacific/Tarawa",
  TONGA_PACIFIC_TONGATAPU: "Pacific/Tongatapu",
  MICRONESIA_PACIFIC_TRUK: "Pacific/Truk",
  US_OUTLYING_ISLANDS_PACIFIC_WAKE: "Pacific/Wake",
  WALLIS_AND_FUTUNA_PACIFIC_WALLIS: "Pacific/Wallis",
};

export const trafficTypeOptions = () => [
  {
    id: "CORPORATE",
    label: t("locations.traffic-type.corporate"),
  },
  {
    id: "GUESTWIFI",
    label: t("locations.traffic-type.guestwifi"),
  },
  { id: "IOT", label: t("locations.traffic-type.iot") },
  { id: "SERVER", label: t("locations.traffic-type.server") },
  { id: "EXTRANET", label: t("locations.traffic-type.extranet") },
];

export const trafficTypesMapping = () => [
  ...trafficTypeOptions(),
  {
    id: "WORKLOAD",
    label: t("locations.traffic-type.workload"),
  },
];

export const listWeek = () => [
  { id: "Sunday", label: t("days.sunday"), val: 1 },
  { id: "Monday", label: t("days.monday"), val: 2 },
  { id: "Tuesday", label: t("days.tuesday"), val: 3 },
  { id: "Wednesday", label: t("days.wednesday"), val: 4 },
  { id: "Thursday", label: t("days.thursday"), val: 5 },
  { id: "Friday", label: t("days.friday"), val: 6 },
  { id: "Saturday", label: t("days.saturday"), val: 7 },
];

export const MGT = "MGT";

export const FORWARDING = "FORWARDING";

export const ENABLED = "Enabled";

export const DISABLED = "Disabled";

export const ENABLING = "ENABLING";

export const DISABLING = "DISABLING";

export const DEFAULTGATEWAY = "Default Gateway";

export const DNS = "DNS Server";

export const DOMAINNAME = "Domain Name";

export const TIMEMAPPING: Record<string, number> = {
  DAY: 30,
  HOUR: 720,
  MINUTE: 43200,
};

export const DHCPOptionsPlaceholderMapping: Record<string, string> = {
  "Default Gateway": "x.x.x.x",
  "DNS Server": "x.x.x.x, x.x.x.x, x.x.x.x, x.x.x.x",
  "Domain Name":
    "mycompany1.com, mycompany2.com, mycompany3.com, mycompany4.com",
};

export const TIMEUNITMAPPING: Record<string, string> = {
  DAY: "days",
  HOUR: "hours",
  MINUTE: "minutes",
};

export const DEFAULTLEASETIME: Record<string, string> = {
  DEFAULTLEASETIME: "86400",
  MAXLEASETIME: "604800",
};

export const LEASETIME: Record<string, number> = {
  MAXLEASE: 31536000,
};

export const LOCATION_PRODUCTS = ["ZIA", "CLOUD_CONNECTOR"] as Product[];

export const IP_CIDR_PLACEHOLDER = "x.x.x.x/xx";

export const IP_PLACEHOLDER = "x.x.x.x";

export const DEFAULT_INTERFACE_MTU = 1500;

export const SUBINTERFACE_MTU_BUFFER = 4; // ZTW appliance subinterface MTU limit is 4 bytes less than the parent interface MTU count.

export const ADMIN_ACCESS_TYPE = "READ_WRITE"; // Used to check for Admin access for locations & appliances.

export const VM_SIZES = {
  SMALL: "SMALL",
  MEDIUM: "MEDIUM",
  LARGE: "LARGE",
};

export const DEFAULT_IP_ADDRESS = "0.0.0.0";

export const DEFAULT_IP_ADDRESS_WITH_CIDR = "0.0.0.0/0";

export const UNMAP_USERS_TIME_DEFAULT = "8";

export const REVALIDATION_TIME_DEFAULT = "4";

export const SHARED_BANDWIDTH_TYPE = "Shared";

export const DEDICATED_BANDWIDTH_TYPE = "Dedicated";
