/* eslint-disable @up/unified-platform/max-file-lines */
"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { type ReactElement, useEffect, useState } from "react";
import useSWR from "swr";
import { faPen } from "@fortawesome/pro-regular-svg-icons";
import useSWRMutation, { type SWRMutationResponse } from "swr/mutation";
import { Alert } from "@xc/legacy-components";
import { LoadingSpinner } from "@zs-nimbus/core";
import { useTranslation } from "react-i18next";
import { Breadcrumbs, type BreadcrumbsItemsArrayProps } from "@zs-nimbus/core";
import LADetailLayout from "../LADetailLayout/LADetailLayout";
import { StatusDetailsMap } from "../Locations/Appliances";
import { extractErrorDetails, hasProductRights } from "../helper";
import { ADMIN_ACCESS_TYPE, LOCATION_PRODUCTS } from "../constants";
import responseTransformer, {
  type TransformedApplianceDetails,
  type ApplianceDetailFromServer,
} from "./ApplianceDetailTransformer";
import { ApplianceDetailsData } from "./components/AppliancesDetailsContent/AppliancesDetailsContent.data";
import ApplianceNetwork from "./components/ApplianceNetwork/ApplianceNetwork";
import { APPLIANCE_NETWORK_DATA } from "./components/ApplianceNetwork/ApplianceNetwork.data";
import ApplianceInterfaces from "./components/ApplianceInterfaces/ApplianceInterfaces";
import { APPLIANCE_INTERFACES_DATA } from "./components/ApplianceInterfaces/ApplianceInterfaces.data";
import AppConnector from "./components/AppConnector/AppConnector";
import { APP_CONNECTOR_DATA } from "./components/AppConnector/AppConnector.data";
import UpgradeSchedule from "./components/UpgradeSchedule/UpgradeSchedule";
import { UPGRADE_SCHEDULE_DATA } from "./components/UpgradeSchedule/UpgradeSchedule.data";
import { APPLIANCE_TROUBLESHOOTING_DATA } from "./components/ApplianceTroubleshooting/ApplianceTroubleshooting.data";
import ApplianceTroubleshooting from "./components/ApplianceTroubleshooting/ApplianceTroubleshooting";
import ZT400Reflection from "./icons/ZT400Reflection";
import ZT800Reflection from "./icons/ZT800Reflection";
import ZT600Reflection from "./icons/ZT600Reflection";
import REDHATReflection from "./icons/REDHATReflection";
import VMWAREESXIReflection from "./icons/VMWAREESXIReflection";
import MSHYPERVReflection from "./icons/MSHYPERVReflection";
import { APPLIANCE_ENDPOINTS } from "./config/apiUtils";
import VMInterface from "./components/ApplianceVMInterface/ApplianceVMInterface";
import { VM_INTERFACE_DATA } from "./components/ApplianceVMInterface/ApplianceVMInterface.data";
import { AppliancesDetailsContent } from "./components/AppliancesDetailsContent/AppliancesDetailsContent";
import { UnifiedLocationApplianceQueries } from "./queries";
import { type TabConfig } from "@/components/VerticalTabs/VerticalTabs";
import {
  API_ENDPOINTS,
  deleteByIdReq,
  getReq,
  postReq,
  putReq,
  type ResData,
} from "@/utils/apiHelper";
import { WithStates } from "@/hoc/WithStates";
import { useFlags } from "@/context/FeatureFlags";
import StatusIndicator from "@/components/StatusIndicator/StatusIndicator";
import {
  endpointConditionHandler,
  type ErrorType,
  removeAlertMessage,
  showErrorAlert,
  showSuccessAlert,
} from "@/app/onboarding/apiHelper";
import { Overlay } from "@/components/OnboardingLayout/Overlay";
import { useProductAccessProvider } from "@/context/ProductAccessProvider";

export type ApplianceDetailProps = {
  applianceId?: string;
};

const getTabs = (
  data: TransformedApplianceDetails,
  triggerUpdateAppliance: (
    payload: ApplianceDetailFromServer,
    apitype: string,
  ) => void,
  onDelete: (id: string) => void,
  readOnly: boolean,
  applId: string,
): TabConfig[] => {
  const { apiResponse, applianceStatus, applianceDetailVersion } = data;
  const {
    applianceDetails: { serialNumber, model },
  } = apiResponse;
  const isPhysicalDevice = serialNumber !== null && model !== null;
  const applianceDetailTab = [
    {
      title: "appliances.menu.appliance-details",
      value: "appliance-details",
      content: () => (
        <AppliancesDetailsContent
          {...{
            ...ApplianceDetailsData,
            groups: [
              {
                heading: undefined,
                list: data.applianceDetails,
              },
            ],
            applianceName: data.applianceDetails[0].value,
            triggerUpdateAppliance: triggerUpdateAppliance,
            apiResponse: apiResponse,
            onDelete: onDelete,
            applianceStatus,
            applianceDetailVersion,
            readOnly,
            applId,
          }}
        />
      ),
      icon: "fa-solid fa-circle-info",
    },
    {
      title: "appliances.menu.network",
      value: "network",
      content: () => (
        <ApplianceNetwork
          {...APPLIANCE_NETWORK_DATA}
          applianceNetworkData={{
            ...APPLIANCE_NETWORK_DATA.applianceNetworkData,
            ...data.applianceNetwork,
          }}
          apiResponse={data.apiResponse}
          triggerUpdateAppliance={triggerUpdateAppliance}
          readOnly={readOnly}
        />
      ),
      icon: "fa-solid fa-sliders-up",
    },
    {
      title: "appliances.menu.interfaces",
      value: "interfaces",
      content: () =>
        isPhysicalDevice ? (
          <ApplianceInterfaces
            {...APPLIANCE_INTERFACES_DATA}
            tableRowData={[...(data?.applianceInterface ?? [])]}
            apiResponse={data.apiResponse}
            triggerUpdateAppliance={triggerUpdateAppliance}
            readOnly={readOnly}
          />
        ) : (
          <VMInterface
            {...VM_INTERFACE_DATA}
            managementInterface={data.vmInterface.managementInterface}
            forwardInterface={data.vmInterface.forwardInterface}
            apiResponse={data.apiResponse}
            triggerUpdateAppliance={triggerUpdateAppliance}
            readOnly={readOnly}
          />
        ),
      icon: "fa-solid fa-ethernet",
    },
    {
      title: "appliances.menu.app-connector",
      value: "app-connector",
      content: () => (
        <AppConnector
          {...APP_CONNECTOR_DATA}
          apiResponse={data.apiResponse}
          isPhysicalDevice={isPhysicalDevice}
          readOnly={readOnly}
          triggerUpdateAppliance={triggerUpdateAppliance}
          connectors={{
            id: 1,
            handleEditLocation: (id: string | number) => console.log(id),
            heading: "appliances.app-connector.heading",
            icon: faPen,
            connectorList: data?.applianceConnector?.connectorList,
            appConnectorInterface:
              data?.applianceConnectorInterface?.connectorList,
          }}
        />
      ),
      icon: "fa-regular fa-plug",
    },
    {
      title: "appliances.menu.upgrade-schedule",
      value: "upgrade-schedule",
      content: () => (
        <UpgradeSchedule
          {...UPGRADE_SCHEDULE_DATA}
          versionData={data.applianceUpgrade}
          initialScheduledData={data.applianceUpgrade.initialScheduledData}
          apiResponse={data.apiResponse}
          triggerUpdateAppliance={triggerUpdateAppliance}
          readOnly={readOnly}
        />
      ),
      icon: "fa-solid fa-clock",
    },
    {
      title: "appliances.menu.troubleshooting",
      value: "troubleshooting",
      content: () => (
        <ApplianceTroubleshooting
          {...APPLIANCE_TROUBLESHOOTING_DATA}
          locationSwitchList={APPLIANCE_TROUBLESHOOTING_DATA.locationSwitchList.map(
            (item, index) => ({
              ...item,
              isEnabled: data.applianceTroubleshooting[index].isEnabled,
            }),
          )}
          apiResponse={data.apiResponse}
          triggerUpdateAppliance={triggerUpdateAppliance}
          readOnly={readOnly}
        />
      ),
      icon: "fa-solid fa-bug",
    },
  ];
  const filterVirtualTabs = applianceDetailTab.filter(
    (_item, index) => !(index === 5),
  );

  return isPhysicalDevice ? applianceDetailTab : filterVirtualTabs;
};

const applianceIconMap = {
  ZT400: <ZT400Reflection />,
  ZT600: <ZT600Reflection />,
  ZT800: <ZT800Reflection />,
  REDHAT_LINUX: <REDHATReflection />,
  VMWARE_ESXI: <VMWAREESXIReflection />,
  MICROSOFT_HYPER_V: <MSHYPERVReflection />,
};

const ApplianceDetail = ({ applianceId = undefined }: ApplianceDetailProps) => {
  const { t } = useTranslation();
  const queryParams = useSearchParams();
  const router = useRouter();
  const pathName = usePathname();
  const tab = queryParams?.get("tab");
  const groupId = queryParams?.get("groupId");
  const applianceNameFromQueryParam = queryParams?.get("name");
  const status = queryParams?.get("status");
  const serialNumber = queryParams?.get("serialNumber");
  const template = queryParams?.get("template");
  const model = queryParams?.get("model");
  const match = /appliances\/(\d+)/.exec(pathName);
  const provStatusMessage = t(
    "appliances.details.toast-message.update-prov-status",
  );
  // TODO when BE is available
  // const id = searchParams.get("id");
  // const groupId = searchParams.get("groupId");
  const [applianceName, setApplianceName] = useState(
    applianceNameFromQueryParam,
  );
  const [detailData, setDetailData] = useState<
    TransformedApplianceDetails | undefined
  >();
  const [alertType, setAlertType] = useState("");
  const [alertMessage, setAlertMessage] = useState<ReactElement | string>();
  const [tabs, setTabs] = useState<TabConfig[]>();
  const [provStatusVal, setProvStatusVal] = useState("");
  const [operationalAdminStatus, setOperationalAdminStatus] = useState("");
  const { can } = useFlags();

  const productAccessInfo = useProductAccessProvider();
  const hasProducts = hasProductRights(LOCATION_PRODUCTS, productAccessInfo);

  if (!hasProducts || !can("showUnifiedLocations")) {
    window.location.replace("/error");
  }

  const applId = applianceId ?? match?.[1];

  const tabIndex = tab
    ? tabs?.findIndex((detailsTab) => detailsTab?.value === tab)
    : -1;

  const defaultTab = tabIndex !== -1 && tabIndex ? tabs?.[tabIndex] : tabs?.[0];

  const [selectedTab, setSelectedTabs] = useState<TabConfig | undefined>(
    defaultTab,
  );

  const { featurePermissions } = (productAccessInfo.features?.CLOUD_CONNECTOR ??
    {}) as {
    featurePermissions?: Record<string, string>;
  };

  const readOnly =
    featurePermissions?.EDGE_CONNECTOR_CLOUD_PROVISIONING !==
      ADMIN_ACCESS_TYPE &&
    featurePermissions?.EDGE_CONNECTOR_TEMPLATE !== ADMIN_ACCESS_TYPE;

  useEffect(() => {
    setSelectedTabs(defaultTab);
  }, [defaultTab]);

  const handleTabClick = (tab: TabConfig) => {
    setAlertType("");
    setAlertMessage("");
    setSelectedTabs(tab);
    const params = new URLSearchParams(queryParams.toString());
    params.set("tab", tab.value);
    router.push(`${pathName}?${params.toString()}`);
  };
  const triggerUpdateAppliance = (
    payload: ApplianceDetailFromServer,
    apiType: string,
  ) => {
    if (apiType === "appliance-prov-status") {
      setProvStatusVal(payload.applianceDetails.deploymentStatus);
    }
    if (apiType === "appliance-admin-status") {
      setOperationalAdminStatus(
        payload.applianceDetails?.operationalAdminStatus ?? "DISABLE",
      );
    } else {
      void updateAppliance[apiType as keyof typeof updateAppliance].trigger(
        payload,
      );
    }
  };

  const updateApplianceProvStatus = useSWR(
    endpointConditionHandler(
      provStatusVal,
      `${API_ENDPOINTS.ZUXP}/unified-locations/graphql`,
    ),
    (url: string) =>
      postReq(url, {
        arg: UnifiedLocationApplianceQueries.updateDeploymentStatus({
          provId: detailData!.apiResponse.provId!,
          status:
            provStatusVal === "NOT_DEPLOYED"
              ? "READY_TO_DEPLOY"
              : provStatusVal,
          name: detailData!.apiResponse?.applianceDetails?.name ?? "",
          provTemplateName: template ?? "",
        }),
      }),
    {
      onSuccess: () => {
        showSuccessAlert(provStatusMessage, setAlertType, setAlertMessage);
        removeAlertMessage(setAlertType, setAlertMessage);
        setProvStatusVal("");
      },
    },
  );

  useEffect(() => {
    void updateApplianceProvStatus.mutate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [provStatusVal]);

  const errorHandler = {
    onError: (err: Error) => {
      const errorCause = err.cause as ErrorType;
      const {
        info: { reason },
      } = errorCause;
      let errorMessage;
      if (reason) {
        errorMessage = extractErrorDetails(reason)?.message;
        errorCause.info.message = errorMessage;
      }

      showErrorAlert(errorCause, setAlertType, setAlertMessage);
    },
  };
  const timedErrorHandler = {
    onError: (err: Error) => {
      const errorCause = err.cause as ErrorType;
      const {
        info: { reason },
      } = errorCause;
      let errorMessage;
      if (reason) {
        errorMessage = extractErrorDetails(reason)?.message;
        errorCause.info.message = errorMessage;
      }
      showErrorAlert(errorCause, setAlertType, setAlertMessage);
      removeAlertMessage(setAlertType, setAlertMessage);
    },
  };
  const deleteApplianceHandler = {
    onSuccess: () => {
      router.push("/appliances");
    },
    ...timedErrorHandler,
  };

  const deleteSubLocationbyId = useSWRMutation(
    APPLIANCE_ENDPOINTS.deleteAppliance,
    deleteByIdReq,
    deleteApplianceHandler,
  );

  const updateApplianceOverview = useSWRMutation(
    APPLIANCE_ENDPOINTS.updateApplianceOverview(applId!),
    putReq,
    {
      onSuccess: () => {
        void applianceDetailFromServer.mutate();
      },
      ...errorHandler,
    },
  );
  const updateApplianceNetwork = useSWRMutation(
    APPLIANCE_ENDPOINTS.updateApplianceNetwork(applId!),
    putReq,
    {
      onSuccess: () => {
        void applianceDetailFromServer.mutate();
      },
      ...errorHandler,
    },
  );

  const updateApplianceSupportTunnel = useSWRMutation(
    APPLIANCE_ENDPOINTS.updateApplianceSupportTunnel(applId!),
    putReq,
    {
      onSuccess: () => {
        void applianceDetailFromServer.mutate();
      },
      ...errorHandler,
    },
  );

  const updateApplianceUpgradeSchedule = useSWRMutation(
    APPLIANCE_ENDPOINTS.updateApplianceUpgradeSchedule(applId!),
    putReq,
    {
      onSuccess: () => {
        void applianceDetailFromServer.mutate();
      },
      ...errorHandler,
    },
  );

  const updateApplianceInterface = useSWRMutation(
    APPLIANCE_ENDPOINTS.updateApplianceInterface(applId!),
    putReq,
    {
      onSuccess: () => {
        void applianceDetailFromServer.mutate();
      },
      ...errorHandler,
    },
  );
  const updateAppConnector = useSWRMutation(
    APPLIANCE_ENDPOINTS.updateAppConnector(applId!),
    putReq,
    {
      onSuccess: () => {
        void applianceDetailFromServer.mutate();
      },
      ...errorHandler,
    },
  );

  const deleteApplianceInterface = useSWRMutation(
    APPLIANCE_ENDPOINTS.deleteInterface(applId!),
    putReq,
    {
      onSuccess: () => {
        void applianceDetailFromServer.mutate();
      },
      ...errorHandler,
    },
  );

  const updateAdminStatus = useSWRMutation(
    APPLIANCE_ENDPOINTS.updateAdminStatus(
      groupId!,
      applId!,
      operationalAdminStatus,
    ),
    putReq,
    {
      onSuccess: () => {
        void applianceDetailFromServer.mutate();
      },
      ...errorHandler,
    },
  );
  useEffect(() => {
    if (operationalAdminStatus) {
      void updateAdminStatus.trigger();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [operationalAdminStatus]);
  const updateAppliance: {
    overview: SWRMutationResponse<ResData, any, string, object | undefined>;
    network: SWRMutationResponse<ResData, any, string, object | undefined>;
    "support-tunnel": SWRMutationResponse<
      ResData,
      any,
      string,
      object | undefined
    >;
    "upgrade-schedule": SWRMutationResponse<
      ResData,
      any,
      string,
      object | undefined
    >;
    "update-interface": SWRMutationResponse<
      ResData,
      any,
      string,
      object | undefined
    >;
    "update-appConnector": SWRMutationResponse<
      ResData,
      any,
      string,
      object | undefined
    >;
    "delete-interface": SWRMutationResponse<
      ResData,
      any,
      string,
      object | undefined
    >;
  } = {
    overview: updateApplianceOverview,
    network: updateApplianceNetwork,
    "support-tunnel": updateApplianceSupportTunnel,
    "upgrade-schedule": updateApplianceUpgradeSchedule,
    "update-interface": updateApplianceInterface,
    "update-appConnector": updateAppConnector,
    "delete-interface": deleteApplianceInterface,
  };
  const applianceDetailFromServer = useSWR(
    APPLIANCE_ENDPOINTS.fetchApplianceById(applId!, {
      groupId: groupId ?? "NONE",
      provTemplateName: template ?? "NONE",
      serialNumber: serialNumber ?? "NONE",
    }),
    getReq,
    {
      onSuccess: (data: ApplianceDetailFromServer) => {
        const transformed: TransformedApplianceDetails =
          responseTransformer(data);
        const tabs = getTabs(
          transformed,
          triggerUpdateAppliance,
          onDelete,
          readOnly,
          applId!,
        );
        setDetailData(transformed);
        setApplianceName(data?.applianceDetails?.name);
        const applianceStatus =
          data?.applianceDetails?.status === "UNREGISTERED";

        tabs.forEach((tab, index) =>
          applianceStatus
            ? index === 0 || index === 5
              ? (tab.disabled = false)
              : (tab.disabled = true)
            : (tab.disabled = false),
        );
        tabs[4].disabled =
          data?.applianceDetails?.deploymentStatus === "DEPLOYED"
            ? false
            : true;

        setTabs(tabs);
        setAlertType("");
        setAlertMessage("");
        setSelectedTabs(tabs[0]);
      },
      ...errorHandler,
    },
  );

  const onDelete = (id: string) => {
    void deleteSubLocationbyId.trigger({ id: `${groupId}/appliance/${id}` });
  };

  const crumbOpts: BreadcrumbsItemsArrayProps[] = [
    {
      href: "/appliances",
      label: t("appliances.menu.appliances"),
      id: "1",
    },
    {
      href: "",
      label:
        applianceName ??
        detailData?.apiResponse.applianceDetails.provTemplateName ??
        t("appliances.unnamed-appliance"),
      id: "2",
    },
  ];

  const { icon, text } =
    StatusDetailsMap[status?.toLowerCase() as keyof typeof StatusDetailsMap] ||
    {};

  const physicalDeviceName = detailData?.apiResponse.applianceDetails.model;
  const isLoading =
    updateApplianceOverview.isMutating ||
    updateApplianceNetwork.isMutating ||
    updateApplianceSupportTunnel.isMutating ||
    updateApplianceUpgradeSchedule.isMutating ||
    updateApplianceInterface.isMutating ||
    deleteApplianceInterface.isMutating ||
    applianceDetailFromServer.isValidating ||
    updateApplianceProvStatus.isValidating ||
    updateAppConnector.isMutating ||
    updateAdminStatus.isMutating;

  return (
    <div
      className="pb-rem-160 flex flex-col space-y-rem-40 h-full -mt-rem-160"
      data-testid="unified-locations-list"
    >
      <WithStates
        loading={applianceDetailFromServer.isLoading}
        loadingComponent={
          <div className={"flex justify-center items-center h-full pt-rem-240"}>
            <LoadingSpinner ariaLabel={t("LOADER_ICON")} />
          </div>
        }
      >
        {isLoading && (
          <div className="z-[999]">
            <Overlay id="appliance-overlay" />
          </div>
        )}
        {alertMessage && (
          <Alert
            alert={{
              message: alertMessage,
              type: alertType,
            }}
          />
        )}
        <div className="flex justify-between border-b border-semantic-border-interactive-primary-disabled p-rem-80">
          <Breadcrumbs items={crumbOpts} />
          {status && (
            <div className="flex justify-end">
              <StatusIndicator icon={icon} text={text} id="appliance" />
            </div>
          )}
        </div>
        {selectedTab && (
          <LADetailLayout
            navTopper={() => (
              <div className="pt-rem-120">
                {
                  applianceIconMap[
                    (physicalDeviceName !== null
                      ? physicalDeviceName
                      : model) as keyof typeof applianceIconMap
                  ]
                }
              </div>
            )}
            verticalTabsProps={{
              tabs: tabs!,
              onTabClick: handleTabClick,
              selectedTab: selectedTab.value,
            }}
            content={selectedTab.content}
          />
        )}
      </WithStates>
    </div>
  );
};

export default ApplianceDetail;
