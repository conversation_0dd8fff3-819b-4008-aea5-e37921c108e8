/* eslint-disable @up/unified-platform/max-file-lines */
import { t } from "i18next";
import Link from "next/link";
import { type ReactElement, useEffect, useState } from "react";
import { <PERSON><PERSON>, LoadingSpinner, SegmentedControl } from "@zs-nimbus/core";
import { faPen, faTrash } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import useSWR from "swr";
import { useSearchParams } from "next/navigation";
import { Alert } from "@xc/legacy-components";
import AppliancesDrawer from "../../Drawers/AppliancesDrawer/AppliancesDrawer";
import { APPLIANCES_DRAWER_DATA } from "../../Drawers/AppliancesDrawer/AppliancesDrawer.data";
import DeleteModal from "../../../Components/DeleteModal/DeleteModal";
import { StatusDetailsMap } from "../../../Locations/Appliances";
import { type EditAppliancesFormDataProps } from "../../types";
import { type ApplianceDetailFromServer } from "../../ApplianceDetailTransformer";
import CardWrapper from "../../../Components/CardWrapper/CardWrapper";
import { statusMap } from "../ApplianceList/ApplianceListConfig";
import { APPLIANCE_ENDPOINTS } from "../../config/apiUtils";
import { extractErrorDetails } from "../../../helper";
import { DISABLING, ENABLING } from "../../../constants";
import { getDataTestId } from "@/utils/utils";
import SegmentControl from "@/components/Analytics/SegmentedControl/SegmentedControl";
import { getReq } from "@/utils/apiHelper";
import { type ErrorType, showErrorAlert } from "@/app/onboarding/apiHelper";

export type ApplianceDetailsProps = {
  id: number | string;
  heading: string;
  applianceName: string | undefined;
  groups: ApplianceGroups[];
  deleteIconText?: string;
  editIconText?: string;
  handleEditLocation: () => void;
  apiResponse?: ApplianceDetailFromServer;
  triggerUpdateAppliance?: (
    payload: ApplianceDetailFromServer,
    type: string,
  ) => void;
  onDelete: (id: string) => void;
  applianceStatus: Items[];
  applianceDetailVersion: Items[];
  readOnly: boolean;
  applId: string;
};

export type ApplianceGroups = {
  heading?: string;
  list: Items[];
};

export type Items = {
  label: string;
  value?: string;
  icon?: string;
  id?: string;
  interactive?: boolean;
  timeZone?: string;
};
type StatusComponentProps = {
  applianceStatus: Items[];
};

type VersionComponentProps = {
  versionData: Items[];
};

const ConnectionLists = ({
  label,
  value,
  icon,
  id,
  interactive = false,
  timeZone,
}: Items) => {
  const clickable = interactive && id;

  return (
    <div
      className="flex flex-col gap-1"
      data-testid={getDataTestId(id, "appliance-details-item")}
    >
      <div className="typography-paragraph1 text-semantic-content-base-tertiary w-full break-words">
        {t(label)}
      </div>
      <div className="flex">
        {icon && (
          <i
            className={`${icon} pr-rem-80`}
            aria-label={`appliance-detail-${t(value!)}`}
          />
        )}

        {clickable ? (
          <Link
            href={`/locations/${id}?name=${value}`}
            data-testid={getDataTestId("branch-connector", "location-label")}
            className="text-semantic-content-interactive-primary-default typography-paragraph1 hover:text-semantic-content-interactive-primary-hover"
          >
            {value}
          </Link>
        ) : value ? (
          <>
            <div className="inline-flex">
              <div
                className={`typography-paragraph1 text-semantic-content-base-primary ${icon ? "mb-rem-40" : ""}`}
              >
                {t(value)}
              </div>
              {timeZone && (
                <div className="typography-paragraph2 text-semantic-content-base-tertiary pl-rem-40 mt-rem-20">
                  &#x2022; {timeZone}
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="typography-paragraph1 text-semantic-content-base-primary">
            --
          </div>
        )}
      </div>
    </div>
  );
};

const ApplianceStatus = ({ label, value }: Items) => {
  const iconValue = statusMap[value as keyof typeof statusMap];

  return (
    <>
      <div
        className="flex"
        data-testid={getDataTestId(t(label), "appliance-details-item")}
      >
        <div className="typography-paragraph1 text-semantic-content-base-tertiary break-words w-44">
          {t(label)}
        </div>
        <div className="flex items-center">
          <i
            className={`${iconValue?.icon} pr-rem-80`}
            aria-label={`appliance-detail-${t(iconValue?.text)}`}
          />

          <div
            className={`typography-paragraph1 text-semantic-content-base-primary `}
          >
            {statusMap[value as keyof typeof statusMap]
              ? t(statusMap[value as keyof typeof statusMap].text)
              : "--"}
          </div>
        </div>
      </div>
    </>
  );
};
const ApplianceOptions = ({ list }: ApplianceGroups) => (
  <div
    className="flex flex-col gap-rem-80"
    data-testid={getDataTestId("options", "appliance-details-group")}
  >
    {list.map((item: Items, key) => (
      <ConnectionLists key={key} {...item} id={item.id ?? key.toString()} />
    ))}
  </div>
);

const segmentConfig = [
  {
    label: "appliances.list.staged",
    value: "STAGED",
  },
  {
    label: "READY_TO_DEPLOY",
    value: "NOT_DEPLOYED",
  },
];

const StatusComponent = ({ applianceStatus }: StatusComponentProps) => (
  <div className="flex flex-col gap-6">
    {/*
     *TODO: This will be part of Phase 4
     */}

    <div className="flex flex-col gap-1">
      {applianceStatus.map((item: Items, key) => (
        <ApplianceStatus key={key} {...item} />
      ))}
    </div>
  </div>
);

const VersionComponent = ({ versionData }: VersionComponentProps) => (
  <div className="flex flex-col gap-6 ">
    <span
      className="typography-header5 text-semantic-content-base-primary"
      aria-label={`appliances.details.version`}
      data-testid={getDataTestId("options", "appliance-details-version-group")}
    >
      {t("appliances.details.version")}
    </span>
    <div
      className="flex flex-col gap-rem-80"
      data-testid={getDataTestId("options", "appliance-details-version-group")}
    >
      {versionData.map((item: Items, key) => (
        <ConnectionLists key={key} {...item} id={item.id ?? key.toString()} />
      ))}
    </div>
  </div>
);

export const AppliancesDetailsContent = ({
  applianceName,
  groups,
  handleEditLocation,
  triggerUpdateAppliance,
  apiResponse,
  onDelete,
  applianceStatus,
  applianceDetailVersion,
  readOnly,
  applId,
}: ApplianceDetailsProps) => {
  const ID = "appliance-details";
  const deployementStatusID = "appliance-details-status";
  const isPhysicalDevice = apiResponse?.applianceDetails?.serialNumber !== null;
  const deploymentStatus = apiResponse?.applianceDetails?.deploymentStatus;
  const operationalAdminStatus =
    apiResponse?.applianceDetails?.operationalAdminStatus;
  const canEditDelete = apiResponse?.id;
  const [deleteAppliance, setDeleteAppliance] = useState<boolean>(false);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [checkAdminStatus, setCheckAdminStatus] = useState(false);
  const [alertType, setAlertType] = useState("");
  const [alertMessage, setAlertMessage] = useState<ReactElement | string>();
  const queryParams = useSearchParams();
  const groupId = queryParams?.get("groupId");
  const serialNumber = queryParams?.get("serialNumber");
  const template = queryParams?.get("template");

  const errorHandler = {
    onError: (err: Error) => {
      const errorCause = err.cause as ErrorType;
      const {
        info: { reason },
      } = errorCause;
      let errorMessage;
      if (reason) {
        errorMessage = extractErrorDetails(reason)?.message;
        errorCause.info.message = errorMessage;
      }

      showErrorAlert(errorCause, setAlertType, setAlertMessage);
    },
  };

  const adminStatus =
    apiResponse?.applianceDetails?.operationalAdminStatus === "DISABLING" ||
    apiResponse?.applianceDetails?.operationalAdminStatus === "ENABLING";

  useSWR(
    adminStatus
      ? APPLIANCE_ENDPOINTS.fetchApplianceById(applId, {
          groupId: groupId ?? "NONE",
          provTemplateName: template ?? "NONE",
          serialNumber: serialNumber ?? "NONE",
        })
      : null,
    getReq,
    {
      refreshInterval: adminStatus || checkAdminStatus ? 180000 : undefined,
      onSuccess: (data: ApplianceDetailFromServer) => {
        setCheckAdminStatus(
          data?.applianceDetails?.operationalAdminStatus === "DISABLING" ||
            data?.applianceDetails?.operationalAdminStatus === "ENABLING",
        );
      },
      ...errorHandler,
    },
  );

  const createAdminConfig = (
    statusCondition: string,
    action: string,
    actionLabel: string,
    oppositeCondition: string,
  ) => ({
    icon:
      operationalAdminStatus === statusCondition ? (
        <LoadingSpinner
          size="small"
          inverted={true}
          ariaLabel={t("LOADER_ICON")}
        />
      ) : null,
    id: operationalAdminStatus === statusCondition ? statusCondition : action,
    label:
      operationalAdminStatus === statusCondition
        ? t(statusCondition)
        : t(actionLabel),
    disabled:
      operationalAdminStatus === oppositeCondition ||
      operationalAdminStatus === action, // True if the opposite condition or current action disables it
  });

  const adminConfig = [
    createAdminConfig(ENABLING, "ENABLE", "ENABLE", DISABLING),
    createAdminConfig(DISABLING, "DISABLE", "DISABLE", ENABLING),
  ];

  const deployStatusToggleVisible =
    (deploymentStatus === "STAGED" || deploymentStatus === "NOT_DEPLOYED") &&
    isPhysicalDevice;
  const isDeployedStatus = deploymentStatus === "DEPLOYED";
  const onClickHandler = () => {
    setOpenDrawer(true);
    handleEditLocation();
  };

  useEffect(() => {
    if (apiResponse) {
      setOpenDrawer(false);
    }
  }, [apiResponse]);

  const applianceDetails = groups[0].list.slice(1, 4);
  applianceDetails[2].icon =
    StatusDetailsMap[
      applianceDetails[2]?.value?.toLowerCase() as keyof typeof StatusDetailsMap
    ]?.icon || "";

  const onUpdate = (formData: EditAppliancesFormDataProps) => {
    const { name, description } = formData;
    const payload = {
      ...apiResponse!,
      applianceDetails: {
        ...apiResponse!.applianceDetails,
        name: name,
        description: description,
      },
    };
    triggerUpdateAppliance?.(payload, "overview");
  };
  const updateDeploymentStatus = (val: string) => {
    const payload = {
      ...apiResponse!,
      applianceDetails: {
        ...apiResponse!.applianceDetails,
        deploymentStatus: val,
      },
    };
    triggerUpdateAppliance?.(payload, "appliance-prov-status");
  };
  const updateAdminStatus = (val: Set<string | number>) => {
    const formattedValue = [...val];
    const payload = {
      ...apiResponse!,
      applianceDetails: {
        ...apiResponse!.applianceDetails,
        operationalAdminStatus: formattedValue[0]?.toString(),
      },
    };
    triggerUpdateAppliance?.(payload, "appliance-admin-status");
  };

  const onDeleteHandler = (id: string) => {
    onDelete(id);
    setDeleteAppliance(false);
  };
  const canEditAppliance =
    apiResponse?.applianceDetails?.deploymentStatus === "DEPLOYED" &&
    apiResponse.applianceDetails.serialNumber === null;

  const activeGroup = groups[0].list[3].value !== "ACTIVE";
  const isDeployedVM = isDeployedStatus && !isPhysicalDevice;

  return (
    <div className="flex gap-rem-160">
      {alertMessage && (
        <Alert
          alert={{
            message: alertMessage,
            type: alertType,
          }}
        />
      )}
      <CardWrapper className="p-rem-160 w-[350px] h-96">
        <div className="flex flex-col w-full gap-6">
          <div className="flex flex-col gap-1">
            <div className="flex justify-between items-center">
              <div
                className="flex items-center text-semantic-content-base-primary"
                data-testid={getDataTestId("layout", ID)}
              >
                <span
                  className="typography-header5 text-semantic-content-base-primary"
                  aria-label={`appliance-detail-heading`}
                  data-testid={getDataTestId("heading", ID)}
                >
                  {t("BranchConnector")}
                </span>
              </div>

              <div className="flex justify-start items-center gap-6">
                <div className="w-5 h-5 justify-center items-center flex">
                  {activeGroup &&
                    canEditAppliance &&
                    !readOnly &&
                    !isDeployedVM && (
                      <Button
                        variant="tertiary"
                        disabled={canEditAppliance}
                        id={getDataTestId("edit-icon", ID)}
                        data-testid={getDataTestId("edit-icon", ID)}
                        onClick={onClickHandler}
                        suffixIcon={<FontAwesomeIcon icon={faPen} />}
                      />
                    )}
                </div>
                {!readOnly && (
                  <div className="w-5 h-5 justify-center items-center flex">
                    {canEditDelete && (
                      <Button
                        id={getDataTestId("delete-icon", ID)}
                        data-testid={getDataTestId("delete-icon", ID)}
                        variant="tertiary"
                        onClick={() => setDeleteAppliance(true)}
                        prefixIcon={<FontAwesomeIcon icon={faTrash} />}
                      />
                    )}
                  </div>
                )}
              </div>
            </div>
            <span
              className="typography-paragraph1 text-semantic-content-base-primary"
              aria-label={`appliance-name`}
              data-testid={getDataTestId("name", ID)}
            >
              {t("appliances.details.appliance-details")}
            </span>
          </div>
          <div
            className="flex flex-wrap gap-rem-160"
            data-testid={getDataTestId("groups", ID)}
          >
            {groups.map((group: ApplianceGroups) => (
              <ApplianceOptions
                key={`appliance-option-${group.heading}`}
                {...group}
              />
            ))}
          </div>
          <DeleteModal
            text="appliances.modal.appliance-subtext"
            isSubInterface={true}
            onClose={() => setDeleteAppliance(false)}
            title="appliances.modal.confirm-delete"
            show={deleteAppliance}
            onDelete={onDeleteHandler}
            id={apiResponse?.id?.toString()}
            item={apiResponse?.applianceDetails?.name}
            warningMessageText="appliances.modal.undo-warning"
            isPhysicalDevice={isPhysicalDevice}
          />
          <AppliancesDrawer
            {...APPLIANCES_DRAWER_DATA}
            appliancesFormData={{
              name: applianceName,
              description: apiResponse?.applianceDetails?.description,
            }}
            applianceDetails={applianceDetails}
            heading="appliances.drawer.appliance.heading"
            openDrawer={openDrawer}
            setOpenDrawer={setOpenDrawer}
            onSave={onUpdate}
          />
        </div>
      </CardWrapper>
      <div className="flex flex-col gap-rem-160">
        <CardWrapper className="flex flex-col w-[380px] h-62 p-rem-160 gap-rem-160">
          <span
            className="typography-header5 text-semantic-content-base-primary"
            aria-label={`aappliances.details.status`}
            data-testid={getDataTestId("admin-status", deployementStatusID)}
          >
            {t("appliances.details.status")}
          </span>
          {isDeployedStatus && (
            <>
              <div className="flex flex-col gap-1 w-[158px]">
                <div
                  className="typography-paragraph1 text-semantic-content-base-tertiary w-full break-words"
                  data-testid={getDataTestId("admin-status", ID)}
                >
                  {t("appliances.details.admin-status")}
                </div>
                <SegmentedControl
                  items={adminConfig}
                  value={operationalAdminStatus}
                  variant="primary"
                  onChange={updateAdminStatus}
                  disabled={readOnly}
                  data-testid="appliance-admin-status-tab"
                />
              </div>
            </>
          )}
          {deployStatusToggleVisible && isPhysicalDevice && (
            <>
              <div className="flex flex-col gap-1 w-[216px]">
                <div className="typography-paragraph1 text-semantic-content-base-tertiary w-full break-words">
                  {t("Deployment Status")}
                </div>
                <SegmentControl
                  customClass="flex h-full !bg-semantic-background-primary border border-semantic-border-base-primary"
                  customSelectionStyle="bg-semantic-surface-interactive-primary-default !text-semantic-content-immutable-white"
                  options={segmentConfig}
                  value={deploymentStatus}
                  disabled={readOnly}
                  onChange={updateDeploymentStatus}
                  id="appliance-status-tab"
                />
              </div>
              <div className="typography-paragraph2 text-semantic-content-base-tertiary w-full break-words">
                {`"${t("READY_TO_DEPLOY")}" ${t("appliances.details.deployment-status-helptext")}`}
              </div>
            </>
          )}
          <StatusComponent applianceStatus={applianceStatus} />
        </CardWrapper>
        <CardWrapper className="w-[380px] h-56 p-rem-160">
          <VersionComponent versionData={applianceDetailVersion} />
        </CardWrapper>
      </div>
    </div>
  );
};
