import { expect, describe, it } from "vitest";
import { checkValidIPRange } from "./validation";


describe("checkValidIPRange", () => {
    it("should return invalid response if invalid IP range provided", () => {
      const inputRange = "**********-**********";
      const [startIPAddress, endIPAddress] = inputRange.split("-");
      const expectedResult = 1;
      const result = checkValidIPRange(startIPAddress, endIPAddress);
      expect(result).toBe(expectedResult);
    });

    it("should return desired result if valid IP range provided", () => {
        const inputRange = "**********-***********";
        const [startIPAddress, endIPAddress] = inputRange.split("-");
        const expectedResult = -1;
        const result = checkValidIPRange(startIPAddress, endIPAddress);
        expect(result).toBe(expectedResult);
      });

      it("should return invalid response if invalid IP range provided", () => {
        const inputRange = "a.20.20.4-20.b.20.1";
        const [startIPAddress, endIPAddress] = inputRange.split("-");
        const expectedResult = 1;
        const result = checkValidIPRange(startIPAddress, endIPAddress);
        expect(result).toBe(expectedResult);
      });
});