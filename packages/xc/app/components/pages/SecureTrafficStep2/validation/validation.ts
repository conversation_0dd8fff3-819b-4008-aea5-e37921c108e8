export const checkValidIPRange = (
  startIPAddress: string,
  endIPAddress: string,
): number => {
  const toNumberArray = (ip: string): number[] => ip?.split(".")?.map(Number);
  const initialValue = toNumberArray(startIPAddress ?? "");
  const endValue = toNumberArray(endIPAddress ?? "");

  for (let i = 0; i < 4; i++) {
    if (initialValue[i] < endValue[i]) return -1;
    if (initialValue[i] > endValue[i]) return 1;
  }

  return 0;
};
