"use client";
import { type ChangeEvent, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { t } from "i18next";
import { Button } from "@zs-nimbus/core";
import {
  REGEX_IPV4,
  REGEX_IPV6,
  REGEX_URL,
  REGEX_IPV4_RANGE,
  REGEX_IPV6_RANGE,
} from "@up/std";
import List from "./List";
import { checkValidIPRange } from "./validation/validation";
import { getDataTestId } from "@/utils/utils";

export type ItemProp = {
  label: string;
  error: string | undefined;
  type: "ipV4" | "ipV6" | "url" | undefined;
};

type InputListProps = {
  id?: string;
  value?: string;
  onAdd: (items: ItemProp[]) => void;
  vpns: ItemProp[];
  className?: string;
  listClasses?: string;
  placeHolder?: string;
  enableLocationCheck?: boolean;
};
const Validator = (
  item: string,
  enableLocationCheck?: boolean,
): string | undefined => {
  if (enableLocationCheck) {
    const isValidIPAddress = REGEX_IPV4.test(item);
    const isValidIPRange = REGEX_IPV4_RANGE.test(item);
    const isRangeInput = item.includes("-");
    if (isRangeInput) {
      if (isValidIPRange) {
        return undefined;
      } else {
        return t("OB_ST_VALID_IP_RANGE");
      }
    } else {
      if (isValidIPAddress) {
        return undefined;
      } else {
        return t("OB_ST_VALID_IP");
      }
    }
  } else if (
    (REGEX_IPV4_RANGE.test(item) ||
      REGEX_IPV6_RANGE.test(item) ||
      REGEX_IPV4.test(item) ||
      REGEX_IPV6.test(item) ||
      REGEX_URL.test(item)) &&
    !enableLocationCheck
  ) {
    return undefined;
  } else {
    return t("OB_ST_URL_INFO");
  }
};
const getItemType = (item: string): "ipV6" | "ipV4" | "url" | undefined => {
  if (REGEX_IPV4_RANGE.test(item)) {
    return "ipV4"; // Valid IPv4 range address
  }
  if (REGEX_IPV6_RANGE.test(item)) {
    return "ipV6"; // Valid IPv6 range address
  }
  if (REGEX_IPV4.test(item)) {
    return "ipV4"; // Valid IPv4 address
  }
  if (REGEX_IPV6.test(item)) {
    return "ipV6"; // Valid IPv6 address
  }
  if (REGEX_URL.test(item)) {
    return "url"; // Valid URL
  }

  return undefined;
};

export default function InputList({
  value,
  onAdd,
  vpns,
  id,
  className,
  listClasses,
  placeHolder,
  enableLocationCheck = false,
}: InputListProps) {
  const ID = getDataTestId("input-list", id);
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState<string>(value ?? "");
  const [isButtonDisabled, setIsButtonDisabled] = useState<boolean>(
    !value || value === "",
  );
  const [items, setItems] = useState<ItemProp[]>(vpns);
  const [showList, setShowList] = useState<boolean>(vpns.length > 0);
  const [isValidIpAndRange, setIsValidIpAndRange] = useState<boolean>(false);

  const handleInputChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(event.target.value);
    setIsButtonDisabled(event.target.value === "");
  };

  useEffect(() => {
    let shouldDisableButton = true;

    if (inputValue && enableLocationCheck) {
      const validation: Record<string, RegExp> = {
        inputValue: REGEX_IPV4,
      };

      const validationRange: Record<string, RegExp> = {
        inputValue: REGEX_IPV4_RANGE,
      };
      const [startIPAddress, endIPAddress] = inputValue.split("-");
      const isValidIPRange = checkValidIPRange(startIPAddress, endIPAddress);
      const isValidIPv4 = validation.inputValue.test(inputValue);
      const isValidIPv4Range =
        validationRange.inputValue.test(inputValue) && isValidIPRange === -1;
      shouldDisableButton = !(isValidIPv4 || isValidIPv4Range);
    }

    if (isValidIpAndRange !== shouldDisableButton) {
      setIsValidIpAndRange(shouldDisableButton);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [inputValue, isValidIpAndRange]);

  const handleAddClick = () => {
    setShowList(true);
    const newItems = Array.from(
      new Set([
        ...items.map((item) => item.label),
        ...inputValue
          .split(",")
          .map((item) => item.trim())
          .filter((item) => item !== ""),
      ]),
    );

    const newItemsWithError = newItems.map((item) => ({
      label: item,
      error: Validator(item, enableLocationCheck),
      type: getItemType(item),
    }));

    setItems(newItemsWithError);
    onAdd(newItemsWithError);
    setInputValue("");
    setIsButtonDisabled(true);
  };

  const handleRemoveAll = () => {
    setItems([]);
    setShowList(false);
    onAdd([]);
  };

  const handleItemRemove = (itemToRemove: ItemProp) => {
    const updatedItems = items.filter(
      (item) => item.label !== itemToRemove.label,
    );

    setItems(updatedItems);
    onAdd(updatedItems);
    if (updatedItems.length === 0) {
      setShowList(false);
    }
  };

  const textInputClass =
    "border border-semantic-border-base-primary text-semantic-content-base-primary px-default py-[0.4rem] rounded-40 text-[0.8125rem] leading-5 font-normal bg";

  return (
    <>
      <div
        className="flex items-center gap-default h-fit border-l border-l-semantic-border-base-subdued pl-l"
        data-testid={getDataTestId("container", ID)}
      >
        <textarea
          className={`${textInputClass} bg-semantic-surface-fields-default w-[280px] h-[95px] resize-none focus-visible-default ${className}`}
          value={inputValue}
          name=""
          id=""
          onChange={handleInputChange}
          placeholder={
            placeHolder ? t(placeHolder) : t("OB_ST_TEXTAREA_PLACEHOLDER")
          }
          data-testid={getDataTestId("textarea", ID)}
        />
        <Button
          disabled={
            isButtonDisabled || (enableLocationCheck && isValidIpAndRange)
          }
          onClick={handleAddClick}
          data-testid={getDataTestId("add-btn", ID)}
        >
          {t("OB_ST_BTN_ADD")}
        </Button>
      </div>

      {showList && (
        <div className={`w-[280px] ml-[17px] ${listClasses}`}>
          <List
            items={items}
            onRemoveAll={handleRemoveAll}
            onItemRemove={handleItemRemove}
            id={ID}
            className={listClasses}
          />
        </div>
      )}
    </>
  );
}
