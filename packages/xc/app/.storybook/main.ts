import path from "path";
import type { StorybookConfig } from "@storybook/nextjs";

type WebpackRule = {
  test: RegExp;
  use: Array<string | Record<string, unknown>>;
};

function isWebpackCssRule(rule: unknown): rule is WebpackRule {
  return (
    typeof rule === "object" &&
    rule !== null &&
    "test" in rule &&
    rule.test instanceof RegExp &&
    rule.test.test(".css")
  );
}

function updatePostcssLoader(rule: WebpackRule) {
  rule.use = rule.use.map((loader) =>
    typeof loader === "string" && loader.includes("postcss-loader")
      ? {
          loader,
          options: {
            postcssOptions: {
              plugins: ["postcss-url"],
            },
          },
        }
      : loader,
  );
}

const config: StorybookConfig = {
  stories: ["../**/*.mdx", "../**/*.stories.@(ts|tsx)"],

  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-interactions",
    "@storybook/addon-designs",
    "@storybook/addon-mdx-gfm",
  ],
  core: {
    disableTelemetry: true,
  },

  framework: {
    name: "@storybook/nextjs",
    options: {},
  },

  docs: {},

  webpackFinal(config) {
    updatePostcssLoader(config.module!.rules!.find(isWebpackCssRule)!);

    if (!("resolve" in config)) config.resolve = {};

    config.resolve!.alias = {
      ...config.resolve!.alias,
      "@": path.resolve(__dirname, ".."),
    };

    return config;
  },
};

export default config;
