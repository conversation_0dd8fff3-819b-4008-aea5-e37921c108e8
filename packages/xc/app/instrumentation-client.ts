import * as Sentry from "@sentry/nextjs";
import { type EnvironmentResponse } from "./utils/environment";

const SENTRY_RELEASE =
  process.env.BUILD_VERSION ??
  process.env.NEXT_PUBLIC_BUILD_VERSION ??
  undefined;

fetch("/environment/config")
  .then((res) => res.json())
  .then((config: EnvironmentResponse) => {
    Sentry.init({
      environment: config.environment,
      enabled: config.sentryEnabled === "true",
      dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
      integrations: [Sentry.replayIntegration()],
      // Session Replay
      replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
      replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
      // Set tracesSampleRate to 1.0 to capture 100%
      // of transactions for tracing.
      // We recommend adjusting this value in production
      // Learn more at
      // https://docs.sentry.io/platforms/javascript/configuration/options/#traces-sample-rate
      tracesSampleRate: 1.0,
      // Replay may only be enabled for the client-side
      // Note: if you want to override the automatic release value, do not set a
      // `release` value here - use the environment variable `SENTRY_RELEASE`, so
      // that it will also get attached to your source maps
      release: SENTRY_RELEASE,
    });
  })
  .catch((err) => {
    console.error("Failed to load Sentry config:", err);
  });

// This export will instrument router navigations, and is only relevant if you enable tracing.
// `captureRouterTransitionStart` is available from SDK version 9.12.0 onwards
export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
