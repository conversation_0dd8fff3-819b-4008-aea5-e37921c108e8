/** @type {import('next').NextConfig} */
import withBundleAnalyzer from "@next/bundle-analyzer";
import withPlugins from "next-compose-plugins";
import XCCopyPlugin from "@xc/webpack-copy-plugin";
import ziaPackagesManifest from "@zia/combined-packages/manifest" with { type: "json" };
import webpack from "webpack";
import { withSentryConfig } from "@sentry/nextjs";

const SENTRY_RELEASE =
  process.env.BUILD_VERSION ??
  process.env.NEXT_PUBLIC_BUILD_VERSION ??
  undefined;

const bundleAnalyzer = withBundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
});

const nextConfig = {
  productionBrowserSourceMaps: process.env.NODE_ENV === "development" || process.env.PRODUCTION_SOURCEMAP_NEEDED === 'true',
  swcMinify: true,
  output: "standalone",
  images: { unoptimized: true },
  eslint: {
    ignoreDuringBuilds: true,
    dirs: [
      "app",
      "components",
      "configs",  
      "context",
      "hoc",
      "modules",
      "telemetry",
      "types",
      "hooks",
      "utils",
      ".storybook",
    ],
  },
  webpack: (config) => {
    // Add custom Webpack configs here
    config.plugins.push(
      new XCCopyPlugin({
        concurrency: 10,
        directories: [
          ...ziaPackagesManifest.patterns,
          {
            source: "./node_modules/@ma/core/bun_oneui_pages_dist/translations",
            destination: "./public/ma/translations",
          },
          {
            source: "./node_modules/@zuxp/zdx-prod/app-dist",
            destination: "./public/zdx",
          },
          {
            source: "./node_modules/@ztds/airgap/oneUI_dist",
            destination: "./public/segmentation",
          },
          {
            source: "./node_modules/@zuxp/edge-ux/oneUI_dist",
            destination: "./public/ec",
          },
          {
            source: "./node_modules/@zuxp/zpa/output/zpn",
            destination: "./public/zpn",
          },
        ],
      }),
    );

    // Enable Module federation only in development mode when ZPA_DEV is true
    if (
      process.env.NODE_ENV === "development" &&
      process.env.ZPA_DEV === "true"
    ) {
      console.warn("Module Federation support is enabled to run ZPA product");
      config.plugins.push(
        new webpack.container.ModuleFederationPlugin({
          name: "oneUI-zpa",
          remotes: {
            zuxp: `zuxp@https://localhost.admin.dev.zpath.net:8080/remoteEntry.js`,
          },
        }),
      );
    }
    return config;
  },
  env: {
    INSTALLED_ZIA_VERSIONS: ziaPackagesManifest.installedVersions,
  },
  skipTrailingSlashRedirect: true,
  experimental: {
    instrumentationHook: true,
    // See: https://nextjs.org/docs/14/app/building-your-application/optimizing/memory-usage#webpack-build-worker
    webpackBuildWorker: true
  },
};

// See: https://github.com/vercel/next.js/discussions/58777
if (process.env.NODE_ENV === "development" || process.env.NEXT_PUBLIC_LOCAL) {
  if (process.env.NEXT_PUBLIC_MOCK) {
    nextConfig.rewrites = async () => {
      return [
        {
          source: "/api/:path*",
          // `:8442` refers to port used for running mock server
          destination: "http://localhost:8442/api/:path*",
        },
        {
          source: "/zsapi/:path*",
          // `:8442` refers to port used for running mock server
          destination: "http://localhost:8442/zsapi/:path*",
        },
      ];
    };
  } else {
    // nextConfig.reactStrictMode =false;
    nextConfig.rewrites = async () => {
      return [
        /* ZDX oneUI dev */
        {
          source: "/zdx/api/:path*",
          destination: "https://admin.zdxpreview.net/zdx/api/:path*",
          // destination: "https://admin.zdxbeta.net/zdx/api/:path*",
          // destination: "https://admin.zdxpreview.net/zdx/api/:path*"
          // destination: "https://**********/zdx/api/:path*",
        },
        {
          source: "/copilot/:path*/",
          destination: "https://api.alpha.zsapi.net/private/copilot/:path*/",
        },
        {
          source: "/webservice/:path*",
          destination: "https://mobile.zsprotect.net/webservice/:path*",
        },
        {
          source: "/papi/:path*",
          destination: "https://mobile.zsprotect.net/papi/:path*",
        },
        {
          source: "/api/service/:path*",
          destination: "http://localhost:8080/:path*",
        },
        {
          source: "/api/:path*",
          destination: "https://pre-dev.console.zscaler.com/:path*",
        },
        {
          source: "/internal/:path*",
          destination: "https://flipt.zuxp.corp.zscaler.com/internal/:path*",
        },
      ];
    };
  }
}

let config = withPlugins([[bundleAnalyzer]], nextConfig);

config = withSentryConfig(config, {
  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,
  // Only print logs for uploading source maps in CI
  // Set to `true` to suppress logs
  silent: !process.env.CI,
  debug: process.env.CI,
  authToken: process.env.SENTRY_AUTH_TOKEN,
  // Upload a larger set of source maps for prettier stack traces
  widenClientFileUpload: true,
  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,
  tunnelRoute: "/monitoring",
  // Migrating away from SHA-based releases due to limitations in capturing artifact additions when package.json remains unchanged between commits
  release: {
    name: SENTRY_RELEASE,
  },
  reactComponentAnnotation: {
    enabled: true
  },
  sourcemaps: {
    deleteSourcemapsAfterUpload: true
  }
});

export default config;
