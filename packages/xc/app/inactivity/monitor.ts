"use client";

import { useCallback, useEffect, useRef } from "react";
import {
  isTrue,
  BEARER_TOKEN,
  TokenUpdatedEvent,
  getBearerToken,
} from "@up/std";

import { debounce } from "lodash-es";
import { inactivityStore, isIdleable, setInactivityState } from "./store";
import { EVENTS_DOCUMENT, EVENTS_WINDOW } from "./constants";
import { type AdvancedSettingsResponse, type InactivityState } from "./types";
import EventBus from "@/c3/event-bus";
import { type BroadcastMessage } from "@/c3/types";
import {
  ActivityQueryEvent,
  EventActivityQuery,
  EventActivityQueryDone,
  EventActivityResponse,
  EventLogEvent,
  EventsAll,
  EventStateTransition,
  LogEvent,
  StateTransitionEvent,
} from "@/c3/events";
import { API_ENDPOINTS, getBaseUrl } from "@/utils/apiHelper";

type Props = {
  idleMinutes?: number;
  preemtiveMinutes?: number; // minutes before idleMinutes to check
  waitingMaxSeconds?: number; // how long to wait when asking for activity
  expiredState?: InactivityState; // state to transition to when we are all done
};

// export const getBearerToken = (): string | null => {
//   if (process.env.NODE_ENV === "development" && process.env.NEXT_PUBLIC_TOKEN) {
//     return process.env.NEXT_PUBLIC_TOKEN ?? null;
//   } else {
//     return sessionStorage?.getItem(BEARER_TOKEN) ?? null;
//   }
// };

// 02/30/25 (mtl): brought in parts of @up/std useIdle hook to for easier testing

export const InactivityMonitor = ({
  idleMinutes = 60,
  preemtiveMinutes = 30,
  waitingMaxSeconds = 2,
  expiredState = "logged-out",
}: Props) => {
  const idleMinutesRef = useRef<number>(idleMinutes);
  const updatedWithZIA = useRef<boolean>(false);

  if (preemtiveMinutes > idleMinutes / 2) {
    console.error(
      "[inactivity-monitor] preemtive minutes should never be more than half of idleMinutes",
    );
  }

  // tracks if we are awaiting a response or not. Done as a ref to survive/not trigger re-render
  const awaitingResponse = useRef<boolean>(false);

  // in-activity timer ref
  const idleTimerRef = useRef<ReturnType<typeof setTimeout>>();

  // preemtive timer ref
  const preemptiveTimerRef = useRef<ReturnType<typeof setTimeout>>();

  // time tracking how long we wait for other possible tabs/windows to respond
  const responseTimerRef = useRef<ReturnType<typeof setTimeout>>();

  useEffect(() => {
    setupListeners();

    return () => {
      teardownListeners();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // reset in activity timer when a qualifying event is observed
  const resetInactivity = useCallback((state: InactivityState = "active") => {
    // clear timeouts, reset state
    awaitingResponse.current = false;
    clearTimeout(responseTimerRef.current);
    clearTimeout(preemptiveTimerRef.current);
    clearTimeout(idleTimerRef.current);

    if (state !== inactivityStore.state) {
      EventBus.send(
        LogEvent(`transitioning ${inactivityStore.state} →  ${state}`, {
          state,
        }),
      );
      setInactivityState(state);
    }
  }, []);

  // callback for preeptive timer
  const handlePreemptiveTimerExpired = useCallback(() => {
    if (!preemptiveTimerRef.current) {
      return;
    }
    clearTimeout(preemptiveTimerRef.current);

    awaitingResponse.current = true;

    // Ask other possible windows/tabs if they are active
    EventBus.send(ActivityQueryEvent());

    //  wait for a small period of time if we dont recieve a response
    //  or only false responses we log out. This is due to us not know how many
    //  other contexts exists.
    responseTimerRef.current = setTimeout(() => {
      awaitingResponse.current = false;
      EventBus.send({ name: EventActivityQueryDone });
      setInactivityState("idle");
      EventBus.send({
        name: "log-event",
        data: { message: "no active replies, transitioning to idle state" },
      });

      // }
    }, waitingMaxSeconds * 1001);
  }, [waitingMaxSeconds]);

  // handler for the full inactivity timer expiring
  const handleIdleTimerExpired = useCallback(() => {
    EventBus.send(LogEvent("in-activtiy time elapsed"));
    EventBus.send(StateTransitionEvent(expiredState));

    resetInactivity(expiredState);
    teardownListeners();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [resetInactivity, expiredState]);

  const startInactivityTimer = useCallback(
    (expires = -1) => {
      const premins =
        preemtiveMinutes > idleMinutesRef.current / 2
          ? idleMinutesRef.current / 2
          : preemtiveMinutes;

      clearTimeout(idleTimerRef.current);
      if (expires > 0 && expires > inactivityStore.expires) {
        inactivityStore.expires = expires;
        idleTimerRef.current = setTimeout(
          handleIdleTimerExpired,
          expires - new Date().getTime(),
        );
      } else {
        inactivityStore.expires =
          new Date().getTime() + idleMinutesRef.current * 60 * 1000;

        idleTimerRef.current = setTimeout(
          handleIdleTimerExpired,
          idleMinutesRef.current * 60 * 1000,
        );
      }
      // update store with state of date when we return false to active queries
      inactivityStore.preemtive =
        new Date().getTime() + (idleMinutesRef.current / 2) * 60 * 1000;
      // setup idle expiry timeout
      clearTimeout(preemptiveTimerRef.current);
      preemptiveTimerRef.current = setTimeout(
        handlePreemptiveTimerExpired,
        (idleMinutesRef.current - premins) * 60 * 1000,
      );
    },
    [
      idleMinutesRef,
      preemtiveMinutes,
      handleIdleTimerExpired,
      handlePreemptiveTimerExpired,
    ],
  );

  useEffect(() => {
    if (updatedWithZIA.current) {
      return;
    }
    // fire and forget to fetch poissbly different zid inactivity timeout value from ZID
    // always makes the request, doesnt always reset. After auth in render tree so will re-fetch/reset on token auto
    // refresh
    void fetch(
      `${getBaseUrl(API_ENDPOINTS.ZIAM)}${API_ENDPOINTS.ZIAM}/advanced-settings`,
      {
        method: "GET",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + getBearerToken(),
        },
      },
    )
      .then((response) => response.json())
      .then((d: AdvancedSettingsResponse) => {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        const idleValue = (d.sessionIdleTimeout ?? 3600) / 60;
        if (idleValue !== idleMinutesRef.current) {
          console.log("inactivity timeout adjusted to", idleValue);
          idleMinutesRef.current = idleValue;
          resetInactivity();
          startInactivityTimer();
        }
      })
      .catch(() => {
        // we can ignore any errors as this is a fire and forget request
      });
  }, [resetInactivity, startInactivityTimer]);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const debouncedResetActivity = debounce((name: string) => {
    if (inactivityStore.state !== "active") {
      EventBus.send(LogEvent(`activity detected: ${name}`));
    }
    resetInactivity();
    startInactivityTimer();
  }, 300);

  const handleActivityListener =
    (name: string): (() => void) =>
    () => {
      if (inactivityStore.state !== expiredState) {
        debouncedResetActivity(name);
      }
    };

  // setup idle event listeners
  const setupListeners = () => {
    EVENTS_DOCUMENT.forEach((ename: string) => {
      document.addEventListener(ename, handleActivityListener(ename));
    });

    EVENTS_WINDOW.forEach((ename: string) => {
      window.addEventListener(ename, handleActivityListener(ename));
    });

    EventBus.addListener(EventsAll, handleEventMessage);

    // reset state and start the monitor
    resetInactivity();
    startInactivityTimer();
  };

  // tear down idle event listeners
  const teardownListeners = () => {
    EVENTS_DOCUMENT.forEach((ename: string) => {
      document.removeEventListener(ename, handleActivityListener(ename));
    });

    EVENTS_WINDOW.forEach((ename: string) => {
      window.removeEventListener(ename, handleActivityListener(ename));
    });

    // add listener to handle specific bus events we care about
    EventBus.removeListener(EventsAll, handleEventMessage);

    clearTimeout(responseTimerRef.current);
    clearTimeout(idleTimerRef.current);
  };

  // // update the store
  // setTimeouts(idleMinutesRef.current);

  const handleEventMessage = (msg: BroadcastMessage) => {
    switch (msg.name) {
      case EventLogEvent: {
        if (msg.from !== EventBus.id) {
          const data = msg.data! ?? {};
          if (data.state === "active") {
            resetInactivity();
            startInactivityTimer();
          }
        }
        break;
      }
      case EventActivityQuery: {
        if (inactivityStore.state !== expiredState) {
          EventBus.send({
            name: EventActivityResponse,
            data: {
              active: isIdleable(),
              state: inactivityStore.state,
              expires: inactivityStore.expires,
            },
          });
          setTimeout(() => {
            EventBus.send(
              LogEvent(
                `responding to ${EventActivityQuery} with active = ${inactivityStore.state == "active"}`,
              ),
            );
          }, 500);
        }
        break;
      }
      // handle responses to activity query
      case EventActivityResponse: {
        if (inactivityStore.state === expiredState) {
          return;
        }

        if (msg.data?.active !== undefined && awaitingResponse.current) {
          if (isTrue(msg.data.active as boolean)) {
            setTimeout(() => {
              EventBus.send({ name: EventActivityQueryDone });
            }, 300);

            resetInactivity("idle");
            startInactivityTimer((msg.data?.expires as number) ?? -1);
          }
        }
        break;
      }

      // if we get a log-out event transition event (or whatever expiredState is), forcibly transition
      case EventStateTransition: {
        if (msg.data?.state && msg.data.state === expiredState) {
          if (inactivityStore.state !== "active") {
            setInactivityState(expiredState);
            if (inactivityStore.state !== expiredState) {
              EventBus.send({
                name: "log-event",
                data: {
                  message: `transitioning to ${expiredState} state via event`,
                },
              });
            }
          }
        }
      }
      default:
        console.log(`${msg.name} is un-handled`);
    }
  };

  return null;
};
