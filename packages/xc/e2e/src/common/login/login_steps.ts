import { expect } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@up/e2e-common";
import LoginPage from "./login_page";

let globalToken: string | null = null;

const { Given, When, Then } = createBdd();

const {
  ONEUI_USERNAME: username = "",
  ONEUI_PASSWORD: password = "",
  ONE_UI_BASE_URL: url = "",
  ONEUI_USERNAME_MA_ONLY: maOnlyUsername = "",
  ONEUI_USERNAME_ZID_ONLY: zidOnlyUsername = "",
  ONEUI_USERNAME_ZTDS_ONLY: ztdsOnlyUsername = "",
  ONEUI_USERNAME_ZTW_ONLY: ztwOnlyUsername = "",
  ONEUI_USERNAME_ZDX_ONLY: zdxOnlyUsername = "",
  ONEUI_USERNAME_ZIA_ONLY: ziaOnlyUsername = "",
  ONEUI_USERNAME_ZPA_ONLY: zpaOnlyUsername = "",
} = process.env;

Given("User open the URL", async ({ page }) => {
  await page.goto(url);
});

When(
  "User enter the valid {string} username and password",
  async ({ page }, userLogin: string) => {
    await page.waitForLoadState("domcontentloaded");
    const user = returnUserEmail(userLogin);
    await LoginPage.login(page, user, password);
  },
);

Then("User login to the console successfully", async ({ page }) => {
  await page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, {
    timeout: 40000,
  });
  await expect(page.getByTestId("nav-pills-tab-0")).toHaveText("Analytics");
});

Then("User waits till onboarding page gets loaded", async ({ page }) => {
  await page.waitForSelector(`[data-testid="three-steps-heading"]`, {
    timeout: 40000,
  });
});

Then("user clicks on Analytics", async ({ page }) => {
  await page.getByTestId("nav-pills-tab-0").click();
  await page.waitForTimeout(10000);
  // await PlaywrightActions.closePopup(page);
  const topnav = await page.getByTestId("mega-menu-container").isVisible();
  if (topnav) {
    await page
      .locator(`(//div[@data-testid="mega-menu-container"]/div/div/button/i)`)
      .click();
  }
  await page.mouse.click(300, 500);
});

Then("User fetch the okta token", async ({ page }) => {
  const raw = await BrowserStorageHelper.getOktaToken(page);
  const token = raw.replace(/^Bearer\s+/i, "");
  globalToken = token;
  console.log(globalToken);
  ApiHelper.setBearerToken(token);
});

function returnUserEmail(userName: string): string {
  return userName === "MA Only User"
    ? maOnlyUsername
    : userName === "ZID Only User"
      ? zidOnlyUsername
      : userName === "ZTDS Only User"
        ? ztdsOnlyUsername
        : userName === "ZTW Only User"
          ? ztwOnlyUsername
          : userName === "ZDX Only User"
            ? zdxOnlyUsername
            : userName === "ZIA Only User"
              ? ziaOnlyUsername
              : userName === "ZPA Only User"
                ? zpaOnlyUsername
                : username;
}
