import { Project } from "@playwright/test";
import { createBddProject } from "@up/e2e-common";
// Note: ONEUI_AUTH_STORAGE_STATE_PATH and ZIA_STANDALONE_AUTH_STORAGE_STATE_PATH are used by
// the test code within these projects to save the auth state, not directly in storageState option here.

const loginProject: Project = createBddProject({
  name: "Login", // This is the OneUI Login
  featuresRoot: 'src/common/login',
  outputDir: "",
  timeout: 120000,
  // This project's tests should save auth state to ONEUI_AUTH_STORAGE_STATE_PATH (defined in playwright.constants.ts)
});

export const loginProjects: Project[] = [loginProject];
