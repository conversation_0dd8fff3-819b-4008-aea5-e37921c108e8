{"name": "@xc/e2e", "version": "0.0.1", "private": true, "type": "module", "scripts": {"playwright:install": "pnpm exec playwright install", "clean:folders": "rimraf .features-gen allure-results playwright-report", "test:e2e": "pnpm run clean:folders && npx bddgen && npx playwright test", "test:parallel": "pnpm run clean:folders && npx bddgen && playwright test --workers=4", "test:e2e:ci": "npx playwright test", "prettier:config": "prettier 'src/**/*.{ts,js}'", "prettier:check": "pnpm prettier:config --check", "prettier": "pnpm prettier:config --write", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.t  s --fix", "verify": "pnpm prettier:check && pnpm lint"}, "dependencies": {"@up/e2e-common": "workspace:*"}, "peerDependencies": {"@playwright/test": "catalog:playwright", "playwright-bdd": "catalog:playwright"}, "devDependencies": {"rimraf": "^6.0.1", "@types/node": "24.0.8", "@up/eslint-config": "workspace:*", "@up/prettier-config": "workspace:*", "@up/typescript-config": "workspace:*", "eslint": "8.57.1", "prettier": "3.6.2", "tsup": "8.5.0", "type-fest": "4.41.0", "typescript": "5.7.3"}, "prettier": "@up/prettier-config"}