# XC Playwright End-to-End Testing Framework Documentation

## Introduction

### Overview

This project is a powerful testing framework using Playwright for GraphQL and REST APIs with support for reporting, BDD, and more.

### Purpose, Scope, and Key Objectives

- Automate end-to-end testing for web applications.
- Support for multiple browsers and platforms.
- Integration with CI/CD pipelines for continuous testing.

### Supported Browsers and Platforms

- **Chromium**
- **Firefox**
- **WebKit**
- **Windows, macOS, Linux**

---

## Getting Started

### Prerequisites

- **Node.js** (v22 or higher)
- **pnpm**

### Step-by-Step Guide

#### Install dependencies

```sh
pnpm install
```

#### Install browsers

```sh
pnpm xc-e2e playwright:install
```

#### Configure environment variables

Create a `.env` file in the root directory and add the necessary environment variables.

### Example

```sh
ONE_UI_BASE_URL=https://beta.console.zscaler.com/
ONEUI_USERNAME=<EMAIL>
ONEUI_PASSWORD=Admin@123
```

### Key Configuration Files

- **playwright.config.ts**: Main configuration file for Playwright.

## 4. Running Tests

### CLI Commands

#### Run all tests

```sh
pnpm xc-e2e test:e2e
```

#### Run all tests in CI

```sh
pnpm xc-e2e test:e2e:ci
```

### Additional Resources

- [Playwright Documentation](https://playwright.dev)
- [Playwright GitHub Repository](https://github.com/microsoft/playwright)
- [Playwright Community](https://playwright.dev/community)
