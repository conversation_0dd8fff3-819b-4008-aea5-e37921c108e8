{"name": "test-automation", "version": "0.0.1", "description": "", "main": "index.js", "keywords": [], "author": "", "license": "ISC", "scripts": {"test": "npx playwright test global_navigation.spec.ts;", "test:ui": "npx playwright test --ui global_navigation.spec.ts;", "test:headed": "npx playwright test --headed global_navigation.spec.ts;", "lint": "eslint **/*.ts --fix"}, "devDependencies": {"@playwright/test": "catalog:playwright", "@types/lodash": "4.17.19", "@types/node": "24.0.8", "eslint-plugin-playwright": "1.6.2", "@typescript-eslint/eslint-plugin": "8.25.0", "eslint": "8.57.1"}, "dependencies": {"dotenv": "16.4.5", "lodash": "4.17.21", "playwright-json-summary-reporter": "1.0.1", "qs": "6.13.0"}}