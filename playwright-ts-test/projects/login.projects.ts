import { Project } from '@playwright/test';
import { createBddProject } from '../playwright.project-factory';
// Note: ONEUI_AUTH_STORAGE_STATE_PATH and ZIA_STANDALONE_AUTH_STORAGE_STATE_PATH are used by
// the test code within these projects to save the auth state, not directly in storageState option here.

const loginProject: Project = createBddProject({
    name: 'Login', // This is the OneUI Login
    paths: ['src/tests/xc/features/login.feature'], // Feature files that perform login and save state
    require: ['src/tests/xc/step_definitions/*_steps.ts', 'src/tests/xc/pages/*_page.ts'],
    outputDir: 'results/Login',
    timeout: 120000,
    // This project's tests should save auth state to ONEUI_AUTH_STORAGE_STATE_PATH (defined in playwright.constants.ts)
});

// const onboardingLoginProject: Project = createBddProject({
//     name: 'OnboardingLogin', // This is the OneUI - Onboarding Login
//     paths: ['src/tests/xc/features/onboardingLogin.feature'], // Feature files that perform login and save state
//     require: ['src/tests/xc/step_definitions/*_steps.ts', 'src/tests/xc/pages/*_page.ts'],
//     outputDir: 'results/OnboardingLogin',
//     timeout: 120000,
//     // This project's tests should save auth state to ONEUI_AUTH_STORAGE_STATE_PATH (defined in playwright.constants.ts)
// });

const ziaStandaloneLoginProject: Project = createBddProject({
    name: 'ZIA_Standalone_Login',
    paths: ['src/tests/zia/features/*.feature'], // Feature files for ZIA standalone login and save state
    require: ['src/tests/zia/step_definitions/*_steps.ts', 'src/tests/zia/pages/*_page.ts'],
    outputDir: 'results/ZIALogin',
    timeout: 120000,
    // This project's tests should save auth state to ZIA_STANDALONE_AUTH_STORAGE_STATE_PATH (defined in playwright.constants.ts)
});

export const loginProjects: Project[] = [
    loginProject,
    ziaStandaloneLoginProject,
    // onboardingLoginProject,
];
