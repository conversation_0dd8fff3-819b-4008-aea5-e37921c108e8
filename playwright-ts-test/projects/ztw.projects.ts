import { Project } from '@playwright/test';
import { createBddProject } from '../playwright.project-factory';
import { ONEUI_AUTH_STORAGE_STATE_PATH, ZTW_STANDALONE_AUTH_STORAGE_STATE_PATH } from '../playwright.constants';
import { devices } from '@playwright/test'; // Import devices for use in project config

export const ztwProjects: Project[] = [
  createBddProject({
    name: 'ZTW_XC',
    paths: ['src/tests/ztw/features/**/*.feature'],
    require: ['src/tests/ztw/step_definitions/**/*_steps.ts','src/tests/ztw/pages/**/*_page.ts'],
    outputDir: 'results/ZTW',
    storageState: ZTW_STANDALONE_AUTH_STORAGE_STATE_PATH,
    dependencies: ['Login'],
    timeout: 100000,
  }),
  createBddProject({
    name: 'ZTW_Standalone',
    paths: ['src/tests/ztw/features/**/*.feature'],
    require: ['src/tests/ztw/step_definitions/**/*_steps.ts','src/tests/ztw/pages/**/*_page.ts'],
    outputDir: 'results/ZTW_Standalone',
    storageState: ZTW_STANDALONE_AUTH_STORAGE_STATE_PATH,
    dependencies: ['ZTW_Login'],
    timeout: 100000,
  }),
  createBddProject({
    name: 'ZTW_Login',
    paths: ['src/tests/ztw/features/*.feature'],
    require: ['src/tests/ztw/step_definitions/*_steps.ts','src/tests/ztw/pages/*_page.ts'],
    outputDir: 'results/ZTWLogin',
    use: {
      ...devices['Desktop Chrome'],
      viewport: { width: 1280, height: 720 }
    },
    storageState: ZTW_STANDALONE_AUTH_STORAGE_STATE_PATH,
    timeout: 120000,
  })
];


  