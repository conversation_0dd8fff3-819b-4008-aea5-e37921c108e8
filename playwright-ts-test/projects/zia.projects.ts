import { Project } from '@playwright/test';
import { createBddProject } from '../playwright.project-factory';
import { ONEUI_AUTH_STORAGE_STATE_PATH, ZIA_STANDALONE_AUTH_STORAGE_STATE_PATH } from '../playwright.constants';
import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

const EXECUTE_IN = process.env.EXECUTE_IN;

// Determine the storage state based on the value of EXECUTE_IN
const storageState =
    EXECUTE_IN === 'OneUi' ? ONEUI_AUTH_STORAGE_STATE_PATH : ZIA_STANDALONE_AUTH_STORAGE_STATE_PATH;

// Determine dependencies based on EXECUTE_IN
const defaultDependencies =
    EXECUTE_IN === 'OneUi' ? ['Login'] : ['ZIA_Standalone_Login'];

export const ziaProjects: Project[] = [
    createBddProject({
        name: 'Z<PERSON> Alerts',
        paths: ['src/tests/zia/features/Alerts/*.feature'],
        require: [
            'src/tests/zia/step_definitions/Alerts/*_steps.ts',
            'src/tests/zia/pages/Alerts/*_page.ts',
        ],
        outputDir: 'results/ZIA_Alerts',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),

    createBddProject({
        name: 'ZIA MyProfile',
        paths: ['src/tests/zia/features/MyProfile/*.feature'],
        require: ['src/tests/zia/step_definitions/MyProfile/*_steps.ts', 'src/tests/zia/pages/MyProfile/*_page.ts'],
        outputDir: 'results/ZIA_MyProfile',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA_MalwarePolicy',
        paths: ['src/tests/zia/features/MalwarePolicy/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_MalwarePolicy',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 200000,
    }),
    createBddProject({
        name: 'ZIA_EventLogs',
        paths: ['src/tests/zia/features/EventLogs/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_EventLogs',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 200000,
    }),
    createBddProject({
        name: 'ZIA_LocationManagement',
        paths: ['src/tests/zia/features/LocationManagement/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_LocationManagement',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 400000,
    }),

    createBddProject({
        name: 'ZIA_bandwidth_control',
        paths: ['src/tests/zia/features/BandwidthControl/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_BandwidthControl',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 200000,
    }),
    createBddProject({
        name: 'ZIA_SecureBrowsing',
        paths: ['src/tests/zia/features/SecureBrowsing/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_SecureBrowsing',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 200000,
    }),
    createBddProject({
        name: 'ZIA_Nanolog',
        paths: ['src/tests/zia/features/NanologStreamingService/*.feature'],
        require: ['src/tests/zia/step_definitions/NanologStreamingService/*_steps.ts', 'src/tests/zia/pages/NanologStreamingService/*_page.ts'],
        outputDir: 'results/ZIA_Nanolog',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 550000,
        ignoreHTTPSErrors: true,
    }),
    createBddProject({
        name: 'ZIA_MobileMalwareProtection',
        paths: ['src/tests/zia/features/MobileMalwareProtection/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_MobileMalwareProtection',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 200000,
    }),
    createBddProject({
        name: 'ZIA_VirtualServiceEdge',
        paths: ['src/tests/zia/features/VirtualServiceEdges/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_VirtualServiceEdges',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 300000,
    }),
    createBddProject({
        name: 'ZIA_DLPIncidentReceiver',
        paths: ['src/tests/zia/features/DLPIncidentReceiver/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_DLPIncidentReceiver',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 300000,
    }),
    createBddProject({
        name: 'ZIA_PartnerIntegrations',
        paths: ['src/tests/zia/features/PartnerIntegrations/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_PartnerIntegrations',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 400000,
    }),
    createBddProject({
        name: 'ZIA_Vpn_Credentials',
        paths: ['src/tests/zia/features/VpnCredentialsManagement/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_VpnCredentialsManagement',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 400000,
    }),
    createBddProject({
        name: 'ZIA_EZ_Agent_Configuration',
        paths: ['src/tests/zia/features/EzAgentConfiguration/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/EzAgentConfiguration',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 400000,
    }),
    createBddProject({
        name: 'ZIA_FTP_Control',
        paths: ['src/tests/zia/features/FTPControl/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_FTPControl',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA_Print_All_Policies',
        paths: ['src/tests/zia/features/PrintAllPolicies/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_PrintAllPolicies',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA_Advanced_Settings',
        paths: ['src/tests/zia/features/AdvancedSettings/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_AdvancedSettings',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA_Authentication_Settings',
        paths: ['src/tests/zia/features/AuthenticationSettings/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_AuthenticationSettings',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA_User_Management',
        paths: ['src/tests/zia/features/userManagement/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_UserManagement',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA Identity Proxy Settings',
        paths: ['src/tests/zia/features/IdentityProxySettings/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_IdentityProxySettings',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA Administrator Management',
        paths: ['src/tests/zia/features/AdministratorManagement/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_AdministratorManagement',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA Role Management',
        paths: ['src/tests/zia/features/RoleManagement/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_RoleManagement',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA Backup And Restore',
        paths: ['src/tests/zia/features/BackupAndRestore/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_BackupAndRestore',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA URL Categories',
        paths: ['src/tests/zia/features/URLCategories/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_URLCategories',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA Time Intervals',
        paths: ['src/tests/zia/features/TimeIntervals/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_TimeIntervals',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA Rule Labels',
        paths: ['src/tests/zia/features/RuleLabels/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_RuleLabels',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA Device Management',
        paths: ['src/tests/zia/features/DeviceManagement/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_DeviceManagement',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA Notification Template',
        paths: ['src/tests/zia/features/NotificationTemplates/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_NotificationTemplate',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA EDNS Client Subnet',
        paths: ['src/tests/zia/features/EDNSClientSubnet/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_EDNSClientSubnet',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA Index Tool',
        paths: ['src/tests/zia/features/IndexTool/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_IndexTool',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA Application Service',
        paths: ['src/tests/zia/features/ApplicationService/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_ApplicationService',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA_Firewall_Control',
        paths: ['src/tests/zia/features/FirewallControl/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_FirewallControl',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
    createBddProject({
        name: 'ZIA_IPv6_Configuration',
        paths: ['src/tests/zia/features/Ipv6Configuration/*.feature'],
        require: ['src/tests/zia/step_definitions/**/*_steps.ts', 'src/tests/zia/pages/**/*_page.ts'],
        outputDir: 'results/ZIA_Ipv6Configuration',
        storageState: storageState,
        dependencies: defaultDependencies,
        timeout: 100000,
    }),
];

