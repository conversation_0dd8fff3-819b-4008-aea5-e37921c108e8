import { Project } from '@playwright/test';
import { createBddProject } from '../playwright.project-factory';
import { ONEUI_AUTH_STORAGE_STATE_PATH } from '../playwright.constants';

export const uiProjects: Project[] = [
    createBddProject({
        name: 'DigitalExperience',
        paths: ['src/tests/xc/features/DigitalExperience/*.feature'],
        require: ['src/tests/xc/step_definitions/DigitalExperience/*_steps.ts', 'src/tests/xc/pages/DigitalExperience/*_page.ts'],
        outputDir: 'results/DE',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 240000,
    }),
    createBddProject({
        name: 'Cybersecurity',
        paths: ['src/tests/xc/features/Cybersecurity/*.feature'],
        require: ['src/tests/xc/step_definitions/Cybersecurity/*_steps.ts', 'src/tests/xc/pages/Cybersecurity/*_page.ts'],
        outputDir: 'results/CS',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 150000,
    }),
    createBddProject({
        name: 'Networking',
        paths: ['src/tests/xc/features/Networking/*.feature'],
        require: ['src/tests/xc/step_definitions/Networking/*_steps.ts', 'src/tests/xc/pages/Networking/*_page.ts'],
        outputDir: 'results/Net',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 120000,
    }),
    createBddProject({
        name: 'GlobalNavigation',
        paths: ['src/tests/xc/features/GlobalNavigation/*.feature'],
        require: ['src/tests/xc/step_definitions/**/*_steps.ts', 'src/tests/xc/pages/**/*_page.ts'],
        outputDir: 'results/GN',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 600000,
    }),
    createBddProject({
        name: 'Operational',
        paths: ['src/tests/xc/features/Operational/*.feature'],
        require: ['src/tests/xc/step_definitions/**/*_steps.ts', 'src/tests/xc/pages/**/*_page.ts'],
        outputDir: 'results/OP',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 100000,
    }),
    createBddProject({
        name: 'DarkThemeSetup',
        paths: ['src/tests/xc/features/DarkTheme/darkTheme.feature'],
        require: ['src/tests/xc/step_definitions/**/*_steps.ts', 'src/tests/xc/pages/**/*_page.ts', 'resources/utils/hooks.ts'],
        outputDir: 'results/DT_Setup',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 120000,
    }),
    createBddProject({
        name: 'DarkTheme',
        paths: ['src/tests/xc/features/DarkTheme/*.feature', '!src/tests/xc/features/DarkTheme/darkTheme.feature'],
        require: ['src/tests/xc/step_definitions/**/*_steps.ts', 'src/tests/xc/pages/**/*_page.ts', 'resources/utils/hooks.ts'],
        outputDir: 'results/DT',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['DarkThemeSetup'],
        timeout: 200000,
    }),
    createBddProject({
        name: 'TopNav',
        paths: ['src/tests/xc/features/TopNav/*.feature'],
        require: ['src/tests/xc/step_definitions/TopNav/*_steps.ts', 'src/tests/xc/pages/TopNav/*_page.ts'],
        outputDir: 'results/TN',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 100000,
    }),
    createBddProject({ 
        name: 'RBAC',
        paths: ['src/tests/xc/features/RBAC/*.feature'],
        require: ['src/tests/xc/step_definitions/**/*_steps.ts', 'src/tests/xc/pages/**/*_page.ts'],
        outputDir: 'results/RBAC',
        timeout: 260000,
        // No storageState or dependencies for RBAC as per original config
    }),
    createBddProject({
        name: 'Home',
        paths: ['src/tests/xc/features/Home/*.feature'],
        require: ['src/tests/xc/step_definitions/Home/*_steps.ts', 'src/tests/xc/pages/Home/*_page.ts'],
        outputDir: 'results/Home',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 300000,
        fullyParallel: false,
    }),
    createBddProject({
        name: 'DataProtection',
        paths: ['src/tests/xc/features/DataProtection/*.feature'],
        require: ['src/tests/xc/step_definitions/DataProtection/*_steps.ts', 'src/tests/xc/pages/DataProtection/*_page.ts'],
        outputDir: 'results/DP',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 120000,
    }),
    createBddProject({
        name: 'Risk360',
        paths: ['src/tests/xc/features/Risk360/*.feature'],
        require: ['src/tests/xc/step_definitions/Risk360/*_steps.ts', 'src/tests/xc/pages/Risk360/*_page.ts'],
        outputDir: 'results/Risk360',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 120000,
        ignoreHTTPSErrors: true,
    }),
    createBddProject({
        name: 'UnifiedLocations',
        paths: ['src/tests/xc/features/UnifiedLocations/*.feature'],
        require: ['src/tests/xc/step_definitions/**/*_steps.ts', 'src/tests/xc/pages/**/*_page.ts'],
        outputDir: 'results/UL',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 500000,
        fullyParallel: false
    }),
    createBddProject({
        name: 'UnifiedSubscriptions',
        paths: ['src/tests/xc/features/UnifiedSubscriptions/*.feature'],
        require: ['src/tests/xc/step_definitions/**/*_steps.ts', 'src/tests/xc/pages/**/*_page.ts'],
        outputDir: 'results/US',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 100000,
    }),
    // createBddProject({
        //     name: 'NewAuthFlow',
        //     paths: ['src/tests/xc/features/AuthFlow/*.feature'],
        //     require: ['src/tests/xc/step_definitions/AuthFlow/*_steps.ts', 'src/tests/xc/pages/AuthFlow/*_page.ts'],
        //     outputDir: 'results/NAF',
        //     timeout: 3800000,
        //     // No storageState or dependencies as per original config
        // }),
    createBddProject({
        name: 'PageLoad',
        paths: ['src/tests/xc/features/PageLoads/*.feature'],
        require: ['src/tests/xc/step_definitions/PageLoads/*_steps.ts', 'src/tests/xc/pages/PageLoads/*_page.ts'],
        outputDir: 'results/PL',
        storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
        dependencies: ['Login'],
        timeout: 6000000,
    }),
    // createBddProject({
    //     name: 'Onboarding',
    //     paths: ['src/tests/xc/features/Onboarding/*.feature'],
    //     require: ['src/tests/xc/step_definitions/Onboarding/*_steps.ts', 'src/tests/xc/pages/Onboarding/*_page.ts'],
    //     outputDir: 'results/Onboarding',
    //     storageState: ONEUI_AUTH_STORAGE_STATE_PATH,
    //     dependencies: ['OnboardingLogin'],
    //     timeout: 1000000,
    // }),
];
