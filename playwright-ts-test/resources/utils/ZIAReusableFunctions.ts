import { Page, expect } from '@playwright/test';
import PlaywrightActions from './PlaywrightActions';

class ZIAReuasableFunctions {
  
  async provideZIAInputFieldText(page:Page,labelName: string,inputText: string): Promise<void> {

    await page.locator('(//*[text()="'+labelName +'"]/../../following-sibling::*//input[contains(@class,"input")])[1]').hover(); 
    await page.locator('(//*[text()="'+labelName +'"]/../../following-sibling::*//input[contains(@class,"input")])[1]').click(); 
    await page.locator('(//*[text()="'+labelName +'"]/../../following-sibling::*//input[contains(@class,"input")])[1]').clear();
    await page.locator('(//*[text()="'+labelName +'"]/../../following-sibling::*//input[contains(@class,"input")])[1]').fill(inputText);
  }
  async clickOnZIALabelDropdown(page:Page,labelName: string,indexNo: number): Promise<void> {

    await page.locator('(//*[text()="'+labelName+'"]/../../following-sibling::*//*[contains(@class,"down")]//*[contains(@class,"dropdown-icon")])['+indexNo+']').hover(); 
    await page.locator('(//*[text()="'+labelName+'"]/../../following-sibling::*//*[contains(@class,"down")]//*[contains(@class,"dropdown-icon")])['+indexNo+']').click(); 
  }
  async selectZIAMenuOptions(page: Page,tenantName: string,menuOption: string) {

    await page.locator('(//*[text()="'+tenantName+'"]/../../../../following-sibling::*//*[contains(@class,"'+menuOption+'")])[1]').isVisible();
    await page.locator('(//*[text()="'+tenantName+'"]/../../../../following-sibling::*//*[contains(@class,"'+menuOption+'")])[1]').hover();
    await page.locator('(//*[text()="'+tenantName+'"]/../../../../following-sibling::*//*[contains(@class,"'+menuOption+'")])[1]').click();
  }
  async clickOnFooterButtonInsideZIAWizard(page: Page,wizardHeaderName: string,footerButtonAction: string) {

    await page.locator('//*[contains(text(),"'+wizardHeaderName+'") and contains(@class,"header")]/../../following-sibling::*//*[contains(@class,"footer")]/*[text()="'+footerButtonAction+'"]').isVisible();
    await page.locator('//*[contains(text(),"'+wizardHeaderName+'") and contains(@class,"header")]/../../following-sibling::*//*[contains(@class,"footer")]/*[text()="'+footerButtonAction+'"]').hover();
    await page.locator('//*[contains(text(),"'+wizardHeaderName+'") and contains(@class,"header")]/../../following-sibling::*//*[contains(@class,"footer")]/*[text()="'+footerButtonAction+'"]').click();
  }
  async captureToogleButtonStatus(page: Page,toogleButtonName: string) {

    await page.locator('(//*[text()="'+toogleButtonName+'"]/../../following-sibling::*//*[contains(@class,"toggle-label")]/I[contains(@class,"times")]/..)[1]').isVisible();
    await page.locator('(//*[text()="'+toogleButtonName+'"]/../../following-sibling::*//*[contains(@class,"toggle-label")]/I[contains(@class,"times")]/..)[1]').hover();
    await page.locator('(//*[text()="'+toogleButtonName+'"]/../../following-sibling::*//*[contains(@class,"toggle-label")]/I[contains(@class,"times")]/..)[1]').getAttribute('class');
  }
  async clickOnToggleButtonIconInZIA(page: Page,toogleButtonName: string) {

    await page.locator('(//*[text()="'+toogleButtonName+'"]/../../following-sibling::*//*[contains(@class,"toggle-label")]/I[contains(@class,"times")]/..)[1]').isVisible();
    await page.locator('(//*[text()="'+toogleButtonName+'"]/../../following-sibling::*//*[contains(@class,"toggle-label")]/I[contains(@class,"times")]/..)[1]').hover();
    await page.locator('(//*[text()="'+toogleButtonName+'"]/../../following-sibling::*//*[contains(@class,"toggle-label")]/I[contains(@class,"times")]/..)[1]').click();
  }
  async captureZIANotificationMessage(page: Page):Promise<void> {

    await page.waitForTimeout(2000);
    await page.locator('//*[contains(@class,"notification-message-content")]').waitFor();
    await page.locator('//*[contains(@class,"notification-message-content")]').textContent();
  }
  async clickOnContentTabTitleInZIA(page: Page,tabName: string):Promise<void> {

    await page.locator('//*[contains(@class,"content-tab-title") and contains(text(),"'+tabName+'")]').isEnabled();
    await page.locator('//*[contains(@class,"content-tab-title") and contains(text(),"'+tabName+'")]').hover();
    await page.locator('//*[contains(@class,"content-tab-title") and contains(text(),"'+tabName+'")]').click();
  }
  async clickOnSingleSelectDropdownRecord(page: Page,dropdownName: string) {

    await page.locator('//*[contains(@class,"list")]//*[text()="'+dropdownName+'"]').hover();
    await page.locator('//*[contains(@class,"list")]//*[text()="'+dropdownName+'"]').click();
  }
  async captureWizardHeaderTitleInZIA(page: Page):Promise<void> {

    await page.locator('//*[contains(@class,"header-title")]//*[contains(@class,"header-label") and text()]').isVisible();
    await page.locator('//*[contains(@class,"header-title")]//*[contains(@class,"header-label") and text()]').hover();
    await page.locator('//*[contains(@class,"header-title")]//*[contains(@class,"header-label") and text()]').textContent();
  }
}

export default new ZIAReuasableFunctions();