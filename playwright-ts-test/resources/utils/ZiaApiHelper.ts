// /Users/<USER>/Desktop/ZIA/xc-ui/playwright-ts-test/resources/utils/ZiaAdminApiHelper.ts
import { Page, APIRequestContext } from "@playwright/test";
import fs from 'fs';
import path from 'path';

// Re-define or import from a shared types file if used elsewhere
export interface NssFeedApiResponse {
  id: number;
  name: string;
  // Add other relevant properties from the API response
}

interface AuthCookie {
  name: string;
  value: string; // Expect value to be a string.
  // other cookie properties if needed e.g. domain, path etc.
}

interface AuthFile {
  cookies: AuthCookie[];
  origins?: any[]; // Not directly used for ZS_SESSION_CODE logic here, but good to acknowledge.
}

const DEFAULT_AUTH_FILE_PATH = 'playwright/.auth/user.json';
// It's recommended to manage the API base URL through environment variables or a central config
const ZIA_ADMIN_API_BASE_URL = process.env.ZIA_ADMIN_API_URL || "https://admin.zsqa.net/zsapi/v1";


export class ZiaAdminApiHelper {
    private page: Page;
    private apiBaseUrl: string;
    private zsCustomCode: string | null = null;
    private authFilePath: string;
    private isSessionActive = false;

    constructor(page: Page, authFilePath: string = DEFAULT_AUTH_FILE_PATH) {
        this.page = page;
        this.apiBaseUrl = ZIA_ADMIN_API_BASE_URL.endsWith('/') ? ZIA_ADMIN_API_BASE_URL.slice(0, -1) : ZIA_ADMIN_API_BASE_URL;
        this.authFilePath = authFilePath;
    }

    /**
     * Activates the API session. This is often required before making other API calls.
     * It includes a check to prevent multiple activation calls.
     */
    public async activateSession(): Promise<void> {
        if (this.isSessionActive) {
            return;
        }

        const headers = await this.getHeaders();
        const apiContext: APIRequestContext = this.page.request;
        console.log('Activating API session...');
        const response = await apiContext.post(`${this.apiBaseUrl}/status/activate`, {
            headers: headers,
        });

        if (!response.ok()) {
            const errorText = await response.text();
            console.error(`Failed to activate session. Status: ${response.status()}, Response: ${errorText}`);
            throw new Error(`Failed to activate session: ${response.status()} ${response.statusText()} - ${errorText}`);
        }
        console.log(`Session activated successfully. Status: ${response.status()}`);
        this.isSessionActive = true;
    }

    private async getZsCustomCode(): Promise<string> {
        if (this.zsCustomCode) {
            return this.zsCustomCode;
        }

        const authFile = path.resolve(process.cwd(), this.authFilePath);
        console.log(`Auth file path resolved to: ${authFile}`);
        if (!fs.existsSync(authFile)) {
            throw new Error(`Auth file not found at ${authFile}. Ensure the path is correct and the file exists.`);
        }

        const authFileContent = fs.readFileSync(authFile, 'utf-8');
        const authData: AuthFile = JSON.parse(authFileContent);
        console.log('Auth file read and parsed successfully.');

        if (!authData || !Array.isArray(authData.cookies)) {
            console.error('Auth data is invalid or cookies array is missing in auth file.');
            throw new Error('Invalid auth file structure: cookies array not found or invalid.');
        }

        const zsSessionCookie = authData.cookies.find(cookie => cookie.name === 'ZS_SESSION_CODE');
        if (!zsSessionCookie || !zsSessionCookie.value) {
            // Since cookie.value is typed as string, !zsSessionCookie.value would primarily catch empty strings.
            // The !zsSessionCookie part handles the case where the cookie itself is not found.
            throw new Error('ZS_SESSION_CODE cookie not found or has no value in auth file.');
        }
        this.zsCustomCode = zsSessionCookie.value;
        console.log(`ZS_CUSTOM_CODE extracted: ${this.zsCustomCode}`);
        return this.zsCustomCode;
    }

    private async getHeaders(): Promise<Record<string, string>> {
        const code = await this.getZsCustomCode();
        // Derive Referer and Origin from the full API base URL
        // e.g., if apiBaseUrl is "https://admin.zsqa.net/zsapi/v1", originRefererBase would be "https://admin.zsqa.net"
        let originRefererBase = this.apiBaseUrl;
        const zsapiIndex = this.apiBaseUrl.indexOf('/zsapi');
        if (zsapiIndex !== -1) {
            originRefererBase = this.apiBaseUrl.substring(0, zsapiIndex);
        } else {
            // Fallback or warning if the URL structure is unexpected
            console.warn(`Unexpected API base URL format for deriving Origin/Referer: ${this.apiBaseUrl}`);
            // Attempt to derive from page URL as a last resort, or use a fixed value if appropriate
            const pageUrl = new URL(this.page.url());
            originRefererBase = pageUrl.origin;
        }

        return {
            'Accept': 'application/json, text/plain, */*',
            'Referer': `${originRefererBase}/`, 
            'Origin': originRefererBase,
            'ZS_CUSTOM_CODE': code,
            'crossdomain': 'true',
            'User-Agent': 'Playwright Test Automation (ZiaAdminApiHelper)',
        };
    }

    public async getAllNssFeeds(): Promise<NssFeedApiResponse[]> {
        await this.activateSession();
        const headers = await this.getHeaders();
        const apiContext: APIRequestContext = this.page.request;
        console.log('Fetching NSS feeds via API helper...');
        const response = await apiContext.get(`${this.apiBaseUrl}/nssFeeds`, {
            params: { timestamp: Date.now() },
            headers: headers,
        });

        if (!response.ok()) {
            const errorText = await response.text();
            console.error(`Failed to fetch NSS feeds. Status: ${response.status()}, Response: ${errorText}`);
            throw new Error(`Failed to fetch NSS feeds: ${response.status()} ${response.statusText()} - ${errorText}`);
        }
        const feeds: NssFeedApiResponse[] = await response.json();
        console.log(`Successfully fetched ${feeds.length} feeds via API helper.`);
        return feeds;
    }

    public async findNssFeedByName(feedName: string): Promise<NssFeedApiResponse | null> {
        console.log(`Searching for NSS feed by name "${feedName}" via API helper.`);
        const feeds = await this.getAllNssFeeds();
        const foundFeed = feeds.find(feed => feed.name === feedName);
        if (foundFeed) {
            console.log(`Feed "${feedName}" found with ID: ${foundFeed.id}.`);
        } else {
            console.log(`Feed "${feedName}" not found via API helper.`);
        }
        return foundFeed || null;
    }

    public async deleteNssFeedById(feedId: number | string): Promise<void> {
        const headers = await this.getHeaders();
        const apiContext: APIRequestContext = this.page.request;
        console.log(`Deleting NSS feed with ID ${feedId} via API helper.`);
        const response = await apiContext.delete(`${this.apiBaseUrl}/nssFeeds/${feedId}`, {
            headers: headers,
        });

        if (!response.ok()) {
            const errorText = await response.text();
            console.error(`Failed to delete NSS feed ${feedId}. Status: ${response.status()}, Response: ${errorText}`);
            throw new Error(`Failed to delete NSS feed ${feedId} via API: ${response.status()} ${response.statusText()} - ${errorText}`);
        }
        console.log(`Feed with ID: ${feedId} deleted successfully via API helper. Status: ${response.status()}`);
    }

    public async deleteNssFeedByName(feedName: string): Promise<void> {
        console.log(`Attempting to delete NSS Feed "${feedName}" using ZiaAdminApiHelper.`);
        const feedToDelete = await this.findNssFeedByName(feedName);

        if (!feedToDelete) {
            console.log(`No record found to delete: "${feedName}" (using helper).`);
            return; 
        }

        console.log(`Found feed "${feedName}" with ID: ${feedToDelete.id} (using helper). Proceeding to delete.`);
        await this.deleteNssFeedById(feedToDelete.id);
        console.log(`Feed with ID: ${feedToDelete.id} and name: "${feedName}" successfully processed for deletion by helper.`);
    }

    /**
     * Creates a new Policy Rule via API.
     * @param specificEndpoint The specific path for this new resource (e.g., 'policyRules').
     * @param policyRuleData The data for the new policy rule (request body).
     * @returns The API response, typically the created policy rule.
     */
    public async createResource(specificEndpoint: string, resourceData: Record<string, any>): Promise<any> {
        const headers = await this.getHeaders();
        const apiContext: APIRequestContext = this.page.request;
        
        // Construct the full URL
        const url = `${this.apiBaseUrl}/${specificEndpoint.startsWith('/') ? specificEndpoint.substring(1) : specificEndpoint}`;

        console.log(`Sending POST request to create resource at: ${url} with body:`, resourceData);

        const response = await apiContext.post(url, {
            data: resourceData, 
            headers: {
                ...headers, // Common headers including ZS_CUSTOM_CODE
                'Content-Type': 'application/json', // Standard for JSON payloads
            },
        });

        if (!response.ok()) {
            const errorText = await response.text();
            console.error(`Failed to create resource at ${specificEndpoint}. Status: ${response.status()}, Response: ${errorText}`);
            throw new Error(`Failed to create resource at ${specificEndpoint}: ${response.status()} ${response.statusText()} - ${errorText}`);
        }

        const responseData = await response.json();
        console.log(`Resource created successfully at ${specificEndpoint}:`, responseData);
        return responseData;
    }

    /**
     * Generic method to send a GET request to a specific endpoint.
     * @param specificEndpoint The specific path for the resource (e.g., 'nssFeeds', 'policyRules/123').
     * @param queryParams Optional query parameters for the GET request.
     * @returns The API response.
     */
    public async getResource(specificEndpoint: string, queryParams?: Record<string, string | number | boolean>): Promise<any> {
        const headers = await this.getHeaders();
        const apiContext: APIRequestContext = this.page.request;
        const url = `${this.apiBaseUrl}/${specificEndpoint.startsWith('/') ? specificEndpoint.substring(1) : specificEndpoint}`;

        console.log(`Sending GET request to: ${url} with params:`, queryParams);

        const response = await apiContext.get(url, {
            params: queryParams, // Your query parameters
            headers: headers,    // Standard auth headers
        });

        if (!response.ok()) {
            const errorText = await response.text();
            console.error(`Failed to fetch resource from ${specificEndpoint}. Status: ${response.status()}, Response: ${errorText}`);
            throw new Error(`Failed to fetch resource from ${specificEndpoint}: ${response.status()} ${response.statusText()} - ${errorText}`);
        }
        const responseData = await response.json();
        console.log(`Resource fetched successfully from ${specificEndpoint}:`, responseData);
        return responseData;
    }
}
