import { chromium, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@playwright/test';
import loginPage from "./src/tests/xc/pages/login_page";
import { config } from "dotenv";

config();

const {
    ONEUI_USERNAME: username,
    ONEUI_PASSWORD: password,
    ONE_UI_BASE_URL: url,
} = process.env;

export default async () => {
  const browser: Browser = await chromium.launch();
  const context: BrowserContext = await browser.newContext();
  const page: Page = await context.newPage();

  // Perform login
  if (url && username && password) {
    await page.goto(url);
    await loginPage.login(page, username, password);
  } else {
    console.error("Missing environment variables for login.");
  }

  // Save the storage state (cookies, localStorage, etc.)
  await context.storageState({ path: 'storageState.json' });

  await browser.close();
};
