import { Page } from '@playwright/test';

class LoginPage {
  async login(page: Page, user: string, pass: string): Promise<void> {
    await page.getByTestId('loginId').click();
    await page.getByTestId('loginId').fill(user);
    await page.getByTestId('next').click();
    await page.getByTestId('password').click();
    await page.getByTestId('password').fill(pass);
    await page.getByTestId('next').click();
  }
}

export default new LoginPage();