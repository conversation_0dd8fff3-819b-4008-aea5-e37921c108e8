import { Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { FILTERS as connectorActivityFilters, DATE_FILTER, TEST_IDS, NAVIGATION, TABLE_SELECTOR, TABLE_ROWS, TAB_SELECTOR } from '../../../../../resources/constants/pages/analytics/networking/connectorActivity';
import Base from "../../../../../lib/pom/Base/Base";

class ConnectorActivity extends Base {

    constructor(page: Page) {
            super(page, {
                pageName: NAVIGATION.pageName,
                menuTestId: NAVIGATION.menuId,
                submenuTestId: NAVIGATION.submenuId,
                urlSuffix: NAVIGATION.url,
                dateFilters: DATE_FILTER,
                selectors: TEST_IDS,
                tableSelectors: TABLE_SELECTOR,
                tableRows: TABLE_ROWS,
                tabSelectors: TAB_SELECTOR,
            });
    }

    async checkFilters(tabName?: string): Promise<void> {
        if(tabName)
        await PlaywrightActions.checkConnectorFilters(this.page, `connector-activity-${tabName}`, connectorActivityFilters[tabName]);
    }
    
    async checkContainerTitle(title: string, tabName?: string): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${this.selectors[`${title}-${tabName}`]}-card-title`, title);
    }

    async checkGraph(title: string, grapType: string, tabName?: string,): Promise<void> {
        await PlaywrightActions.isWidgetLoaded(this.page, `${this.selectors[`${title}-${tabName}`]}`, grapType);
    }
    
    async checkTimestamp(): Promise<void> {
        await this.page.waitForTimeout(10000);
        await PlaywrightActions.checkTimeStamp(this.page, "networking-connector-activity-last-updated-time");
    }

}

export default ConnectorActivity;