import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { DATE_FILTER, TEST_IDS, NAVIGATION, TAB_SELECTOR } from '../../../../../resources/constants/pages/analytics/networking/networking';
import Base from "../../../../../lib/pom/Base/Base";


class Networking extends Base {

    constructor(page: Page) {
            super(page, {
                pageName: NAVIGATION.pageName,
                menuTestId: NAVIGATION.menuId,
                urlSuffix: NAVIGATION.url,
                dateFilters: DATE_FILTER,
                selectors: TEST_IDS,
                tabSelectors: TAB_SELECTOR,
            });
    }

    async checkLabelWithValue(title: string, tabName: string, valueType: string): Promise<void> {
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[`Traffic in my Organization-${tabName}`]);
        if(!needData)
        {
            await PlaywrightActions.checkHeaderByTestId(this.page, `${this.selectors[`${title}-${tabName}`]}-label`, title);
            await PlaywrightActions.getValue(this.page, `${this.selectors[`${title}-${tabName}`]}-value`, valueType);
        }
    }

    async checkValue(containerName: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[containerName]);
        if(!needData)
        {
            let value = await PlaywrightActions.getValue(this.page, `${this.selectors[containerName]}-value`);
            await expect(value).toBeGreaterThanOrEqual(0);
        }
    }

    async checkLabelGraph(title: string, tabName: string, grapType: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[`Traffic in my Organization-${tabName}`]);
        if(!needData)
        {
            await PlaywrightActions.checkGraph(this.page, `${this.selectors[`${title}-${tabName}`]}-${grapType}`);
        }
    }

}

export default Networking;