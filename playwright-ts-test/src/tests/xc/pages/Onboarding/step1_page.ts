import { expect, Page } from "@playwright/test";
import CommonFunctions from "../../../../../resources/utils/CommonFunctions";
import { ONBOARDING_STEPS_SELCTORS as stepsSelector,  } from '../../../../../resources/constants/pages/onboarding/landing';
import { NAVIGATION, TEST_IDS, FILTERS, TABLE_SELECTOR, TABLE_ROWS, USER_DETAILS as userData } from '../../../../../resources/constants/pages/onboarding/setupUsers';
import OnboardingBasePage from '../../../../../lib/pom/Onboarding/Onboarding';

class OnboardingSetUp extends OnboardingBasePage {
    constructor(page: Page) {
        super(page, {
            pageName: NAVIGATION.pageName,
            urlSuffix: NAVIGATION.url,
            selectors: TEST_IDS,
            filters: FILTERS,
            tableRows: TABLE_ROWS,
            tableSelectors: TABLE_SELECTOR,
        });
    }

    async navigateTo(): Promise<void> {
        await this.page.getByTestId(`three-steps-step-${stepsSelector["Set Up Users"].testId}-edit`).click();
        await CommonFunctions.waitForPageLoad(this.page, `${this.selectors["Users"]}-heading`);
    }

    async uploadCSV(): Promise<void> {
        await this.page.getByTestId(this.selectors["Upload CSV"]).setInputFiles("");

    }

    async addUser(): Promise<void> {
        await this.page.getByTestId(this.selectors["Add User"]).click();
        await expect(this.page.getByTestId(this.selectors["Add User Popup"])).toBeVisible();
        expect(await this.page.getByTestId(this.selectors["Add User Popup Header"]).textContent()).toBe("Add User");
        await this.page.locator('label[for="name"]');
        await this.page.locator('label[for="email"]');
        await this.page.getByRole('textbox', { name: 'Name' }).fill(userData.Name);
        await this.page.getByRole('textbox', { name: 'Email' }).fill(userData.Email);
        await this.page.getByLabel("LoginID");
        await this.page.getByLabel("Domain");
        await this.page.getByTestId(this.selectors["Domain"]).click();
        await expect(this.page.getByTestId(this.selectors["Domain Popup"])).toBeVisible();
        const domain = await this.page.getByTestId(`${this.selectors["Domain Options"]}-0`).textContent();   
        await this.page.getByTestId(`${this.selectors["Domain Options"]}-0`).click();
        await expect(this.page.locator(`[title="@${domain}"]`)).toBeVisible();
        await this.page.getByText("Choose Role");
        await this.page.getByRole('radio').nth(1).click();
        expect(await this.page.getByTestId(this.selectors["Add User Popup Footer"]).textContent()).toBe("After you complete onboarding, you can assign more granular administrators like Cybersecurity, Data Protection, Networking and others.");
        await this.page.getByRole('button', { name: 'Add User' }).nth(-1).click();
        await this.page.waitForSelector(`[data-testid="alert-0"]`, {timeout:5000});
    }   
    
    async deleteUser(): Promise<void> {
        await this.page.getByTestId(`${this.selectors["Users"]}-search-bar-input`).fill(userData.Name);
        await this.page.waitForTimeout(3000);
        await this.page.getByTestId(`icon1-z-menu`).click();
        await expect(this.page.getByTestId(this.selectors["Delete User Popup"])).toBeVisible();
        await this.page.waitForTimeout(3000);
        await this.page.getByTestId(this.selectors["Delete User Popup"]).getByRole('button', { name: 'Delete' }).click();
        await this.page.waitForSelector(`[data-testid="alert-0"]`, {timeout:5000});
    }
}

export default OnboardingSetUp;
