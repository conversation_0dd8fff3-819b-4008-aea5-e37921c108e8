import { Page } from "@playwright/test";
import {NAVIGATION, TEST_IDS } from '../../../../../resources/constants/pages/onboarding/landing';
import OnboardingBasePage from '../../../../../lib/pom/Onboarding/Onboarding';

class OnboardingLanding extends OnboardingBasePage {
    constructor(page: Page) {
        super(page, {
            pageName: NAVIGATION.pageName,
            urlSuffix: NAVIGATION.url,
            selectors: TEST_IDS,
        });
    }
}

export default OnboardingLanding;