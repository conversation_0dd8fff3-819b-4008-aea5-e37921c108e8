import { expect, Page } from "@playwright/test";
import CommonFunctions from "../../../../../resources/utils/CommonFunctions";
import { ONBOARDING_STEPS_SELCTORS as stepsSelector,  } from '../../../../../resources/constants/pages/onboarding/landing';
import { NAVIGATION, TEST_IDS as selectors, FILTERS, TABLE_SELECTOR, TABLE_ROWS, INPUT as inputs } from '../../../../../resources/constants/pages/onboarding/secureTraffic';
import OnboardingBasePage from '../../../../../lib/pom/Onboarding/Onboarding';
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { StepKey } from "../../../../../resources/types/types";

class OnboardingSecureTraffic extends OnboardingBasePage {
    constructor(page: Page) {
        super(page, {
            pageName: NAVIGATION.pageName,
            urlSuffix: NAVIGATION.url,
            filters: FILTERS,
            tableRows: TABLE_ROWS,
            tableSelectors: TABLE_SELECTOR,
        });
    }

    async navigateTo(): Promise<void> {
        await this.page.getByTestId(`three-steps-step-${stepsSelector["Secure Traffic"].testId}-button`).click();
        await CommonFunctions.waitForPageLoad(this.page, `secure-traffic-step-1-stepper-label`);
    }

    async checkStepperLabel(label: string, step: StepKey): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${selectors[step][label]}-stepper-label`, label);
    }

    async checkLabel(label: string, step: StepKey): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${selectors[step][label]}-label`, label);
    }

    async checkHeader(header: string, step: StepKey): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${selectors[step][header]}-heading`, header);
    }

    async checkSubHeader(subHeader: string, step: StepKey): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${selectors[step][subHeader]}-sub-heading`, subHeader);
    }

    async checkDescription(description: string, step?: StepKey): Promise<void> {
        step && await PlaywrightActions.checkHeaderByTestId(this.page, `${selectors[step][description]}-description`, description);
    }

    async checkPrompt(prompt: string, step?: StepKey): Promise<void> {
        step && await PlaywrightActions.checkHeaderByTestId(this.page, `${selectors[step][prompt]}-prompt`, prompt);
    }

    async checkNote(note: string, step: StepKey): Promise<void> {
        await PlaywrightActions.checkHeaderByTestId(this.page, `${selectors[step][note]}-note`, note);
    }

    async checkTile(step: StepKey): Promise<void> {
        await expect(this.page.getByTestId(`${selectors[step]["macOS"]}-selected`)).toBeVisible();
        expect(await this.page.getByTestId(`${selectors[step]["macOS"]}-label`).textContent()).toBe("macOS");
        await expect(this.page.getByTestId(`${selectors[step]["Windows"]}-selected`)).toBeVisible();
        expect(await this.page.getByTestId(`${selectors[step]["Windows"]}-label`).textContent()).toBe("Windows");
    }
    
    async clickonTile(tile: string, step: StepKey): Promise<void> {
        const adjacentTile = tile == "Windows" ? "macOS" : "Windows";
        await this.page.getByTestId(selectors[step][adjacentTile]).click();
        await expect(this.page.getByTestId(`${selectors[step][tile]}-selected`)).toBeVisible();
        await expect(this.page.getByTestId(`${selectors[step][adjacentTile]}-selected`)).not.toBeVisible();
    }

    async checkVPNOptions(): Promise<void> {
        expect(await this.page.getByTestId("radio-button-label").nth(0).textContent()).toBe("No");
        expect(await this.page.getByTestId("radio-button-label").nth(1).textContent()).toBe("Yes");
        const no = this.page.getByTestId("radio-button-input").nth(0);
        const yes = this.page.getByTestId("radio-button-input").nth(1);
        await no.click();
        await expect(no).toBeChecked();
        await expect(yes).not.toBeChecked();
        await yes.click();
        await expect(yes).toBeChecked();
        await expect(no).not.toBeChecked();    
    }

    async checkVPNConfig(step: StepKey): Promise<void> {
        expect(await this.page.getByTestId(selectors[step]["VPN Input"]).getAttribute('placeholder')).toBe(selectors[step]["VPN Placeholder"]);
        await this.page.getByTestId(selectors[step]["VPN Input"]).click();
        await expect(this.page.getByTestId(selectors[step]["Add"])).toBeDisabled();
        await this.page.getByTestId(selectors[step]["VPN Input"]).fill(inputs["VPN Input"]);
        await expect(this.page.getByTestId(selectors[step]["Add"])).toBeEnabled();
        await this.page.getByTestId(selectors[step]["Add"]).click();
        await expect(this.page.getByTestId(selectors[step]["Search Bar"])).toBeVisible();
        await expect(this.page.getByTestId(`${selectors[step]["10.0.0.1"]}-container`)).toBeVisible();
        await expect(this.page.getByTestId(`${selectors[step]["vpn.acme.com"]}-container`)).toBeVisible();
        await expect(this.page.getByTestId(`${selectors[step]["2001:0db8:85a3:0000:0000:8a2e:0370:7334"]}-container`)).toBeVisible();
        await this.page.getByTestId(`${selectors[step]["Search Bar"]}-input`).fill("10.0.0.1");
        expect(await this.page.getByTestId(`${selectors[step]["10.0.0.1"]}-label`).textContent()).toBe("10.0.0.1");
        await this.page.getByTestId(`${selectors[step]["10.0.0.1"]}-delete-icon`).click();
        await this.page.getByTestId(`${selectors[step]["Search Bar"]}-input`).clear();
        await expect(this.page.getByTestId(`${selectors[step]["10.0.0.1"]}-container`)).toBeVisible();
        await expect(this.page.getByTestId(`${selectors[step]["vpn.acme.com"]}-container`)).toBeVisible();
        await expect(this.page.getByTestId(`${selectors[step]["2001:0db8:85a3:0000:0000:8a2e:0370:7334"]}-container`)).not.toBeVisible();
        await this.page.getByTestId(selectors[step]["Remove All"]).click();
        await expect(this.page.getByTestId(selectors[step]["Search Bar"])).not.toBeVisible();
        await expect(this.page.getByTestId(`${selectors[step]["10.0.0.1"]}-container`)).not.toBeVisible();
        await this.page.getByTestId(selectors[step]["VPN Input"]).fill(inputs["VPN Input"]);
        await this.page.getByTestId(selectors[step]["Add"]).click();
    }

    async checkTableWithSearchbar(tableName: string, step?:StepKey): Promise<void> {
        let hover: boolean= false;
        await PlaywrightActions.checkTableWithSearchbar(this.page, this.tableSelectors[tableName], this.tableRows[tableName], hover);
    }

    async clickOnUserOnTable(step: StepKey): Promise<void> {
        await this.page.getByTestId(selectors[step]["Checkbox"]).nth(2).click();
    }

    async checkToggle(step: StepKey): Promise<void> {
        await expect(this.page.getByTestId(selectors[step]["Toggle"])).toBeEnabled();
        await this.page.getByTestId(selectors[step]["Toggle"]).click();
        await expect(this.page.getByTestId(selectors[step]["Toggle"])).toBeDisabled();
    }

    async clickonFooterButton(button: string, step: StepKey): Promise<void> {
        await this.page.getByTestId(selectors[step][button]).click();
    }

    async checkFilter(step?: StepKey): Promise<void> {
        step && await PlaywrightActions.checkOnboardingFilters(this.page, selectors[step]["Filters"], this.filters);
    }
    
}

export default OnboardingSecureTraffic;
