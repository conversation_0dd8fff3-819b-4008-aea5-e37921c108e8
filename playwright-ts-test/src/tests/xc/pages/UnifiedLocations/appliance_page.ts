import { expect, Page } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import { config } from 'dotenv';
config();
class Appliance {
    menuIds: { [key: string]: string };
    subMenuIds: { [key: string]: string };
    tabs: { [key: string]: string };
    data: { [key: string]: string };
    verticalMenuIds: { [key: string]: string };
    fields: {
        searchTab: string;
        applianceName: string;
        location: string;
        applianceGroup: string;
        type: string;
        operationalStatus: string;
        provisioningStatus:string;
        mgtIpAddress: string;
    };
    constructor() {
        this.menuIds = {
            Infrastructure: "nav-pills-tab-3",
        };
        this.subMenuIds = {
             Connectors: "mm-tabs-tab-3",
        }
        this.tabs = {
            Edge: "location-table-segment-control-1",
            Cloud: "location-table-segment-control-2",
        };
        this.verticalMenuIds = {
            "Edge": "mega-menu-tabs-vertical-tab-1",
            "Cloud": "mega-menu-tabs-vertical-tab-2",
        };
        this.fields = {
            searchTab: "appliance-list-search-bar-input",
            applianceName: "z-data-table-row-appliance-list-name-0",
            location: "z-data-table-row-appliance-list-location-1",
            applianceGroup: "z-data-table-row-appliance-list-applianceGroup-2",
            type: "z-data-table-row-appliance-list-type-3",
            operationalStatus: "z-data-table-row-appliance-list-operational-status-4",
            provisioningStatus: "z-data-table-row-appliance-list-provisioning-status-5",
            mgtIpAddress: "z-data-table-row-appliance-list-ip-6"
        };
        this.data = {
            "Appliance Details": "appliance-details-item-0",
        };
    }
       async connectors(page: Page): Promise<void> {
            await page.waitForTimeout(6000);
            await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Infrastructure'], 'Infrastructure');
            await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Connectors"], "Connectors");
            await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Edge"], "Edge");
            await PlaywrightActions.checkPageRenderWithTheme(page, "Appliances", "mega-menu-columns-group-0-link-0", "//div[@data-testid='appliance-list-button-group']");
        }

        async searchAppliance(page: Page, s: string): Promise<void> {
            await page.waitForTimeout(4000);
            await page.getByTestId(this.fields.searchTab).click();
            await page.getByTestId(this.fields.searchTab).fill(s);
            await page.waitForTimeout(4000);
            await page.locator(`//div[@data-testid='${this.fields.applianceName}']/..//span[contains(text(),'${s}')]`).isVisible();
        }

        async verifyAppliancesData(page: Page): Promise<void> {
            const name = await page.locator(`//div[@data-testid='${this.fields.applianceName}']/..//span[contains(text(),'LR192306000255')]`);
            expect(name).toHaveText('LR192306000255');
            const type = await page.locator(`//div[@data-testid='${this.fields.type}']/..//span[contains(text(),'ZT600')]`);
            expect(type).toHaveText('ZT600');
            const connectionTypes = await page.getByTestId(this.fields.applianceGroup);
            expect(connectionTypes).toHaveText('Group_1736500260449_1');
            const location = await page.getByTestId(this.fields.location);
            expect(location).toHaveText('Location_1736500258722_1');
            const operationalStatus = await page.getByTestId(this.fields.operationalStatus);
            expect(operationalStatus).toHaveText('Inactive');
            const provisioningStatus = await page.getByTestId(this.fields.provisioningStatus);
            expect(provisioningStatus).toHaveText('Deployed');
            const mgtIpAddress = await page.getByTestId(this.fields.mgtIpAddress);
            expect(mgtIpAddress).toHaveText('0.0.0.0/0');
        }

        async verifyVMData(page: Page): Promise<void> {
            const name = await page.locator(`//div[@data-testid='${this.fields.applianceName}']/..//span[contains(text(),'BC_Test_Automation')]`);
            expect(name).toHaveText('BC_Test_Automation');
            const type = await page.locator(`//div[@data-testid='${this.fields.type}']/..//span[contains(text(),'VMware ESXi')]`);
            expect(type).toHaveText('VMware ESXi');
        }

        async verifyApplianceDetails(page: Page, name: string): Promise<void> {
            const applianceName = await page.getByText(name);
            expect(applianceName).not.toContainText('--');
        }
    
    }
export default new Appliance();