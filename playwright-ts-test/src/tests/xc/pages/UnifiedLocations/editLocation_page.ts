import { expect, Page } from "@playwright/test";

class EditLocation {
  menuIds: { [key: string]: string };
  subMenuIds: { [key: string]: string };
  tabs: { [key: string]: string };
  data: { [key: string]: string };
  locationGroups: { [key: string]: string };
  fields: {
    searchTab: string;
    tableData: string;
    connectionTypes: string;
    sublocations: string;
  };
  drawerData: { [key: string]: string };

  constructor() {
    this.subMenuIds = {
      Locations: "mm-tabs-tab-2",
    };
    this.menuIds = {
      Infrastructure: "nav-pills-tab-3",
    };

    this.tabs = {
      Edge: "location-table-segment-control-1",
      Cloud: "location-table-segment-control-2",
    };

    this.fields = {
      searchTab: "location-table-search-bar-input",
      tableData: "unified-locations-tooltip",
      connectionTypes: "label-badge",
      sublocations: "z-data-table-row-location-list-subLocationCount-4",
    };

    this.data = {
      America: "location-overview",
      "IPsec/GRE": "unified-locations-ipsec-gre",
      Sublocations: "sub-location",
    };

    this.locationGroups = {
      "Exclude from Manual Location Groups":
        "drawer-checkbox-exclude-manual-location",
      "Exclude from Dynamic Location Groups":
        "drawer-checkbox-exclude-dynamic-location",
    };

    this.drawerData = {
      "Connection Options": "connection-options-drawer-footer-save",
      "Location": "location-drawer-footer-save"
    }
  }

  async clickOnTab(page: Page, tabName: string): Promise<void> {
    await page.waitForSelector('[data-testid="vertical-tabs"]');
    const elements = page.locator('[data-testid="vertical-tabs-tab"]');
    const count = await elements.count();

    for (let i = 0; i < count; i++) {
      const element = elements.nth(i);
      const rawText = await element.textContent();
      const text = rawText?.trim();
      if (text === tabName) {
        await element.click();
      }
    }
  }

  async preAuthEnableToggle(page: Page): Promise<void> {
    await page.getByTestId('location-connection-options-edit-button').click();
    await page.waitForTimeout(3000);

    await page.waitForSelector("input#caution-warning");
    await page.locator("input#caution-warning").click();

    await page.waitForSelector("input#aup-warning");
    await page.locator("input#aup-warning").click();

    await page.getByTestId("form-input-aup-frequency").click();
    await page.getByTestId("form-input-aup-frequency").fill("20");

    await page.waitForSelector("input#internet-access");
    await page.locator("input#internet-access").click();

    await page.waitForSelector("input#ssl-inspection");
    await page.waitForTimeout(4000);
    await page.locator("input#ssl-inspection").click();

    await page.waitForSelector('label[for="enforce-firewall-control"]');
    const toggleFirewall = page
      .locator('label[for="enforce-firewall-control"]')
      .filter({ hasText: "Enforce Firewall Control" });
    await toggleFirewall.click();
    await page.waitForTimeout(1000);
    await page.waitForSelector("input#enable-ips-control");
    await page.locator("input#enable-ips-control").click();

    await page.waitForSelector('label[for="xff-forwarding"]');
    const toggleXff = page
      .locator('label[for="xff-forwarding"]')
      .filter({ hasText: "Enable XFF Forwarding" });
    await toggleXff.click();

    await page.waitForSelector('label[for="bandwidth-control"]');
    const toggleBandwidth = page
      .locator('label[for="bandwidth-control"]')
      .filter({ hasText: "Enforce Bandwidth Control" });
    await toggleBandwidth.click();
    await page.getByTestId("form-input-download-speed").click();
    await page.getByTestId("form-input-download-speed").fill("5");

    await page.getByTestId("form-input-upload-speed").click();
    await page.getByTestId("form-input-upload-speed").fill("5");

  }

  async postAuthEnableToggle(page: Page): Promise<void> {
    await page.getByTestId('location-connection-options-edit-button').click();
    await page.waitForTimeout(3000);
    await page.waitForSelector('label[for="authentication"]');
    const toggle = page
      .locator('label[for="authentication"]')
      .filter({ hasText: "Enforce Authentication" });
    await toggle.click();

    await page.waitForTimeout(1000);
    const kerberosAuth = page.locator("//div[@data-testid='drawer-checkbox-kerberosAuthentication']/..//span[contains(text(),'Enable Kerberos Authentication')]");
    const basicAuth = page.locator("//div[@data-testid='drawer-checkbox-basicAuthentication']/..//span[contains(text(),'Enable Basic Authentication')]");
    const digestAuth = page.locator("//div[@data-testid='drawer-checkbox-digestAuthentication']/..//span[contains(text(),'Enable Digest Authentication')]");

    if(await kerberosAuth.isVisible()){
      await page.locator("input#kerberosAuthentication").click();
    }

    if(await basicAuth.isVisible()){
      await page.locator("input#basicAuthentication").click();
    }

    if(await digestAuth.isVisible()){
    await page.locator("input#digestAuthentication").click();
    }

    await page.waitForSelector("input#ipSurrogate");
    await page.locator("input#ipSurrogate").click();
    await page.waitForTimeout(1000);

    await page.getByTestId("form-input-unmapUsers").click();
    await page.getByTestId("form-input-unmapUsers").fill("10");

    await page.waitForSelector("input#useIpSurrogate");
    await page.locator("input#useIpSurrogate").click();

    await page.getByTestId("form-input-revalidation").click();
    await page.getByTestId("form-input-revalidation").fill("5");
  }

  async clickSaveButton(page: Page, type: string): Promise<void> {
    await page.getByTestId(`${this.drawerData[type]}`).click();
    await page.waitForTimeout(10000);
  }

  async preAuthDetailsVerification(page: Page): Promise<void> {
    await page.waitForSelector(
      '[data-testid="location-connection-options-details-0"]',
    );
    const cautionWarningState = await page
      .locator('[data-testid="location-connection-options-0-1"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const aupWarningState = await page
      .locator('[data-testid="location-connection-options-0-2"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const firewallState = await page
      .locator('[data-testid="location-connection-options-1-0"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const ipsControllState = await page
      .locator('[data-testid="location-connection-options-1-1"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const xffState = await page
      .locator('[data-testid="location-connection-options-2-0"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const bandwidthControlState = await page
      .locator('[data-testid="location-connection-options-3-0"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();

    const isCautionEnabled = cautionWarningState.includes("Enabled");
    const isAupEnabled = aupWarningState.startsWith("Enabled");
    const isFirewallEnabled = firewallState.includes("Enabled");
    const isIpsControlEnabled = ipsControllState.includes("Enabled");
    const isXffEnabled = xffState.includes("Enabled");
    const isBandwidthEnabled = bandwidthControlState.includes("Disabled");

    const isFieldsEnabled =
      isCautionEnabled ||
      isAupEnabled ||
      isFirewallEnabled ||
      isIpsControlEnabled ||
      isXffEnabled ||
      isBandwidthEnabled;

    if (!isFieldsEnabled) {
      throw new Error("Fields is not Enabled");
    }

    if (aupWarningState?.trim() === "Enabled: 30 days") {
      throw new Error("AUP Warning is not Enabled for 30 days");
    }
  }

  async postAuthDetailsVerification(page: Page): Promise<void> {
    await page.waitForSelector(
      '[data-testid="location-connection-options-details-0"]',
    );
    const authenticationState = await page
      .locator('[data-testid="location-connection-options-0-0"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const ipSurrogate = await page
      .locator('[data-testid="location-connection-options-0-2"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();

    const isAuthenticationEnabled = authenticationState.includes("Enabled");
    const isIpSurrogateEnabled = ipSurrogate.startsWith("Enabled");

    if (!isAuthenticationEnabled) {
      throw new Error("Authentication is not Enabled");
    }

    if (!isIpSurrogateEnabled) {
      throw new Error("ipSurrogate is not Enabled");
    }

    const authenticationTypes = await page.getByText("Authentication Types");
    expect(authenticationTypes).not.toContainText('--');
  }

  async verifyConnectionOptionDefaultState(page: Page): Promise<void> {
    const authenticationState = await page
      .locator('[data-testid="location-connection-options-0-0"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const cautionWarningState = await page
      .locator('[data-testid="location-connection-options-0-1"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const aupWarningState = await page
      .locator('[data-testid="location-connection-options-0-2"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const firewallState = await page
      .locator('[data-testid="location-connection-options-1-0"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const ipsControlState = await page
      .locator('[data-testid="location-connection-options-1-1"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const xffState = await page
      .locator('[data-testid="location-connection-options-2-0"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const bandwidthControlState = await page
      .locator('[data-testid="location-connection-options-3-0"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();

    const isAuthDisabled = authenticationState.includes("Disabled");
    const isCautionDisabled = cautionWarningState.includes("Disabled");
    const isAupDisabled = aupWarningState.startsWith("Disabled");
    const isFirewallDisabled = firewallState.startsWith("Disabled");
    const isIpsControlDisabled = ipsControlState.startsWith("Disabled");
    const isXffDisabled = xffState.startsWith("Disabled");
    const isBandwidthDisabled = bandwidthControlState.startsWith("Disabled");

    const isFieldsDisabled =
      isCautionDisabled ||
      isAuthDisabled ||
      isAupDisabled ||
      isFirewallDisabled ||
      isIpsControlDisabled ||
      isXffDisabled ||
      isBandwidthDisabled;

    if (!isFieldsDisabled) {
      throw new Error("Fields is not Disabled");
    }

  }

  async deleteFromOverview(page: Page): Promise<void>{
        await page.getByTestId('location-overview-delete').click();
        await page.getByText('Confirm Delete').isVisible();
        await page.locator("//span[text()='Delete']").click();
        await page.locator("//div[text()='Location has been successfully removed from the system.']").isVisible({timeout: 7000});

    }

    async fillMandatoryValuesAgain(page: Page, name: string): Promise<void> {
        await page.getByTestId("form-input-name").click();
        await page.getByTestId("form-input-name").fill(name);
        await page.getByTestId("form-input-cityState").click();
        await page.getByTestId("form-input-cityState").fill("Toronto");
        await page.getByTestId("drawer-select-country").click();
        await page.getByTestId("basic-search-input").click();
        await page.getByTestId("basic-search-input").fill("Canada");
        await page
          .getByTestId(
            "zselect-container-searchable-list-filtered-z-list-list-item-item-0",
          )
          .click();
        await page.getByTestId("drawer-select-timezone").click();
        await page.getByTestId("basic-search-input").click();
        await page.getByTestId("basic-search-input").fill("GMT-04:00");
        await page.getByText("GMT-04:00").click();
        await page.getByTestId("input-list-textarea-description").click();
        await page.getByTestId("input-list-textarea-description").fill("Testing Again");
      }

      async clickSyncLocation(page: Page): Promise<void>{
        const syncLocation = await page.getByTestId('add-button-group-sync-location');
        expect(syncLocation).toHaveText('Sync Locations');
        await page.getByTestId('add-button-group-sync-location').click();
        await page.getByTestId('add-button-group-sync-location').isDisabled();
    }

    async verifyNoResultFound(page: Page): Promise<void>{
        await page.waitForTimeout(6000);
        const noResultFound = await page.locator("//h5[text()='No Results Found']");
        expect(noResultFound).toHaveText('No Results Found');
  }

    async selectVPNAndProxyPort(page: Page, trafficType: string): Promise<void>{
        await page.getByTestId('drawer-select-traffic-type').click();
        await page.getByText(trafficType).click();
        await page.getByTestId('drawer-select-staticIPAddresses').click();
        await page.getByTestId('zselect-container-searchable-list-z-list-list-item-item-0').click();
        await page.getByTestId('drawer-select-staticIPAddresses').click();
    }

    async verifyIPSecDetails(page: Page): Promise<void>{
        await page.waitForTimeout(7000);
        const vpnCredentials = await page.getByText('VPN Credentials');
        expect(vpnCredentials).not.toContainText('--');

        const virtualZens = await page.getByText('Virtual Service Edges');
        expect(virtualZens).not.toContainText('--');

        const virtualZenClusters = await page.getByText('Virtual Service Edge Clusters');
        expect(virtualZenClusters).not.toContainText('--');
    }

    async iotDiscoveryToggle(page: Page): Promise<void> {
          await page.getByTestId('location-connection-options-edit-button').click();
          await page.waitForTimeout(3000);
        if (await page.isVisible('label[for="iot-discovery"]')) {
            const iotDiscovery = page
            .locator('label[for="iot-discovery"]')
            .filter({ hasText: 'Enable IoT Discovery' });
      
            if (await iotDiscovery.isVisible()) {
                await iotDiscovery.click();
                await page.waitForTimeout(1000);
                await page.waitForSelector('input#iot-policy');
                await page.locator('input#iot-policy').click();
            } else {
                throw new Error('Enable IoT Discovery label is not visible.');
            }
        } else {
            throw new Error('IoT Discovery is unavailable for this tenant');
        }
    }
    
    async iotVerification(page: Page): Promise<void> {
    await page.waitForSelector(
      '[data-testid="location-connection-options-details-1"]',
    );
    const iotDiscovery = await page
      .locator('[data-testid="location-connection-options-1-0"]')
      .locator(".text-semantic-content-base-primary")
      .innerText();
    const isIotDiscoveryEnabled = iotDiscovery.includes("Enabled");

    if (!isIotDiscoveryEnabled) {
      throw new Error("Iot Discovery is not Enabled");
    }

  }


}

export default new EditLocation();

