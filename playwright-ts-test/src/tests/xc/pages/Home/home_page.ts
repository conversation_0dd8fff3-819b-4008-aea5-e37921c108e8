import { Page, expect } from "@playwright/test";
import CommonFunctions from "../../../../../resources/utils/CommonFunctions";
import Base from '../../../../../lib/pom/Base/Base';
import { 
    FILTERS as filterData, 
    RESOURCES_DATA as sectionData, 
    TILE_DATA as tileData, 
    TEST_IDS as selectors, 
    RESIZE_TEST_IDS as resizeSelectors,
    NAVIGATION, 
    BANNER_DATA as bannerData 
} from '../../../../../resources/constants/pages/home/<USER>';
import { TEST_IDS as globalNav} from '../../../../../resources/constants/pages/gobalNavigation/globalNav';
import { HOME_ROUTES as routes } from '../../../../../resources/constants/routes';
import { SectionItem } from "../../../../../resources/types/types";

class Home extends Base {

    constructor(page: Page) {
        super(page, {
            pageName: NAVIGATION.pageName,
            urlSuffix: NAVIGATION.url,
        });
    }

    async getWidthClass(testId: string): Promise<string> {
        const classAttr = await this.page.locator(testId).getAttribute("class");
        if (!classAttr) return "unknown";
        if (classAttr.includes("w-full")) return "w-full";
        if (classAttr.includes("w-1/2")) return "w-1/2";
        return "unknown";
    }

    async verifyResizeTwice(testId: string, resizeButtonTestId: string): Promise<void> {
        let initial = await this.getWidthClass(testId);
        expect(["w-full", "w-1/2"]).toContain(initial);
        for (let i = 0; i < 2; i++) {
            await this.page.locator(testId).hover();
            await this.page.getByTestId(resizeButtonTestId).click();
            await this.page.waitForTimeout(1000);
            const final = await this.getWidthClass(testId);
            expect(["w-full", "w-1/2"]).toContain(final);
            expect(final).not.toBe(initial);
            initial = final;
        }
    }

    async verifyRemove(testId: string, removeButtonTestId: string): Promise<void> {
        await this.page.locator(testId).hover();
        await this.page.getByTestId(removeButtonTestId).click();
        await expect(this.page.locator(testId)).not.toBeVisible();
    }

    async homeAnalyticsFooter(): Promise<void> {
        for (const item of tileData)
        {
            await expect(this.page.getByTestId(`${item.cardname}-card-container`)).toBeVisible();
            expect(await this.page.getByTestId(`${item.cardname}-card-title`).textContent()).toBe(`Analytics | ${item.title}`);
            await this.page.getByTestId(`${item.cardname}-footer-navLink-z-button-link`).click();
            await this.page.waitForTimeout(10000);
            await expect(this.page).toHaveURL(`${this.baseUrl}${item.footerRoute}`);
            await this.page.getByTestId("navigation-logo").click();
            await this.page.waitForTimeout(5000);
        }
    }

    async reset(): Promise<void> {
        await this.page.waitForTimeout(8000);
        const reset = await this.page.getByTestId(selectors["Reset"]).isVisible();
        if(!reset)
        {
            await this.page.getByTestId(selectors["Customize"]).click();
        }
        await this.page.getByTestId(selectors["Reset"]).click();
        await this.page.getByTestId(selectors["Save"]).click();
    }

    async checkResize(): Promise<void> {
        await this.page.getByTestId(selectors["Customize"]).click();
        for (const item of Object.keys(resizeSelectors))
        {
            await this.verifyResizeTwice(`//div[@data-item-id="${resizeSelectors[item]}"]`, `${resizeSelectors[item]}-resize-trash-resize`);
        }
        await this.page.getByTestId(selectors["Cancel"]).click();
    }

    async checkRemove(): Promise<void> {
        await this.page.getByTestId(selectors["Customize"]).click();
        for (const item of Object.keys(resizeSelectors))
        {
            await this.verifyRemove(`//div[@data-item-id="${resizeSelectors[item]}"]`, `${resizeSelectors[item]}-resize-trash-remove`);
        }
        await this.page.getByTestId(selectors["Cancel"]).click();
    }

    async checkBannerLoad(): Promise<void> {
        await this.page.waitForTimeout(1000);
        const bannerLength = await this.page.getByTestId(`${selectors["Banner"]}-container`).count();
        expect(bannerLength).toBeGreaterThan(0);
    }

    async checkBanner(): Promise<void> {
        const bannerLength = await this.page.getByTestId(`${selectors["Banner"]}-container`).count();
        for (let i=0; i < bannerLength; i++) {
            const isLastBanner = i === bannerLength - 1;

            await expect(this.page.getByTestId(`${selectors["Banner"]}-container`).nth(i)).toBeVisible();
            await this.page.getByTestId(`${selectors["Banner"]}-container`).nth(i).hover();
            if (bannerLength > 1) {
                await expect(this.page.getByTestId(`carousel-${selectors["Banner"]}-show-side-${isLastBanner ? "left" : "right"}`)).toBeVisible();
            }
            expect(await this.page.getByTestId(`${selectors["Banner"]}-title`).nth(i).textContent()).toBe(bannerData.title);
            expect(await this.page.getByTestId(`${selectors["Banner"]}-description`).nth(i).textContent()).toBe(bannerData.description);
            expect(await this.page.getByTestId(`${selectors["Banner"]}-start-tour`).nth(i).textContent()).toBe(bannerData.key);
            if (bannerLength > 1) {
                await this.page.getByTestId(`carousel-${selectors["Banner"]}-show-side--${isLastBanner ? "left" : "right"}`).click();
            }
        }
    }

    async checkBannerUpdate(): Promise<void> {
        const bannerLength = await this.page.getByTestId(`${selectors["Banner"]}-container`).count();
        if (bannerLength > 1) {
            for (let i=0; i < bannerLength; i++) {
                const isLastBanner = i === bannerLength - 1;

                await expect(this.page.getByTestId(`${selectors["Banner"]}-container`).nth(i)).toBeVisible();
                await this.page.getByTestId(`${selectors["Banner"]}-container`).nth(i).hover();
                await expect(this.page.getByTestId(`carousel-${selectors["Banner"]}-show-side-${isLastBanner ? "left" : "right"}`)).toBeVisible();
                await this.page.getByTestId(`${selectors["Analytics"]}-select-analytics-title`).click();
                await this.page.waitForTimeout(5000);
            }
        }
    }
    
    async checkBackButton(): Promise<void> {
        await this.page.getByTestId(globalNav["Analytics"]).click();
        await this.page.waitForTimeout(10000);
        await this.page.goBack();
        await expect(this.page).toHaveURL(this.url);
    }

    async checkCancelButton(): Promise<void> {
        await expect(this.page.getByTestId(`${selectors["Analytics"]}-card-container`)).toBeVisible();
        await this.page.getByTestId(selectors["Customize"]).click();
        await this.page.getByTestId(`lm_analytics_networking-card-container`).click();
        await this.page.getByTestId(selectors["Cancel"]).click();
        await expect(this.page.getByTestId(`${selectors["Analytics"]}-card-container`)).toBeVisible();
        await expect(this.page.getByTestId(`${selectors["Analytics"]}-networking-card-container`)).not.toBeVisible();
    }

    async checkCustomize(): Promise<void> {
        await expect(this.page.getByTestId(`${selectors["Analytics"]}-card-container`)).toBeVisible();
        await this.page.getByTestId(selectors["Customize"]).click();
        await this.page.getByTestId(`lm_analytics_networking-card-container`).click();
        await this.page.getByTestId(selectors["Save"]).click();
        await expect(this.page.getByTestId(`${selectors["Analytics"]}-card-container`)).not.toBeVisible();
        await expect(this.page.getByTestId(`${selectors["Analytics"]}-networking-card-container`)).toBeVisible();
        await this.page.waitForTimeout(2000);
        await this.page.reload();
        await this.page.waitForTimeout(5000);
        await expect(this.page.getByTestId(`${selectors["Analytics"]}-card-container`)).not.toBeVisible();
        await expect(this.page.getByTestId(`${selectors["Analytics"]}-networking-card-container`)).toBeVisible();
    }
    
    async checkAnalyticsCards(): Promise<void> {
        await this.page.waitForTimeout(5000);
        await this.page.getByTestId(selectors["Customize"]).click();
        expect(await this.page.getByTestId(`${selectors["Analytics"]}-select-analytics-title`).textContent()).toBe("What analytics are you most interested in?");
        expect(await this.page.getByTestId(`${selectors["Analytics"]}-select-analytics-subtitle`).textContent()).toBe("Select a category to view on your personalized homepage.");
        for (const item of tileData)
        {
            await expect(this.page.getByTestId(`${item.key}-card-container`)).toBeVisible();
            expect(await this.page.getByTestId(`${item.key}-tile-title`).textContent()).toBe(item.title);
            expect(await this.page.getByTestId(`${item.key}-tile-description`).textContent()).toBe(item.description);
            await this.page.getByTestId(`${item.key}-card-container`).click();
            await this.page.waitForTimeout(2000);
            await this.page.locator(`//div[@data-id="hover-add-line-row-1"]`).hover();
            const box = await this.page.locator('[data-testid="add-popover"]').boundingBox();
            if (box) {
                await this.page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
                await this.page.waitForTimeout(1000);
                await this.page.mouse.down();
                await this.page.mouse.up();
            }
            await expect(this.page.getByTestId("add-popover-list")).toBeVisible();
            await this.page.getByTestId("item-buttonANALYTICS").click();
        }
        await this.page.getByTestId(selectors["Save"]).click();
        await this.page.waitForTimeout(10000);
        await this.homeAnalyticsFooter();
    }

    async verifyRBACAnalyticsCard(): Promise<void> {
        await this.page.waitForTimeout(5000);
        expect(await this.page.getByTestId(`${selectors["Analytics"]}-select-analytics-title`).textContent()).toBe("What analytics are you most interested in?");
        expect(await this.page.getByTestId(`${selectors["Analytics"]}-select-analytics-subtitle`).textContent()).toBe("Select a category to view on your personalized homepage.");
    }

    async verifyRBACAnalyticsCardNotVisible(): Promise<void> {
        await this.page.waitForTimeout(5000);
        await expect(this.page.getByTestId(`${selectors["Analytics"]}-card-container`)).not.toBeVisible();
    }
    
    async verifyRBACAnalyticsCardTiles(data: string[]): Promise<void> {
        await this.page.waitForTimeout(5000);
        for (const item of tileData)
        {
            if(data.includes(item.title))
            {
                await expect(this.page.getByTestId(`${item.key}-card-container`)).toBeVisible();
                expect(await this.page.getByTestId(`${item.key}-tile-title`).textContent()).toBe(item.title);
                expect(await this.page.getByTestId(`${item.key}-tile-description`).textContent()).toBe(item.description);
            }
            else
            {
                await expect(this.page.getByTestId(`${item.key}-card-container`)).not.toBeVisible();
            }
        }
    }
            
    async checkRecent(): Promise<void> {
        await expect(this.page.getByTestId(`${selectors["Recently Viewed"]}-card-container`)).toBeVisible();
        expect(await this.page.getByTestId(`${selectors["Recently Viewed"]}-card-title`).textContent()).toBe("Recently Viewed");
        const recentCount = await this.page.locator("//article[@data-testid='homepage-recently-viewed-card-container']//li").count();
        if(recentCount >= 5)
        {
            expect(await this.page.getByTestId(`${selectors["Recently Viewed"]}-footer-button`).textContent()).toBe("Load More");
        }
    }
            
    async checkNews(): Promise<void> {
        await expect(this.page.getByTestId(`${selectors["News"]}-card-container`)).toBeVisible();
        expect(await this.page.getByTestId(`${selectors["News"]}-card-title`).textContent()).toBe("News");
        expect(await this.page.getByTestId(`${selectors["News"]}-card-footer-link`).textContent()).toBe("View More");
        await CommonFunctions.checkFilters(this.page, filterData.News);
        for(let idx=0; idx<3; idx++)
        {
            await this.page.getByTestId(`news-list-item-${idx}`).click();
            await expect(this.page.getByTestId("news-feed-modal-fs-modal")).toBeVisible();
            await this.page.getByTestId(`news-feed-back-button`).click();
        }
        const viewMore = await this.page.getByTestId(`${selectors["News"]}-card-footer-link`);
        const [newPage] = await Promise.all([ 
            this.page.waitForEvent("popup"), 
            viewMore.click(),
        ]);
        await expect(newPage).toHaveURL(routes["News"]);
        await this.page.bringToFront();
    }
            
    async checkLearnAboutProducts(): Promise<void> {
        await expect(this.page.getByTestId(`${selectors["Learn About Products"]}-card-container`)).toBeVisible();
        expect(await this.page.getByTestId(`${selectors["Learn About Products"]}-card-title`).textContent()).toBe("Learn About Products");
        await CommonFunctions.checkFilters(this.page, filterData.LearnAboutProducts);
    }

    async checkLearnAboutProductsLoad(): Promise<void> {
        await this.page.waitForTimeout(2000);
        expect(await this.page.getByTestId(`${selectors["Learn About Products"]}-card-container`).count()).toBeGreaterThan(0);
        expect(await this.page.locator(`[data-testid^="carousel-homepage-videos"] img`).count()).toBeGreaterThan(0);
    }
            
    async checkResources(): Promise<void> {
        await expect(this.page.getByTestId(`${selectors["Resources"]}-card-container`)).toBeVisible();
        expect(await this.page.getByTestId(`${selectors["Resources"]}-card-title`).textContent()).toBe("Resources");
        await Promise.all(
            sectionData.map(async ( item: SectionItem) => {
              expect(await this.page.getByTestId(`${selectors["Resources"]}-homepage-${item.key}-title`).textContent()).toBe(item.title);
              expect(await this.page.getByTestId(`${selectors["Resources"]}-homepage-${item.key}-description`).textContent()).toBe(item.description);
            })
        );
    }
}
        
export default Home;
        