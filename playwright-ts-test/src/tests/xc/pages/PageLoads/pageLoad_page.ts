import { Page, Response } from "@playwright/test";
import { config } from "dotenv";
import CommonFunctions from "../../../../../resources/utils/CommonFunctions";
import { FailedAPI } from "../../../../../resources/types/types";
config();

class PageLoad {
    page: Page;
    baseUrl : string;
    failedResponses : FailedAPI[];


    constructor(page: Page) {
        this.page = page
        this.baseUrl = process.env.ONE_UI_BASE_URL ?? "";
        this.failedResponses = [];
    }

    async trackResponsesAfterClick(page: Page, clickTime: number): Promise<() =>void> {
        const handler = (response: Response) => {
            const responseTime = Date.now();
            const durationSinceClick = responseTime - clickTime;
            if (response.status() >= 400 || durationSinceClick > 10000) {
                this.failedResponses.push({
                    method: response.request().method(),
                    url: response.url(),
                    status: response.status(),
                    timeTaken: durationSinceClick/1000,
                });
            }
    };

    page.on('response', handler);

    return () => {
        page.off('response', handler);
    }; 
}


    async waitForNetworkIdle(page: Page, idleTime = 500, timeout = 50000): Promise<void> {
    return new Promise((resolve, reject) => {
        let lastTime = Date.now();
        let timeoutId: NodeJS.Timeout;

        const checkIdle = () => {
            if (Date.now() - lastTime >= idleTime) {
                cleanup();
                resolve();
            }
        };

        const onRequest = () => { lastTime = Date.now(); };
        const onResponse = () => { lastTime = Date.now(); };

        const cleanup = () => {
            clearTimeout(timeoutId);
            page.off('request', onRequest);
            page.off('response', onResponse);
        };

        page.on('request', onRequest);
        page.on('response', onResponse);

        timeoutId = setInterval(checkIdle, 15000);

        setTimeout(() => {
            cleanup();
            reject(new Error(`Timed out waiting for network idle after ${timeout} ms`));
        }, timeout);
    });
    }

    async closePopup(page: Page): Promise<void> {
        const popup = await page.locator("(//i[@class='fas fa-times notification-close-icon'])").isVisible();
        if (popup) {
          await page.locator("(//i[@class='fas fa-times notification-close-icon'])").click();
          await page.mouse.move(50, 50);
          await page.waitForTimeout(2000);
        }
        const zbanner = await page.getByTestId("dismiss-banner-btn").isVisible();
        if (zbanner) {
          await page.getByTestId("dismiss-banner-btn").click();
          await page.mouse.move(50, 50);
          await page.waitForTimeout(2000);
        }
        const errorCard = await page.locator(`//div[@class="notification error"]`).isVisible();
        if(errorCard)
        {
          await page.locator(`//span[@class="notification-close-icon"]`).click();
          await page.mouse.move(50, 50);
          await page.waitForTimeout(2000);
        }
        const maskPopup = await page.locator(`//div[@class="dialog-mask help-dialog-mask"]`).isVisible();
        if(maskPopup)
        {
          await page.locator("(//i[@class='dialog-header-close fas fa-times -js-ok-button'])").click();
          await page.mouse.move(50, 50);
          await page.waitForTimeout(2000);
        }
        const emergencyPopup = await page.locator(`[data-testid="dismiss-alert-btn"]`).isVisible();
        if(emergencyPopup)
        {
          await page.locator(`[data-testid="dismiss-alert-btn"]`).click();
          await page.mouse.move(50, 50);
          await page.waitForTimeout(2000);
        }
    }

    async goto(): Promise<void> {
        await this.page.goto(this.baseUrl);
        await CommonFunctions.waitForPageLoad(this.page, "nav-pills-tab-0");
        await this.page.getByTestId("nav-pills-tab-0").click();
        await this.waitForNetworkIdle(this.page);
        await this.page.waitForTimeout(20000);
    }

    async checkLeftNavAPIFailures(): Promise<void> {
        const menuCount = await this.page.locator("[data-testid='left-nav-collapsible-nav-item']").count();
        // this.page.on("response", this.onResponse);
        for(let count = 0; count < menuCount; count++)
        {
            await this.page.locator("[data-testid='left-nav-collapsible-nav-item']").nth(count).click();
            const mainClickTime = Date.now();
            const cleanupMain = await this.trackResponsesAfterClick(this.page, mainClickTime);
            await this.waitForNetworkIdle(this.page);
            await this.page.waitForTimeout(3000);
            cleanupMain();
            const submenuCount = await this.page.locator("[data-testid='left-nav-collapsible-nav-item']").nth(count).locator("[data-testid='left-nav-collapsible-nav-item-sub-menu']").count();
            for(let subCount = 0; subCount < submenuCount; subCount++)
            {
                await this.page.locator("[data-testid='left-nav-collapsible-nav-item']").nth(count).locator("[data-testid='left-nav-collapsible-nav-item-sub-menu']").nth(subCount).click();
                const mainClickTime = Date.now();
                const cleanupSub = await this.trackResponsesAfterClick(this.page, mainClickTime);
                await this.waitForNetworkIdle(this.page);
                await this.page.waitForTimeout(3000);
                cleanupSub();
            }
        }
        console.log(this.failedResponses);
    }

    async checkTopNavAPIFailures(): Promise<void> {
        // this.page.on("response", this.onResponse);
        for (let tabIndex = 1; ; tabIndex++) {
            const topTab = this.page.locator(`[data-testid="nav-pills-tab-${tabIndex}"]`);
            if (!(await topTab.isVisible())) break;
            await this.page.waitForTimeout(1000);
            await topTab.click();
            await topTab.hover();
            for (let mmTabIndex = 0; ; mmTabIndex++) {
                const mmTab = this.page.locator(`[data-testid="mm-tabs-tab-${mmTabIndex}"]`);
                if (!(await mmTab.isVisible())) break;
                await this.page.waitForTimeout(1000);
                await mmTab.click();
                const isVerticalTab = await this.page.locator(`[data-testid="mega-menu-tabs-vertical-tab-0"]`).isVisible();
                if(isVerticalTab)
                {
                    for (let verticalTabIndex = 0; ; verticalTabIndex++) {
                        const verticalTab = this.page.locator(`[data-testid="mega-menu-tabs-vertical-tab-${verticalTabIndex}"]`);
                        if (!(await verticalTab.isVisible())) break;
                        await this.page.waitForTimeout(1000);
                        await verticalTab.click();
                        for (let groupIndex = 0; ; groupIndex++) {
                            const group = this.page.locator(`[data-testid="mega-menu-columns-group-${groupIndex}"]`);
                            if (!(await group.isVisible())) break;
                            for (let linkIndex = 0; ; linkIndex++) {
                                const link = this.page.locator(`[data-testid="mega-menu-columns-group-${groupIndex}-link-${linkIndex}"]`);
                                if (!(await link.isVisible())) break;
                                await this.page.waitForTimeout(1000);
                                await link.click();
                                const mainClickTime = Date.now();
                                const cleanup = await this.trackResponsesAfterClick(this.page, mainClickTime);
                                await this.waitForNetworkIdle(this.page);
                                await this.closePopup(this.page);
                                await topTab.click();
                                await topTab.hover();
                                cleanup();
                            }
                        }
                    }
                }
                else 
                {
                    for (let groupIndex = 0; ; groupIndex++) {
                        const group = this.page.locator(`[data-testid="mega-menu-columns-group-${groupIndex}"]`);
                        if (!(await group.isVisible())) break;
                        for (let linkIndex = 0; ; linkIndex++) {
                            const link = this.page.locator(`[data-testid="mega-menu-columns-group-${groupIndex}-link-${linkIndex}"]`);
                            if (!(await link.isVisible())) break;
                            await link.click();
                            const mainClickTime = Date.now();
                            const cleanup = await this.trackResponsesAfterClick(this.page, mainClickTime);
                            await this.waitForNetworkIdle(this.page);
                            await this.closePopup(this.page);
                            await topTab.click();
                            await topTab.hover();
                            cleanup();
                        }
                    }
                }
            }
        }   
        console.log(this.failedResponses);
    }
}

export default PageLoad;