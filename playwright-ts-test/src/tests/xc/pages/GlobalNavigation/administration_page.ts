import { expect, Page } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';

class Administration {
    private menuIds: Record<string, string>;
    private subMenuIds: Record<string, string>;
    private verticalMenuIds: Record<string, string>;

    constructor() {
        this.menuIds = { 
            Administration: "nav-pills-tab-1", 
            Policies: "nav-pills-tab-2", 
            Infrastructure: "nav-pills-tab-3", 
            Logs: "nav-pills-tab-4",
        };

        this.subMenuIds = { 
            "Account Management": "mm-tabs-tab-0", 
            "Admin Management": "mm-tabs-tab-1", 
            Identity: "mm-tabs-tab-2", 
            Entitlements: "mm-tabs-tab-3", 
            "API Configuration": "mm-tabs-tab-4", 
            Alerts: "mm-tabs-tab-5", 
            "Backup & Restore": "mm-tabs-tab-6",
        };

        this.verticalMenuIds = { 
            "Administrator Management": "mega-menu-tabs-vertical-tab-0", 
            "Role Based Access Control": "mega-menu-tabs-vertical-tab-1",
            "Audit Logs": "mega-menu-tabs-vertical-tab-2",
            ZIdentity: "mega-menu-tabs-vertical-tab-0",
            "Internet & SaaS": "mega-menu-tabs-vertical-tab-1",
            "Private Access": "mega-menu-tabs-vertical-tab-2",
            OneAPI: "mega-menu-tabs-vertical-tab-0",
            "Legacy API": "mega-menu-tabs-vertical-tab-1",
        };
    }

    async verifyGlobalNavigationTitle(page: Page, s: string): Promise<void> {
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds[s], s);
    }

    async clicksOnTheAccountManagement(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Account Management"], "Account Management");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Organization", "mega-menu-columns-group-0-link-0", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Linked Services", "mega-menu-columns-group-0-link-1", `(//div[@class="typography-header3 page-title"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Private Access Tenant", "mega-menu-columns-group-0-link-2", "(//div[text()='Company'])");
    }

    async clickSubscriptionAndBranding(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Subscriptions", "mega-menu-columns-group-0-link-3", "(//div[@data-testid='unified-subscriptions-list'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Branding", "mega-menu-columns-group-0-link-4", "(//section[text()='Branding'])");
    }

    async clickAdminManagement(page: Page, s: string): Promise<void> {
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Admin Management"], "Admin Management");
        await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds[s], s);
    }

    async clickAdministratorManagementLinks(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "Internet Access Administrators", "mega-menu-columns-group-0-link-0", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Branch and Cloud Connector", "mega-menu-columns-group-0-link-1", "(//span[@class='source-ip-groups'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Sign-On Policies", "mega-menu-columns-group-1-link-0", `(//section[@class="typography-header3 page-title"])`);
    }

    async clickRolesAndEntitlementsLinks(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "Unified User Interface", "mega-menu-columns-group-0-link-0", `(//section[text()="ZIdentity Roles"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Internet & SaaS", "mega-menu-columns-group-0-link-1", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Private Access", "mega-menu-columns-group-0-link-2", "(//div[@id='page-right-controls-wrapper'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Digital Experience", "mega-menu-columns-group-0-link-3", `(//h1[text()="Role Management"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Connectors", "mega-menu-columns-group-0-link-4", "(//h1[text()='Administration Management'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Branch and Cloud Connector", "mega-menu-columns-group-0-link-5", "(//span[text()='Role Management'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Private App Microtenants", "mega-menu-columns-group-1-link-0", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Administrative Entitlements", "mega-menu-columns-group-2-link-0", "(//section[text()='Administrative Entitlements'])");

        // await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        // await PlaywrightActions.checkPageRenderWithTheme(page, "Private Access Exec Insights", "mega-menu-columns-group-2-link-1", "(//span[@data-testid='refresh-view-icon'])");
    }

    async clickAuditLogsLink(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "Unified User Interface", "mega-menu-columns-group-0-link-0", "(//section[text()='Audit Logs'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Internet & SaaS", "mega-menu-columns-group-0-link-1", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Private Access", "mega-menu-columns-group-0-link-2", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Digital Experience", "mega-menu-columns-group-0-link-3", "(//h1[text()='Audit Logs'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Mobile Administration", "mega-menu-columns-group-0-link-4", "(//h1[text()='Audit Logs'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Branch and Cloud", "mega-menu-columns-group-0-link-5", `(//div[@class="page-title"])`);
    }

    async clickIdentity(page: Page, s: string): Promise<void> {
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Identity"], "Identity");
        await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds[s], s);
    }

    async clickZIdentityLinks(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "External Identities", "mega-menu-columns-group-0-link-0", "(//section[text()='External Identities'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await expect(page.getByTestId("mega-menu-columns-group-0-link-1")).toHaveText("ZIdentity Portal");
        const [newPage] = await Promise.all([ 
            page.waitForEvent("popup"), 
            page.getByTestId("mega-menu-columns-group-0-link-1").click(),
        ]);
        await expect(newPage.locator("(//p[@class='title-name'])")).toBeVisible({ timeout: 10000 });
        await page.bringToFront();
        await page.waitForTimeout(2000);
        await page.mouse.move(10,10);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Users", "mega-menu-columns-group-1-link-0", "(//section[text()='Users'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "User Groups", "mega-menu-columns-group-1-link-1", "(//section[text()='User Groups'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Departments", "mega-menu-columns-group-1-link-2", "(//section[text()='Departments'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Attributes", "mega-menu-columns-group-1-link-3", "(//section[text()='Attributes'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "User Risk Scores", "mega-menu-columns-group-1-link-4", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Domains", "mega-menu-columns-group-2-link-0", `(//section[@class="typography-header3 page-title"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Password Complexity", "mega-menu-columns-group-3-link-0", "(//section[text()='Password Policy'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Authentication Methods", "mega-menu-columns-group-3-link-1", "(//section[text()='Authentication Methods'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Authentication Session", "mega-menu-columns-group-3-link-2", "(//section[text()='Settings'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "IDP Device Token", "mega-menu-columns-group-4-link-0", "(//section[text()='Client Connector Device Token'])");
    }

    async clickInternetAndSaaS(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "Internet Authentication Settings", "mega-menu-columns-group-0-link-0", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "SCIM Event Logs", "mega-menu-columns-group-1-link-0", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "User Management", "mega-menu-columns-group-2-link-0", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Identity Proxy Settings", "mega-menu-columns-group-3-link-0", "(//span[@class='-js-title-text'])");
    }

    async clickPrivateAccess(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "IDP Configuration", "mega-menu-columns-group-0-link-0", `(//span[@data-testid="refresh-view-icon"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "SAML Attributes", "mega-menu-columns-group-0-link-1", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Settings", "mega-menu-columns-group-0-link-2", `(//form[@id="authSettingsForm"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "SCIM Attributes", "mega-menu-columns-group-1-link-0", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "SCIM Users", "mega-menu-columns-group-1-link-1", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "SCIM Groups", "mega-menu-columns-group-1-link-2", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "SCIM Sync Logs", "mega-menu-columns-group-1-link-3", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Machine Groups", "mega-menu-columns-group-2-link-0", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Machine Provisioning Keys", "mega-menu-columns-group-2-link-1", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Partner Login", "mega-menu-columns-group-3-link-0", "(//h1[text()='ZPA Partner Logins'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Emergency Access", "mega-menu-columns-group-4-link-0", `(//span[@data-testid="refresh-view-icon"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Emergency Access Users", "mega-menu-columns-group-4-link-1", "(//span[@data-testid='refresh-view-icon'])");
    }

    async clickEntitlements(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Entitlements"], "Entitlements");
    }

    async clickEntitlementsClientConnectorServiceEntitlement(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "Private Access", "mega-menu-columns-group-0-link-0", "(//h1[text()='Zscaler Service Entitlement'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Digital Experience", "mega-menu-columns-group-0-link-1", "(//h1[text()='Zscaler Service Entitlement'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Internet Access", "mega-menu-columns-group-0-link-2", "(//h1[text()='Zscaler Service Entitlement'])");
    }

    async clickAPIConfiguration(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["API Configuration"], "API Configuration");
        await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["OneAPI"], "OneAPI");
    }

    async clickOneAPILinks(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "API Clients", "mega-menu-columns-group-0-link-0", "(//section[text()='API Clients'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "API Resources", "mega-menu-columns-group-0-link-1", "(//section[text()='API Resources'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Tokens", "mega-menu-columns-group-0-link-2", "(//section[text()='ZIdentity Issued Tokens'])");
    }

    async clickLegacyAPI(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["API Configuration"], "API Configuration");
        await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Legacy API"], "Legacy API");
    }

    async clickLegacyAPILinks(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "Internet & SaaS API", "mega-menu-columns-group-0-link-0", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Private Access API", "mega-menu-columns-group-0-link-1", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Digital Experience API", "mega-menu-columns-group-0-link-2", "(//h1[text()='API Key Management'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Client Connector API", "mega-menu-columns-group-0-link-3", "(//h1[text()='Public API'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "API Key Management", "mega-menu-columns-group-0-link-4", "(//div[text()='API Key Management'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Zero Trust Branch API Keys", "mega-menu-columns-group-0-link-5", `//div[@class="grid-card-container"]`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Zero Trust Branch API Docs", "mega-menu-columns-group-0-link-6", `//h2[text()="Zero Trust Branch API Server."]`);
    }

    async clickAlerts(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Alerts"], "Alerts");
    }

    async clickAlertLinks(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "Security & UEBA Alerts", "mega-menu-columns-group-0-link-0", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Platform Alerts", "mega-menu-columns-group-0-link-1", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Alerts", "mega-menu-columns-group-1-link-0", "(//p[text()='Alerts'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Rules", "mega-menu-columns-group-1-link-1", "(//p[text()='Rules'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Templates", "mega-menu-columns-group-1-link-2", `(//div[@class="app-table-container-wrapper"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Webhooks", "mega-menu-columns-group-1-link-3", `(//h1[text()="Integrations"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Notifications", "mega-menu-columns-group-2-link-0", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Alarms", "mega-menu-columns-group-3-link-0", "(//h2[text()='Alarms'])");
    }

    async clickBackupAndRestore(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Administration"], "Administration");
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Backup & Restore"], "Backup & Restore");
    }

    async clickInternetAndSaaSApplications(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "Internet & SaaS Applications", "mega-menu-columns-group-0-link-0", "(//span[@class='-js-title-text'])");
    }
}

export default new Administration();