import { Page } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';

class Logs {
    private menuIds: Record<string, string>;
    private subMenuIds: Record<string, string>;

    constructor() {
        this.menuIds = {
            Administration: "nav-pills-tab-1",
            Policies: "nav-pills-tab-2",
            Infrastructure: "nav-pills-tab-3",
            Logs: "nav-pills-tab-4",
        };

        this.subMenuIds = {
            Insights: "mm-tabs-tab-0",
            logStreaming: "mm-tabs-tab-1"
        }
    }

    async clickLogsInsights(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds['Insights'], 'Insights');
    }

    async clickInsightLinks(page: Page): Promise<void> {
        await PlaywrightActions.checkPageRenderWithTheme(page, "Web Insights", "mega-menu-columns-group-0-link-0", "//span[@class='insights-title' and text()='Web Insights']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Mobile Insights", "mega-menu-columns-group-0-link-1", "//span[@class='insights-title' and text()='Mobile Insights']");
        
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Firewall Insights", "mega-menu-columns-group-0-link-2", "//span[@class='insights-title' and text()='Firewall Insights']");     
        
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "DNS Insights", "mega-menu-columns-group-0-link-3", "//span[@class='insights-title' and text()='DNS Insights']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Tunnel Insights", "mega-menu-columns-group-0-link-4", "//span[@class='insights-title' and text()='Tunnel Insights']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "SaaS Security Insights", "mega-menu-columns-group-0-link-5", "//span[@class='insights-title' and text()='SaaS Security Insights']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Endpoint DLP Insights", "mega-menu-columns-group-0-link-6", "//span[@class='insights-title' and text()='Endpoint DLP Insights']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Extranet Insights", "mega-menu-columns-group-0-link-7", "//span[@class='insights-title' and text()='Extranet Insights']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Diagnostics", "mega-menu-columns-group-1-link-0", "//a[@id='diagnostics']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Support Information", "mega-menu-columns-group-1-link-1", `(//span[@data-action="refresh"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Live Logs", "mega-menu-columns-group-1-link-2", "//span[text()='LIVE']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Usage", "mega-menu-columns-group-1-link-3", `//div[@id='zpn-react-container']`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "MITRE ATT&CK", "mega-menu-columns-group-1-link-4", "//p[text()='MITRE ATT&CK']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Live Sessions", "mega-menu-columns-group-2-link-0", `(//a[@id="privilegedSessions"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Recordings", "mega-menu-columns-group-2-link-1", `(//a[@id="privilegedSessions"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Session Insights", "mega-menu-columns-group-3-link-0", "//span[@class='component-header']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "DNS Insights", "mega-menu-columns-group-3-link-1", "//span[@class='component-header']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Tunnel Insights", "mega-menu-columns-group-3-link-2", "//span[@class='component-header']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Packet Logs", "mega-menu-columns-group-4-link-0", "//h2[text()='Packet Logs']");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Flow Logs", "mega-menu-columns-group-4-link-1", "//h2[text()='Flow Logs']");
    }

    async clickLogsLogStreaming(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds['logStreaming'], 'Log Streaming');
    }

    async clickLogStreamingLinks(page: Page): Promise<void> {
        // await PlaywrightActions.checkPageRenderWithTheme(page, "Nanolog Streaming Service", "mega-menu-columns-group-0-link-0", "//span[text()='NSS Servers']");

        // await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Log Receivers", "mega-menu-columns-group-1-link-0", `(//span[@data-testid="refresh-view-icon"])`);

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Logs'], 'Logs');
        await PlaywrightActions.checkPageRenderWithTheme(page, "Log Connector Groups", "mega-menu-columns-group-1-link-1", `(//span[@data-testid="refresh-view-icon"])`);
    }
}

export default new Logs();