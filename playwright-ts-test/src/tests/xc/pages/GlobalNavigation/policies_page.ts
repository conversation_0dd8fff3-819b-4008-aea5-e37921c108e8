import { Page, expect } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';

class policies {
  private menuIds: Record<string, string>;
  private subMenuIds: Record<string, string>;
  private verticalMenuIds: Record<string, string>;
    private fields = {
        digitalExperience: 'Digital Experience Monitoring',
    };

    constructor() {
        this.menuIds = {
            Administration: "nav-pills-tab-1",
            Policies: "nav-pills-tab-2",
            Infrastructure: "nav-pills-tab-3",
            Logs: "nav-pills-tab-4",
        };

        this.subMenuIds = {
            AccessControl: "mm-tabs-tab-0",
            Cybersecurity: "mm-tabs-tab-1",
            CommonConfiguration: "mm-tabs-tab-4",
            DigitalExperience: "mm-tabs-tab-3"
        }

        this.verticalMenuIds = {
            InternetAndSaaS: "mega-menu-tabs-vertical-tab-0",
            PrivateApplications: "mega-menu-tabs-vertical-tab-1",
            Clientless: "mega-menu-tabs-vertical-tab-3",
            InlineSecurity: "mega-menu-tabs-vertical-tab-0",
            Resources: "mega-menu-tabs-vertical-tab-2",
            Firewall: "mega-menu-tabs-vertical-tab-2",
        };
    }

    getSelectorForTestId(testId: string): string {
        return `//button[@data-testid="${testId}"]`;
    }   

 async policies_globalNav(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
 }

 async accessControl(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds['AccessControl'], 'Access Control');
 }

 async cyberSecurity(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds['Cybersecurity'], 'Cybersecurity');
 }


 async commonConfiguration(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds['CommonConfiguration'], 'Common Configuration');
 }

async globalNavPoliciesURL(page: Page): Promise<void> {
    await page.waitForTimeout(2000);
    await page.getByRole('button', { name: 'Policies Down icon' }).click();
}


async policies_Links(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['PrivateApplications'], 'Private Applications');
    await PlaywrightActions.checkPageRender(page, "Access Policy", "mega-menu-columns-group-0-link-0", "//div[@class='repeater-items  dummy-repeater']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Timeout Policy", "mega-menu-columns-group-0-link-1", "//div[@class='repeater-items  dummy-repeater']");
}

async appSegments_Links(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['PrivateApplications'], 'Private Applications');
    await PlaywrightActions.checkPageRender(page, "App Segments", "mega-menu-columns-group-1-link-0", `(//a[@id="applications"])`);

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Segment Groups", "mega-menu-columns-group-1-link-1", `//table[@id="applicationGroupsTable"]`);
}

async servers_Links(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['PrivateApplications'], 'Private Applications');
    await PlaywrightActions.checkPageRender(page, "Servers", "mega-menu-columns-group-2-link-0", "//div[@class='repeater-items  dummy-repeater']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Server Groups", "mega-menu-columns-group-2-link-1", "//div[@class='repeater-items  dummy-repeater']");
}

async firewall_Links(page: Page): Promise<void>{
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Firewall'], 'Firewall');
    await PlaywrightActions.checkPageRender(page, "Firewall Filtering Policy", "mega-menu-columns-group-0-link-0", "//span[text()='Firewall Filtering Policy']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Firewall'], 'Firewall');
    await PlaywrightActions.checkPageRender(page, "FTP Control", "mega-menu-columns-group-0-link-1", "//span[text()='FTP Control' and @class='-js-title-text']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Firewall'], 'Firewall');
    await PlaywrightActions.checkPageRender(page, "DNS Control", "mega-menu-columns-group-1-link-0", "//span[text()='DNS Control' and @class='-js-title-text']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Firewall'], 'Firewall');
    await PlaywrightActions.checkPageRender(page, "DNS Application Group", "mega-menu-columns-group-1-link-1", "//span[text()='DNS Application Group']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Firewall'], 'Firewall');
    await PlaywrightActions.checkPageRender(page, "EDNS Client Subnet", "mega-menu-columns-group-1-link-2", "//span[text()='EDNS Client Subnet Prefix Objects']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Firewall'], 'Firewall');
    await PlaywrightActions.checkPageRender(page, "Network Services", "mega-menu-columns-group-2-link-0", "//span[text()='Services']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Firewall'], 'Firewall');
    await PlaywrightActions.checkPageRender(page, "Network Applications", "mega-menu-columns-group-3-link-0", "//li[@data-label='APPLICATIONS']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Firewall'], 'Firewall');
    await PlaywrightActions.checkPageRender(page, "Network Application Groups", "mega-menu-columns-group-3-link-1", "//span[text()='Application Groups']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Firewall'], 'Firewall');
    await PlaywrightActions.checkPageRender(page, "Application Services", "mega-menu-columns-group-3-link-2", "//span[text()='Application Service Groups']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Firewall'], 'Firewall');
    await PlaywrightActions.checkPageRender(page, "IP & FQDN Groups", "mega-menu-columns-group-4-link-0", "//span[text()='Source IPv4 Groups']");
}

async accessMethods_Links(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Clientless'], 'Clientless');
    await PlaywrightActions.checkPageRender(page, "Browser Access", "mega-menu-columns-group-0-link-0", "//a[@class='zpa-tab active']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "User Portals", "mega-menu-columns-group-0-link-1", "//div[@class='repeater-items  dummy-repeater']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Privileged Portals", "mega-menu-columns-group-0-link-2", "//div[@class='repeater-items  dummy-repeater']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Certificates", "mega-menu-columns-group-0-link-3", "//div[@class='repeater-items  dummy-repeater']");
}

async userPortalConfiguration_Links(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Clientless'], 'Clientless');
    await PlaywrightActions.checkPageRender(page, "Portal Links", "mega-menu-columns-group-2-link-0", "//div[@class='repeater-items  dummy-repeater']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Acceptable Use Policy", "mega-menu-columns-group-2-link-1", "//div[@class='repeater-items  dummy-repeater']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Client Connector Download Links", "mega-menu-columns-group-2-link-2", "//div[@class='repeater-items  dummy-repeater']");
}

async privateAppProtection(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['InlineSecurity'], 'Inline Security');
    await PlaywrightActions.checkPageRender(page, "Protection Policies", "mega-menu-columns-group-2-link-0", "//div[@class='repeater-items  dummy-repeater']//span[text()='AppProtection']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['InlineSecurity'], 'Inline Security');
    await PlaywrightActions.checkPageRender(page, "Protected Applications", "mega-menu-columns-group-2-link-1", "//a[@class='zpa-tab active']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds['Cybersecurity'], 'Cybersecurity');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['InlineSecurity'], 'Inline Security');
    await PlaywrightActions.checkPageRender(page, "Protection Controls", "mega-menu-columns-group-2-link-2", "//div[@class='repeater-items  dummy-repeater']/span[text()='Protection Controls']");
    
    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds['Cybersecurity'], 'Cybersecurity');
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['InlineSecurity'], 'Inline Security');
    await PlaywrightActions.checkPageRender(page, "Protection Profiles", "mega-menu-columns-group-2-link-3", "//div[@class='repeater-items  dummy-repeater']/span[text()='Protection Profiles']");
}


async resources_link(page: Page): Promise<void> {
    await PlaywrightActions.checkPageRender(page, "Labels", "mega-menu-columns-group-3-link-0", "//h1[@data-testid='page-header-title']");
}

async digitalExperience(page: Page) {
    await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds['DigitalExperience'], 'Digital Experience Monitoring');
}

async settings_link(page: Page): Promise<void> {
    await PlaywrightActions.checkPageRender(page, "Inventory Settings", "mega-menu-columns-group-2-link-0", "//h1[@data-testid='page-header-title']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Data Collection Integrations", "mega-menu-columns-group-2-link-1", "//h1[@data-testid='page-header-title']");
}

async selfService_link(page: Page): Promise<void> {
    await PlaywrightActions.checkPageRender(page, "Self Service", "mega-menu-columns-group-1-link-0", "//h1[@data-testid='page-header-title']");
}

async configuration_link(page: Page): Promise<void> {
    await PlaywrightActions.checkPageRender(page, "Configuration", "mega-menu-columns-group-0-link-0", "//div[@data-testid='applications-tab']");
}


async endUserNotifications_link(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Resources'], 'Resources');
    await PlaywrightActions.checkPageRender(page, "End User Notifications", "mega-menu-columns-group-2-link-0", "//span[text()='Browser']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Client Connector Notifications", "mega-menu-columns-group-2-link-1", "//h1[text()='Client Connector Notification']");
}

async connectors_link(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['Resources'], 'Resources');
    await PlaywrightActions.checkPageRender(page, "Device Management", "mega-menu-columns-group-3-link-0", "//span[text()='Devices']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Device Posture", "mega-menu-columns-group-3-link-1", "//h1[text()='Device Posture']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Internet & SaaS Posture Profiles", "mega-menu-columns-group-3-link-2", "//h1[text()='Internet & SaaS Posture Profiles']");
}

async uRLControl(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['InternetAndSaaS'], 'Internet & SaaS');
    await PlaywrightActions.checkPageRender(page, "URL Filtering", "mega-menu-columns-group-0-link-0", "//span[text()='URL Filtering Policy']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "URL Categories", "mega-menu-columns-group-0-link-1", "//li[@data-label='URL_CATEGORIES']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Advanced Settings", "mega-menu-columns-group-0-link-2", "//span[text()='Advanced Policy Settings']");
}

async fileTypeControl(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['InternetAndSaaS'], 'Internet & SaaS');
    await PlaywrightActions.checkPageRender(page, "File Type Control", "mega-menu-columns-group-1-link-0", "//span[@class='-js-title-text']");

    // await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    // await PlaywrightActions.checkPageRender(page, "Custom File Types", "mega-menu-columns-group-1-link-1", "//span[@class='-js-title-text']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Mobile App Store Control", "mega-menu-columns-group-1-link-1", "//span[@class='-js-title-text']");
}
 
async saaSApplicationControl(page: Page): Promise<void> {
    await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds['InternetAndSaaS'], 'Internet & SaaS');
    await PlaywrightActions.checkPageRender(page, "Unified SaaS Applications", "mega-menu-columns-group-2-link-0", "//h3[text()='Applications']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "SaaS Applications", "mega-menu-columns-group-2-link-1", "//div[text()='Applications']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Policies", "mega-menu-columns-group-2-link-2", "//span[text()='Cloud App Control Policy']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Risk Profiles", "mega-menu-columns-group-2-link-3", "//span[text()='Cloud Application']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Application Tags", "mega-menu-columns-group-2-link-4", "//div[text()='Cloud Application Tags']");

    await PlaywrightActions.verifyTextAndHover(page, this.menuIds['Policies'], 'Policies');
    await PlaywrightActions.checkPageRender(page, "Tenant Profiles", "mega-menu-columns-group-2-link-5", "//span[text()='Add Tenant Profile']");
}
}

export default new policies();