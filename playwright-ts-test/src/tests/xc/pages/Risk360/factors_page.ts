import { expect, Page } from "@playwright/test";
import { config } from "dotenv";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";
const factorsPageTitle = 'Contributing Factors to Organizational Risk Score';
const factorsPageURL = url+"analytics/risk360/factors?grpId=0&factorId=0";

class Risk360Factors {
  private page: Page;

  
  constructor(page: Page) {
      this.page = page;
  }

  //#region Page Elements

  private get riskCategoryFilterSelected() {
    return this.page.locator('.filter-item.selected');
  }

  private get factorsPageTitle() {
    return this.page.locator('.factors-header-title');
  }

  //#endregion

  //#region Actions

  async checkPageUrlAndTitle(): Promise<void> {
    await PlaywrightActions.checkUrl(this.page, factorsPageURL);
    const actualPageTitle = await this.factorsPageTitle.textContent();
    expect(actualPageTitle).toMatch(factorsPageTitle);
  }

  /**
   * Validates the Risk score category filter selected
   * @param expectedRiskCategory expected Risk score category filter
   */
  async validateRiskCategoryFilterSelected(expectedRiskCategory: string) {
    const actualRiskScoreCategory = await this.riskCategoryFilterSelected.textContent();
    expect(actualRiskScoreCategory).toContain(expectedRiskCategory);
  }

  //#endregion

  //#region Private Methods

  //#endregion


}

export default Risk360Factors;
