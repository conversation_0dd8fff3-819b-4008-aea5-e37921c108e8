import { expect, Page } from "@playwright/test";
import { config } from "dotenv";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";
const insightsPageTitle = 'Insights';
const insightsPageURL = url+"analytics/risk360/insights";

class Risk360Insights{
  private page: Page;

  
  constructor(page: Page) {
      this.page = page;
  }

  //#region Page Elements

  private get insightsPageTitle() {
    return this.page.locator('.typography-paragraph1-strong.text-semantic-content-base-primary.flex');
  }

  private get filterSelected() {
    return this.page.locator('.key-cards-wrapper.selected');
  }

  private get categoryOfFirstInsightCard() {
    return this.page.locator('.severtity-level-text').first();
  }


  //#endregion

  //#region Actions

  async checkPageUrlAndTitle(): Promise<void> {
    await PlaywrightActions.checkUrl(this.page, insightsPageURL);
    const actualPageTitle = await this.insightsPageTitle.textContent();
    expect(actualPageTitle).toMatch(insightsPageTitle);
  }

  async validateSelectedFilter(expectedFilter: string) {
    const filter = await this.filterSelected.textContent();
    expect(filter).toContain(expectedFilter);
  }

  async validateCategoryOfFirstInsightCard(expectedCategory: string) {
    const actualCategory = await this.categoryOfFirstInsightCard.textContent();
    expect(actualCategory).toContain(expectedCategory);
  }
  //#endregion

  //#region Private Methods

  //#endregion


}

export default Risk360Insights;