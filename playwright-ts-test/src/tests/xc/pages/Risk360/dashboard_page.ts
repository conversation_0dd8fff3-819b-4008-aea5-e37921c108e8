import { expect, Locator, Page } from "@playwright/test";
import { config } from "dotenv";
import {
  DashboardSection
} from "../../../../../resources/types/risk360";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import CommonFunctions from "../../../../../resources/utils/CommonFunctions";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";
const risk360DashboardURL = url+"analytics/risk360";
const FEATURE_NOT_SUBSCRIBED_TEXT = "Feature not subscribed. To access this feature, you need Risk360 Advanced subscription. Please contact your Zscaler Account Team for more information.";
const DEFAULT_STRATEGY_NAME = "Default";
const DEFAULT_STRATEGY_TOOLTIP_TEXT = "This is the Zscaler-defined peer scoring strategy with the Industry Vertical, Geographical Region, and Revenue ID attributes. You can modify the values of default attributes or click Add Custom Strategy to create a new strategy."
const TOP_RISKY_LOCATIONS_COUNT = 5;
const riskCategories: string[] = ['External Attack Surface', 'Compromise', 'Lateral Propagation', 'Data Loss'];

const riskCategoryToolTipData = [
  {
    riskCategory: 'External Attack Surface',
    toolTipExpectedText: 'Threat actor attempts to discover your external attack surface exposed to the internet.',
  },
  {
    riskCategory: 'Compromise',
    toolTipExpectedText:
      'Threat actor attempts to compromise your corporate asset via threats delivered from the internet.',
  },
  {
    riskCategory: 'Lateral propagation',
    toolTipExpectedText: 'Threat actor attempts to move laterally within your environment from the compromised asset.',
  },
  {
    riskCategory: 'Data Loss',
    toolTipExpectedText: 'Threat actor steals sensitive data as part of the actions on this stage.',
  },
];

class Risk360Dashboard {
  private page: Page;

  
  constructor(page: Page) {
      this.page = page;
  }

  //#region Page Elements

  private get analytics () {
    return this.page.getByText('Analytics', { exact: true });
  }

  private get risk360Menu () {
    return this.page.getByText('Risk', { exact: true });
  }

  private overallRiskScore () {
    return this.page.locator('.arc-inner-state-score');
  }

  private get dashboardCardContainer() {
    return this.page.locator('.risk360-card-wrapper');
  }

  private get financeIcon() {
    return this.page.locator(`[data-icon="dollar-sign"]`);
  }

  private get viewDetails() {
    return this.page.getByText('View Details', { exact: true });
  }

  private get contributingFactorsByEntityViewAllButton() {
    return this.page.locator('.factors-distribution-title .view-all-button');
  }

  private get topTenFactorsViewAllButton() {
    return this.page.locator('.top-factors-title .view-all-button');
  }

  private get highImpactRecommendationsViewAllButton() {
    return this.page.locator('.recommendation-top .view-all-button');
  }

  private get peerScoreSettingsIcon() {
    return this.page.locator(`[data-icon="gear"]`);
  }

  private get addCustomStrategyButton() {
    return this.page.getByText('Add Custom Strategy', { exact: true });
  }

  private get addCustomStrategyButtonDisabledTooltip() {
    return this.page.locator('.peer-score-disabled-strategy-tooltip');
  }

  private get rowsInPeerScoreSettingsTable() {
    return this.page.locator('.risk-peer-score-table .rt-tr-group');
  }

  private get defaultStrategyName() {
    return this.page.locator('.peer-render-strategy-name');
  }

  private get defaultStrategyTooltipIcon() {
    return this.page.locator('.peer-strategy-name-icon-box .svg-inline--fa.fa-circle-info');
  }

  private get defaultStrategyTooltip() {
    return this.page.locator(`[id="peer-strategy-name-icon-1"]`);
  }

  private get peerScoreSettingsDropdownList() {
    return this.page.locator('.peer-score-list.shadow-medium');
  }

  private get peerScoreSettingsSaveButton() {
    return this.page.locator('.add-peer-custom-strategy-footer .primary-button');
  }

  private get peerScoreSettingsCancelButton() {
    return this.page.locator('.add-peer-custom-strategy-footer .cancel-button');
  }

  private get peerScoreSettingsDrawerCloseButton() {
    const section = this.page.locator('.peer-score-subtitle-flex');
    return section.locator(`[data-icon="xmark"]`);
  }

  private get riskValue() {
    return this.page.locator('.location-value');
  }
  
  private get riskEventBubble() {
    return this.page.locator('.leaflet-interactive');
  }

  private get riskCategoryInRiskEventBubble() {
    return this.page.locator('.risk-icon');
  }

  private get bubbleTooltipCloseBtn() {
    return this.page.locator("[aria-label='Close popup']");
  }

  private get riskEventCount() {
    return this.page.locator('.evt-count');
  }

  private get riskSeverity() {
    return this.page.locator('.arc-inner-state-level');
  }

  //#endregion

  //#region Actions

  async navigateToRisk360Dashboard(): Promise<void> {
      if (!url) {
        throw new Error('ONE_UI_BASE_URL environment variable is not set');
      }
      await this.page.goto(risk360DashboardURL);
      await this.page.waitForTimeout(10000);
      // await this.analytics.click();
      // await this.page.waitForTimeout(10000);
      // await this.risk360Menu.click();
      // await this.page.waitForTimeout(10000);
  }


async validateOrgRiskScore(riskCategory: string) {
    let score: string | null = null;
    if (riskCategory === "Overall Organization") 
    {
      score =  await this.overallRiskScore().textContent();
    }
    else 
    {
      score =  await this.getRiskScoreByCategory(riskCategory).textContent();
    }

    if (score === null) {
        throw new Error("Unable to retrieve the organization's overall risk score. Text content is null.");
    }
    const orgRiskScore = parseFloat(score);
    console.log(riskCategory+" Risk score is "+orgRiskScore);
    expect(orgRiskScore).toBeGreaterThanOrEqual(1);
    expect(orgRiskScore).toBeLessThanOrEqual(100);
  }

  async clickRiskScoreCategory(riskCategory: string) {
    await this.getRiskScoreCategory(riskCategory).click();
  }

  /**
   * Hovers on Finance icon in 'Organization Risk Score' widget
   */
  async hoverOnFinanceIcon() {
    await this.financeIcon.hover();
  }

  /**
   * Clicks View Details button in Financial Risk tooltip of 'Organization Risk Score' widget
   */
  async clickViewDetails() {
    await this.viewDetails.click();
  }

  async clickContributingFactorsByEntityViewAllButton() {
    await this.contributingFactorsByEntityViewAllButton.click();
  }

  async clickTopTenFactorsViewAllButton() {
    await this.topTenFactorsViewAllButton.click();
  }

  async clickHighImpactRecommendationsViewAllButton() {
    await this.highImpactRecommendationsViewAllButton.click();
  }

  async clickPeerScoreSettingsIcon() {
    await this.peerScoreSettingsIcon.click();
  }

  async hoverOnAddCustomStrategyButton() {
    await this.addCustomStrategyButton.hover();
  }

  async verifyAddCustomStrategyDisabledTooltip() {
    const tooltipText = await this.addCustomStrategyButtonDisabledTooltip.textContent();
    expect(tooltipText).toEqual(FEATURE_NOT_SUBSCRIBED_TEXT);
  }

  async verifyOnlyDefaultStrategyIsAvailable() {
    const defaultStrategyName = await this.defaultStrategyName.textContent();
    if (!defaultStrategyName) {
      throw new Error("Default strategy name element was found, but it has no text content.");
    }
    expect(defaultStrategyName).toContain(DEFAULT_STRATEGY_NAME);
    const rowsCount = await this.rowsInPeerScoreSettingsTable.count();
    expect(rowsCount).toEqual(1);
  }

  async verifyDefaultStrategyTooltipText() {
    await this.defaultStrategyTooltipIcon.click();
    await expect(this.defaultStrategyTooltip).toBeVisible();
    const tooltipText = await this.defaultStrategyTooltip.textContent();
    if (!tooltipText) {
      throw new Error("Default strategy tooltip was visible, but it has no text content.");
    }
    expect(tooltipText).toContain(DEFAULT_STRATEGY_TOOLTIP_TEXT);
  }

  async verifyDefaultStrategyCannotBeDeleted() {
    const defaultStrategyRow = await CommonFunctions.findRowByUniqueString(this.page, DEFAULT_STRATEGY_NAME);
    expect(defaultStrategyRow.locator(`[data-icon="trash"]`)).not.toBeVisible();
  }

  async clickPeerScoreStrategyEditIcon(strategyName: string) {
    const strategyRow = await CommonFunctions.findRowByUniqueString(this.page, strategyName);
    await strategyRow.locator(`[data-icon="pencil"]`).click();
  } 

  async setPeerScoreAttribute(attributeName: string, attributeValue: string) {
    await this.getPeerScoreAttributeDropdown(attributeName).click();
    const listItem = this.peerScoreSettingsDropdownList.getByText(attributeValue);
    await listItem.scrollIntoViewIfNeeded();
    await listItem.click();
  }

  async clickPeerScoreSettingsSaveButton() {
    await this.peerScoreSettingsSaveButton.click();
  }

  async clickPeerScoreSettingsCancelButton() {
    await this.peerScoreSettingsCancelButton.click();
  }

  async validateToasterMessage(page: Page, message: string) {
    await PlaywrightActions.validateToasterMessage(page, message);
  }

  async closePeerScoreSettingsDrawer() {
    await this.peerScoreSettingsDrawerCloseButton.click();
  }

  async validateRiskValueDescendingOrder() {
    // Wait for the first risk value to be visible to ensure the list has loaded.
    await this.riskValue.first().waitFor();

    const riskValueActualOrderStrings = await this.riskValue.allTextContents();
    const riskValueActualOrder = riskValueActualOrderStrings.map(value => parseFloat(value));

    // Create a copy of the actual order and sort it in descending order to get the expected order.
    const riskValueExpectedOrder = [...riskValueActualOrder].sort((a, b) => b - a);
    expect(riskValueActualOrder).toEqual(riskValueExpectedOrder);
  }

  async validateZoomButtonIsDisabled(buttonTitle: string) {
    const zoomButton = this.getZoomButton(buttonTitle);
    await expect(zoomButton).toBeDisabled();
  }

  async clickZoomButton(buttonTitle: string) {
    await this.getZoomButton(buttonTitle).click();
  }

  async clickRiskEventBubble() {
    await this.riskEventBubble.last().click();
  }

  async validateRiskCategoryInRiskEventBubble() {
    const riskCategory = await this.riskCategoryInRiskEventBubble.first().textContent();
    if (!riskCategory) {
      throw new Error("The risk category element was found, but it has no text content.");
    }
    expect(riskCategories).toContain(riskCategory);
    return riskCategory;
  }

  async validateToolTipIsVisible(toolTipWrapper: string) {
    await this.getToolTipWrapper(toolTipWrapper).isVisible();
  }

  async closeRiskEventBubbleTooltip() {
    await this.bubbleTooltipCloseBtn.click();
  }

  async clickRiskEventCount() {
    await this.riskEventCount.first().click();
  }

  /**
   * Validates tooltip text for a given risk category
   * @param riskCategory Organization Risk Categories
   * @param expectedText Risk Category wise expected tooltip text
   */
  async validateRiskCategoryToolTipText() {
    for(const dataset of riskCategoryToolTipData) {
    await this.getRiskCategoryToolTipIcon(dataset.riskCategory).click();
    const toolTipTextWebElement = this.getRiskCategoryToolTipText(dataset.riskCategory);
    await PlaywrightActions.validateTextContent(toolTipTextWebElement, dataset.toolTipExpectedText);
    }
  }

  /**
   * Clicks checkbox of element with the given locator
   * @param checkBox Locator of risk score check box for Zscaler or Industry peer
   */
  async clickCheckBox(checkBox: string) {
    await this.getRiskScoreCheckBox(checkBox).click();
  }

  /**
   * Clicks on above graph layer above required month in provided Dashboard graph
   * @param month first three char of the month in sentenced case
   * @param graphSection graph section in the Dashboard
   */
  async clickGraphAboveMonth(month: string) {
    const section = this.getDashboardSection(DashboardSection.riskScoreTrend);
    const sectionGraph = this.getGraphBody(section).getByText(month);
    await sectionGraph.first().click({ position: { x: -10, y: -20 }, force: true, noWaitAfter: false });
  }

  /**
   * Validates the presence of given text in tooltip
   * @param expectedText Text to be validated in tooltip
   * @param toolTipWrapper Locator for the tooltip wrapper
   */
  async validateTextPresenceInToolTip(expectedText: string, toolTipWrapper: string) {
    const toolTipText = await this.getToolTipWrapper(toolTipWrapper).textContent();
    expect(toolTipText).toContain(expectedText);
  }

  /**
   * Validates the absence of given text in tooltip
   * @param expectedText Text to be validated in tooltip
   * @param toolTipWrapper Locator for the tooltip wrapper
   */
  async validateTextAbsenceInToolTip(expectedText: string, toolTipWrapper: string) {
    const toolTipText = await this.getToolTipWrapper(toolTipWrapper).textContent();
    expect(toolTipText).not.toContain(expectedText);
  }

  /**
   * Clicks on Organization Risk Severity to get the tooltip
   */
  async clickOrgRiskSeverityTooltip() {
    await this.riskSeverity.click();
  }

  /**
   * Validates Contributing factors count for a given entity
   * @param entityName Name of the entity
   */
  async validateContributingFactorsCountByEntity(entityName: string) {
    const factorCountByEntityText = await this.getContributingFactorsCountByEntity(entityName).textContent();
    if (factorCountByEntityText === null) {
      throw new Error("Unable to retrieve contributing factors count. Text content is null.");
    }
    const factorCountByEntity = parseFloat(factorCountByEntityText);
    expect(factorCountByEntity).toBeGreaterThanOrEqual(1);
  }

  /**
   * Validates entity description for a given entity
   * @param entityName Name of the entity
   * @param expectedEntityDescription expected description of the entity
   */
  async validateEntityDescription(entityName: string, expectedEntityDescription:string) {
    const entityDescriptionWebElement = this.getEntityDescription(entityName);
    await PlaywrightActions.validateTextContent(entityDescriptionWebElement, expectedEntityDescription);
  }

  //#endregion

  //#region Private Methods

  /**
   * Returns the locator of Risk Score by Category
   * @param riskCategory Different Risk Categories like External Attack Surface, Compromise, Lateral Propagation, Data Loss
   * @returns Locator of Risk Score by Category
   */
  private getRiskScoreByCategory(riskCategory: string) {
    return this.page.locator('.risk-bar-container').filter({ hasText: riskCategory }).locator('.score').first();
  }

  /**
   * Returns the locator of the section in Dashboard
   * @param section section in the Dashboard
   * @returns locator of the provided dashboard section
   */
  private getDashboardSection(section: DashboardSection) {
    const locatorMap = {
      [DashboardSection.riskScoreTrend]: this.dashboardCardContainer.filter({ hasText: 'Risk Score Trend' }),
      [DashboardSection.organizationRiskScore]: this.dashboardCardContainer.filter({
        hasText: 'Organization Risk Score',
      }),
      [DashboardSection.contributingFactorsByEntity]: this.page.locator('.factors-distribution'),
    };
    return locatorMap[section];
  }

  /**
   * Returns locator to get the Risk score category in Organization Risk Score widget
   * @param riskCategory Risk Score category name
   * @returns locator to get the Risk score category in Organization Risk Score widget
   */
  private getRiskScoreCategory(riskCategory: string) {
    const section = this.getDashboardSection(DashboardSection.organizationRiskScore);
    return section.getByText(riskCategory).first();
  }

  private getPeerScoreAttributeDropdown(attributeName: string) {
    return this.page.locator(`.peer-score-label  :below(:text("${attributeName}"))`).first();
  }

  private getZoomButton(buttonTitle: string) {
    return this.page.getByRole('button', { name: buttonTitle });
  }

  private getToolTipWrapper(toolTipWrapper: string) {
    return this.page.locator(toolTipWrapper);
  }

  /**
   * Returns locator of tooltip icon based for a given risk category
   * @param riskCategory Organization Risk Categories
   * @returns locator of tooltip icon based for a given risk category
   */
  private getRiskCategoryToolTipIcon(riskCategory: string) {
    return this.getRiskCategorySection(riskCategory).locator("[data-icon='circle-info']").first();
  }

  /**
   * Returns locator to get the tooltip text for a given risk category
   * @param riskCategory Organization Risk Categories
   * @returns locator to get the tooltip text for a given risk category
   */
  private getRiskCategoryToolTipText(riskCategory: string) {
    return this.getRiskCategorySection(riskCategory).locator('.risk360-tooltip-text').first();
  }

  /**
   * Returns locator of the risk category section within Organization Risk Score widget
   * @param riskCategory Organization Risk Categories
   * @returns locator of the risk category section within Organization Risk Score widget
   */
  private getRiskCategorySection(riskCategory: string) {
    const dashboardSection = this.getDashboardSection(DashboardSection.organizationRiskScore);
    return dashboardSection.locator('.risk-bar-container').filter({ hasText: riskCategory });
  }

  /**
   * Returns the locator of checkbox - Zscaler Risk Score or Industry peer average score
   * @param checkBox Locator of risk score check box for Zscaler or Industry peer
   * @returns Locator for checkbox - Zscaler Risk Score or Industry peer average score
   */
  private getRiskScoreCheckBox(checkBox: string) {
    return this.page.locator(checkBox);
  }

  /**
   * Returns graph locator in the parent section of the Dashboard
   * @param parent parent wrapper of the graph
   * @returns locator of the graph
   */
  private getGraphBody(parent: Locator) {
    return parent.locator('.chart-body');
  }

  /**
   * Returns locator to get Risk contributing factors count for a given entity
   * @param entityName Name of the entity
   * @returns locator to get Risk contributing factors count for a given entity
   */
  private getContributingFactorsCountByEntity(entityName: string) {
    const section = this.getDashboardSection(DashboardSection.contributingFactorsByEntity);
    return section.locator(`.label-number:below(:text("${entityName}"))`).first();
  }

  /**
   * Returns locator to get the Entity description
   * @param entityName Name of the entity
   * @returns locator to get the Entity description
   */
  private getEntityDescription(entityName: string) {
    const section = this.getDashboardSection(DashboardSection.contributingFactorsByEntity);
    return section.locator(`.bottom-placeholder:below(:text("${entityName}"))`).first();
  }

  //#endregion


}

export default Risk360Dashboard;
