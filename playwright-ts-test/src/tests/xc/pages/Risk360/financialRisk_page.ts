import { expect, Page } from "@playwright/test";
import { config } from "dotenv";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";
const financialRiskPageTitle = 'Financial Risk';
const financialRiskPageURL = url+"analytics/risk360/financial-risk";
const financialLossRangeTooltipTextInDefaultValuesSection = "This is your default lower and upper bound of the financial loss in case your organization faces a breach.";
const financialLossRangeTooltipTextInCustomizedValuesSection = "Enter the lower and upper bound of the financial loss, in case your organization faces a breach.";
const customizedValuesTooltipText = "You can customize the default financial settings to your desired values. The Risk360 service uses these custom values to compute your organization financial exposure. Complete all the fields if you decide to update this section.";


class Risk360FinancialRisk {
  private page: Page;

  
  constructor(page: Page) {
      this.page = page;
  }

  //#region Page Elements

  private get financialRiskPageTitle() {
    return this.page.locator('.financial-risk-title');
  }

  private get financialRiskSettingsButton() {
    return this.page.getByText('Settings', { exact: true });
  }

  private get customizedValuesDropdownList() {
    return this.page.locator('.financial-account-list');
  }

  private get financialLossLowerBound() {
    return this.page.locator('.bound-loss').first();
  }

  private get financialLossUpperBound() {
    return this.page.locator('.bound-loss').last(); 
  }

  private get financialRiskSettingsSaveButton() {
    return this.page.locator('.financial-display-footer .primary-button');
  }

  private get financialRiskSettingsCancelButton() {
    return this.page.locator('.financial-display-footer .cancel-button');
  }

  private get financialRiskSettingsResetToDefaultButton() {
    return this.page.locator('.financial-display-footer .reset-to-default-btn');
  }

  private get financialRiskSettingsDrawerCloseButton() {
    const section = this.page.locator('.financial-settings-title-flex');
    return section.locator(`[data-icon="xmark"]`);
  }

  private get financialLossRangeTooltipIconInDefaultValuesSection() {
    return this.page.locator('.financial-loss-icon').first();
  }

  private get financialLossRangeTooltipInDefaultValuesSection() { 
    return this.page.locator('.financial-loss-icon-tooltip-body').first();
  }

  private get financialLossRangeTooltipIconInCustomizedValuesSection() { 
    return this.page.locator('.financial-loss-icon').last();
  }

  private get financialLossRangeTooltipInCustomizedValuesSection() { 
    return this.page.locator('.financial-loss-icon-tooltip-body').last();
  }

  private get customizedValuesTooltipIcon() {
    return this.page.locator('.customized-finance-icon');
  }

  private get customizedValuesTooltip() {
    return this.page.locator('.customized-finance-tooltip-body');
  }

  //#endregion

  //#region Actions

  async checkPageUrlAndTitle(): Promise<void> {
    await PlaywrightActions.checkUrl(this.page, financialRiskPageURL);
    const actualPageTitle = await this.financialRiskPageTitle.textContent();
    expect(actualPageTitle).toMatch(financialRiskPageTitle);
  }

  async navigateToRisk360FinancialRiskPage(): Promise<void> {
      if (!url) {
        throw new Error('ONE_UI_BASE_URL environment variable is not set');
      }
      await this.page.goto(financialRiskPageURL);
  }

  async clickFinancialRiskSettingsButton() {
    await this.financialRiskSettingsButton.click();
  }

  async setCustomizedValues(attributeName: string, attributeValue: string) {
    await this.getCustomizedValuesDropdown(attributeName).click();
    const listItem = this.customizedValuesDropdownList.getByText(attributeValue);
    await listItem.scrollIntoViewIfNeeded();
    await listItem.click();
  }

  async setFinancialLossLowerBound(value: string) {
    await this.financialLossLowerBound.fill(value);
  }

  async setFinancialLossUpperBound(value: string) {
    await this.financialLossUpperBound.fill(value);
  }

  async clickFinancialRiskSettingsSaveButton() {
    await this.financialRiskSettingsSaveButton.click();
  }

  async clickFinancialRiskSettingsCancelButton() {
    await this.financialRiskSettingsCancelButton.click();
  }

  async clickResetToDefaultButton() {
    await this.financialRiskSettingsResetToDefaultButton.click();
  }

  async validateToasterMessage(page: Page, message: string) {
    await PlaywrightActions.validateToasterMessage(page, message);
  }

  async clickFinancialRiskSettingsDrawerCloseButton() {
    await this.financialRiskSettingsDrawerCloseButton.click();
  }

  async verifySaveButtonIsDisabled() {
    await expect(this.financialRiskSettingsSaveButton).toHaveClass(/disabled/);
  }

  async verifyResetToDefaultButtonIsDisabled() {
    await expect(this.financialRiskSettingsResetToDefaultButton).toHaveClass(/disabled/);
  }

  async verifyFinancialLossRangeTooltipTextInDefaultValuesSection() { 
    await this.financialLossRangeTooltipIconInDefaultValuesSection.hover();
    await expect(this.financialLossRangeTooltipInDefaultValuesSection).toBeVisible();
    const tooltipText = await this.financialLossRangeTooltipInDefaultValuesSection.textContent();
    expect(tooltipText).toContain(financialLossRangeTooltipTextInDefaultValuesSection);
  }

  async verifyFinancialLossRangeTooltipTextInCustomizedValuesSection() { 
    await this.financialLossRangeTooltipIconInCustomizedValuesSection.hover();
    await expect(this.financialLossRangeTooltipInCustomizedValuesSection).toBeVisible();
    const tooltipText = await this.financialLossRangeTooltipInCustomizedValuesSection.textContent();
    expect(tooltipText).toContain(financialLossRangeTooltipTextInCustomizedValuesSection);
  }

  async verifyCustomizedValuesTooltipText() {
    await this.customizedValuesTooltipIcon.hover();
    await expect(this.customizedValuesTooltip).toBeVisible();
    const tooltipText = await this.customizedValuesTooltip.textContent();
    expect(tooltipText).toContain(customizedValuesTooltipText);
  }

  //#endregion

  //#region Private Methods

  private getCustomizedValuesDropdown(attributeName: string) {
    const section = this.page.locator('.risk-customized-finance-profile');
    return section.locator(`.financial-account-label  :below(:text("${attributeName}"))`).first();
  }

  //#endregion


}

export default Risk360FinancialRisk;