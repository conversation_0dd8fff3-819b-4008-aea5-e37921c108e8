import { Page, expect } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import { NAVIGATION, TEST_IDS,DATE_FILTER,TAB_SELECTOR } from '../../../../../resources/constants/pages/analytics/Cybersecurity/cybersecurityDashboard';
import Base from "../../../../../lib/pom/Base/Base";

class CybersecurityDashboard  extends Base{
  constructor(page: Page) {
              super(page, {
                  pageName: NAVIGATION.pageName,
                  menuTestId: NAVIGATION.menuId,
                  submenuTestId: NAVIGATION.submenuId,
                  urlSuffix: NAVIGATION.url,
                  dateFilters: DATE_FILTER,
                  // filters: FILTERS,
                   selectors: TEST_IDS,
                   //tableRows: TABLE_ROWS,
                   tabSelectors: TAB_SELECTOR
              });
          }
  async checkCount(containerName: string):Promise<void>{
    await this.page.waitForTimeout(5000);
      let value = await PlaywrightActions.getValue(this.page, `${this.selectors[containerName]}-total-count`);
      await expect(value).toBeGreaterThanOrEqual(0);
  }
}

export default CybersecurityDashboard;