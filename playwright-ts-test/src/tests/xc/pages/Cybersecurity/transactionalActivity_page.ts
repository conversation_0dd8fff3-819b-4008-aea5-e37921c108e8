
import { Page } from '@playwright/test';
import { NAVIGATION, TEST_IDS,TAB_SELECTOR } from '../../../../../resources/constants/pages/analytics/Cybersecurity/transactionalActivity';
import Base from "../../../../../lib/pom/Base/Base";

class TransactionalActivity extends Base {
  constructor(page: Page) {
              super(page, {
                  pageName: NAVIGATION.pageName,
                  menuTestId: NAVIGATION.menuId,
                  submenuTestId: NAVIGATION.submenuId,
                  urlSuffix: NAVIGATION.url,
                  selectors: TEST_IDS,
                  tabSelectors: TAB_SELECTOR
              });
          }
}

export default TransactionalActivity;