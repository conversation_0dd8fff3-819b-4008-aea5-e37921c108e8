import { Page, expect } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import { TABLE_SELECTOR,TABLE_ROWS,NAVIGATION, TEST_IDS } from '../../../../../resources/constants/pages/analytics/Cybersecurity/sandboxThreats';
import Base from "../../../../../lib/pom/Base/Base";
class SandboxThreats extends Base {
  
  constructor(page: Page) {
          super(page, {
              pageName: NAVIGATION.pageName,
              menuTestId: NAVIGATION.menuId,
              submenuTestId: NAVIGATION.submenuId,
              urlSuffix: NAVIGATION.url,
               selectors: TEST_IDS,
               tableRows: TABLE_ROWS,
               tableSelectors: TABLE_SELECTOR
          });
      }

}

export default SandboxThreats;