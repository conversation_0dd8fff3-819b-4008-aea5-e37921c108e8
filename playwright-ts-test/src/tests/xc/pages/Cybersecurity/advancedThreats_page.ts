import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { TABLE_SELECTOR,TABLE_ROWS,NAVIGATION, TEST_IDS } from '../../../../../resources/constants/pages/analytics/Cybersecurity/advancedThreats';
import Base from "../../../../../lib/pom/Base/Base";
class AdvancedThreats extends Base  {

  constructor(page: Page) {
            super(page, {
                pageName: NAVIGATION.pageName,
                menuTestId: NAVIGATION.menuId,
                submenuTestId: NAVIGATION.submenuId,
                urlSuffix: NAVIGATION.url,
                selectors: TEST_IDS,
                tableRows: TABLE_ROWS,
                tableSelectors: TABLE_SELECTOR
            });
        }
  
}

export default AdvancedThreats;
