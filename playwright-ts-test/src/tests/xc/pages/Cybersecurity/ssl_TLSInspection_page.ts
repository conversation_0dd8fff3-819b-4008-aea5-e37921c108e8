import { Page } from '@playwright/test';
import { TEST_IDS,NAVIGATION } from '../../../../../resources/constants/pages/analytics/Cybersecurity/ssl_TLSInspection';
import Base from "../../../../../lib/pom/Base/Base";

class SSL_TLSInspection extends Base {

  constructor(page: Page) {
            super(page, {
                pageName: NAVIGATION.pageName,
                menuTestId: NAVIGATION.menuId,
                submenuTestId: NAVIGATION.submenuId,
                urlSuffix: NAVIGATION.url,
                 selectors: TEST_IDS,
    
            });
        }
}

export default SSL_TLSInspection;