import { Page } from '@playwright/test';
import { NAVIGATION, TEST_IDS } from '../../../../../resources/constants/pages/analytics/Cybersecurity/threatLocations';
import Base from "../../../../../lib/pom/Base/Base";

class ThreatLocations  extends Base{
 
  constructor(page: Page) {
            super(page, {
                pageName: NAVIGATION.pageName,
                menuTestId: NAVIGATION.menuId,
                submenuTestId: NAVIGATION.submenuId,
                urlSuffix: NAVIGATION.url,
                 selectors: TEST_IDS,
  
            });
        }

}

export default ThreatLocations;