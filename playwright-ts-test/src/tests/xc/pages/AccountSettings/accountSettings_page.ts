import { expect, Page } from '@playwright/test';
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config as dotenvConfig } from 'dotenv';
import apiHelper from '../../../../../resources/utils/ApiHelper';
dotenvConfig();
const url: string = process.env.ONE_UI_BASE_URL ?? "";

const Selectors: Record<string, string> = {
  "Account Settings": "top-nav-options-account-popover-option-0"
};

class AccountSettings {
  private page: Page;
  private userProfileData: any;
  constructor(page: Page) {
    this.page = page;
  }

  async navigateToAccountSettings(accountSettings: string): Promise<void> {
    await this.page.getByTestId('nav-account-popover').click();
    const accountSettingLabel = this.page.getByTestId(Selectors["Account Settings"]);
    await accountSettingLabel.waitFor({ state: 'visible', timeout: 10000 });
    const labelText = await accountSettingLabel.textContent();
    console.log(`Label text: ${labelText}`);
    expect(labelText).toContain(accountSettings);
    await accountSettingLabel.click();
  }
  
  async navigateToNetworking(): Promise<void> {
    if (!url) {
      throw new Error('ONE_UI_BASE_URL environment variable is not set');
    }
    await this.page.goto(url);
    await this.page.waitForTimeout(10000);
    await this.page.getByTestId("left-nav-collapsible-nav-item-lm_analytics_networking-button").click();
    await this.page.waitForTimeout(10000);
  }
  async checkPageUrl(): Promise<void> {
    await PlaywrightActions.checkUrl(this.page, url + "analytics/networking");
  }
  async verifyLabelName(expectedLabel: string): Promise<void> {
    const label = await this.page.getByText(expectedLabel).nth(0);
    const actualLabel = await label.textContent();
    expect(actualLabel).toBe(expectedLabel);
  }
  async verifyHeaderName(expectedHeader: string): Promise<void> {
    const header = await this.page.getByTestId('accounts-setting-details');
    const actualHeader = await header.textContent();
    expect(actualHeader).toContain(expectedHeader);
  }
  async verifyPageHeaderName(expectedHeader: string): Promise<void> {
    const header = await this.page.getByRole('heading', { name: expectedHeader });
    const actualHeader = await header.textContent();
    expect(actualHeader).toBe(expectedHeader);
  }
  async cancelButton(): Promise<void> {
    await this.page.waitForTimeout(2000);
    await this.page.getByTestId('nav-pills-tab-0').click();
  }
  async fetchUserProfile(): Promise<void> {
    if (!this.userProfileData) {
      try {
        const response = await apiHelper.sendGetRequest('/private/zuxp/v1/user-profile');
        expect(response).toBeDefined();
        this.userProfileData = response;
      } catch (error) {
        console.error('Failed to fetch user profile. Ensure token is set before calling this method.');
        throw error;
      }
    }
  }
  async verifyZscalerCloud(expectedCloud: string): Promise<void> {
    await this.fetchUserProfile();
    expect(this.userProfileData.preferences?.ziaCloud).toBe(expectedCloud);
  }
  async verifyLoginID(): Promise<void> {
    await this.fetchUserProfile();
    // 1) Grab API value
    const apiLogin = this.userProfileData.user?.loginName;
    if (!apiLogin) throw new Error('API returned no loginName');
    // 2) Fetch Login ID text on the UI
    const uiLoginRaw = await this.page.getByTestId('accounts-account-details-item-0').textContent();
    const uiLogin = uiLoginRaw?.trim();
    if (!uiLogin) throw new Error('UI did not render a Login ID');
    // 3) Compare
    expect(uiLogin).toContain(apiLogin);
  }
  async verifyOrganizationID(): Promise<void> {
    await this.fetchUserProfile();
    // 1) Grab API value
    const apiOrg = this.userProfileData.tenant?.name;
    if (!apiOrg) throw new Error('API returned no tenant.name');
    // 2) Fetch Organization ID on the UI
    const uiOrgRaw = await this.page.getByTestId('accounts-account-details-item-1').textContent();
    const uiOrg = uiOrgRaw?.trim();
    if (!uiOrg) throw new Error('UI did not render an Organization ID');
    // 3) Compare
    expect(uiOrg).toContain(apiOrg);
  }
  async verifyLanguagePreference(): Promise<void> {
    await this.fetchUserProfile();
    const expectedLang = this.userProfileData.preferences.language;  // e.g. "en-us"
    const uiLangRaw = await this.page.getByTestId('accounts-setting-details-language-zselect-container-summarize-text').textContent();
    const uiLang = uiLangRaw?.trim() ?? '';
    // Map the human-readable UI text to the API code
    const uiToApiMap: Record<string, string> = {
      'English (US)':                 'en-us',
      'Chinese Taiwan Traditional':   'zh-tw',
      'French':                       'fr-fr',
      'German':                       'de-de',
      'Japanese':                     'ja-jp',
      'Spanish':                      'es-es',
    };
    const normalized = uiToApiMap[uiLang];
    if (!normalized) {
      throw new Error(`Unrecognized UI language "${uiLang}" – please update uiToApiMap`);
    }
    expect(normalized).toBe(expectedLang);
  }
  async verifyTimeZonePreference(): Promise<void> {
    await this.fetchUserProfile();
    const expectedTz = this.userProfileData.preferences.timeZone;   // e.g. "Asia/Calcutta"
    const uiTzRaw = await this.page.getByTestId('accounts-setting-details-time-zone-zselect-container-summarize-text').textContent();
    const uiTzTrimmed = uiTzRaw?.trim() ?? '';
    // e.g. "(UTC+05:30) Asia/Calcutta" → "Asia/Calcutta"
    const uiTz = uiTzTrimmed.replace(/^\(UTC[^\)]+\)\s*/, '');
    expect(uiTz).toBe(expectedTz);
  }
  async verifyDarkModePreference(): Promise<void> {
    await this.fetchUserProfile();
    const expectedTheme = this.userProfileData.preferences.darkMode;
    const shouldBeOn = expectedTheme === 'on';
    // TODO: replace with your actual locator for the "Dark Theme" label/toggle
    const darkThemeLabel = this.page.locator(
      `xpath=//label[@data-selected and .//div/span[contains(text(),"Dark Theme")]]`
    );
    // Grab the raw attribute (could be "true", "false", or null if absent)
    const rawSelected = await darkThemeLabel.getAttribute('data-selected');
    const isUiOn = rawSelected === 'true';
    expect(isUiOn).toBe(shouldBeOn);
  }
}
export default AccountSettings;