import { Page, expect } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';




const MenuIds: Record<string, string> = { 
    Administration: "nav-pills-tab-1", 
    Policies: "nav-pills-tab-2", 
    Infrastructure: "nav-pills-tab-3", 
    Logs : "nav-pills-tab-4"
};

const SubMenuIds: Record<string, string> = { 
    // "Account Management": "mm-tabs-tab-0",
    "Admin Management": "mm-tabs-tab-0", 
    "Identity": "mm-tabs-tab-1", 
    "API Configuration": "mm-tabs-tab-2", 
    "Alerts" : "mm-tabs-tab-3",

    "Digital Experience Monitoring": "mm-tabs-tab-0",
};

const VerticalMenuIds: Record<string, string> = { 
    "Role Based Access Control": "mega-menu-tabs-vertical-tab-0",
    "Audit Logs": "mega-menu-tabs-vertical-tab-1",
    "ZIdentity": "mega-menu-tabs-vertical-tab-0",
    "Legacy API": "mega-menu-tabs-vertical-tab-0",
};

class ZdxOnlyUser{

   async verifyTitle(page: any, s: string): Promise<void> {
       const title = await page.locator(`//h5[text()='${s}']`);
       expect(title).toHaveText(s);
   }

   async captureScreenshot(page: any, menu:string, tab: string): Promise<void> {
       await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
       await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);

       await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `zdxActualScreenshots/Actual_${tab}.png`, `zdxExpectedScreenshots/Expected_${tab}.png`, `zdxActualScreenshots/diff${tab}.png`);
   }

   async captureScreenshotWithVerticalMenu(page: any, menu: string, tab: string, verticalMenu: string){
        await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
        await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);
        await PlaywrightActions.verifyTextAndClick(page, VerticalMenuIds[verticalMenu], verticalMenu);

        await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `zdxActualScreenshots/Actual_${verticalMenu}.png`, `zdxExpectedScreenshots/Expected_${verticalMenu}.png`, `zdxActualScreenshots/diff${verticalMenu}.png`);
   }

   async verifyScreenshot(): Promise<void> {
       await PlaywrightActions.verifyScreenshot();
   }


}
export default new ZdxOnlyUser();