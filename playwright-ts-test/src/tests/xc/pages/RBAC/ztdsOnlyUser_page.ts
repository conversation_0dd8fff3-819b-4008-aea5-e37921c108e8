import { expect } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';

const MenuIds: Record<string, string> = { 
    Administration: "nav-pills-tab-0",
    Policies: "nav-pills-tab-1",
    Infrastructure: "nav-pills-tab-2", 
    Logs: "nav-pills-tab-3"
};

const SubMenuIds: Record<string, string> = { 
    "Account Management": "mm-tabs-tab-0",
    "Identity": "mm-tabs-tab-1", 
    "API Configuration": "mm-tabs-tab-2", 
    "Alerts": "mm-tabs-tab-3",
    "Access Control": "mm-tabs-tab-0",
    "Connectors": "mm-tabs-tab-0",
    "Locations": "",
    "Insights": "mm-tabs-tab-0"
};

const VerticalMenuIds: Record<string, string> = { 
    "ZIdentity": "mega-menu-tabs-vertical-tab-0",
    "Internet & SaaS": "",
    "Segmentation": "mega-menu-tabs-vertical-tab-0",
    "Edge": "mega-menu-tabs-vertical-tab-0",
};

class ZTDSOnlyUser{

    async verifyOperationalTab(page: any){
        await page.getByRole('button', { name: 'Operational icon Operational' }).isDisabled();
        await page.locator('(//i[@aria-label="Disabled icon"])[4]').click();
        await page.waitForTimeout(2000);
        const hoverText = await page.locator('//div[@class="flex typography-paragraph1 text-semantic-content-base-primary"]');
        expect(hoverText).toHaveText('This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.');
    }

    async verifySwitchToExistingReports(page: any){
        expect(await page.getByTestId("left-nav-collapsible-nav-item-menudevicesegmentation-button")).toBeVisible();
        expect(await page.getByTestId("switch-to-existing-reports-title").textContent()).toBe("Switch to Existing Reports");
        await expect(page.getByTestId("switch-to-existing-reports-toggle")).not.toBeChecked();
        await page.getByTestId("switch-to-existing-reports-toggle").click();
        expect(await page.getByTestId("left-nav-collapsible-nav-item-menudevicesegmentation-button")).toBeVisible();
        await expect(page.getByTestId("switch-to-existing-reports-toggle")).not.toBeChecked();
    }

    async captureScreenshot(page: any, menu:string, tab: string): Promise<void> {
           await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
           await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);
    
           await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `ztdsActualScreenshots/Actual_${tab}.png`, `ztdsExpectedScreenshots/Expected_${tab}.png`, `ztdsActualScreenshots/diff${tab}.png`);
       }
    
       async captureScreenshotWithVerticalMenu(page: any, menu: string, tab: string, verticalMenu: string){
            await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
            await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);
            await PlaywrightActions.verifyTextAndClick(page, VerticalMenuIds[verticalMenu], verticalMenu);
    
            await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `ztdsActualScreenshots/Actual_${verticalMenu}.png`, `ztdsExpectedScreenshots/Expected_${verticalMenu}.png`, `ztdsActualScreenshots/diff${verticalMenu}.png`);
       }
    
       async verifyScreenshot(): Promise<void> {
           await PlaywrightActions.verifyScreenshot();
       }
}

export default new ZTDSOnlyUser();