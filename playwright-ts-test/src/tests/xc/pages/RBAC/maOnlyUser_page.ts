import { expect } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import { MENU_IDS as menuIds, SUBMENU_IDS as submenuIds} from '../../../../../resources/constants/navigation/leftNav';

const Selectors: Record<string, string> = {
    "User Devices": "ops-devices-user-devices-value",
    "User Devices Graph": "ops-devices-user-devices",
    "Version Distribution Graph": "ops-devices-version-dist",
    "Device Operating System Graph": "ops-devices-device-os",
    "Device Model Graph": "ops-devices-device-model",
    "Device State Graph": "ops-devices-device-state"
};

const Widgets: Record<string, string> = {
    "Traffic in my Organization": "traf-in-org",
    "Internet Traffic Distribution": "traffic-distribution",
    "Top Locations sending Internet Traffic to Zscaler" : "primary-traffic-sources",
    "Top Zscaler Data Centers Used": "connectivity-center-data-centers",
    "Devices Discovered": "devices-discovered"
};

const WidgetsWithAccessRestricted: Record<string, string> = {
    "Traffic in my Organization": "networking-traf-in-org-traffic-sum-internet",
    "Internet Traffic Distribution": "networking-traffic-distribution",
    "Top Locations sending Internet Traffic to Zscaler" : "networking-primary-traffic-sources",
    "Top Zscaler Data Centers Used": "networking-connectivity-center-data-centers",
    "Devices Discovered": "networking-devices-discovered"
};

const Tabs: Record<string, string> = {
    "Devices": "lm_analytics_operational_operational-button",
    "Appliances": "lm_analytics_operational_operational-button"
};

const MenuIds: Record<string, string> = { 
    Administration: "nav-pills-tab-1", 
    Policies: "nav-pills-tab-2", 
    Infrastructure: "nav-pills-tab-3", 
};

const SubMenuIds: Record<string, string> = { 
    // "Account Management": "mm-tabs-tab-0",
    "Admin Management": "mm-tabs-tab-0", 
    "Identity": "mm-tabs-tab-1", 
    "Entitlements": "mm-tabs-tab-2", 
    "API Configuration": "mm-tabs-tab-3", 
    "Common Configuration": "mm-tabs-tab-0",
    "Locations": "mm-tabs-tab-0",
    "Connectors": "mm-tabs-tab-1",
    "Common Resources": "mm-tabs-tab-2"
};

const VerticalMenuIds: Record<string, string> = { 
    "Role Based Access Control": "mega-menu-tabs-vertical-tab-0",
    "Audit Logs": "mega-menu-tabs-vertical-tab-1",
    "ZIdentity": "mega-menu-tabs-vertical-tab-0",
    "Private Access": "mega-menu-tabs-vertical-tab-1",
    "Legacy API": "mega-menu-tabs-vertical-tab-0",
    "Client": "mega-menu-tabs-vertical-tab-0",
    "Resources": "mega-menu-tabs-vertical-tab-0",
    "Application": "mega-menu-tabs-vertical-tab-0",
    "Deployment": "mega-menu-tabs-vertical-tab-1"
};

class MaOnlyUser {

   async viewWidgetHeading(page: any, title: string): Promise<void> {
       await CommonFunctions.verifyTextByTestId(page, `networking-${Widgets[title]}-card-title`, title);
   }

      async isNotVisible(page: any, menu: string, submenu?: string): Promise<void> {
        submenu ? await CommonFunctions.isNotVisible(page, menuIds[menu], submenuIds[submenu]) : await CommonFunctions.isNotVisible(page, menuIds[menu]);
   }

   async verifyAccessRestrictedText(page: any, s: string, title: string): Promise<void> {
       await page.waitForTimeout(5000);
       const accessRestricted = await page.getByTestId(`subscription-required-analytic-state-${WidgetsWithAccessRestricted[title]}`).getByText(s);
       expect(accessRestricted).toHaveText(s); 
       await page.getByTestId(`subscription-required-analytic-state-${WidgetsWithAccessRestricted[title]}`).getByLabel('Lock icon').isVisible();
       await page.getByTestId(`subscription-required-analytic-state-${WidgetsWithAccessRestricted[title]}`).getByText('This is either due to your').isVisible();
   }

   async verifyConnectorActivityTab(page: any): Promise<void> {
       await page.getByTestId('left-nav-collapsible-nav-item-0-sub-menu-0-lm_analytics_networking_connector_activity-button').isDisabled();
       await page.locator('(//i[@aria-label="Disabled icon"])[1]').click();
       await page.waitForTimeout(2000);
       const hoverText = await page.locator('//div[@class="flex typography-paragraph1 text-semantic-content-base-primary"]');
       expect(hoverText).toHaveText('This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.');
   }

   async verifyDigitalExperienceTab(page: any): Promise<void> {
       await page.getByTestId('left-nav-collapsible-nav-item-1-lm_analytics_de_de-button').isDisabled();
       await page.locator('(//i[@aria-label="Disabled icon"])[2]').click();
       await page.waitForTimeout(2000);
       const hoverText2 = await page.locator('//div[@class="flex typography-paragraph1 text-semantic-content-base-primary"]');
       expect(hoverText2).toHaveText('This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.');
   }

   async verifyCybersecurityTab(page: any): Promise<void> {
       await page.getByTestId('left-nav-collapsible-nav-item-2-lm_analytics_cs_cs-button').isDisabled();
       await page.locator('(//i[@aria-label="Disabled icon"])[3]').click();
       await page.waitForTimeout(2000);
       const hoverText3 = await page.locator('//div[@class="flex typography-paragraph1 text-semantic-content-base-primary"]');
       expect(hoverText3).toHaveText('This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.');
   }

   async verifyAppliancesTab(page: any): Promise<void> {
       await page.getByRole('button', { name: 'Operational icon Operational' }).click();
       await page.getByTestId('layout-collapsible-side-panel-nav-item-operational-sub-menuitem-1').isDisabled();
       await page.locator('(//i[@aria-label="Disabled icon"])[4]').click();
       await page.waitForTimeout(2000);
       const hoverText4 = await page.locator('//div[@class="flex typography-paragraph1 text-semantic-content-base-primary"]');
       expect(hoverText4).toHaveText('This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.');
   }
 
//    async clickOperationalDevicesTab(page: any, tab: string): Promise<void> {
//        await page.waitForTimeout(5000); 
//        await PlaywrightActions.waitAndClickByTestId(page, `left-nav-collapsible-nav-item-${Tabs[tab]}`);
//        await page.waitForTimeout(5000); 
//        await page.getByTestId("left-nav-collapsible-nav-item-sub-menu-lm_analytics_operational_devices-button").click();
//        await page.waitForTimeout(10000); 
//        const devices = await page.getByTestId('analytics-layout-breadcrumb').getByText('Devices');
//        expect(devices).toHaveText('Devices');
//    }

async clickOperationalDevicesTab(page: any, tab: string): Promise<void> {
    // Wait and click on the collapsible nav tab
    await page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, { timeout: 15000 });
    await page.getByTestId("nav-pills-tab-0").click();
    await page.waitForTimeout(2000);
    await page.mouse.click(300, 500);
    const navItemSelector = `[data-testid="left-nav-collapsible-nav-item-${Tabs[tab]}"]`;
    await page.waitForSelector(navItemSelector, { state: 'visible', timeout: 25000 });
    await PlaywrightActions.waitAndClickByTestId(page, `left-nav-collapsible-nav-item-${Tabs[tab]}`);

    // Wait and click on the submenu button
    const subMenuSelector = '[data-testid="left-nav-collapsible-nav-item-sub-menu-lm_analytics_operational_devices-button"]';
    await page.waitForSelector(subMenuSelector, { state: 'visible', timeout: 25000 });
    await page.getByTestId("left-nav-collapsible-nav-item-sub-menu-lm_analytics_operational_devices-button").click();

    // Wait for breadcrumb to have 'Devices' text
    const breadcrumb = await page.getByTestId('analytics-layout-breadcrumb').getByText('Devices');
    await expect(breadcrumb).toHaveText('Devices', { timeout: 25000 });
}


   async verifyUserDevicesTitle(page: any, s: string): Promise<void> {
    await page.getByTestId('tooltip-icon-ops-devices-user-devices').click();
    const tooltip = await page.locator("//div[text()='This data represent the amount of registered devices currently available in your Organization']");
    expect(tooltip).toHaveText('This data represent the amount of registered devices currently available in your Organization');
    await page.waitForTimeout(3000);

    await PlaywrightActions.waitAndClickByTestId(page, 'analytics-layout-modal-pill-grp');
    const userDevices = await page.getByTestId('ops-devices-user-devices-card-title');
    expect(userDevices).toHaveText(s);

    const userDevicesCount= await PlaywrightActions.waitAndGetByTestId(page, `${Selectors[s]}`);
    const valueText = (await userDevicesCount.textContent()) ?? "";
    console.log('Number of Transactions:', valueText);
    
    CommonFunctions.multiplierBasedOnSuffix(valueText);
   }

   async verifyDevicesGraph(page: any, title: string, graphType: string): Promise<void> {
        const selector = Selectors[title];
        await PlaywrightActions.isWidgetLoaded(page, selector, graphType);
    }

   async verifyUserDevicesTab(page: any){
    await page.getByTestId('ops-devices-user-devices-stacked-bar-chart').locator('canvas').nth(1).click({
        position: {
          x: 415,
          y: 34
        }
      });
    const deviceDistribution = await page.getByText('Device Distribution');
    expect(deviceDistribution).toHaveText('Device Distribution');
    await page.getByTestId('ops-devices-user-devices-tabs-tab-label-0').click();
    const deviceOperatingSystem = await page.getByTestId('deviceOS-header-cell');
    expect(deviceOperatingSystem).toHaveText('Device Operating System');
    await page.getByTestId('ops-devices-user-devices-tabs-tab-label-1').click();
    const deviceOperatingSystemM = await page.getByTestId('deviceOS-header-cell');
    expect(deviceOperatingSystemM).toHaveText('Device Operating System');
    // await page.getByTestId('ops-devices-user-devices-tabs-tab-label-2').click();
    // const deviceOperatingSystemA = await page.getByTestId('deviceOS-header-cell');
    // expect(deviceOperatingSystemA).toHaveText('Device Operating System');
    await page.getByTestId('ops-devices-user-devices-drawer-overlay-close').click();
}

   async verifyTitle(page: any, s: string): Promise<void> {
       const title = await page.locator(`//h5[text()='${s}']`);
       expect(title).toHaveText(s);
   }

   async captureScreenshot(page: any, menu:string, tab: string): Promise<void> {
       await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
       await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);

       await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `maActualScreenshots/Actual_${tab}.png`, `maExpectedScreenshots/Expected_${tab}.png`, `maActualScreenshots/diff${tab}.png`);
   }

   async captureScreenshotWithVerticalMenu(page: any, menu: string, tab: string, verticalMenu: string){
        await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
        await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);
        await PlaywrightActions.verifyTextAndClick(page, VerticalMenuIds[verticalMenu], verticalMenu);

        await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `maActualScreenshots/Actual_${verticalMenu}.png`, `maExpectedScreenshots/Expected_${verticalMenu}.png`, `maActualScreenshots/diff${verticalMenu}.png`);
   }

   async verifyScreenshot(): Promise<void> {
       await PlaywrightActions.verifyScreenshot();
   }

}

export default new MaOnlyUser();