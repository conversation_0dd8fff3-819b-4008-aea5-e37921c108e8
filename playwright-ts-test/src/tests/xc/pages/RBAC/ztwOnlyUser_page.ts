import { expect, Page } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import fs from 'fs';
import pixelmatch from 'pixelmatch';
import { PNG } from 'pngjs';
let numDiffPixels : number;

const Selectors: Record<string, string> = {
    "Deployment Status Graph": "ops-appliances-deployment-status",
    "Active Status Graph": "ops-appliances-active-status"
};

const TableSelectors: Record<string, string> = {
    "Deployed Connectors": "ops-appliances-geo-table",
};
  
const TableRows: Record<string, Record<string, string>> = {
    "Deployed Connectors": {
      "ecName": "Name",
      "type": "Type",
      "group": "Group",
      "location": "Location",
      "status": "Status"
    }
};

const MenuIds: Record<string, string> = { 
    Administration: "nav-pills-tab-1",
    Policies: "nav-pills-tab-2",
    Infrastructure: "nav-pills-tab-3", 
    Logs: "nav-pills-tab-4"
};

const SubMenuIds: Record<string, string> = { 
    // "Account Management": "mm-tabs-tab-0",
    "Admin Management": "mm-tabs-tab-0",
    "Identity": "mm-tabs-tab-1", 
    "API Configuration": "mm-tabs-tab-2",
    // "Access Control": "",
    "Locations": "mm-tabs-tab-0",
    "Connectors": "mm-tabs-tab-1",
    "Common Resources": "mm-tabs-tab-2",
    "Insights": "mm-tabs-tab-0"
};

const VerticalMenuIds: Record<string, string> = { 
    "Administrator Management": "mega-menu-tabs-vertical-tab-0",
    "Role Based Access Control": "mega-menu-tabs-vertical-tab-1",
    "Audit Logs": "mega-menu-tabs-vertical-tab-2",
    "ZIdentity": "mega-menu-tabs-vertical-tab-0",
    "Legacy API": "mega-menu-tabs-vertical-tab-0",
    "Internet & SaaS": "",
    "Client": "mega-menu-tabs-vertical-tab-0",
    "Edge": "mega-menu-tabs-vertical-tab-1",
    "Cloud": "mega-menu-tabs-vertical-tab-2",
    "Gateways": "mega-menu-tabs-vertical-tab-0",
    "Application": "mega-menu-tabs-vertical-tab-1",
    "Deployment": "mega-menu-tabs-vertical-tab-2"
};

class ZTWOnlyUser {
    private readonly applicationsFilters: string[];
    constructor() {
        this.applicationsFilters= [
            "Type",
            "Locations"
          ];
    }

    async verifyAccessRestrictedTextWithoutBCC(page: Page, expectedText: string): Promise<void> {
        await page.waitForTimeout(5000);
        await expect(page.getByTestId('card-title')).toBeVisible();

        const testIds = [
            'subscription-required-analytic-state-networking-traf-in-org-traffic-sum-internet',
            'subscription-required-analytic-state-traffic-distribution',
            'subscription-required-analytic-state-primary-traffic-sources',
            'subscription-required-analytic-state-data-center',
            'subscription-required-analytic-state-devices-discovered'
        ];

        for (const testId of testIds) {
            const accessRestricted = page.getByTestId(testId).getByText('Access Restricted');
            await expect(accessRestricted).toHaveText(expectedText);
            await expect(page.getByTestId(testId).getByLabel('Lock icon')).toBeVisible();
            await expect(page.getByTestId(testId).getByText('This is either due to your')).toBeVisible();
        }

        await page.getByTestId('connectivity-center-tabs-tab-label-0').click();
    }

    async verifyCybersecurityTabZTW(page: Page): Promise<void> {
        await expect(page.getByRole('button', { name: 'Cybersecurity icon' })).toBeDisabled();
        await page.locator('(//i[@aria-label="Disabled icon"])[2]').click();
        await page.waitForTimeout(2000);

        const hoverText3 = page.locator('//div[@class="flex typography-paragraph1 text-semantic-content-base-primary"]');
        await expect(hoverText3).toHaveText(
            'This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.'
        );
    }

    async verifyDevicesTab(page: Page): Promise<void> {
        await page.getByRole('button', { name: 'Operational icon Operational' }).click();
        await page.getByTestId('layout-collapsible-side-panel-nav-item-operational-sub-menuitem-0').isDisabled();
        await page.locator('(//i[@aria-label="Disabled icon"])[3]').click();
        await page.waitForTimeout(2000);
        const hoverText4 = await page.locator('//div[@class="flex typography-paragraph1 text-semantic-content-base-primary"]');
        expect(hoverText4).toHaveText('This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.');
    }

    async verifyBranchAndCloudConnectorsGraph(page: Page): Promise<void> {
        const canvasLocator = await page.locator('canvas').nth(1);
        // Ensure the canvas exists and is visible
        await expect(canvasLocator).toBeVisible();
        // await CommonFunctions.canvasHasNonZeroSize(canvasLocator, 432, 67);
    }

    async clickViewConnectorActivityButton(page: any) {
        await page.getByTestId('branch-cloud-connector-footer-navLink-z-button-link').isVisible();
        await page.getByTestId('branch-cloud-connector-footer-navLink-z-button-link').click();
    }

    async verifyConnectorActivityScreen(page: Page, s: string) {
        await page.waitForTimeout(4000);
        const connectorActivity = await page.getByTestId('analytics-layout-breadcrumb').getByText('Connector Activity');
        await expect(connectorActivity).toHaveText(s);
        await page.getByTestId('layout-collapsible-side-panel-nav-item-networking-sub-menuitem-0').click();
        await page.getByTestId('analytics-layout-breadcrumb').getByText('Connector Activity').isVisible();
    }

    async clickConnectorTab(page: any) {
        await page.getByTestId('layout-collapsible-side-panel-nav-item-networking-sub-menuitem-0').click();
        await page.waitForTimeout(2000);
        const connectorActivity = await page.getByTestId('analytics-layout-breadcrumb').getByText('Connector Activity');
        await expect(connectorActivity).toHaveText('Connector Activity');
    }

    async verifyTitle(page: Page, s: string) {
        await page.getByTestId('analytics-layout-breadcrumb').getByText('Connector Activity').isVisible();
        const title = page.locator(`//h5[text()="${s}"]`);
        await expect(title).toHaveText(s);
    }

    async verifyGraph(page: Page, title: string, graphType: string): Promise<void> {
        const selector = Selectors[title];
        await PlaywrightActions.isWidgetLoaded(page, selector, graphType);
    }

    async verifyAppliancesTable(page: Page, title: string, hoverable: boolean = true): Promise<void>{
        await page.waitForTimeout(5000);
        await PlaywrightActions.checkTable(page, `${TableSelectors[title]}-z-table`, TableRows[title], hoverable);
    }

    async checkAppliancesFilters(page: any): Promise<void> {
        await PlaywrightActions.checkConnectorFilters(page, "analytics-layout", this.applicationsFilters);
    }

    async verifyTrafficVolumeAcrossService( page: any ) {
        const canvasLocator = page.getByTestId('branch-connector-traffic-vol-donut-chart').locator('canvas').nth(1);
        await expect(canvasLocator).toBeVisible();
        // await CommonFunctions.canvasHasNonZeroSize(canvasLocator, 155, 296);
    }

    async verifySessionAcrossService( page: Page ) {
        const canvasLocator = page.getByTestId('branch-connector-session-donut-chart').locator('canvas').nth(1);
        await expect(canvasLocator).toBeVisible();
        // await CommonFunctions.canvasHasNonZeroSize(canvasLocator, 383, 165);
    }

    async searchInput( page: Page ) {
        await page.getByTestId('branch-geo-branch-geo-table-search-bar-input').click();
        await page.getByTestId('branch-geo-branch-geo-table-search-bar-input').fill('linux');
        await page.getByTestId('z-data-table-row-branch-geo-branch-geo-table-ecName-0').getByTestId('col-item');
    }

    async switchToCloudConnectors(page: Page, s: string) {
        const cloudConnector = page.getByTestId('connectivity-center-tabs-tab-label-1');
        await expect(cloudConnector).toHaveText(s);
        await cloudConnector.click();
    }

    async verifyTrafficVolumeAcrossServiceCloudConnector(page: any){
        const noResultFound = await PlaywrightActions.waitAndGetByTestId(page, 'empty-card-analytic-state-cloud-connector-traffic-volume');
            
            if(await noResultFound.isVisible()){
                expect(noResultFound).toContainText("No Results Found");
                await page.getByTestId('empty-card-analytic-state-cloud-connector-traffic-volume').getByLabel('Search icon').isVisible();
            }
             else{
                const canvasLocator = await page.getByTestId('cloud-connector-traffic-volume-donut-chart').locator('canvas').nth(1);
                // Ensure the canvas exists and is visible
                await expect(canvasLocator).toBeVisible();
                // await CommonFunctions.canvasHasNonZeroSize(canvasLocator, 345, 96);
            }
    }

    async verifySessionAcrossServiceCloudConnector(page: any){
        const noResultFound = await PlaywrightActions.waitAndGetByTestId(page, 'empty-card-analytic-state-cloud-connector-session');
            
            if(await noResultFound.isVisible()){
                expect(noResultFound).toContainText("No Results Found");
                await page.getByTestId('empty-card-analytic-state-cloud-connector-session').getByLabel('Search icon').isVisible();
            }
             else{
                const canvasLocator = await page.getByTestId('cloud-connector-session-donut-chart').locator('canvas').nth(1);
                // Ensure the canvas exists and is visible
                await expect(canvasLocator).toBeVisible();
                // await CommonFunctions.canvasHasNonZeroSize(canvasLocator, 383, 165);
            }
    }

    async verifyConnectorTableCloudConnector(page: any){
        const column1 = await page.getByTestId('ecName-header-cell');
        expect(column1).toHaveText('Name');

        const column2 = await page.getByTestId('group-header-cell');
        expect(column2).toHaveText('Group');

        const column3= await page.getByTestId('location-header-cell');
        expect(column3).toHaveText('Location');

        const column4 = await page.getByTestId('geoLocation-header-cell');
        expect(column4).toHaveText('Geolocation');

        const column5 = await page.getByTestId('autoScale-header-cell');
        expect(column5).toHaveText('Auto Scaling');

        const column6 = await page.getByTestId('status-header-cell');
        expect(column6).toHaveText('Status');

        const column7 = await page.getByTestId('vmSize-header-cell');
        expect(column7).toHaveText('VM Size');

        await page.getByTestId('z-data-table-row-cloud-geo-cloud-geo-table-ecName-0').getByTestId('col-item').click();
        await page.getByTestId('cloud-geo-cloud-geo-table-drawer-overlay').getByText('zs-cc-vpc-0ae244138c7eb44cf-eu-north-1a-VM-YI3Zz').isVisible();
        await page.getByTestId('cloud-geo-cloud-geo-table-drawer-overlay').getByText('Cloud Connector').isVisible();
        await page.getByTestId('connector-detail-tabs-tab-label-0').isVisible();
        await page.getByTestId('connector-detail-tabs-tab-label-1').click();
        await page.getByTestId('connector-detail-tabs-tab-label-2').click();
        await page.getByTestId('cloud-geo-cloud-geo-table-drawer-overlay-close').click();
    }

    async navigateToAppliances(page: any){
        await page.getByTestId("left-nav-collapsible-nav-item-lm_analytics_operational_operational-expansion-expansion-button").click();
        await page.waitForTimeout(5000); 
        await page.getByTestId('left-nav-collapsible-nav-item-sub-menu-lm_analytics_operational_appliances-button').click();
        await page.waitForTimeout(10000); 
        const Appliances = await page.getByTestId('analytics-layout-breadcrumb').getByText('Appliances');
        expect(Appliances).toHaveText('Appliances');
    }

    async verifyConnectorTableAppliances(page: any){
        const column1 = await page.getByTestId('ecName-header-cell');
        expect(column1).toHaveText('Name');

        const column2 = await page.getByTestId('type-header-cell');
        expect(column2).toHaveText('Type');

        const column3 = await page.getByTestId('group-header-cell');
        expect(column3).toHaveText('Group');

        const column4= await page.getByTestId('location-header-cell');
        expect(column4).toHaveText('Location');

        const column5 = await page.getByTestId('status-header-cell');
        expect(column5).toHaveText('Status');

        await page.getByTestId('ops-appliances-cloud-geo-cloud-geo-table-search-bar-input').click();
        await page.getByTestId('ops-appliances-cloud-geo-cloud-geo-table-search-bar-input').fill('linux');
        await page.getByText('linux-group-VM-QMceA').click();
    }

    async verifyDevicesAccessRestricted(page:any , s: string){
        const userDevices = await page.getByTestId('ops-devices-devices-os-dis-card-title');
        expect(userDevices).toHaveText('User Devices');
        const accessRestricted1 = await page.getByTestId('subscription-required-analytic-state-ops-devices-devices-os-dis').getByText('Access Restricted');
        expect(accessRestricted1).toHaveText(s);
        await page.getByTestId('subscription-required-analytic-state-ops-devices-devices-os-dis').getByLabel('Lock icon').isVisible();
        await page.getByTestId('subscription-required-analytic-state-ops-devices-devices-os-dis').getByText('This is either due to your').isVisible();

        const versionDistribution = await page.getByTestId('ops-devices-version-dist-card-title');
        expect(versionDistribution).toHaveText('Version Distribution');
        const accessRestricted2 = await page.getByTestId('subscription-required-analytic-state-ops-devices-version-dist').getByText('Access Restricted');
        expect(accessRestricted2).toHaveText(s);
        await page.getByTestId('subscription-required-analytic-state-ops-devices-version-dist').getByLabel('Lock icon').isVisible();
        await page.getByTestId('subscription-required-analytic-state-ops-devices-version-dist').getByText('This is either due to your').isVisible();

        const deviceOperatingSystem = await page.getByTestId('ops-devices-device-os-card-title');
        expect(deviceOperatingSystem).toHaveText('Device Operating System');
        const accessRestricted3 = await page.getByTestId('subscription-required-analytic-state-ops-devices-device-os').getByText('Access Restricted');
        expect(accessRestricted3).toHaveText(s);
        await page.getByTestId('subscription-required-analytic-state-ops-devices-device-os').getByLabel('Lock icon').isVisible();
        await page.getByTestId('subscription-required-analytic-state-ops-devices-device-os').getByText('This is either due to your').isVisible();

        const deviceModel = await page.getByTestId('ops-devices-device-model-card-title');
        expect(deviceModel).toHaveText('Device Model');
        const accessRestricted4 = await page.getByTestId('subscription-required-analytic-state-ops-devices-device-model').getByText('Access Restricted');
        expect(accessRestricted4).toHaveText(s);
        await page.getByTestId('subscription-required-analytic-state-ops-devices-device-model').getByLabel('Lock icon').isVisible();
        await page.getByTestId('subscription-required-analytic-state-ops-devices-device-model').getByText('This is either due to your').isVisible();

        const deviceState = await page.getByTestId('ops-devices-device-state-card-title');
        expect(deviceState).toHaveText('Device State');
        const accessRestricted5 = await page.getByTestId('subscription-required-analytic-state-ops-devices-device-state').getByText('Access Restricted');
        expect(accessRestricted5).toHaveText(s);
        await page.getByTestId('subscription-required-analytic-state-ops-devices-device-state').locator('div').isVisible();
        await page.getByTestId('subscription-required-analytic-state-ops-devices-device-state').getByText('This is either due to your').isVisible();
    }

     async captureScreenshot(page: any, menu:string, tab: string): Promise<void> {
        await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
        await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);
        
        await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `ztwActualScreenshots/Actual_${tab}.png`, `ztwExpectedScreenshots/Expected_${tab}.png`, `ztwActualScreenshots/diff${tab}.png`);
    }
        
    async captureScreenshotWithVerticalMenu(page: any, menu: string, tab: string, verticalMenu: string){
        await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
        await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);
        await PlaywrightActions.verifyTextAndClick(page, VerticalMenuIds[verticalMenu], verticalMenu);
        
        await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `ztwActualScreenshots/Actual_${verticalMenu}.png`, `ztwExpectedScreenshots/Expected_${verticalMenu}.png`, `ztwActualScreenshots/diff${verticalMenu}.png`);
    }
        
    async verifyScreenshot(): Promise<void> {
        await PlaywrightActions.verifyScreenshot();
    }
}

export default new ZTWOnlyUser();
