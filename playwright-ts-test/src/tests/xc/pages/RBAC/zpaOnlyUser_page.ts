import { expect } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';


const MenuIds: Record<string, string> = { 
    Administration: "nav-pills-tab-1", 
    Policies: "nav-pills-tab-2", 
    Infrastructure: "nav-pills-tab-3", 
    Logs : "nav-pills-tab-4"
};

const SubMenuIds: Record<string, string> = { 
    "Account Management": "mm-tabs-tab-0",
    "Admin Management": "mm-tabs-tab-1", 
    "Identity": "mm-tabs-tab-2", 
    "API Configuration": "mm-tabs-tab-3", 
    "Alerts" : "mm-tabs-tab-4",
    "Backup & Restore":"mm-tabs-tab-5",

    "Access Control": "mm-tabs-tab-0",
    "Cybersecurity": "mm-tabs-tab-1", 

    "Private Access":"mm-tabs-tab-0",

    "Insights": "mm-tabs-tab-0",
    "Log Streaming": "mm-tabs-tab-1"
};

const VerticalMenuIds: Record<string, string> = { 
    "Role Based Access Control": "mega-menu-tabs-vertical-tab-0",
    "Audit Logs": "mega-menu-tabs-vertical-tab-1",

    "ZIdentity": "mega-menu-tabs-vertical-tab-0",
    "Private Access": "mega-menu-tabs-vertical-tab-1",

    "Legacy API": "mega-menu-tabs-vertical-tab-0",


    "Private Applications":"mega-menu-tabs-vertical-tab-0",
    "Clientless":"mega-menu-tabs-vertical-tab-1",

    "Inline Security":"mega-menu-tabs-vertical-tab-0",

    "Component":"mega-menu-tabs-vertical-tab-0",
    "Business Continuity":"mega-menu-tabs-vertical-tab-1",
    "Client Connector Policies":"mega-menu-tabs-vertical-tab-2",
};

class ZpaOnlyUser{

   async captureScreenshot(page: any, menu:string, tab: string): Promise<void> {
       await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
       await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);

       await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `zpaActualScreenshots/Actual_${tab}.png`, `zpaExpectedScreenshots/Expected_${tab}.png`, `zpaActualScreenshots/diff${tab}.png`);
   }

   async captureScreenshotWithVerticalMenu(page: any, menu: string, tab: string, verticalMenu: string){
        await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
        await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);
        await PlaywrightActions.verifyTextAndClick(page, VerticalMenuIds[verticalMenu], verticalMenu);

        await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `zpaActualScreenshots/Actual_${verticalMenu}.png`, `zpaExpectedScreenshots/Expected_${verticalMenu}.png`, `zpaActualScreenshots/diff${verticalMenu}.png`);
   }

    async verifyScreenshot(): Promise<void> {
      await PlaywrightActions.verifyScreenshot();
    }

    async verifyConnectorActivityTab(page: any): Promise<void> {
        // await page.getByTestId('left-nav-collapsible-nav-item-0-sub-menu-0-lm_analytics_networking_connector_activity-button').isDisabled();
        await page.waitForTimeout(2000);

        await page.locator('(//i[@aria-label="Disabled icon"])[1]').click();
        await page.waitForTimeout(2000);
        const hoverText = await page.locator('//div[@class="flex typography-paragraph1 text-semantic-content-base-primary"]');
        expect(hoverText).toHaveText('This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.');
    }

    async disableSTER(page: any): Promise<void> {
        await expect(page.getByTestId("switch-to-existing-reports-toggle")).toBeChecked();
        await page.getByTestId("switch-to-existing-reports-toggle").click();
        await page.waitForTimeout(2000);
        await expect(page.getByTestId("switch-to-existing-reports-toggle")).not.toBeChecked();
    }

}
export default new ZpaOnlyUser();