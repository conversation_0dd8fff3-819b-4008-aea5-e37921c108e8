import { expect, Page } from '@playwright/test';
import CommonFunctions from '../../../../../resources/utils/CommonFunctions';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';

const MenuIds: Record<string, string> = { 
    Administration: "nav-pills-tab-0", 
    Infrastructure: "nav-pills-tab-1", 
};

const SubMenuIds: Record<string, string> = { 
    "Account Management": "mm-tabs-tab-0",
    "Admin Management": "mm-tabs-tab-1", 
    "Identity": "mm-tabs-tab-2", 
    "API Configuration": "mm-tabs-tab-3", 
    "Locations": "mm-tabs-tab-0",
};

const VerticalMenuIds: Record<string, string> = { 
    "Administrator Management": "mega-menu-tabs-vertical-tab-0",
    "Role Based Access Control": "mega-menu-tabs-vertical-tab-1",
    "Audit Logs": "mega-menu-tabs-vertical-tab-2",
    "ZIdentity": "mega-menu-tabs-vertical-tab-0",
    "OneAPI": "mega-menu-tabs-vertical-tab-0",
};

class ZIDOnlyUser{

    async verifyOperationalTab(page: Page){
        await page.getByRole('button', { name: 'Operational icon Operational' }).isDisabled();
        await page.locator('(//i[@aria-label="Disabled icon"])[4]').click();
        await page.waitForTimeout(2000);
        const hoverText = await page.locator('//div[@class="flex typography-paragraph1 text-semantic-content-base-primary"]');
        expect(hoverText).toHaveText('This is either due to your role not having permissions for this content or your organization is not licensed for this feature. Please contact your Zscaler administrator for further assistance.');
    }

    async captureScreenshot(page: Page, menu:string, tab: string): Promise<void> {
           await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
           await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);
    
           await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `zidActualScreenshots/Actual_${tab}.png`, `zidExpectedScreenshots/Expected_${tab}.png`, `zidActualScreenshots/diff${tab}.png`);
    }
    
    async captureScreenshotWithVerticalMenu(page: Page, menu: string, tab: string, verticalMenu: string){
         await PlaywrightActions.verifyTextAndHover(page, MenuIds[menu], menu);
         await PlaywrightActions.verifyTextAndClick(page, SubMenuIds[tab], tab);
         await PlaywrightActions.verifyTextAndClick(page, VerticalMenuIds[verticalMenu], verticalMenu);
         await PlaywrightActions.captureAndCompareScreenshot(page, '//div[@data-testid="mega-menu-content"]', `zidActualScreenshots/Actual_${verticalMenu}.png`, `zidExpectedScreenshots/Expected_${verticalMenu}.png`, `zidActualScreenshots/diff${verticalMenu}.png`);
    }
    
    async verifyScreenshot(): Promise<void> {
        await PlaywrightActions.verifyScreenshot();
    }
    
    async checkIsMenuVisible(page: Page, menu: string): Promise<void> {
         expect(await page.getByTestId("nav-pills-tab-0").textContent()).not.toBe(menu);
    }
}

export default new ZIDOnlyUser();