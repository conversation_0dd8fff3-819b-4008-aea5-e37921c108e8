import { Page, expect } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";

class ThemePage {
  private page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async navigateToProfileSettings(accountSettings: string): Promise<void> {
    await this.page.getByTestId('nav-account-popover').click();

    // Wait for the Account Settings label to be visible
    const accountSettingLabel = this.page.getByTestId('top-nav-options-account-popover-option-0');
    await accountSettingLabel.waitFor({ state: 'visible', timeout: 10000 });

    // Extract the text content of the label
    const labelText = await accountSettingLabel.textContent();
    console.log(`Label text: ${labelText}`);

    // Assert that the text content matches the expected value
    expect(labelText?.trim()).toBe(accountSettings);

    // Click the Account Settings label
    await accountSettingLabel.click();
}

async selectDarkOption(darkThemeOption: string): Promise<void> {
  const themePage = await this.page.getByText('Theme', { exact: true });
  const themeText = await themePage.textContent();

  expect(themeText?.trim()).toBe('Theme');

  // Click the "Light Theme" button and wait for the "Dark Theme" button to be visible
  const lightThemeLocator = this.page.locator('//div/span[contains(text(),"Light Theme")]');
  await lightThemeLocator.waitFor({ state: 'visible', timeout: 10000 });
  await lightThemeLocator.click();

  // Click the "Dark Theme" button
  const darkThemeLocator = this.page.locator(`//div/span[contains(text(),"${darkThemeOption}")]`);
  await darkThemeLocator.waitFor({ state: 'visible', timeout: 10000 });
  await darkThemeLocator.click();

  // Wait for the "Save" button to be visible and click it
  const saveButton = this.page.getByTestId('accounts-flow-control-left-btn-0');
  await saveButton.waitFor({ state: 'visible', timeout: 10000 });
  await saveButton.click();

  // Wait for the success message to be visible
  const successMessage = this.page.getByTestId('accounts-setting-details-time-zone-change-password-alert-1');
  await successMessage.waitFor({ state: 'visible', timeout: 10000 });

  // Verify the success message
  const changesSaved = await successMessage.textContent();
  expect(changesSaved?.trim()).toContain('Changes has been saved.');
}

  async fetchBackgroundColor(): Promise<boolean> {
    return PlaywrightActions.fetchAndValidateDarkTheme(this.page);
  }

  async verifyTheme(expectedTheme: string): Promise<void> {
    await PlaywrightActions.verifyTheme(this.page, expectedTheme);
  }

}

export default ThemePage;