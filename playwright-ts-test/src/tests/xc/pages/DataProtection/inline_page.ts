import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from "dotenv";
import { TableRowType } from "../../../../../resources/types/types";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class Inline {
  private page: Page;
  private readonly deDateFilter: string[];
  private HighRiskEliminateTableRows: TableRowType;
  private HighRiskSecureTableRows: TableRowType;

  constructor(page: Page) {
    this.page = page;

    this.deDateFilter = ["1 Day", "7 Days", "30 Days"];

    this.HighRiskEliminateTableRows = {
      "Top High Risk Applications to Eliminate": {
        applicationNameEliminate: "Application Name",
        riskEliminate: "Risk",
        usersEliminate: "Users",
        actionEliminate: "Action",
      },
    };

    this.HighRiskSecureTableRows = {
      "Top High Risk Applications to Secure": {
        applicationNameSecure: "Application Name",
        riskSecure: "Risk",
        usersSecure: "Users",
        actionSecure: "Action",
      },
    };
  }

  async navigateToDP(): Promise<void> {
    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set");
    }
    await this.page.goto(url);
    await this.page.waitForSelector(
      `(//button[@data-testid="nav-pills-tab-0"])`,
      { timeout: 40000 },
    );
    await this.page.getByTestId("nav-pills-tab-0").click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId(
        "left-nav-collapsible-nav-item-lm_analytics_data_protection-button",
      )
      .click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId(
        "left-nav-collapsible-nav-item-sub-menu-lm_analytics_data_channels-button",
      )
      .click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId("dp-data-channels-files-inline-trend-card-container")
      .click();
    await this.page.waitForTimeout(10000);
  }

  async checkPageUrl(): Promise<void> {
    await PlaywrightActions.checkUrl(
      this.page,
      url + "analytics/data-protection/data-channels?tab=inline",
    );
  }

  async checkDeDateFilter(): Promise<void> {
    await PlaywrightActions.checkDateFilter(
      this.page,
      "data-protection-data-channels",
      this.deDateFilter,
    );
  }

  async checkTopSensitiveDataTypes(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-dc-inline-tsdt-card-container"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-dc-inline-tsdt-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-dc-inline-tsdt-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId("dp-dc-inline-tsdt-vertical-bar-chart"),
    ).toBeVisible();
  }

  async checkSensitiveGenAIApplications(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-dc-sensitive-gen-ai-card-container"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-dc-sensitive-gen-ai-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-dc-sensitive-gen-ai-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId(
        "dp-dc-sensitive-gen-ai-sensitive-gen-ai-volume-donut-chart",
      ),
    ).toBeVisible();
  }

  async checkTopHighRiskApplicationsEliminate(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-dc-top-high-applications-eliminates-card-container"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-dc-top-high-applications-eliminates-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-dc-top-high-applications-eliminates-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId(
        "dp-dc-top-high-applications-eliminates-card-description",
      ),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-dc-top-high-applications-eliminates-z-table"),
    ).toBeVisible();
  }

  async verifyTopHighRiskApplicationsEliminateTable(
    page: Page,
    title: string,
    hoverable: boolean = true,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    let needData = await PlaywrightActions.isDataAvailable(
      this.page,
      "dp-dc-top-high-applications-eliminates",
    );

    if (!needData) {
      await PlaywrightActions.checkTable(
        page,
        "dp-dc-top-high-applications-eliminates-z-table",
        this.HighRiskEliminateTableRows[title],
        hoverable,
      );
    }
  }

  async verifyColorCodingInRiskLevels(page: Page): Promise<void> {
    await page.waitForTimeout(5000);
    const elements = page.getByTestId(
      "z-data-table-row-dp-dc-top-high-applications-eliminates-riskEliminate-1",
    );
    const count = await elements.count();
    for (let i = 0; i < count; i++) {
      const element = elements.nth(i);
      await expect(element).toBeVisible();
      const icon = element.locator("i");
      const text = await element.locator(".column span").textContent();
      if (text?.trim() === "Critical") {
        await expect(icon).toHaveClass(
          /text-dataviz-severity-primary-critical/,
        );
      } else if (text?.trim() === "High") {
        await expect(icon).toHaveClass(/text-dataviz-severity-primary-high/);
      } else if (text?.trim() === "Medium") {
        await expect(icon).toHaveClass(/text-dataviz-severity-primary-medium/);
      } else {
        console.error(
          `Error: Unexpected severity ${text?.trim()} for element ${i + 1}`,
        );
        throw new Error(
          `Error: Unexpected severity ${text?.trim()} for element ${i + 1}`,
        );
      }
    }
  }

  async verifyApplicationNamesUsersCount(page: Page): Promise<void> {
    await page.waitForTimeout(2000);
    const applicationNameElements = page.getByTestId(
      "z-data-table-row-dp-dc-top-high-applications-eliminates-applicationNameEliminate-0",
    );
    const applicationNameElement = applicationNameElements.nth(0);
    await expect(applicationNameElement).toBeVisible();
    const applicationNameText = await applicationNameElement
      .locator(".column span")
      .textContent();

    if (!applicationNameText) {
      throw new Error("The application name element contains an empty span.");
    }

    const riskElements = page.getByTestId(
      "z-data-table-row-dp-dc-top-high-applications-eliminates-riskEliminate-1",
    );
    const riskElement = riskElements.nth(0);
    await expect(riskElement).toBeVisible();
    const riskText = await riskElement.locator(".column span").textContent();
    if (!riskText) {
      throw new Error("The risk element contains an empty span.");
    }

    const usersElements = page.getByTestId(
      "z-data-table-row-dp-dc-top-high-applications-eliminates-usersEliminate-2",
    );
    const usersElement = usersElements.nth(0);
    await expect(usersElement).toBeVisible();
    const usersText = await usersElement.locator(".column span").textContent();
    if (!usersText) {
      throw new Error("The users element contains an empty span.");
    }

    const actionElements = page.getByTestId(
      "z-data-table-row-dp-dc-top-high-applications-eliminates-actionEliminate-3",
    );
    const actionElement = actionElements.nth(0);
    await expect(actionElement).toBeVisible();
    const actionText = await actionElement
      .locator(".column span")
      .textContent();
    if (!actionText) {
      throw new Error("The action element contains an empty span.");
    }
  }

  async verifyActionEditPolicy(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(2000);
    await page.waitForSelector(".pin-right.data-table-col-3");

    const actionElements = page.getByTestId(
      "z-data-table-row-dp-dc-top-high-applications-eliminates-actionEliminate-3",
    );
    const actionElement = actionElements.nth(0);
    await expect(actionElement).toBeVisible();
    const editPolicyLink = await actionElement.locator(
      "a.text-semantic-content-interactive-primary-default.typography-paragraph1 span.ml-m",
    );
    await expect(editPolicyLink).toBeVisible();
    await editPolicyLink.click();
    await page.waitForNavigation();
    await expect(page).toHaveURL(
      /\/internet-saas#policy\/web\/url-and-cloud-app-control\?CLOUD_APP_CONTROL_POLICY/,
    );
  }

  async checkTopHighRiskApplicationsSecure(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-dc-top-high-applications-secure-card-container"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-dc-top-high-applications-secure-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-dc-top-high-applications-secure-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId("dp-dc-top-high-applications-secure-card-description"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-dc-top-high-applications-secure-z-table"),
    ).toBeVisible();
  }

  async verifyTopHighRiskApplicationsSecureTable(
    page: Page,
    title: string,
    hoverable: boolean = true,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    let needData = await PlaywrightActions.isDataAvailable(
      this.page,
      "dp-dc-top-high-applications-secure",
    );

    if (!needData) {
      await PlaywrightActions.checkTable(
        page,
        "dp-dc-top-high-applications-secure-z-table",
        this.HighRiskSecureTableRows[title],
        hoverable,
      );
    }
  }

  async checkSensitiveFilesMLCategories(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId(
        "dp-dc-sensitive-files-top-ml-categories-card-container",
      ),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-dc-sensitive-files-top-ml-categories-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-dc-sensitive-files-top-ml-categories-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId(
        "dp-dc-sensitive-files-top-ml-categories-spider-node-chart",
      ),
    ).toBeVisible();
  }

  async checkTimeRangeFilter(page: Page): Promise<void> {
    await this.page.waitForTimeout(2000);

    const timeRangeDropdown = await page.getByTestId(
      "date-time-range-dropdown-data-protection-data-channels-zselect-container",
    );

    await timeRangeDropdown.click({ timeout: 2000 });

    const dropdownOptions = page.getByTestId(
      "date-time-range-dropdown-data-protection-data-channels-zselect-container-dropdown-content-container",
    );
    await expect(dropdownOptions).toBeVisible();

    await page.waitForTimeout(2000);
    const last7DaysOption = page.getByTestId(
      "date-time-range-dropdown-data-protection-data-channels-zselect-container-z-list-list-item-item-1",
    );
    await expect(last7DaysOption).toContainText("7 Days");
    await last7DaysOption.click({ timeout: 2000 });

    // Verify that the dropdown collapses and updates to "7 Days"
    await expect(dropdownOptions).not.toBeVisible();
    const selectedOption = timeRangeDropdown.locator(".date-time-range-text");
    await expect(selectedOption).toContainText("7 Days");
  }

  async checkClickViewAllGenAIActivity(page: Page): Promise<void> {
    await page.waitForTimeout(2000);
    await expect(
      page.getByTestId("dp-dc-sensitive-gen-ai-card-container"),
    ).toBeVisible();

    const actionElement = page.getByTestId(
      "dp-dc-sensitive-gen-ai-card-footer-nav-lin",
    );
    await expect(actionElement).toBeVisible();
    const link = page.getByTestId(
      "dp-dc-sensitive-gen-ai-footer-navLink-z-button-link",
    );
    // Verify the href attribute of the link points to the expected URL
    const href = await link.getAttribute("href");
    expect(href).toBe("/internet-saas#generative-ai-report");
  }

  async checkClickViewAllDataDiscovery(page: Page): Promise<void> {
    await page.waitForTimeout(2000);
    await expect(
      page.getByTestId("dp-dc-sensitive-files-top-ml-categories-card-container"),
    ).toBeVisible();

    const actionElement = page.getByTestId(
      "dp-dc-sensitive-files-top-ml-categories-card-footer-nav-link",
    );
    await expect(actionElement).toBeVisible();
    const link = page.getByTestId(
      "dp-dc-sensitive-files-top-ml-categories-footer-navLink-z-button-link",
    );
    // Verify the href attribute of the link points to the expected URL
    const href = await link.getAttribute("href");
    expect(href).toBe("/internet-saas#cybersecurity-insights");
  }
}

export default Inline;
