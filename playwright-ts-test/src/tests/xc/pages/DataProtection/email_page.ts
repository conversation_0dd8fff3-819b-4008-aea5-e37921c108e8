import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class Email {
  private page: Page;
  private readonly deDateFilter: string[];

  constructor(page: Page) {
    this.page = page;

    this.deDateFilter = ["1 Day", "7 Days", "30 Days"];
  }

  async navigateToDP(): Promise<void> {
    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set");
    }
    await this.page.goto(url);
    await this.page.waitForSelector(
      `(//button[@data-testid="nav-pills-tab-0"])`,
      { timeout: 40000 },
    );
    await this.page.getByTestId("nav-pills-tab-0").click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId(
        "left-nav-collapsible-nav-item-lm_analytics_data_protection-button",
      )
      .click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId(
        "left-nav-collapsible-nav-item-sub-menu-lm_analytics_data_channels-button",
      )
      .click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId("dp-data-channels-files-email-trend-card-container")
      .click();
    await this.page.waitForTimeout(10000);
  }

  async checkPageUrl(): Promise<void> {
    await PlaywrightActions.checkUrl(
      this.page,
      url + "analytics/data-protection/data-channels?tab=email",
    );
  }

  async checkDeDateFilter(): Promise<void> {
    await PlaywrightActions.checkDateFilter(
      this.page,
      "data-protection-data-channels",
      this.deDateFilter,
    );
  }

  async checkTopDomainsSensitiveDataBeingSentTo(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId(
        "dp-top-domains-sensitive-data-being-sent-to-card-container",
      ),
    ).toBeVisible();
    await expect(
      page.getByTestId(
        "dp-top-domains-sensitive-data-being-sent-to-card-title",
      ),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-top-domains-sensitive-data-being-sent-to-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId(
        "dp-top-domains-sensitive-data-being-sent-to-card-content",
      ),
    ).toBeVisible();
  }

  async checkTopUsersEmailIncidents(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-top-users-email-incidents-card-container"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-top-users-email-incidents-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-top-users-email-incidents-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId("dp-top-users-email-incidents-card-content"),
    ).toBeVisible();
  }
}

export default Email;
