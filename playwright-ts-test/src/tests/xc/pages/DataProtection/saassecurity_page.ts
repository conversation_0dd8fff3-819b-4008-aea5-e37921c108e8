import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class SaasSecurity {
  private page: Page;
  private readonly deDateFilter: string[];

  constructor(page: Page) {
    this.page = page;

    this.deDateFilter = ["1 Day", "7 Days", "30 Days"];
  }

  async navigateToDP(): Promise<void> {
    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set");
    }
    await this.page.goto(url);
    await this.page.waitForSelector(
      `(//button[@data-testid="nav-pills-tab-0"])`,
      { timeout: 40000 },
    );
    await this.page.getByTestId("nav-pills-tab-0").click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId(
        "left-nav-collapsible-nav-item-lm_analytics_data_protection-button",
      )
      .click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId(
        "left-nav-collapsible-nav-item-sub-menu-lm_analytics_data_channels-button",
      )
      .click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId("dp-data-channels-files-saas-security-trend-card-container")
      .click();
    await this.page.waitForTimeout(10000);
  }

  async checkPageUrl(): Promise<void> {
    await PlaywrightActions.checkUrl(
      this.page,
      url + "analytics/data-protection/data-channels?tab=saas-security",
    );
  }

  async checkDeDateFilter(): Promise<void> {
    await PlaywrightActions.checkDateFilter(
      this.page,
      "data-protection-data-channels",
      this.deDateFilter,
    );
  }

  async checkSaasIncidents(page: Page, expectedText: string): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-dc-saas-incidents-card-container"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-dc-saas-incidents-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-dc-saas-incidents-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId("dp-dc-saas-incidents-stacked-bar-chart"),
    ).toBeVisible();
  }

  async checkTopUsersSaasIncidents(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-dc-top-saas-incidents-card-container"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-dc-top-saas-incidents-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-dc-top-saas-incidents-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId("dp-dc-top-users-saas-incidents-z-table"),
    ).toBeVisible();
  }

  async checkEmptyStateTopUsers(page: Page): Promise<void> {
    await page.waitForTimeout(5000);
    const topUsersContainer = await page.getByTestId(
      "dp-dc-top-users-saas-incidents-card-container",
    );
    if (await topUsersContainer.isVisible()) {
      const emptyStateContainer = await page.getByTestId(
        "empty-card-analytic-state-dp-dc-top-users-saas-incidents",
      );

      if (await emptyStateContainer.isVisible()) {
        const noResultsText = await emptyStateContainer.locator(
          "span.typography-header5.mt-m",
        );
        await expect(noResultsText).toHaveText("No Results Found");
      } else {
        throw new Error("Top Users section is NOT empty.");
      }
    } else {
      throw new Error("Top Users card container not visible");
    }
  }

  async checkSaasApplicationsDataExposure(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-dc-saas-applications-data-exposure-card-container"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-dc-saas-applications-data-exposure-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-dc-saas-applications-data-exposure-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId("dp-dc-saas-applications-data-exposure-z-table"),
    ).toBeVisible();
  }

  async checkSaasIncidentsIconEachLegend(page: Page): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-dc-saas-incidents-card-container"),
    ).toBeVisible();

    const iconNames = ["private", "internal", "external"];

    for (let idx = 0; idx < iconNames.length; idx++) {
      // Locate the icon
      const icon = page.getByTestId(
        `dp-dc-saas-incidents-${iconNames[idx]}-icon`,
      );

      // Hover over the icon
      await icon.hover();

      // Wait briefly to ensure tooltip appears
      await page.waitForTimeout(2000);

      // Locate the tooltip
      const tooltipDescription = page.getByTestId(
        `tooltip-box-dp-dc-saas-incidents-${iconNames[idx]}`,
      );

      // Ensure the tooltip is visible
      await expect(tooltipDescription).toBeVisible();

      // Verify tooltip contains the expected text (replace 'Expected Tooltip Text' with expected tooltip content)
      const tooltipText = await tooltipDescription.textContent();

      // Remove hover by moving the mouse to an area outside the icons (e.g., top-left corner or empty space)
      await page.mouse.move(0, 0); // Move cursor to the top-left corner of the viewport
      await page.waitForTimeout(500);
    }
  }
}

export default SaasSecurity;
