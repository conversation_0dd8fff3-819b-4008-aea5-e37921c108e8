import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class Endpoints {
  private page: Page;
  private readonly deDateFilter: string[];

  constructor(page: Page) {
    this.page = page;

    this.deDateFilter = ["1 Day", "7 Days", "30 Days"];
  }

  async navigateToDP(): Promise<void> {
    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set");
    }
    await this.page.goto(url);
    await this.page.waitForSelector(
      `(//button[@data-testid="nav-pills-tab-0"])`,
      { timeout: 40000 },
    );
    await this.page.getByTestId("nav-pills-tab-0").click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId(
        "left-nav-collapsible-nav-item-lm_analytics_data_protection-button",
      )
      .click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId(
        "left-nav-collapsible-nav-item-sub-menu-lm_analytics_data_channels-button",
      )
      .click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId("dp-data-channels-files-endpoints-trend-card-container")
      .click();
    await this.page.waitForTimeout(10000);
  }

  async checkPageUrl(): Promise<void> {
    await PlaywrightActions.checkUrl(
      this.page,
      url + "analytics/data-protection/data-channels?tab=endpoints",
    );
  }

  async checkDeDateFilter(): Promise<void> {
    await PlaywrightActions.checkDateFilter(
      this.page,
      "data-protection-data-channels",
      this.deDateFilter,
    );
  }

  async checkTotalEndpointIncidents(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-total-endpoint-incidents-card-container"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-total-endpoint-incidents-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-total-endpoint-incidents-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId("dp-total-endpoint-incidents-donut-chart"),
    ).toBeVisible();
  }

  async TopUsersEndpointIncidents(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-top-endpoint-incidents-card-container"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-top-endpoint-incidents-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-top-endpoint-incidents-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId(
        "top-users-endpoint-z-table",
      ),
    ).toBeVisible();
  }

  async checkTopEndpointsSensitiveDataBeingExfiltrated(
    page: Page,
    expectedText: string,
  ): Promise<void> {
    await page.waitForTimeout(5000);
    await expect(
      page.getByTestId("dp-top-endpoint-sensitive-data-being-exfiltrated-card-container"),
    ).toBeVisible();
    await expect(
      page.getByTestId("dp-top-endpoint-sensitive-data-being-exfiltrated-card-title"),
    ).toBeVisible();
    const title = await page
      .getByTestId("dp-top-endpoint-sensitive-data-being-exfiltrated-card-title")
      .getByText(expectedText);
    await expect(title).toHaveText(expectedText);
    await expect(
      page.getByTestId("dp-top-endpoint-sensitive-data-being-exfiltrated-app-exp-trend"),
    ).toBeVisible();
  }


  async checkEmptyStateEndpointIncidents(page: Page): Promise<void> {
    await page.waitForTimeout(5000);
    const endpointIncidentsContainer = await page.getByTestId(
      "dp-total-endpoint-incidents-card-container",
    );
    if (await endpointIncidentsContainer.isVisible()) {
      const emptyStateContainer = await page.getByTestId(
        "empty-card-analytic-state-dp-total-endpoint-incidents",
      );

      if (await emptyStateContainer.isVisible()) {
        const noResultsText = await emptyStateContainer.locator(
          "span.typography-header5.mt-m",
        );
        await expect(noResultsText).toHaveText("No Results Found");
      } else {
        throw new Error("Endpoint Incidents section is NOT empty.");
      }
    } else {
      throw new Error("Endpoint Incidents card container not visible");
    }
  }

  async checkClickViewEndpointActivity(page: Page): Promise<void> {
    await page.waitForTimeout(2000);
    await expect(
      page.getByTestId("dp-top-endpoint-sensitive-data-being-exfiltrated-card-container"),
    ).toBeVisible();

    const actionElement = page.getByTestId(
      "dp-top-endpoint-sensitive-data-being-exfiltrated-card-footer-nav-lin",
    );
    await expect(actionElement).toBeVisible();

    const link = await actionElement.getByTestId(
      "dp-top-endpoint-sensitive-data-being-exfiltrated-footer-navLink-z-button-link",
    );
    await expect(link).toBeVisible();
    await link.click();
    await page.waitForNavigation();
    await expect(page).toHaveURL(
      /\/internet-saas#endpoint-dlp-report/,
    );
  }

}

export default Endpoints;
