import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from "dotenv";
import {
  SelectorsType,
  TableRowType,
} from "../../../../../resources/types/types";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class Dashboard {
  private page: Page;
  private readonly deDateFilter: string[];
  private readonly deFilters: string[];
  private readonly Selectors: SelectorsType;
  private readonly tabSelectors: SelectorsType;
  private readonly routes: SelectorsType;
  private TableRows: TableRowType;

  constructor(page: Page) {
    this.page = page;

    this.routes = {
      DataChannel: "analytics/data-protection/data-channel",
    };

    this.deDateFilter = ["1 Day", "7 Days", "30 Days"];

    this.deFilters = ["Departments"];

    this.Selectors = {
      "Total Incidents": "data-protection-DP",
    };

    this.tabSelectors = {
      Zscaler: "data-protection-net-latency-geo-view-segment-control-0",
    };

    this.TableRows = {
      "Application Experience": {
        app_id_name: "Application Name",
      },
    };
  }

  async navigateToDP(): Promise<void> {
    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set");
    }
    await this.page.goto(url);
    await this.page.waitForSelector(
      `(//button[@data-testid="nav-pills-tab-0"])`,
      { timeout: 40000 },
    );
    await this.page.getByTestId("nav-pills-tab-0").click();
    await this.page.waitForTimeout(10000);
    await this.page
      .getByTestId(
        "left-nav-collapsible-nav-item-lm_analytics_data_protection-button",
      )
      .click();
    await this.page.waitForTimeout(10000);
  }

  async checkDeDateFilter(): Promise<void> {
    await PlaywrightActions.checkDateFilter(
      this.page,
      "data-protection",
      this.deDateFilter,
    );
  }

  async checkDeFilters(): Promise<void> {
    await PlaywrightActions.checkFilters(
      this.page,
      "data-protection",
      this.deFilters,
    );
  }

  async checkFooterNavigaion(
    navigationPage: string,
    title: string,
  ): Promise<void> {
    let needData = await PlaywrightActions.isDataAvailable(
      this.page,
      this.Selectors[title],
    );
    if (!needData) {
      await PlaywrightActions.footerNavigaion(
        this.page,
        `${this.Selectors[title]}-footer-navLink-z-button-link`,
        url + this.routes[navigationPage],
      );
    }
  }

  async checkPageUrl(): Promise<void> {
    await PlaywrightActions.checkUrl(
      this.page,
      url + "analytics/data-protection",
    );
  }

  async checkContainerTitle(title: string): Promise<void> {
    await PlaywrightActions.checkHeaderByTestId(
      this.page,
      `${this.Selectors[title]}-card-title`,
      title,
    );
  }

  async checkGraph(title: string, grapType: string): Promise<void> {
    await this.page.waitForTimeout(5000);
    let needData = await PlaywrightActions.isDataAvailable(
      this.page,
      this.Selectors[title],
    );
    if (!needData) {
      await PlaywrightActions.checkGraph(
        this.page,
        `${this.Selectors[title]}-${grapType}`,
      );
    }
  }

  async checkTotalIncidents(): Promise<void> {
    await this.page.waitForTimeout(5000);
    await this.page
      .getByTestId("dp-total-incidents-card-container")
      .isVisible();
    await this.page.getByTestId("dp-total-incidents-card-title").isVisible();
    await this.page
      .getByTestId("dp-total-incidents-donut-chart")
      .locator("canvas")
      .nth(1)
      .click({
        position: {
          x: 293,
          y: 50,
        },
      });
  }

  async checkUsersMostIncidents(): Promise<void> {
    await this.page.waitForTimeout(5000);
    await this.page.getByText("Users with Most Incidents").isVisible();
    await this.page.getByText("InlineSaaS").isVisible();
    await this.page
      .getByTestId("dp-users-most-incidents-z-table-z-list")
      .isVisible();
  }

  async checkAllChannelsData(): Promise<void> {
    await this.page.waitForTimeout(5000);
    await this.page.getByTestId("card-container").click();
    await this.page.getByTestId("dp-all-data-channels-card-title").click();
    await this.page
      .getByTestId("dp-all-data-channels-inline-container-trend")
      .isVisible();
    await this.page
      .getByTestId("dp-all-data-channels-saas-security-container-trend")
      .isVisible();
    await this.page
      .getByTestId("dp-all-data-channels-endpoints-container-trend")
      .isVisible();
    await this.page
      .getByTestId("dp-all-data-channels-email-container-trend")
      .isVisible();
  }

  async checkSensitiveDataAtRest(): Promise<void> {
    await this.page.waitForTimeout(5000);
    await this.page
      .getByTestId("sensitive-data-rest-card-container")
      .isVisible();
    await this.page.getByTestId("sensitive-data-rest-card-title").isVisible();
    await this.page
      .getByTestId("sensitive-data-rest-card-description")
      .isVisible();
    await this.page
      .getByTestId("sensitive-data-rest-segment-control")
      .isVisible();
  }

  async switchToTab(tabName: string): Promise<void> {
    await PlaywrightActions.checkSwitchableTabs(
      this.page,
      this.tabSelectors[tabName],
    );
  }

  async checkTable(tableName: string): Promise<void> {
    let needData = await PlaywrightActions.isDataAvailable(
      this.page,
      this.Selectors[tableName],
    );
    if (!needData) {
      let hover: boolean = true;
      await PlaywrightActions.checkTable(
        this.page,
        `${this.Selectors[tableName]}-z-table`,
        this.TableRows[tableName],
        hover,
      );
    }
  }

  async checkCards(cardName: string, containerName: string): Promise<void> {
    let needData = await PlaywrightActions.isDataAvailable(
      this.page,
      this.Selectors[containerName],
    );
    if (!needData) {
      let hover: boolean = true;
      await PlaywrightActions.checkCards(
        this.page,
        cardName,
        this.Selectors[cardName],
        this.TableRows[cardName],
        hover,
      );
    }
  }

  async expandCardFooterNavigation(
    navigationPage: string,
    cardName: string,
    containerName: string,
  ): Promise<void> {
    let needData = await PlaywrightActions.isDataAvailable(
      this.page,
      this.Selectors[containerName],
    );
    if (!needData) {
      await expect(
        this.page.getByTestId(`${this.Selectors[cardName]}-disabled`),
      ).toBeVisible();
      const text = await this.page
        .locator(
          `(//div[@data-testid='${this.Selectors[cardName]}']/div/div)[1]`,
        )
        .textContent();
      await expect(text).toBe(cardName);
      const incidents: string | null = await this.page
        .locator(`(//div[@data-testid='${this.Selectors[cardName]}']/div/ul)`)
        .textContent();
      await this.page
        .locator(
          `(//div[@data-testid='${this.Selectors[cardName]}']/div/div)[1]`,
        )
        .click();
      if (incidents && Number(incidents[0]) > 0) {
        let hover: boolean = false;
        await expect(
          this.page.getByTestId(`${this.Selectors[cardName]}-expanded`),
        ).toBeVisible();
        const tableName = cardName.toLowerCase();
        await PlaywrightActions.checkTable(
          this.page,
          `${tableName}-table-z-table`,
          this.TableRows[cardName],
          hover,
        );
        await this.checkFooterNavigaion(
          navigationPage,
          `${containerName} - ${cardName}`,
        );
      }
    }
  }

  async checkSensitiveDataAtRestSegment(page: Page): Promise<void> {
    await page.waitForTimeout(2000);
    const classificationTab = page.getByTestId(
      "sensitive-data-rest-segment-control-0",
    );
    const dlpEngineTab = page.getByTestId(
      "sensitive-data-rest-segment-control-1",
    );

    await expect(classificationTab).toBeVisible({ timeout: 1000 });
    await expect(dlpEngineTab).toBeVisible({ timeout: 1000 });
  }

  async checkSensitiveDataAtRestDropdowns(page: Page): Promise<void> {
    await page
      .getByTestId("channel-type-zselect-container")
      .click({ timeout: 1000 });
    const channelDropdown = await page.getByTestId(
      "channel-type-zselect-container-dropdown-content-container",
    );
    await expect(channelDropdown).toBeVisible({ timeout: 2000 });

    const exactAiTypecheckbox = await page.locator(
      'span[for="exactAIBreakdown"]',
    );
    await exactAiTypecheckbox.click({ timeout: 2000 });

    await page.getByTestId("ai-type-zselect-container").click();
    const aiDropdown = await page.getByTestId(
      "ai-type-zselect-container-dropdown-content-container",
    );
    await expect(aiDropdown).toBeVisible({ timeout: 2000 });
  }

  async checkSensitiveDataAtRestClearFilters(page: Page): Promise<void> {
    await page.waitForTimeout(2000);
    await page
      .getByTestId("clear-channel-ai-type-filter")
      .click({ timeout: 1000 });
    await expect(
      page.getByTestId(
        "channel-type-zselect-container-dropdown-content-container",
      ),
    ).not.toBeVisible({ timeout: 2000 });
    await expect(
      page.getByTestId("ai-type-zselect-container-dropdown-content-container"),
    ).not.toBeVisible({ timeout: 2000 });
  }

  async checkEmptyStateTotalIncidents(page: Page): Promise<void> {
    await page.waitForTimeout(2000);
    const endpointIncidentsContainer = await page.getByTestId(
      "dp-total-incidents-card-container",
    );
    if (await endpointIncidentsContainer.isVisible()) {
      const emptyStateContainer = await page.getByTestId(
        "empty-card-analytic-state-dp-total-incidents",
      );

      if (await emptyStateContainer.isVisible()) {
        const noResultsText = await emptyStateContainer.locator(
          "span.typography-header5.mt-m",
        );
        await expect(noResultsText).toHaveText("No Results Found");
      } else {
        throw new Error("Total Incidents section is NOT empty.");
      }
    } else {
      throw new Error("Total Incidents card container not visible");
    }
  }

  async checkPageRendringTime(page: Page): Promise<void> {
    await page.waitForTimeout(2000);
    const targetLoadTimeMs = 3000; // ~3 seconds in milliseconds
    const performanceTiming = await page.evaluate(() => performance.timing);
    const renderTime =
      performanceTiming.loadEventEnd - performanceTiming.navigationStart;
    console.log(`Page render time: ${renderTime}ms`);
    // Verify the rendering time is within the target range
    expect(renderTime).toBeLessThan(targetLoadTimeMs);
  }
}

export default Dashboard;
