import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { FILTERS, DATE_FILTER, TEST_IDS, NAVIGATION, TABLE_SELECTOR, TABLE_ROWS } from '../../../../../resources/constants/pages/analytics/digitalExperience/applicationById';
import Base from "../../../../../lib/pom/Base/Base";
import { ROUTES } from "../../../../../resources/constants/routes";
import CommonFunctions from "../../../../../resources/utils/CommonFunctions";

class ApplicationByIdPage extends Base {
    constructor(page: Page) {
        super(page, {
            pageName: NAVIGATION.pageName,
            menuTestId: NAVIGATION.menuId,
            submenuTestId: NAVIGATION.submenuId,
            urlSuffix: NAVIGATION.url,
            dateFilters: DATE_FILTER,
            filters: FILTERS,
            selectors: TEST_IDS,
            tableSelectors: TABLE_SELECTOR, 
            tableRows: TABLE_ROWS,
        });
    }

    async navigateTo(): Promise<void> {
        await CommonFunctions.navigateTo(this.page, this.menuTestId, this.submenuTestId);
        await CommonFunctions.clickTableRow(this.page, this.tableSelectors["Application"]);
        await this.page.waitForTimeout(10000);
    }

    async checkUrl(): Promise<void> {
        const baseUrl = process.env.ONE_UI_BASE_URL?.replace(/\/$/, "");
        const url = new URL(this.page.url());

        await expect(url.origin).toBe(baseUrl);
        await expect(url.pathname).toBe(ROUTES["ApplicationById"]);

        const id = url.searchParams.get("id");
        const name = url.searchParams.get("name");

        await expect(id && /^\d+$/.test(id)).toBe(true);
        await expect(name).toBeTruthy();
    }

    async checkProgressContainer(progressCntName: string): Promise<void> {
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[progressCntName]);
        if(!needData)
        {
            await PlaywrightActions.checkProgressContainer(this.page, this.selectors[progressCntName]);
        }
    }

    async checkProbesStatus(probeCntName:string): Promise<void> {
        let probe = await this.page.getByTestId(`${this.tableSelectors[probeCntName]}-tab-0-item`).isVisible();
        if (probe)
        {
            let hover = false;
            await this.page.getByTestId(`${this.tableSelectors[probeCntName]}-tab-0-item`).click();
            await expect(this.page.getByTestId(`${this.tableSelectors[probeCntName]}-tab-0-expanded`)).toBeVisible();
            await PlaywrightActions.checkTable(this.page, `${this.tableSelectors[probeCntName]}-z-table`, this.tableRows[probeCntName], hover)
        }
    }
}

export default ApplicationByIdPage;