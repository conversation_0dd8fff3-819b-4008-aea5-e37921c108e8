import { Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { FILTERS, DATE_FILTER, TEST_IDS, NAVIGATION, TABLE_SELECTOR, TABLE_ROWS } from '../../../../../resources/constants/pages/analytics/digitalExperience/applications';
import Base from "../../../../../lib/pom/Base/Base";

class Application extends Base {
    constructor(page: Page) {
        super(page, {
            pageName: NAVIGATION.pageName,
            menuTestId: NAVIGATION.menuId,
            submenuTestId: NAVIGATION.submenuId,
            urlSuffix: NAVIGATION.url,
            dateFilters: DATE_FILTER,
            filters: FILTERS,
            selectors: TEST_IDS,
            tableSelectors: TABLE_SELECTOR, 
            tableRows: TABLE_ROWS,
        });
    }

  async checkTableData(tableName: string): Promise<boolean> {
      await this.page.waitForTimeout(5000);
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.tableSelectors[tableName]);
      if(!needData)
      {
          return await PlaywrightActions.isDataInTable(this.page, `${this.tableSelectors[tableName]}-z-table`);
      }
      return false;
  }
}

export default Application;
