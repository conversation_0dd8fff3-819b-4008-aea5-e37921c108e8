import { expect, Page } from "@playwright/test";
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
import { FILTERS, DATE_FILTER, TEST_IDS, NAVIGATION, TABLE_SELECTOR, TABLE_ROWS } from '../../../../../resources/constants/pages/analytics/digitalExperience/selfservice';
import Base from "../../../../../lib/pom/Base/Base";

class SelfService extends Base {
    constructor(page: Page) {
        super(page, {
            pageName: NAVIGATION.pageName,
            menuTestId: NAVIGATION.menuId,
            submenuTestId: NAVIGATION.submenuId,
            urlSuffix: NAVIGATION.url,
            dateFilters: DATE_FILTER,
            filters: FILTERS,
            selectors: TEST_IDS,
            tableSelectors: TABLE_SELECTOR,
            tableRows: TABLE_ROWS,
        });
    }
  
    async checkValue(containerName: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[containerName]);
        if(!needData)
        {
            let value = await PlaywrightActions.getValue(this.page, `${this.selectors[containerName]}-value`);
            await expect(value).toBeGreaterThanOrEqual(0);
        }
    }

}

export default SelfService;
