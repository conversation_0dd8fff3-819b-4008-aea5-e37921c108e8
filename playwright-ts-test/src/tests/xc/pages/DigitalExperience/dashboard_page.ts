import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { FILTERS, DATE_FILTER, TEST_IDS, NAVIGATION, TAB_SELECTOR, TABLE_ROWS, TABLE_SELECTOR } from '../../../../../resources/constants/pages/analytics/digitalExperience/digitalExperience';
import Base from "../../../../../lib/pom/Base/Base";

class Dashboard extends Base {
  
  constructor(page: Page) {
        super(page, {
            pageName: NAVIGATION.pageName,
            menuTestId: NAVIGATION.menuId,
            urlSuffix: NAVIGATION.url,
            dateFilters: DATE_FILTER,
            filters: FILTERS,
            selectors: TEST_IDS,
            tabSelectors: TAB_SELECTOR,
            tableSelectors: TABLE_SELECTOR,
            tableRows: TABLE_ROWS,
        });
      
  }

  async checkAppsCount(containerName: string): Promise<void> {
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[containerName]);
      if(!needData)
      {
          let poorApps = await PlaywrightActions.getValue(this.page, `${this.selectors[containerName]}-z-list-value-0`);
          let okayApps = await PlaywrightActions.getValue(this.page, `${this.selectors[containerName]}-z-list-value-1`);
          let goodApps = await PlaywrightActions.getValue(this.page, `${this.selectors[containerName]}-z-list-value-2`);
          console.log(poorApps, okayApps, goodApps);
          await expect(poorApps+okayApps+goodApps).toBeGreaterThanOrEqual(0);
      }
  }

  async checkIncidentsAndUsers(containerName: string): Promise<void> {
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[containerName]);
      if(!needData)
      {
          let totalIncidents = await PlaywrightActions.getValue(this.page, `user-experience-z-list-value-0`);
          let impactedUsers = await PlaywrightActions.getValue(this.page, `user-experience-z-list-value-1`);
          await expect(totalIncidents).toBeGreaterThanOrEqual(0);
          await expect(impactedUsers).toBeGreaterThanOrEqual(0);
      }
  }

  async checkCards(cardName: string, containerName: string): Promise<void> {
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[containerName]);
      if(!needData)
      {
          let hover: boolean= true;
          await PlaywrightActions.checkCards(this.page, cardName, this.selectors[cardName], this.tableRows[cardName], hover);
      }
  }

  async expandCardFooterNavigation(navigationPage: string, cardName: string, containerName: string): Promise<void> {
      let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[containerName]);
      if(!needData)
      {
          await expect(this.page.getByTestId(`${this.selectors[cardName]}-disabled`)).toBeVisible();
          const text = await this.page.locator(`(//div[@data-testid='${this.selectors[cardName]}']/div/div)[1]`).textContent();
          await expect(text).toBe(cardName);
          const incidents: string | null = await this.page.locator(`(//div[@data-testid='${this.selectors[cardName]}']/div/ul)`).textContent();
          await this.page.locator(`(//div[@data-testid='${this.selectors[cardName]}']/div/div)[1]`).click();
          if(incidents && Number(incidents[0]) > 0 )
          {
              let hover: boolean= false;
              await expect(this.page.getByTestId(`${this.selectors[cardName]}-expanded`)).toBeVisible();
              const tableName = cardName.toLowerCase();
              await PlaywrightActions.checkTable(this.page, `${tableName}-table-z-table`, this.tableRows[cardName], hover);
              await this.checkFooterNavigaion(navigationPage, `${containerName} - ${cardName}`)
          }
      }
  }

}

export default Dashboard;
