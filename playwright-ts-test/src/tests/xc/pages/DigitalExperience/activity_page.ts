import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { FILTERS, DATE_FILTER, TEST_IDS, NAVIGATION } from '../../../../../resources/constants/pages/analytics/digitalExperience/activity';
import Base from "../../../../../lib/pom/Base/Base";

class Activity extends Base {

    constructor(page: Page) {
        super(page, {
            pageName: NAVIGATION.pageName,
            menuTestId: NAVIGATION.menuId,
            submenuTestId: NAVIGATION.submenuId,
            urlSuffix: NAVIGATION.url,
            dateFilters: DATE_FILTER,
            filters: FILTERS,
            selectors: TEST_IDS,
        });
    }

    async checkValue(containerName: string): Promise<void> {
        await this.page.waitForTimeout(5000);
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[containerName]);
        if(!needData)
        {
            let value = await PlaywrightActions.getValue(this.page, `${this.selectors[containerName]}-value`);
            await expect(value).toBeGreaterThanOrEqual(0);
        }
    }

    async checkUserDistributionCount(containerName: string): Promise<void> {
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[containerName]);
        if(!needData)
        {
            let poorApps = await PlaywrightActions.getValue(this.page, `${this.selectors[containerName]}-z-list-value-0`);
            let okayApps = await PlaywrightActions.getValue(this.page, `${this.selectors[containerName]}-z-list-value-1`);
            let goodApps = await PlaywrightActions.getValue(this.page, `${this.selectors[containerName]}-z-list-value-2`);
            await expect(poorApps+okayApps+goodApps).toBeGreaterThanOrEqual(0);
        }
    }

}

export default Activity;