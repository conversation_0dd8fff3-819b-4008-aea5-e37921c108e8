import { Page } from "@playwright/test";
import { FILTERS, DATE_FILTER, TEST_IDS, NAVIGATION, TABLE_SELECTOR, TABLE_ROWS } from '../../../../../resources/constants/pages/analytics/digitalExperience/meetings';
import Base from "../../../../../lib/pom/Base/Base";

class Meetings extends Base {
    constructor(page: Page) {
            super(page, {
                pageName: NAVIGATION.pageName,
                menuTestId: NAVIGATION.menuId,
                submenuTestId: NAVIGATION.submenuId,
                urlSuffix: NAVIGATION.url,
                dateFilters: DATE_FILTER,
                filters: FILTERS,
                selectors: TEST_IDS,
                tableSelectors: TABLE_SELECTOR,
                tableRows: TABLE_ROWS,
        });
    }   
}

export default Meetings;
