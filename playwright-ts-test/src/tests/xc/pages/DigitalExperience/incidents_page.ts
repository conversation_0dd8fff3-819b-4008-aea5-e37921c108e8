import { expect, Page } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { DATE_FILTER, TEST_IDS, NAVIGATION, TABLE_ROWS, TABLE_SELECTOR, FILTERS as typeFilterData } from '../../../../../resources/constants/pages/analytics/digitalExperience/incidents';
import Base from "../../../../../lib/pom/Base/Base";

class Incidents extends Base {
    constructor(page: Page) {
        super(page, {
            pageName: NAVIGATION.pageName,
            menuTestId: NAVIGATION.menuId,
            submenuTestId: NAVIGATION.submenuId,
            urlSuffix: NAVIGATION.url,
            dateFilters: DATE_FILTER,
            selectors: TEST_IDS,
            tableSelectors: TABLE_SELECTOR,
            tableRows: TABLE_ROWS,
        })
    }
    
    async checkTypeFilter(): Promise<void> {
        await PlaywrightActions.checkfilterByType(this.page, this.selectors["Type Filter"], typeFilterData);;
    }

    async checklistHeader(title: string, containerName: string): Promise<void> {
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[containerName]);
        if(!needData)
        {
            await PlaywrightActions.checkHeader(this.page, `(//div[@data-testid="${this.selectors[title]}"]/div[@class='flex items-center']/div)`, title);
        }
    }

    async checklistValue(title: string, containerName: string): Promise<void> {
        let needData = await PlaywrightActions.isDataAvailable(this.page, this.selectors[containerName]);
        if(!needData)
        {
            let data = await PlaywrightActions.getValue(this.page, this.selectors[title]);
            await expect(data).toBeGreaterThanOrEqual(0);
        }
    }

}

export default Incidents;
