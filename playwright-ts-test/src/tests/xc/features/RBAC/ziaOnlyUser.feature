@RBAC @xc
Feature: To verify the ZIA only user role based access

# XC-7821
Background: Login to the Zscaler Console with valid credentails
    Given User open the URL
    When User enter the valid "ZIA Only User" username and password
    Then User login to the console successfully

# XC-9052
@home @regression
Scenario: Verify Banner in Home Page 
    When User is in Home Page
    Then Verify the Banner in Home Page

# XC-9052
@home @sanity
Scenario: Verify Recently Viewed Container in Home Page 
    When User is in Home Page
    Then Verify the Recently Viewed Container in Home Page

# XC-9052
@home @regression
Scenario: Verify News Container in Home Page 
    When User is in Home Page
    Then Verify the News Container in Home Page

# XC-9052
@home @regression
Scenario: Verify Learn About Products Container in Home Page 
    When User is in Home Page
    Then Verify the Learn About Products Container in Home Page

# XC-9052
@home @sanity
Scenario: Verify Resources Container in Home Page 
    When User is in Home Page
    Then Verify the Resources Container in Home Page

# XC-9052, XC-9046
@home @sanity
Scenario: Verify Analytics Cards in Home Page - RBAC
   When User is in Home Page
   Then Verify the Analytics Cards in Home Page - RBAC
   Then Verify the following Analytics Cards in Home Page:
   | Networking    |
   | Cybersecurity |

@sanity
Scenario: To verify Digital Experience, Connector Activity and Opertional menus are not visible
   Then user clicks on Analytics
   Then User verify the "Digital Experience" menu is not visible
   Then User verify the "Operational" menu is not visible
   Then User verify the "Connector Activity" submenu under "Networking" is not visible

# XC-5905
@sanity
@networking
Scenario: Verify filters in Networking Page
   When User is in Networking Page
   Then Verify the filters in Networking Page

# XC-5906, XC-5907
@sanity
@networking
  Scenario: Verify Traffic in my Organization
   When User is in Networking Page
   Then Verify the title "Traffic in my Organization" in Networking Page
   Then Ensure that you are in "Internet & SaaS" tab in Networking Page
   Then Verify the label and value of "Number of Transactions" under "Internet & SaaS" in Networking Page
   Then Verify the label and value of "Traffic Volume" under "Internet & SaaS" in Networking Page
   Then Ensure that "Traffic in my Organization" graph canvas of type "multiline-chart" is present under "Internet & SaaS" in Networking Page
   Then Ensure that you are in "Private" tab in Networking Page
   Then Verify the label and value of "Number of Transactions" under "Private" in Networking Page
   Then Verify the label and value of "Traffic Volume" under "Private" in Networking Page
   Then Ensure that "Traffic in my Organization" graph canvas of type "multiline-chart" is present under "Private" in Networking Page

# XC-5911
@sanity
@networking
Scenario: Verify Internet Traffic Distribution
  When User is in Networking Page 
  Then Verify the title "Internet Traffic Distribution" in Networking Page
  Then Ensure that "Internet Traffic Distribution" graph canvas of type "donut-chart" is present in Networking Page

# XC-5920
@sanity
@networking
Scenario: Verify Top Locations sending Internet Traffic to Zscaler
  When User is in Networking Page 
  Then Verify the title "Top Locations sending Internet Traffic to Zscaler" in Networking Page
  Then Ensure that "Top Locations sending Internet Traffic to Zscaler" graph canvas of type "horizontal-bar-chart" is present in Networking Page

# XC-5912
@sanity
@networking
Scenario: Verify Data Centers
  When User is in Networking Page 
  Then Ensure that you are in "Data Centers" tab in Networking Page
  Then Verify the title "Top Zscaler Data Centers Used" in Networking Page
  Then Ensure that "Top Zscaler Data Centers Used" graph canvas of type "world-bubbles-chart" is present in Networking Page

# XC-5912
@sanity
@networking
Scenario: Verify App Connectors
  When User is in Networking Page 
  Then Ensure that you are in "App Connectors" tab in Networking Page
  Then Verify the title "Locations with Zscaler Connectors" in Networking Page
  Then Ensure that "Locations with Zscaler Connectors" graph canvas of type "world-icon-chart" is present in Networking Page

# XC-5912
@sanity
@networking
Scenario: Verify Branch & Cloud Connectors
  When User is in Networking Page 
  Then Ensure that you are in "Branch & Cloud Connectors" tab in Networking Page
  Then Verify the title "Branch & Cloud Connectors" in Networking Page
  Then Ensure that "Branch & Cloud Connectors" graph canvas of type "world-pie-chart" is present in Networking Page
  Then Ensure User can navigate to "Connector Activity" Page while clicking on footer under "Branch & Cloud Connectors" in Networking Page

@advancedthreat @sanity
Scenario: Verify date dropdown filters in Advanced Threats Page
   When User is in Advanced Threats Page
   Then Verify the default date in date dropdown is "14 Days" in Advanced Threats Page
   Then Verify the date dropdown in Advanced Threats Page

# XC-6157
@advancedthreat @sanity
Scenario: To verify the Advanced Threats Incidents widgets
   When User is in Advanced Threats Page 
   Then Verify the title "Incoming Real Time Threats" in Advanced Threats Page 
   Then Ensure that "Incoming Real Time Threats" graph canvas of type "multiline-chart" is present in Advanced Threats Page
  
# XC-6157
@advancedthreat @sanity
Scenario: To verify the Incoming Real Time Threats
   When User is in Advanced Threats Page 
   Then Verify the title "Incoming Real Time Threats" in Advanced Threats Page 
   Then Ensure that "Advanced Threat Incidents" table is present in Advanced Threats page


# XC-6149
@cybersecurity
Scenario: Verify date dropdown filters in Cybersecurity Page
   When User is in Cybersecurity Page
   Then Verify the default date in date dropdown is "14 Days" in Cybersecurity Page
   Then Verify the date dropdown in Cybersecurity Page

@cybersecurity @sanity
Scenario: To verify the transactions widget in Cybersecurity for malicious transactions
   When User is in Cybersecurity Page 
   Then Verify the title "Your Cybersecurity Transactions" in Cybersecurity Page
   Then Verify the Description "Malicious transactions are threats that have been detected and blocked by Zscaler." in Cybersecurity Page
   Then Verify the title "Total Threats Blocked" in Cybersecurity Page
   Then Verify the value of "Total Threats Blocked" in Cybersecurity Page
   Then Ensure that "Total Threats Blocked Graph" graph canvas of type "multiline-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "Transactional Activity" Page while clicking on footer under "Total Threats Blocked Graph" in Cybersecurity Page

# XC-6154 
@cybersecurity @sanity
Scenario: To verify the Advanced threats Categories bar graph
   When User is in Cybersecurity Page 
   Then Verify the title "Advanced Threat Categories" in Cybersecurity Page
   Then Ensure that "Advanced Threat Categories" graph canvas of type "multiline-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "Advanced Threats" Page while clicking on footer under "Advanced Threat Categories" in Cybersecurity Page

@cybersecurity @sanity
Scenario: To verify the Top Threat Locations bar graph
   When User is in Cybersecurity Page 
   Then Verify the title "Top Threat Locations" in Cybersecurity Page
   Then Ensure that "Top Threat Locations" graph canvas of type "horizontal-bar-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "Threat Locations" Page while clicking on footer under "Top Threat Locations" in Cybersecurity Page

# XC-6166
@cybersecurity @sanity
Scenario: To verify the SSL/TLS Inspection Review bar line chart
   When User is in Cybersecurity Page 
   Then Verify the title "Your SSL/TLS Inspection Review" in Cybersecurity Page
   Then Ensure that "Your SSL/TLS Inspection Review" graph canvas of type "bar-line-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "SSL/TLS Inspection" Page while clicking on footer under "Your SSL/TLS Inspection Review" in Cybersecurity Page

# XC-6162
@cybersecurity @sanity
Scenario: To verify the Sandbox Threats widget
   When User is in Cybersecurity Page 
   Then Verify the title "Sandbox Threats" in Cybersecurity Page
   Then Ensure that "Sandbox Threats" graph canvas of type "sankey-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "Sandbox Threats" Page while clicking on footer under "Sandbox Threats" in Cybersecurity Page

# XC-6150
@cybersecurity @sanity
Scenario: To verify the transactions widget in Cybersecurity for Policy Blocks
   When User is in Cybersecurity Page 
   Then Ensure that you are in "Policy Blocks" tab in Cybersecurity Page
   Then Verify the title "Your Cybersecurity Transactions" in Cybersecurity Page
   Then Verify the Description "Blocks that are caused by policies you have configured to limit exposure to certain types of web content." in Cybersecurity Page
   Then Verify the title "Total Policy Blocks" in Cybersecurity Page
   Then Verify the value of "Total Policy Blocks" in Cybersecurity Page
   Then Ensure that "Total Policy Blocks Graph" graph canvas of type "multiline-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "Transactional Activity" Page while clicking on footer under "Total Policy Blocks Graph" in Cybersecurity Page

@sandboxthreat @sanity
Scenario: Verify date dropdown filters in Sandbox Threats Page
   When User is in Sandbox Threats Page
   Then Verify the default date in date dropdown is "14 Days" in Sandbox Threats Page
   Then Verify the date dropdown in Sandbox Threats Page

# XC-4814
@sandboxthreat @regression
Scenario: To verify the Policy Actions & Verdicts for Files Known by Cloud Effect widget
  When User is in Sandbox Threats Page 
  Then Verify the title "Policy Actions & Verdicts for Files Known by Cloud Effect" in Sandbox Threats Page 
  Then Ensure that "Policy Actions & Verdicts for Files Known by Cloud Effect" graph canvas of type "vertical-bar-chart" is present in Sandbox Threats Page
  
@sandboxthreat @sanity
Scenario: To verify the Policy Actions & Verdicts for Unknown Files widget
    When User is in Sandbox Threats Page 
    Then Verify the title "Policy Actions & Verdicts for Unknown Files" in Sandbox Threats Page 
    Then Ensure that "Policy Actions & Verdicts for Unknown Files" graph canvas of type "vertical-bar-chart" is present in Sandbox Threats Page 

# XC-6186
@sandboxthreat @sanity
Scenario: To verify the Top Users Generating Sandbox Threats widget
    When User is in Sandbox Threats Page 
    Then Verify the title "Top Users Generating Sandbox Threats" in Sandbox Threats Page 
    Then Ensure that "Top Users Generating Sandbox Threats" graph canvas of type "horizontal-bar-chart" is present in Sandbox Threats Page 

# XC-6187
@sandboxthreat @sanity
Scenario: To verify the Top Sandbox Incidents widget
    When User is in Sandbox Threats Page 
    Then Verify the title "Top Sandbox Incidents" in Sandbox Threats Page 
    Then Ensure that "Top Sandbox Incidents" table is present in Sandbox Threats page

@ssl_tlsinspection @sanity
Scenario: Verify date dropdown filters in SSL/TLS Inspection Page
   When User is in SSL/TLS Inspection Page
   Then Verify the default date in date dropdown is "14 Days" in SSL/TLS Inspection Page
   Then Verify the date dropdown in SSL/TLS Inspection Page

# XC-6169 XC-6196
@ssl_tlsinspection @sanity
Scenario: To verify the Threats Blocked by SSL TLS Inspection
      When User is in SSL/TLS Inspection Page 
      Then Verify the title "Threats Blocked by SSL/TLS Inspection" in SSL/TLS Inspection Page
      Then Ensure that "Threats Blocked by SSL/TLS Inspection" graph canvas of type "vertical-bar-chart" is present in SSL/TLS Inspection Page
      Then Ensure that "Threats Blocked by SSL/TLS Inspection" graph canvas of type "donut-chart" is present in SSL/TLS Inspection Page
   

# XC-6198
@ssl_tlsinspection @sanity
Scenario: To verify the Top High Volume Applications bar graph
     When User is in SSL/TLS Inspection Page 
     Then Verify the title "What areas are you not inspecting traffic?" in SSL/TLS Inspection Page
     Then Verify the Description "Zscaler recommends enabling SSL/TLS Inspection in the below areas to block incoming threats." in SSL/TLS Inspection Page
     Then Verify the title "Top High Volume Applications" in SSL/TLS Inspection Page
     Then Ensure that "Top High Volume Applications" graph canvas of type "horizontal-bar-chart" is present in SSL/TLS Inspection Page
  

# XC-6199
@ssl_tlsinspection @sanity
Scenario: To verify the Top Locations graph
     When User is in SSL/TLS Inspection Page 
     Then Verify the title "Top Locations" in SSL/TLS Inspection Page
     Then Ensure that "Top Locations" graph canvas of type "horizontal-bar-chart" is present in SSL/TLS Inspection Page
  

# XC-6200
@ssl_tlsinspection @sanity
Scenario: To verify the Top URL Categories bar graph
     When User is in SSL/TLS Inspection Page 
     Then Verify the title "Top URL Categories" in SSL/TLS Inspection Page
     Then Ensure that "Top URL Categories" graph canvas of type "horizontal-bar-chart" is present in SSL/TLS Inspection Page
  
@threatlocations @sanity
Scenario: Verify date dropdown filters in Threat Locations Page
   When User is in Threat Locations Page
   Then Verify the default date in date dropdown is "14 Days" in Threat Locations Page
   Then Verify the date dropdown in Threat Locations Page
    
# XC-6161 XC-6190 XC-6189
@threatlocations @sanity
Scenario: To verify the Threat Locations widgets
    When User is in Threat Locations Page 
    Then Verify the title "Total Threats Trend" in Threat Locations Page
    Then Ensure that "Total Threats Trend" graph canvas of type "multiline-chart" is present in Threat Locations Page 

# XC-6191 XC-6192
@threatlocations @sanity
Scenario: To verify the Top Threat Categories graph
    When User is in Threat Locations Page 
    Then Verify the title "Top Threat Categories" in Threat Locations Page
    Then Ensure that "Top Threat Categories" graph canvas of type "bubble-chart" is present in Threat Locations Page 


# XC-6193
@threatlocations @sanity
Scenario: To verify the Top Users Generating Threats bar graph
    When User is in Threat Locations Page 
    Then Verify the title "Top Users Generating Threats" in Threat Locations Page
    Then Ensure that "Top Users Generating Threats" graph canvas of type "horizontal-bar-chart" is present in Threat Locations Page

# XC-6194
@threatlocations @sanity
Scenario: To verify the Top Applications Generating Threats bar graph
     When User is in Threat Locations Page 
     Then Verify the title "Top Applications Generating Threats" in Threat Locations Page
     Then Ensure that "Top Applications Generating Threats" graph canvas of type "horizontal-bar-chart" is present in Threat Locations Page

@transactionalactivity @sanity
Scenario: Verify date dropdown filters in Transactional Activity Page
   When User is in Transactional Activity Page
   Then Verify the default date in date dropdown is "14 Days" in Transactional Activity Page
   Then Verify the date dropdown in Transactional Activity Page

# XC-6153 XC-6170 XC-6172
@transactionalactivity @sanity
Scenario: To verify the Transactional Activity tab and To verify the All Threats graph 
    When User is in Transactional Activity Page 
    Then Verify the title "All Threats" in Transactional Activity Page

@transactionalactivity @sanity
Scenario: To verify the All Threats graph 
    When User is in Transactional Activity Page 
    Then Ensure that "All Threats" graph canvas of type "multiline-chart" is present in Transactional Activity Page 

@transactionalactivity @sanity
Scenario: To verify the Top Users Generating Threats horizontal bar graph
   When User is in Transactional Activity Page 
   Then Verify the title "Top Users Generating Threats" in Transactional Activity Page
   Then Ensure that "Top Users Generating Threats" graph canvas of type "horizontal-bar-chart" is present in Transactional Activity Page 

# XC-6173
@transactionalactivity @sanity
Scenario: To verify the Top Departments Generating Threats horizontal bar graph
   When User is in Transactional Activity Page 
   Then Verify the title "Top Departments Generating Threats" in Transactional Activity Page
   Then Ensure that "Top Departments Generating Threats" graph canvas of type "horizontal-bar-chart" is present in Transactional Activity Page 

# XC-6174
@transactionalactivity @sanity
Scenario: To verify the Your Policy Blocks graph
   When User is in Transactional Activity Page 
   Then Ensure that you are in "Policy Blocks" tab in Transactional Activity Page
   Then Ensure that "Your Policy Blocks" graph canvas of type "multiline-chart" is present in Transactional Activity Page

@transactionalactivity @sanity
Scenario: To verify the Top Application Class bar graph
   When User is in Transactional Activity Page 
   Then Ensure that you are in "Policy Blocks" tab in Transactional Activity Page
   Then Ensure that "Top Application Class" graph canvas of type "horizontal-bar-chart" is present in Transactional Activity Page

@transactionalactivity @sanity
Scenario: To verify the Top URL Categories Blocked bar graph
   When User is in Transactional Activity Page 
   Then Ensure that you are in "Policy Blocks" tab in Transactional Activity Page
   Then Ensure that "Top URL Categories Blocked" graph canvas of type "horizontal-bar-chart" is present in Transactional Activity Page
 

# XC-6177
@transactionalactivity @sanity
Scenario: To verify the Top Cloud Applications Blocked bubble graph
   When User is in Transactional Activity Page 
   Then Ensure that you are in "Policy Blocks" tab in Transactional Activity Page
   Then Ensure that "Top Cloud Applications Blocked" graph canvas of type "bubbles-chart" is present in Transactional Activity Page


# XC-6178
@transactionalactivity @sanity
Scenario: To verify the Top Blocked File Types no results found
   When User is in Transactional Activity Page 
   Then Ensure that you are in "Policy Blocks" tab in Transactional Activity Page
   Then Ensure that "Top Blocked File Types" graph canvas of type "horizontal-bar-chart" is present in Transactional Activity Page

# XC-7821
@sanity
   Scenario: To verify the Administration global navigation for ZIA Only user
     Given User capture the screenshot for ZIA "Administration" to "Account Management"
     Then Verify the difference between the screenshots for ZIA
     
# XC-7821
@sanity
   Scenario: To verify the ZIA Administration for Admin Management
    Given User capture the screenshot for ZIA "Administration" to "Admin Management"
    Then Verify the difference between the screenshots for ZIA

# XC-7821
@sanity
 Scenario: To verify the ZIA Administration -> Admin Management navigation for Role Based Access Control
    Given User capture the screenshot for ZIA "Administration" to "Admin Management" then "Role Based Access Control"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Administration -> Admin Management navigation for Administrator Management
    Given User capture the screenshot for ZIA "Administration" to "Admin Management" then "Administrator Management"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Administration -> Admin Management navigation for Audit Logs
    Given User capture the screenshot for ZIA "Administration" to "Admin Management" then "Audit Logs"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Administration -> Identity navigation for ZIdentity
    Given User capture the screenshot for ZIA "Administration" to "Identity" then "ZIdentity"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Administration -> Identity navigation for Internet & SaaS
    Given User capture the screenshot for ZIA "Administration" to "Identity" then "Internet & SaaS"
    Then Verify the difference between the screenshots for ZIA


# XC-7821
@sanity
   Scenario: To verify the ZIA Administration for API Configuration
    Given User capture the screenshot for ZIA "Administration" to "API Configuration" then "Legacy API"
    Then Verify the difference between the screenshots for ZIA


# XC-7821
@sanity
   Scenario: To verify the ZIA Administration for Alerts
    Given User capture the screenshot for ZIA "Administration" to "Alerts"
    Then Verify the difference between the screenshots for ZIA


# XC-7821
@sanity
   Scenario: To verify the ZIA Administration for Backup and Restore
    Given User capture the screenshot for ZIA "Administration" to "Backup & Restore"
    Then Verify the difference between the screenshots for ZIA

#  Scenario: To verify the ZIA Policies -> Access Control navigation for Internet & SaaS
#     Given User capture the screenshot for ZIA "Policies" to "Access Control" then "Internet & SaaS"
#     Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Policies -> Access Control navigation for Firewall
    Given User capture the screenshot for ZIA "Policies" to "Access Control" then "Firewall"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Policies -> Cybersecurity navigation for Inline Security 
    Given User capture the screenshot for ZIA "Policies" to "Cybersecurity" then "Inline Security"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Policies -> Cybersecurity navigation for SaaS Security API
    Given User capture the screenshot for ZIA "Policies" to "Cybersecurity" then "SaaS Security API"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Policies -> Cybersecurity navigation for Partner Integrations
    Given User capture the screenshot for ZIA "Policies" to "Cybersecurity" then "Partner Integrations"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Policies -> Data Protection navigation for Policy
    Given User capture the screenshot for ZIA "Policies" to "Data Protection" then "Policy"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Policies -> Data Protection navigation for Common Resources
    Given User capture the screenshot for ZIA "Policies" to "Data Protection" then "Common Resources"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Policies -> Common Configuration navigation for Out-of-Band CASB
    Given User capture the screenshot for ZIA "Policies" to "Common Configuration" then "Out-of-Band CASB"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Policies -> Common Configuration navigation for SSL/TLS Inspection
    Given User capture the screenshot for ZIA "Policies" to "Common Configuration" then "SSL/TLS Inspection"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Policies -> Common Configuration navigation for Resources
    Given User capture the screenshot for ZIA "Policies" to "Common Configuration" then "Resources"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Policies -> Common Configuration navigation for Advanced
    Given User capture the screenshot for ZIA "Policies" to "Common Configuration" then "Advanced"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Infrastructure -> Internet & SaaS navigation for Traffic Forwarding
    Given User capture the screenshot for ZIA "Infrastructure" to "Internet & SaaS" then "Traffic Forwarding"
    Then Verify the difference between the screenshots for ZIA
@sanity
 Scenario: To verify the ZIA Infrastructure -> Internet & SaaS navigation for Network Policies
    Given User capture the screenshot for ZIA "Infrastructure" to "Internet & SaaS" then "Network Policies"
    Then Verify the difference between the screenshots for ZIA

# XC-7821
@sanity
   Scenario: To verify the ZIA Infrastructure for Locations
    Given User capture the screenshot for ZIA "Infrastructure" to "Locations"
    Then Verify the difference between the screenshots for ZIA

# XC-7821
@sanity
   Scenario: To verify the ZIA Logs for Insights
    Given User capture the screenshot for ZIA "Logs" to "Insights"
    Then Verify the difference between the screenshots for ZIA

# XC-7821
@sanity
   Scenario: To verify the ZIA Logs for Log Streaming
    Given User capture the screenshot for ZIA "Logs" to "Log Streaming"
    Then Verify the difference between the screenshots for ZIA

