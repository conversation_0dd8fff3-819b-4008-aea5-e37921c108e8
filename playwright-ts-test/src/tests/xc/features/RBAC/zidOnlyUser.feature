@RBAC @xc
Feature: To verify the ZID only user role based access

# XC-7824

Background: Login to the Zscaler Console with valid credentails
    Given User open the URL
    When User enter the valid "ZID Only User" username and password
    Then User login to the console successfully

# XC-9051
@home @regression
Scenario: Verify Banner in Home Page 
    When User is in Home Page
    Then Verify the Banner in Home Page

# XC-9051
@home @sanity
Scenario: Verify Recently Viewed Container in Home Page 
    When User is in Home Page
    Then Verify the Recently Viewed Container in Home Page

# XC-9051
@home @regression
Scenario: Verify News Container in Home Page 
    When User is in Home Page
    Then Verify the News Container in Home Page

# XC-9051
@home @regression
Scenario: Verify Learn About Products Container in Home Page 
    When User is in Home Page
    Then Verify the Learn About Products Container in Home Page

# XC-9051
@home @sanity
Scenario: Verify Resources Container in Home Page 
    When User is in Home Page
    Then Verify the Resources Container in Home Page

# XC-9051
@home @sanity
Scenario: Verify Analytics Cards in Home Page - RBAC
   When User is in Home Page
   Then Verify the Analytics Cards is not available in Home Page - RBAC

@sanity
Scenario: To verify Analytics Option is Not Visible
    Then User Checks for the "Analytics" Option is Not Visible

# XC-7824
@sanity
   Scenario: To verify the Administration -> Account Management global navigation
     Given User capture the screenshot for "Administration" to "Account Management" in ZID
     Then Verify the difference between the screenshots for ZID user

# XC-7824
@sanity
   Scenario: To verify the Administration -> Admin Management global navigation for Administrator Management
     Given User capture the screenshot for "Administration" to "Admin Management" then "Administrator Management" in ZID
     Then Verify the difference between the screenshots for ZID user

# XC-7824
@sanity
   Scenario: To verify the Administration -> Admin Management global navigation for Role Based Access Control
     Given User capture the screenshot for "Administration" to "Admin Management" then "Role Based Access Control" in ZID
     Then Verify the difference between the screenshots for ZID user

# XC-7824
@sanity
   Scenario: To verify the Administration -> Admin Management global navigation for Audit Logs
     Given User capture the screenshot for "Administration" to "Admin Management" then "Audit Logs" in ZID
     Then Verify the difference between the screenshots for ZID user

# XC-7824
@sanity
   Scenario: To verify the Administration -> Identity global navigation for ZIdentity
     Given User capture the screenshot for "Administration" to "Identity" then "ZIdentity" in ZID
     Then Verify the difference between the screenshots for ZID user

# XC-7824
@sanity
   Scenario: To verify the Administration -> API Configuration global navigation for OneAPI
     Given User capture the screenshot for "Administration" to "API Configuration" then "OneAPI" in ZID
     Then Verify the difference between the screenshots for ZID user

## XC-7824
#   Scenario: To verify the Infrastructure -> Locations global navigation
#     Given User capture the screenshot for "Infrastructure" to "Locations" in ZID
#     Then Verify the difference between the screenshots for ZID user