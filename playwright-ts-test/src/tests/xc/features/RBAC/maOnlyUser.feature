@RBAC @xc
Feature: To verify the MA only user role based access

# XC-7823
Background: Login to the Zscaler Console with valid credentails
     Given User open the URL
     When User enter the valid "MA Only User" username and password
     Then User login to the console successfully

# XC-9045
@home @regression
Scenario: Verify Banner in Home Page 
    When User is in Home Page
    Then Verify the Banner in Home Page

# XC-9045
@home @sanity
Scenario: Verify Recently Viewed Container in Home Page 
    When User is in Home Page
    Then Verify the Recently Viewed Container in Home Page

# XC-9045
@home @regression
Scenario: Verify News Container in Home Page 
    When User is in Home Page
    Then Verify the News Container in Home Page

# XC-9045
@home @regression
Scenario: Verify Learn About Products Container in Home Page 
    When User is in Home Page
    Then Verify the Learn About Products Container in Home Page

# XC-9045
@home @sanity
Scenario: Verify Resources Container in Home Page 
    When User is in Home Page
    Then Verify the Resources Container in Home Page

# XC-9045
@home @sanity
Scenario: Verify Analytics Cards in Home Page - RBAC
   When User is in Home Page
   Then Verify the Analytics Cards in Home Page - RBAC
   Then Verify the following Analytics Cards in Home Page:
   | Devices |

# XC-7823
@sanity
Scenario: To verify  Networking, Digital Experience, Cybersecurity and Appliances menus are not visible
     Then user clicks on Analytics
     Then User verify the "Networking" menu is not visible
     Then User verify the "Digital Experience" menu is not visible
     Then User verify the "Cybersecurity" menu is not visible
     Then User verify the "Appliances" submenu under "Operational" is not visible

#  XC-7823
@sanity
   Scenario: To verify the user devices graph
     Then user clicks on Analytics
     Given User navigates to the Operational "Devices" tab
     When User verify the "User Devices" title, tooltip and user devices count
     Then Verify the "User Devices Graph" graph canvas of type "stacked-bar-chart"

# XC-7823
@sanity
   Scenario: To verify the user devices distribution tab
     Then user clicks on Analytics
     Given User navigates to the Operational "Devices" tab
     Then User clicks on the graph and verify the tab details

# XC-7823
@sanity
   Scenario: To verify the version distribution graph
     Then user clicks on Analytics
     Given User navigates to the Operational "Devices" tab
     When User verify the "Version Distribution" title in devices tab
     Then Verify the "Version Distribution Graph" graph canvas of type "donut-chart"

# XC-7823
@sanity
   Scenario: To verify the device operating system graph
     Then user clicks on Analytics
     Given User navigates to the Operational "Devices" tab
     When User verify the "Device Operating System" title in devices tab
     Then Verify the "Device Operating System Graph" graph canvas of type "horizontal-bar-chart"

# XC-7823
@sanity
   Scenario: To verify the Device Model graph
     Then user clicks on Analytics
     Given User navigates to the Operational "Devices" tab
     When User verify the "Device Model" title in devices tab
     Then Verify the "Device Model Graph" graph canvas of type "horizontal-bar-chart"

# XC-7823
@sanity
   Scenario: To verify the Device State graph
     Then user clicks on Analytics
     Given User navigates to the Operational "Devices" tab
     When User verify the "Device State" title in devices tab
     Then Verify the "Device State Graph" graph canvas of type "donut-chart"

# XC-7823
# @sanity
#    Scenario: To verify the Administration global navigation for MA Only user
#      Given User capture the screenshot for "Administration" to "Account Management"
#      Then Verify the difference between the screenshots

# XC-7823
@sanity
   Scenario: To verify the Administration -> Admin Management global navigation for Role Based Access Control
     Given User capture the screenshot for "Administration" to "Admin Management" then "Role Based Access Control"
     Then Verify the difference between the screenshots

# XC-7823
@sanity
   Scenario: To verify the Administration -> Admin Management global navigation for Audit Logs
     Given User capture the screenshot for "Administration" to "Admin Management" then "Audit Logs"
     Then Verify the difference between the screenshots

# XC-7823  
@sanity   
   Scenario: To verify the Administration -> Identity global navigation for ZIdentity
     Given User capture the screenshot for "Administration" to "Identity" then "ZIdentity"
     Then Verify the difference between the screenshots

# XC-7823
@sanity
   Scenario: To verify the Administration -> Identity global navigation for Private Access
     Given User capture the screenshot for "Administration" to "Identity" then "Private Access"
     Then Verify the difference between the screenshots

# XC-7823
@sanity
   Scenario: To verify the Administration -> Entitlements global navigation
     Given User capture the screenshot for "Administration" to "Entitlements"
     Then Verify the difference between the screenshots

# XC-7823
@sanity
   Scenario: To verify the Administration -> API Configuration global navigation for Legacy API
     Given User capture the screenshot for "Administration" to "API Configuration" then "Legacy API"
     Then Verify the difference between the screenshots

# XC-7823
@sanity
   Scenario: To verify the Policies -> Common Configuration global navigation for Resources
     Given User capture the screenshot for "Policies" to "Common Configuration" then "Resources"
     Then Verify the difference between the screenshots

# XC-7823
@sanity
   Scenario: To verify the Infrastructure  -> Locations global navigation
     Given User capture the screenshot for "Infrastructure" to "Locations"
     Then Verify the difference between the screenshots

# XC-7823
@sanity
   Scenario: To verify the Infrastructure  -> Connectors global navigation for Client
     Given User capture the screenshot for "Infrastructure" to "Connectors" then "Client"
     Then Verify the difference between the screenshots

# XC-7823
@sanity
   Scenario: To verify the Infrastructure  -> Common Resources global navigation for Application
     Given User capture the screenshot for "Infrastructure" to "Common Resources" then "Application"
     Then Verify the difference between the screenshots

# XC-7823
@sanity
   Scenario: To verify the Infrastructure  -> Common Resources global navigation for Deployment
     Given User capture the screenshot for "Infrastructure" to "Common Resources" then "Deployment"
     Then Verify the difference between the screenshots