@RBAC @xc
Feature: To verify the ZTDS only user role based access

# XC-7826
 Background: Login to the Zscaler Console with valid credentails
    Given User open the URL
    When User enter the valid "ZTDS Only User" username and password
    Then User login to the console successfully

# XC-9050
@home @regression
Scenario: Verify Banner in Home Page 
    When User is in Home Page
    Then Verify the Banner in Home Page

# XC-9050
@home @sanity
Scenario: Verify Recently Viewed Container in Home Page 
    When User is in Home Page
    Then Verify the Recently Viewed Container in Home Page

# XC-9050
@home @regression
Scenario: Verify News Container in Home Page 
    When User is in Home Page
    Then Verify the News Container in Home Page

# XC-9050
@home @regression
Scenario: Verify Learn About Products Container in Home Page 
    When User is in Home Page
    Then Verify the Learn About Products Container in Home Page

# XC-9050
@home @sanity
Scenario: Verify Resources Container in Home Page 
    When User is in Home Page
    Then Verify the Resources Container in Home Page

# XC-9050
@home @sanity
Scenario: Verify Analytics Cards in Home Page - RBAC
   When User is in Home Page
   Then Verify the Analytics Cards is not available in Home Page - RBAC

# XC-7826
Scenario: To verify Analytics Option is Not Visible
    Then User Checks for the "Analytics" Option is Not Visible

# XC-7826
@sanity
   Scenario: To verify the Administration -> Account Management global navigation
    Given User capture the screenshot for "Administration" to "Account Management" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user

# XC-7826
@sanity
   Scenario: To verify the Administration -> Identity global navigation for ZIdentity
    Given User capture the screenshot for "Administration" to "Identity" then "ZIdentity" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user

# XC-7826
@sanity
   Scenario: To verify the Administration -> API Configuration global navigation
    Given User capture the screenshot for "Administration" to "API Configuration" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user

# XC-7826
@sanity
   Scenario: To verify the Administration -> Alerts global navigation
    Given User capture the screenshot for "Administration" to "Alerts" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user

# # XC-7826
#    Scenario: To verify the Policies -> Access Control global navigation for Internet & SaaS
#     Given User capture the screenshot for "Policies" to "Access Control" then "Internet & SaaS" in ZTDS
#     Then Verify the difference between the screenshots for ZTDS user

# XC-7826
@sanity
   Scenario: To verify the Policies -> Access Control global navigation for Segmentation
    Given User capture the screenshot for "Policies" to "Access Control" then "Segmentation" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user
    
# # XC-7826
#    Scenario: To verify the Infrastructure -> Locations global navigation
#     Given User capture the screenshot for "Infrastructure" to "Locations" in ZTDS
#     Then Verify the difference between the screenshots for ZTDS user

# XC-7826   
@sanity
   Scenario: To verify the Infrastructure -> Connectors global navigation for Edge
    Given User capture the screenshot for "Infrastructure" to "Connectors" then "Edge" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user

# XC-7826
@sanity
   Scenario: To verify the Logs -> Insights global navigation
    Given User capture the screenshot for "Logs" to "Insights" in ZTDS
    Then Verify the difference between the screenshots for ZTDS user