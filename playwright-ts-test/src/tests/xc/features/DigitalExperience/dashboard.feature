@dedashboard @xc
Feature: Check Digital Experience Dashboard

# XC-5939
@sanity
Scenario: Verify Date filter in Digital Experience 
  When User is in digital experience dashboard
  Then Verify the date dropdown in DE page
  Then Verify the filters in DE page

# XC-5940, XC-5943
@sanity
Scenario: Verify How is my overall Digital Experience 
  When User is in digital experience dashboard
  Then Verify the title "How is my overall Digital Experience?" in DE page
  Then Ensure that "How is my overall Digital Experience?" graph canvas of type "vertical-bar-chart" is present in DE page
  Then Ensure User can navigate to "Activity" Page while clicking on footer under "How is my overall Digital Experience?" in DE Page

# XC-5944, XC-5945
@regression
Scenario: Verify the Network Latency Geoview
  When User is in digital experience dashboard
  Then Verify the title "Network Latency Geoview" in DE page
  Then Ensure that you are in "Zscaler" tab in DE page
  Then Ensure that "Network Latency Geoview" graph canvas of type "world-bubbles-chart" is present in DE page
  Then Ensure that you are in "DNS Resolution" tab in DE page
  Then Ensure that "Network Latency Geoview" graph canvas of type "world-bubbles-chart" is present in DE page

# XC-5948, XC-5952
@regression
Scenario: Verify the Application Experience
  When User is in digital experience dashboard
  Then Verify the title "Application Experience" in DE page
  Then Verify the "Application Experience" apps count in DE page
  Then Ensure that "Application Experience" table is present in DE page
  Then Ensure User can navigate to "Applications" Page while clicking on footer under "Application Experience" in DE Page

# XC-5951, XC-5953
@sanity
Scenario: Verify the Unified Communication Experience
  When User is in digital experience dashboard
  Then Verify the title "Unified Communication Experience" in DE page
  Then Ensure that "Unified Communication Experience" graph canvas of type "donut-chart" is present in DE page
  Then Ensure User can navigate to "Meetings" Page while clicking on footer under "Unified Communication Experience" in DE Page

# XC-5955, XC-5956, XC-5957, XC-5959
@regression
Scenario: Verify What is impacting my User Experience
  When User is in digital experience dashboard
  Then Verify the title "What is impacting my User Experience?" in DE page
  Then Verify the Total Incidents and Impacted users under "What is impacting my User Experience?" in DE page
  Then Ensure the "Device", "Networking" and "Application" cards are present under "What is impacting my User Experience?" in DE page
  Then Ensure User can navigate to "Incidents" Page while clicking on footer under "Application" in "What is impacting my User Experience?" in DE Page

# XC-5961
@sanity
Scenario: Verify the End Point Self Service
  When User is in digital experience dashboard
  Then Verify the title "End Point Self Service" in DE page
  Then Ensure that "End Point Self Service" graph canvas of type "stacked-bar-chart" is present in DE page
  Then Ensure User can navigate to "Self Service" Page while clicking on footer under "End Point Self Service" in DE Page
