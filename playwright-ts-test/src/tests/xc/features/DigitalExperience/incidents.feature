@incidents @xc
Feature: Check Digital Experience > Incidents
 
# XC-5982
@sanity
Scenario: Verify filters in Incidents Page 
  When User is in Incidents Page
  Then Verify the Type filter in Incidents page
  Then Verify the date dropdown in Incidents page

# XC-5983, XC-5984
@sanity
Scenario: Verify Total Incidents and Impacted Users
  When User is in Incidents Page 
  Then Verify the title "Incidents Across Key Areas" in Incidents Page 
  Then Verify the list title "Total Incidents" under "Incidents Across Key Areas" in Incidents Page 
  Then Verify the list value "Total Incidents Value" under "Incidents Across Key Areas" in Incidents Page 
  Then Verify the list title "Impacted Users" under "Incidents Across Key Areas" in Incidents Page 
  Then Verify the list value "Impacted Users Value" under "Incidents Across Key Areas" in Incidents Page 

# XC-5987
@sanity
Scenario: Verify Incidents Over Time 
  When User is in Incidents Page 
  Then Verify the title "Incidents Over Time" in Incidents Page 
  Then Ensure that "Incidents Over Time" graph canvas of type "vertical-bar-chart" is present in Incidents Page

# XC-5988
@sanity
Scenario: Verify Impacted Users Over Time 
  When User is in Incidents Page 
  Then Verify the title "Impacted Users Over Time" in Incidents Page 
  Then Ensure that "Impacted Users Over Time" graph canvas of type "vertical-bar-chart" is present in Incidents Page

# XC-5989
@sanity
Scenario: Verify Incidents by Epicenters 
  When User is in Incidents Page 
  Then Verify the title "Incidents by Epicenters" in Incidents Page 
  Then Ensure that "Incidents by Epicenters" graph canvas of type "world-icon-chart" is present in Incidents Page

# XC-5991, XC-5992
@sanity
Scenario: Verify Incidents Table 
  When User is in Incidents Page 
  Then Verify the title "Incidents" in Incidents Page
  Then Ensure that "Incidents" Search Bar is present in Incidents page
  Then Ensure that "Incidents" table is present in Incidents page