@selfservice @xc
Feature: Check Digital Experience > Self Service

# XC-6002
@sanity
Scenario: Verify filters in Self Service Page 
  When User is in Self Service Page
  Then Verify the filters in Self Service page
  Then Verify the date dropdown in Self Service page

# XC-6004
@sanity
Scenario: Verify Total Notifications Sent
  When User is in Self Service Page
  Then Verify the title "Total Notifications Sent" in Self Service Page
  Then Verify the value of "Total Notifications Sent" in Self Service Page

# XC-6005
@sanity
Scenario: Verify Notifications By Type
  When User is in Self Service Page
  Then Verify the title "Notifications By Type" in Self Service Page
  Then Ensure that "Notifications By Type" graph canvas of type "stacked-bar-chart" is present in Self Service Page

# XC-6007
@sanity
Scenario: Verify Total Users Notified
  When User is in Self Service Page
  Then Verify the title "Total Users Notified" in Self Service Page
  Then Verify the value of "Total Users Notified" in Self Service Page

# XC-6008
@sanity
Scenario: Verify Users Found Notifications Helpful
  When User is in Self Service Page
  Then Verify the title "Users Found Notifications Helpful" in Self Service Page
  Then Verify the value of "Users Found Notifications Helpful" in Self Service Page

# XC-6009
@sanity
Scenario: Verify Active Users With Self Service
  When User is in Self Service Page
  Then Verify the title "Active Users With Self Service" in Self Service Page
  Then Verify the value of "Active Users With Self Service" in Self Service Page

# XC-6010
@sanity
Scenario: Verify Users Who Disabled Notifications
  When User is in Self Service Page
  Then Verify the title "Users Who Disabled Notifications" in Self Service Page
  Then Verify the value of "Users Who Disabled Notifications" in Self Service Page

# XC-6011
@sanity
Scenario: Verify Notifications Sent Over Time
  When User is in Self Service Page
  Then Verify the title "Notifications Sent Over Time" in Self Service Page
  Then Ensure that "Notifications Sent Over Time" graph canvas of type "vertical-bar-chart" is present in Self Service Page

# XC-6012, XC-6013
@sanity
Scenario: Verify Notifications Table
  When User is in Self Service Page
  Then Verify the title "Notifications" in Self Service Page
  Then Ensure that "Notifications" Search Bar is present in Self Service page
  Then Ensure that "Notifications" table is present in Self Service page
