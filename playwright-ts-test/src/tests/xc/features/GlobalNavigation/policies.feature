@policies @xc
Feature: Verify the networking component

  #  Background: Login to the Zscaler Console with valid credentails
  #   Given User open the URL
  #   When User enter the valid "One UI User" username and password
  #   Then User login to the console successfully


# Access Control
# Internet & SaaS
@sanity
Scenario: Verify all the navigation link of policies > Access control > Internet & SaaS > URL Control are working fine
    Then User click on policies from global navigation
    And click on access control option
    Then User verify all the global nav users under URL Control are working fine
@sanity
Scenario: Verify all the navigation link of policies > Access control > Internet & SaaS > File Type Control are working fine
    Then User click on policies from global navigation
    And click on access control option
    Then User verify all the global nav users under File Type Control are working fine
@sanity
Scenario: Verify all the navigation link of policies > Access control > Internet & SaaS > SaaS Application Control are working fine
    Then User click on policies from global navigation
    And click on access control option
    Then User verify all the global nav users under SaaS Application Control are working fine

# Private Applications
@sanity
  Scenario: Verify all the navigation link of policies > Access control > Private Applications > Policies are working fine
    Then User click on policies from global navigation
    And click on access control option
    Then User verify all the global nav users under policies are working fine

@sanity
  Scenario: Verify all the navigation link of policies > Access control > Private Applications > App Segments are working fine
    Then User click on policies from global navigation
    And click on access control option
    Then User verify all the global nav users under App Segments are working fine

@sanity
  Scenario: Verify all the navigation link of policies > Access control > Private Applications > Servers are working fine
    Then User click on policies from global navigation
    And click on access control option
    Then User verify all the global nav users under Servers are working fine

# Firewall
@sanity
  Scenario: Verify all the navigation link of policies > Access control > Firewall >links are working fine
    Then User click on policies from global navigation
    And click on access control option
    Then User verify all the global nav users under policies Firewall are working fine 

# Clientless
@sanity
  Scenario: Verify all the navigation link of policies > clientless > Access Methods
    Then User click on policies from global navigation
    And click on access control option
    Then User verify all the global nav users under Access Methods are working fine

@sanity
  Scenario: Verify all the navigation link of policies > clientless > User Portal Configuration
    Then User click on policies from global navigation
    And click on access control option
    Then User verify all the global nav users under User Portal Configuration are working fine

# Cybersecurity
# Inline Security
@sanity
  Scenario: Verify all the navigation link of policies > Cybersecurity > Inline Security > Private App Protection
    Then User click on policies from global navigation
    And click on cybersecurity option
    Then User verify all the global nav users under Private App Protection are working fine

# Digital Experience Monitoring
# Configuration
@sanity
   Scenario: Verify all the navigation link of policies > Digital Experience Monitoring > Configuration
    Then User click on policies from global navigation
    And click on Digital Experience Monitoring option
    Then User verify all the global nav users under Configuration are working fine
@sanity
  Scenario: Verify all the navigation link of policies > Digital Experience Monitoring > Self Service
    Then User click on policies from global navigation
    And click on Digital Experience Monitoring option
    Then User verify all the global nav users under Self Service are working fine

@sanity
   Scenario: Verify all the navigation link of policies > Digital Experience Monitoring > settings
    Then User click on policies from global navigation
    And click on Digital Experience Monitoring option
    Then User verify all the global nav users under settings are working fine
@sanity
   Scenario: Verify all the navigation link of policies > Digital Experience Monitoring > Resources
    Then User click on policies from global navigation
    And click on Digital Experience Monitoring option
    Then User verify all the global nav users under Resources are working fine

# Common Configuration
# Resources
@sanity
   Scenario: Verify all the navigation link of policies > Common Configuration > Resources > End User Notifications
    Then User click on policies from global navigation
    And click on Common Configuration option
    Then User verify all the global nav users under End User Notifications are working fine
@sanity
    Scenario: Verify all the navigation link of policies > Common Configuration > Resources > Connectors
    Then User click on policies from global navigation
    And click on Common Configuration option
    Then User verify all the global nav users under Connectors are working fine
