@administration @xc
Feature: To verify the global navigation for Administration

# XC-7660
@sanity
   Scenario: To verify the Administration - Account Management links
    Given User is able to view "Administration" in the global navigation
    When User clicks on Account Management links under company profile and verify they are directed to respective screens
    Then User clicks on subscription and branding links and verify the screens

# XC-7659
@sanity
   Scenario: To verify the Admin Management - Administrator Management links
    Given User is able to view "Administration" in the global navigation
    When User clicks on the admin management and then "Administrator Management"
    Then User clicks on the link and verify the screens

# XC-7659
@sanity
   Scenario: To verify the Admin Management - Role Based Access Control links
    Given User is able to view "Administration" in the global navigation
    When User clicks on the admin management and then "Role Based Access Control"
    Then User verify the roles and Entitlements links
    
# XC-7659
@sanity
   Scenario: To verify the Admin Management - Audit Logs links
    Given User is able to view "Administration" in the global navigation
    When User clicks on the admin management and then "Audit Logs"
    Then User verify the audit logs links

# XC-7659
@sanity
   Scenario: To verify the Identity - ZIdentity links
    Given User is able to view "Administration" in the global navigation
    When User clicks on the Identity and selects ZIdentity
    Then User verify the IDP Configuration, User Management, Domains, Passwords Authentication and Connectors links

# XC-7659
@sanity
   Scenario: To verify the Identity - Internet & SaaS links
    Given User is able to view "Administration" in the global navigation
    When User clicks on the Identity and selects Internet and SaaS
    Then User verify the Internet Authentication Setting, SCIM Event Logs, User Management and Identity Proxy links

# XC-7659
@sanity
   Scenario: To verify the Identity - Private Access links
    Given User is able to view "Administration" in the global navigation
    When User clicks on the Identity and selects Private Access
    Then User verify the IDP Configuration, Device Authentication and Partner Login links

# XC-7660
@sanity
   Scenario: To verify the Entitlements links
    Given User is able to view "Administration" in the global navigation
    When User clicks on the Entitlements
    Then User verify the Private Access, Digital Experience and Internet Access links

# XC-7659
@sanity
   Scenario: To verify the API Configuration - OneAPI links
    Given User is able to view "Administration" in the global navigation
    When User clicks on the API Configuration and OneAPI
    Then User verify the OneAPI links

# XC-7659
@sanity
   Scenario: To verify the API Configuration - Legacy API links
    Given User is able to view "Administration" in the global navigation
    When User clicks on the API Configuration and Legacy API
    Then User verify the Legacy API links

# XC-7660
@sanity
   Scenario: To verify the Alerts links
    Given User is able to view "Administration" in the global navigation
    When User clicks on the Alerts
    Then User verify the Internet and SaaS, Digital Experience Monitoring, Private Access and Zero Trust Branch links

# XC-7660
@sanity
   Scenario: To verify the Backup & Restore links
    Given User is able to view "Administration" in the global navigation
    When User clicks on the Backup and Restore
    Then User verify the Backup and Restore links   