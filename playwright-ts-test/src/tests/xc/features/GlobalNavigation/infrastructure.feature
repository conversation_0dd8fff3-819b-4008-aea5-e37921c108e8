@Infrastructure @xc
Feature: To verify the global navigation for Infrastructure

# Private Access
# Component XC-7660, XC-7661
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Private Access > Component 
    Given User is able to view "Infrastructure" in the global navigation
    Then click on private access option
    Then click on Component and verify all the links are working fine


# Business Continuity
#  XC-7660
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Private Access > Business Continuity 
    Given User is able to view "Infrastructure" in the global navigation
    Then click on private access option
    Then click on Business Continuity and verify all the links are working fine


# Client Connector Policies
# XC-7659
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Private Access > Client Connector Policies
    Given User is able to view "Infrastructure" in the global navigation
    Then click on private access option
    Then click on Client Connector Policies and verify all the links are working fine

# Internet && SaaS  
# Traffic Forwarding
# XC-7659
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Internet && SaaS  > Traffic Forwarding
    Given User is able to view "Infrastructure" in the global navigation
    Then click on Internet & SaaS access option
    Then click on Traffic Forwarding and verify all the links are working fine

# Network Policies
# XC-7659
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Internet && SaaS  > Network Policies
    Given User is able to view "Infrastructure" in the global navigation
    Then click on Internet & SaaS access option
    Then click on Network Policies and verify all the links are working fine

# # Traffic Capture
# @sanity
#   Scenario: Verify all the navigation link of Infrastructure > Internet && SaaS  > Traffic Capture
#     Given User is able to view "Infrastructure" in the global navigation
#     Then click on Internet & SaaS access option
#     Then click on Traffic Capture and verify all the links are working fine

# Locations
# XC-7659
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Locations
    Given User is able to view "Infrastructure" in the global navigation
    Then click on Locations and verify all the links are working fine

# Connectors
# Client
# XC-7659
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Connectors > Client
    Given User is able to view "Infrastructure" in the global navigation
    Then click on Connectors option
    Then click on Client and verify all the links are working fine

# Edge
# XC-7659
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Connectors > Edge
    Given User is able to view "Infrastructure" in the global navigation
    Then click on Connectors option
    Then click on Edge and verify all the links are working fine

# Cloud
# XC-7659
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Connectors > Cloud
    Given User is able to view "Infrastructure" in the global navigation
    Then click on Connectors option
    Then click on Cloud and verify all the links are working fine

# Common Resources
# Gateways
# XC-7661
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Common Resources > Gateways 
    Given User is able to view "Infrastructure" in the global navigation
    Then click on Common Resources option
    Then click on Gateways and verify all the links are working fine

# Application
# XC-7661
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Common Resources > Application 
    Given User is able to view "Infrastructure" in the global navigation
    Then click on Common Resources option
    Then click on Application and verify all the links are working fine

# Deployment
# XC-7661
@sanity
  Scenario: Verify all the navigation link of Infrastructure > Common Resources > Deployment 
    Given User is able to view "Infrastructure" in the global navigation
    Then click on Common Resources option
    Then click on Deployment and verify all the links are working fine