@darkTheme @xc
Feature: Verify the networking component
# XC-11031 XC-11032
@networking
@sanity
Scenario: Verify Dark Theme in Networking Page
  When User is in Networking Page
  Then User fetches the background theme color
  And Verify the theme is "Dark"

@connectoractivity
@sanity
Scenario: Verify Dark Theme in Connector Activity Page
    When User is in Connector Activity Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@dedashboard
@sanity
Scenario: Verify Dark Theme in digital experience dashboard Page    
    When User is in digital experience dashboard
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@activity
@sanity
Scenario: Verify Dark Theme in Activity Page    
    When User is in Activity Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@applications
@sanity
Scenario: Verify Dark Theme in Application Page    
    When User is in Application Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@incidents
@sanity
Scenario: Verify Dark Theme in Incidents Page    
    When User is in Incidents Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@meetings
@sanity
Scenario: Verify Dark Theme in Meetings Page    
    When User is in Meetings Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@selfservice
@sanity
Scenario: Verify Dark Theme in Self Service Page    
    When User is in Self Service Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@cybersecurity
@sanity
Scenario: Verify Dark Theme in Cybersecurity Page 
    When User is in Cybersecurity Page 
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@transactionalactivity
@sanity
Scenario: Verify Dark Theme in Transactional Activity Page    
    When User is in Transactional Activity Page 
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@advancedthreat
@sanity
Scenario: Verify Dark Theme in Advanced Threats Page    
    When User is in Advanced Threats Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@sandboxthreat
@sanity
Scenario: Verify Dark Theme in Sandbox Threats Page    
    When User is in Sandbox Threats Page 
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@threatlocations
@sanity
Scenario: Verify Dark Theme in Threat Locations Page    
    When User is in Threat Locations Page
    Then User fetches the background theme color
    And Verify the theme is "Dark"
    
@ssl_tlsinspection
@sanity
Scenario: Verify Dark Theme in SSL/TLS Inspection Page    
    When User is in SSL/TLS Inspection Page 
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@sanity
Scenario: Verify Dark Theme in Operational devices Page    
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    Then User fetches the background theme color
    And Verify the theme is "Dark"

@sanity
Scenario: Verify Dark Theme in Operational Appliances Page    
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    And User navigates to the Appliances tab
    Then User fetches the background theme color
    And Verify the theme is "Dark"