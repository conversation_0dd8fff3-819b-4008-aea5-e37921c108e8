@darkTheme @xc
Feature: To verify the global navigation for Dark Theme
# XC-11033 
# Administration
@sanity
Scenario: To verify the Administration - Account Management links
  Given User is able to view "Administration" in the global navigation
   When User clicks on Account Management links under company profile and verify they are directed to respective screens
   Then User clicks on subscription and branding links and verify the screens

@sanity
Scenario: To verify the Admin Management - Administrator Management links
  Given User is able to view "Administration" in the global navigation
   When User clicks on the admin management and then "Administrator Management"
   Then User clicks on the link and verify the screens

@sanity
Scenario: To verify the Admin Management - Role Based Access Control links
  Given User is able to view "Administration" in the global navigation
   When User clicks on the admin management and then "Role Based Access Control"
   Then User verify the roles and Entitlements links

@regression
Scenario: To verify the Admin Management - Audit Logs links
  Given User is able to view "Administration" in the global navigation
   When User clicks on the admin management and then "Audit Logs"
   Then User verify the audit logs links

@regression
Scenario: To verify the Identity - ZIdentity links
  Given User is able to view "Administration" in the global navigation
   When User clicks on the Identity and selects ZIdentity
   Then User verify the IDP Configuration, User Management, Domains, Passwords Authentication and Connectors links
@regression
Scenario: To verify the Identity - Internet & SaaS links
  Given User is able to view "Administration" in the global navigation
   When User clicks on the Identity and selects Internet and SaaS
   Then User verify the Internet Authentication Setting, SCIM Event Logs, User Management and Identity Proxy links
@regression
Scenario: To verify the Identity - Private Access links
  Given User is able to view "Administration" in the global navigation
   When User clicks on the Identity and selects Private Access
   Then User verify the IDP Configuration, Device Authentication and Partner Login links
   
@regression
Scenario: To verify the Entitlements links
  Given User is able to view "Administration" in the global navigation
   When User clicks on the Entitlements
   Then User verify the Private Access, Digital Experience and Internet Access links

@regression
Scenario: To verify the API Configuration - OneAPI links
  Given User is able to view "Administration" in the global navigation
   When User clicks on the API Configuration and OneAPI
   Then User verify the OneAPI links

@regression
Scenario: To verify the API Configuration - Legacy API links
  Given User is able to view "Administration" in the global navigation
   When User clicks on the API Configuration and Legacy API
   Then User verify the Legacy API links

@regression
Scenario: To verify the Alerts links
  Given User is able to view "Administration" in the global navigation
   When User clicks on the Alerts
   Then User verify the Internet and SaaS, Digital Experience Monitoring, Private Access and Zero Trust Branch links

@regression
Scenario: To verify the Backup & Restore links
  Given User is able to view "Administration" in the global navigation
   When User clicks on the Backup and Restore
   Then User verify the Backup and Restore links

# Infrastructure
@regression
Scenario: Verify all the navigation link of Infrastructure > Private Access > Component 
  Given User is able to view "Infrastructure" in the global navigation
   Then click on private access option
   Then click on Component and verify all the links are working fine

@regression
Scenario: Verify all the navigation link of Infrastructure > Private Access > Business Continuity 
  Given User is able to view "Infrastructure" in the global navigation
   Then click on private access option
   Then click on Business Continuity and verify all the links are working fine

@regression
Scenario: Verify all the navigation link of Infrastructure > Private Access > Client Connector Policies
  Given User is able to view "Infrastructure" in the global navigation
   Then click on private access option
   Then click on Client Connector Policies and verify all the links are working fine
@regression
Scenario: Verify all the navigation link of Infrastructure > Internet && SaaS  > Traffic Forwarding
  Given User is able to view "Infrastructure" in the global navigation
   Then click on Internet & SaaS access option
   Then click on Traffic Forwarding and verify all the links are working fine

@regression
Scenario: Verify all the navigation link of Infrastructure > Internet && SaaS  > Network Policies
  Given User is able to view "Infrastructure" in the global navigation
   Then click on Internet & SaaS access option
   Then click on Network Policies and verify all the links are working fine

@regression
Scenario: Verify all the navigation link of Infrastructure > Locations
  Given User is able to view "Infrastructure" in the global navigation
   Then click on Locations and verify all the links are working fine

@regression
Scenario: Verify all the navigation link of Infrastructure > Connectors > Client
  Given User is able to view "Infrastructure" in the global navigation
   Then click on Connectors option
   Then click on Client and verify all the links are working fine

@regression
Scenario: Verify all the navigation link of Infrastructure > Connectors > Edge
  Given User is able to view "Infrastructure" in the global navigation
   Then click on Connectors option
   Then click on Edge and verify all the links are working fine

@regression
Scenario: Verify all the navigation link of Infrastructure > Connectors > Cloud
  Given User is able to view "Infrastructure" in the global navigation
   Then click on Connectors option
   Then click on Cloud and verify all the links are working fine

@regression
Scenario: Verify all the navigation link of Infrastructure > Common Resources > Gateways 
  Given User is able to view "Infrastructure" in the global navigation
   Then click on Common Resources option
   Then click on Gateways and verify all the links are working fine

@regression
Scenario: Verify all the navigation link of Infrastructure > Common Resources > Application 
  Given User is able to view "Infrastructure" in the global navigation
   Then click on Common Resources option
   Then click on Application and verify all the links are working fine

@regression
Scenario: Verify all the navigation link of Infrastructure > Common Resources > Deployment 
  Given User is able to view "Infrastructure" in the global navigation
   Then click on Common Resources option
   Then click on Deployment and verify all the links are working fine

# Logs
@regression
Scenario: To verify the Logs - Insights links
  Given User is able to view "Logs" in the global navigation
   When User clicks on the Insights
   Then User verify the Internet and SaaS, Private Applications, Branch and Cloud Connectors and Zero Trust Branch links

@regression
Scenario: To verify the Logs - Log Streaming links
  Given User is able to view "Logs" in the global navigation
   When User clicks on the Log Streaming
   Then User verify the Internet Log Streaming, and Private Log Streaming links  
