@networking @xc

Feature: Verify the networking component

# XC-5905
@sanity
  Scenario: Verify filters in Networking Page
    When User is in Networking Page
    Then Verify the filters in Networking Page

# XC-5906, XC-5907
@sanity
  Scenario: Verify Traffic in my Organization
    When User is in Networking Page
    Then Verify the title "Traffic in my Organization" in Networking Page
    Then Ensure that you are in "Internet & SaaS" tab in Networking Page
    Then Verify the label and value of "Number of Transactions" under "Internet & SaaS" in Networking Page
    Then Verify the label and value of "Traffic Volume" under "Internet & SaaS" in Networking Page
    Then Ensure that "Traffic in my Organization" graph canvas of type "multiline-chart" is present under "Internet & SaaS" in Networking Page
    Then Ensure that you are in "Private" tab in Networking Page
    Then Verify the label and value of "Number of Transactions" under "Private" in Networking Page
    Then Verify the label and value of "Traffic Volume" under "Private" in Networking Page
    Then Ensure that "Traffic in my Organization" graph canvas of type "multiline-chart" is present under "Private" in Networking Page

# XC-5911
@sanity
Scenario: Verify Internet Traffic Distribution
  When User is in Networking Page 
  Then Verify the title "Internet Traffic Distribution" in Networking Page
  Then Ensure that "Internet Traffic Distribution" graph canvas of type "donut-chart" is present in Networking Page

# XC-5920
@sanity
Scenario: Verify Top Locations sending Internet Traffic to Zscaler
  When User is in Networking Page 
  Then Verify the title "Top Locations sending Internet Traffic to Zscaler" in Networking Page
  Then Ensure that "Top Locations sending Internet Traffic to Zscaler" graph canvas of type "horizontal-bar-chart" is present in Networking Page

# XC-5912
@sanity
Scenario: Verify Data Centers
  When User is in Networking Page 
  Then Ensure that you are in "Data Centers" tab in Networking Page
  Then Verify the title "Top Zscaler Data Centers Used" in Networking Page
  Then Ensure that "Top Zscaler Data Centers Used" graph canvas of type "world-bubbles-chart" is present in Networking Page

# XC-5912
@sanity
Scenario: Verify App Connectors
  When User is in Networking Page 
  Then Ensure that you are in "App Connectors" tab in Networking Page
  Then Verify the title "Locations with Zscaler Connectors" in Networking Page
  Then Ensure that "Locations with Zscaler Connectors" graph canvas of type "world-icon-chart" is present in Networking Page

# XC-5912
@sanity
Scenario: Verify Branch & Cloud Connectors
  When User is in Networking Page 
  Then Ensure that you are in "Branch & Cloud Connectors" tab in Networking Page
  Then Verify the title "Branch & Cloud Connectors" in Networking Page
  Then Ensure that "Branch & Cloud Connectors" graph canvas of type "world-pie-chart" is present in Networking Page
  Then Ensure User can navigate to "Connector Activity" Page while clicking on footer under "Branch & Cloud Connectors" in Networking Page