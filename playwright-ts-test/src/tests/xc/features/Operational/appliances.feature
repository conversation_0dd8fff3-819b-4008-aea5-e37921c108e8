@xc
Feature: To verify the Appliances component in Operational

# XC-7044 XC-7045
@sanity
   Scenario: Verify filters in Appliances page
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    And User navigates to the Appliances tab
    Then Verify the filters in Operational Appliances page

# XC-7046 XC-7048
@sanity
   Scenario: To verify Deployment Status widget in the Appliances tab
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    And User navigates to the Appliances tab
    And User verify the title "Deployment Status"
    Then Verify the "Deployment Status Graph" graph canvas of type "donut-chart" is present in Appliances

# XC-7047 XC-7049
@sanity
   Scenario: To verify Active Status widget in the Appliances tab
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    And User navigates to the Appliances tab
    And User verify the title "Active Status"
    Then Verify the "Active Status Graph" graph canvas of type "donut-chart" is present in Appliances

# XC-7825
@sanity
   Scenario: To verify Deployed Connector table in the Appliances tab
    Given User open the URL
    When User navigates to the Operational "Devices" tab
    And User navigates to the Appliances tab
    Then Verify the "Deployed Connectors" table in Appliances
