@AccountSettings @Login
Feature: Verify the Account Settings component

  Background:
    When After login user is in Networking Page
    Then User fetch the okta token
    Given User navigates to the "Account Settings" option

  Scenario: Verify the Account Settings page header
    Then User verify the "Account Settings" page header

  Sc<PERSON>rio: Verify the Login ID label
    Then verify the label "Login ID"

  Scenario: Verify the Organization ID label
    Then verify the label "Organization ID"

  Scenario: Verify the Zscaler Cloud label
    Then verify the label "Zscaler Cloud"

  Scenario: Verify the Settings section header
    Then User verify the "Settings" header

  Scenario: Verify the Language label
    Then verify the label "Language"

  Scenario: Verify the Time Zone label
    Then verify the label "Time Zone"

  Scenario: Verify the Password label
    Then verify the label "Password"

  Scenario: Verify the Theme label
    Then verify the label "Theme"

  Scenario: Verify the Default Zscaler Cloud label
    Then verify the label "Default Zscaler Cloud"

  Scenario: Verify Cancel button returns to default tab
    Then user click on cancel button

  Scenario: To verify the Login ID matches user profile API
    Then Verify the Login ID with user profile API

  Scenario: To verify the Organization ID matches user profile API
    Then Verify the Organization ID with user profile API

  Scenario: To verify the Language preference with user profile API
    Then Verify the Language preference with user profile API

  Scenario: To verify the Time Zone preference with user profile API
    Then Verify the Time Zone preference with user profile API
    
  Scenario: To verify the Theme (dark mode) preference with user profile API
    Then Verify the Theme dark mode preference with user profile API