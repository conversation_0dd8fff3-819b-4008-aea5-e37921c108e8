@onboarding-securetraffic @onboarding-xc @landing
Feature: Verify the Onboarding Secure Traffic

# XC-2516
Scenario: Verify SetUp Users completed 
    When User is in Onboarding Landing Page
    Then Verify Three steps as step 1 "completed", step 2 "incomplete", step 3 "incomplete" in Landing Page

# XC-10110, XC-10109, XC-2519, XC-2518, XC-2517, XC-2516, XC-2515, XC-2514, XC-2513, XC-2512
Scenario: Verify Secure Traffic
    When User is in Onboarding Landing Page
    Then User enters into Secure Traffic Page
    Then Verify the "Step 1 of 4" stepper label on "Step 1" of Secure Traffic Page
    Then Verify user can able to see "Clients" Heading on "Step 1" of Secure Traffic Page
    Then Verify user can able to see "Help us secure your organization by answering a few questions so we can optimize your initial security configuration. Don’t worry, you can refine these initial settings after you are up and running with Zscaler." Description on "Step 1" of Secure Traffic Page
    Then Verify user can able to see "Which of these platforms do you use?" Prompt on "Step 1" of Secure Traffic Page
    Then User checks tile on "Step 1" of Secure Traffic Page
    Then User Selects on "Windows" tile on "Step 1" of Secure Traffic Page
    Then User clicks on "Next" on "Step 1" of Secure Traffic Page
    Then Verify the "Step 2 of 4" stepper label on "Step 2" of Secure Traffic Page
    Then Verify user can able to see "Traffic Forwarding" Heading on "Step 2" of Secure Traffic Page
    Then Verify user can able to see "Help us secure your organization by answering a few questions so we can optimize your initial security configuration. Don’t worry, you can refine these initial settings after you are up and running with Zscaler." Description on "Step 2" of Secure Traffic Page
    Then Verify user can able to see "Do you currently have a VPN configured?" Prompt on "Step 2" of Secure Traffic Page
    Then check the VPN options on "Step 2" of Secure Traffic Page
    Then Verify user can able to see "What are the IP addresses or hostnames of your VPN?" Prompt on "Step 2" of Secure Traffic Page
    Then Verify user can able to see "Note: This traffic will bypass Zero Trust security." Note on "Step 2" of Secure Traffic Page
    Then check the VPN configuration box on "Step 2" of Secure Traffic Page
    Then User clicks on "Next" on "Step 2" of Secure Traffic Page
    Then Verify the "Step 3 of 4" stepper label on "Step 3" of Secure Traffic Page
    Then Verify user can able to see "Select Users for Zscaler Client Connector Distribution" Heading on "Step 3" of Secure Traffic Page
    Then Verify user can able to see "Select users to receive an email to download Zscaler Client Connector. Installing Zscaler Client Connector allows them to connect to the Zero Trust Exchange and be protected through Zscaler. Users must have admin rights on the endpoint to install Zscaler Client Connector." Description on "Step 3" of Secure Traffic Page
    Then Verify Filters on "Step 3" of Secure Traffic Page
    Then Verify Search Bar along with "Secure Traffic" Table on "Step 3" of Secure Traffic Page
    Then User clicks username from table on "Step 3" of Secure Traffic Page
    Then User clicks on "Next" on "Step 3" of Secure Traffic Page
    Then Verify the "Step 4 of 4" stepper label on "Step 4" of Secure Traffic Page
    Then Verify user can able to see "Distribute Zscaler Client Connector Application" Heading on "Step 4" of Secure Traffic Page
    Then Verify user can able to see "As a final step, start flowing traffic through the Zscaler Client Connector application to your users." Sub Heading on "Step 4" of Secure Traffic Page
    Then Verify user can able to see "Enable automatic client updates (recommended)" Toggle Label on "Step 4" of Secure Traffic Page
    Then User Verify the Toggle on "Step 4" of Secure Traffic Page
    Then Verify user can able to see "Your users receive the following email with instructions to download and install Zscaler Client Connector:" Heading on "Step 4" of Secure Traffic Page
    # # Then user clicks on "Finish" on "Step 4" of Secure Traffic Page