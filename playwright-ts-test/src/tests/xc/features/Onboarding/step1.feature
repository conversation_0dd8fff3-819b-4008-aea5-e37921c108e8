@onboarding-step1 @onboarding-xc
Feature: Verify the Onboarding step1

# XC-11083
Scenario: Verify Step1 Page Title
    When User is in Onboarding Landing Page
    Then User enters into step1 Page
    Then Verify user can able to see "Users" Heading in step1 Page

# XC-2505
Scenario: Verify Filters
    When User is in Onboarding Landing Page
    Then User enters into step1 Page
    Then Verify Filters in step1 Page

# XC-2504
Scenario: Verify Table along with Search Bar
    When User is in Onboarding Landing Page
    Then User enters into step1 Page
    Then Verify "Users" Table along with Search Bar in step1 Page

# Scenario: Verify Upload CSV
#     When User is in Onboarding Landing Page
#     Then User enters into step1 Page
#     Then Verify Upload CSV in step1 Page

# XC-2502, XC-1328
Scenario: Verify Add User
    When User is in Onboarding Landing Page
    Then User gets Users Count in Onboarding Landing Page
    Then User enters into step1 Page
    Then Users Adds a User in step1 Page
    Then Users checks count after adding user in Onboarding Landing Page
    Then User gets Users Count in Onboarding Landing Page
    Then User enters into step1 Page
    Then Users deletes a User in step1 Page
    Then Users checks count after deleting a user in Onboarding Landing Page


