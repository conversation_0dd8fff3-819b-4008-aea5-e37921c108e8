@dpdashboard
Feature: Check Data Protection Data Channels Inline

Scenario: Verify Widgets in Data Channels Inline Page
  When User is in data channels inline page
  Then Verify the date filter in inline page
  Then Verify Time Range filter
  Then Verify the "Top Sensitive Data Types" bar chart widget in inline page
  Then Verify the "Sensitive GenAI Applications" donut chart widget in inline page
  Then Verify Click View All GenAI Activity
  Then Verify the "Top High Risk Applications to Eliminate" lowest usage widget in inline page
  Then Verify the "Top High Risk Applications to Secure" most usage widget in inline page
  Then Verify Click View All Data Discovery
  Then Verify the "Top High Risk Applications to Secure" most usage table in inline page
  Then Verify the "Sensitive Files in Top 10 ML Categories" spider node chart widget in inline page
  When Verify the "Top High Risk Applications to Eliminate" lowest usage table in inline page
  Then Verify the colors used for Critical, High, Medium risks
  Then Verify the Application names with users count should be available
  Then Verify the "Top high-risk application to Eliminate" Action Edit Policy
