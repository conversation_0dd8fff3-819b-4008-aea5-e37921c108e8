@dpdashboard
Feature: Check Data Protection Data Channels Saas Security

Scenario: Verify Widgets in Data Channels Saas Security
  When User is in data channels saas security
  Then Verify the date filter in saas security page
  Then Verify the "SaaS Incidents" bar chart widget in saas security page
  Then Verify information icon against each legend
  Then Verify the "SaaS Applications with Data Exposure" saas applications table widget in saas security page
  Then Verify the "Top Users with SaaS Incidents" users table widget in saas security page
  Then Verify the empty state for top users
