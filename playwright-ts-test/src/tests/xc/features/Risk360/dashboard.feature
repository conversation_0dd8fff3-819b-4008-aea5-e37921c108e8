@risk360Dashboard

Feature: Verify Risk360 Dashboard

  Scenario Outline: Verify Risk score in Organization Risk Score widget for a given risk category
    Given User is in Risk360 Dashboard
    Then User verifies the "<risk category>" risk score in Organization Risk Score widget
    Examples:
       |risk category          |
       |Overall Organization   |
       |External Attack Surface|
       |Compromise             |
       |Lateral Propagation    |
       |Data Loss              |


  Scenario Outline: Validate redirection from Organization Risk Score widget to Factors page when user clicks on Risk score category
    Given User is in Risk360 Dashboard
    When User clicks on the risk category "<risk category>" in Organization Risk Score widget
    Then Verify that user lands on Factors page
    Then Verify the Risk Score Category filter selected in Factors page "<risk category>"
    Examples:
       |risk category          |
       |External Attack Surface|
       |Compromise             |
       |Lateral Propagation    |
       |Data Loss              |

  Scenario: Verify Drill-down from Dashboard to Financial Risk page when User clicks View Details button in Financial Risk Tooltip
    Given User is in Risk360 Dashboard
    When User hovers on Finance icon in Organization Risk Score widget
    And User clicks on View Details button in Financial Risk tooltip   
    Then Verify that user lands on Financial Risk page

  Scenario: Verify drill-down from Dashboard to Factors page when User clicks on View All button in Contributing Factors by Entity section
    Given User is in Risk360 Dashboard
    When User clicks on View All button in Contributing Factors by Entity
    Then Verify that user lands on Factors page

  Scenario: Verify drill-down from Dashboard to Factors page when User clicks on View All button in Top 10 Factors section
    Given User is in Risk360 Dashboard
    When User clicks on View All button in Top 10 Factors
    Then Verify that user lands on Factors page   

  Scenario: Verify drill-down from Dashboard to Factors page when User clicks on View All button in High Impact Recommendations section
    Given User is in Risk360 Dashboard
    When User clicks on View All button in High Impact Recommendations
    Then Verify that user lands on Insights page

  Scenario: Verify that Risk360 customer with Standard SKU cannot add a custom strategy in Peer Score Settings and only default strategy is applicable
    Given User is in Risk360 Dashboard
    When User clicks on peer score settings icon
    And User hovers on Add Custom Strategy button
    Then Verify the tooltip text for Feature not subscribed  
    Then Verify that only Default strategy is displayed in Peer Score Settings table
    Then Verify Default strategy tooltip text

  Scenario: Verify that Risk360 customer with Standard SKU cannot delete the Default peer score strategy
    Given User is in Risk360 Dashboard
    When User clicks on peer score settings icon
    Then Verify that Default strategy cannot be deleted

  Scenario: Verify that Risk360 customer with Standard SKU can edit the Default peer score strategy
    Given User is in Risk360 Dashboard
    When User clicks on peer score settings icon
    And User clicks on Edit button of Peer score strategy with name "Default"
    And User sets the value for field "Industry Vertical" as "Healthcare" in peer score settings
    And User sets the value for field "Geographical Region" as "APJ" in peer score settings
    And User sets the value for field "Revenue ID" as "1M to 10M" in peer score settings
    And User clicks on Save button of Peer score settings
    Then Verify toaster with message "Selected strategy has been edited successfully."
    And User clicks on Edit button of Peer score strategy with name "Default"
    And User sets the value for field "Industry Vertical" as "Other" in peer score settings
    And User sets the value for field "Geographical Region" as "AMER" in peer score settings
    And User sets the value for field "Revenue ID" as "10B to 100B" in peer score settings
    And User clicks on Save button of Peer score settings
    Then Verify toaster with message "Selected strategy has been edited successfully."  

  Scenario: Verify that Top Risky Locations tile in Risk Events by Location widget displays the top risky locations in descending order  
    Given User is in Risk360 Dashboard
    Then Verify that Top Risky Locations tile in Risk Events by Location widget displays the top risky locations in descending order

  Scenario: Verify Zoom button functionality in Risk Events by Location map
    Given User is in Risk360 Dashboard
    Then Verify that Zoom Out button is in disabled state by default
    And User clicks on "Zoom In" button in Risk Events by Location map
    And User clicks on "Zoom Out" button in Risk Events by Location map
    Then Verify that Zoom Out button is in disabled state by default
    
  Scenario: Verify Risk Event bubble tooltip and verify drill-down to insights page upon clicking on risk event count
    Given User is in Risk360 Dashboard
    When User clicks on Risk Event bubble
    Then Verify that "Risk Event Bubble" Tooltip is visible
    Then Verify the Risk category in Risk event bubble tooltip
    And User closes the Risk Event Bubble tooltip
    And User clicks on Risk Event bubble
    And User clicks on event count in Risk event bubble tooltip
    Then Verify that correct Risk category filter is set in Insights page
    Then Verify that insight card of correct risk category is displayed in Insights page 

  Scenario: Verify tooltips of different risk categories in Organization Risk Score widget
  Given User is in Risk360 Dashboard
  Then Verify the tool tip text displayed for different risk categories   

  Scenario: Verify that Customized Risk Score is hidden in Risk Score Trend when Customized Risk Score checkbox is unchecked and it is visible when checkbox is checked in Risk Score Trend widget
    Given User is in Risk360 Dashboard
    And User clicks the "Customized Risk Score" check box in Risk score trend widget
    And User clicks on Risk score trend graph to trigger the tooltip
    Then Verify that Risk Score tooltip is visible and "Customized" Risk Score is hidden in Risk score trend
    And User clicks the "Customized Risk Score" check box in Risk score trend widget
    And User clicks on Risk score trend graph to trigger the tooltip
    Then Verify that Risk Score tooltip is visible and "Customized" Risk Score is visible in Risk score trend

  Scenario: Verify that Industry Peer Average Risk Score is hidden in Risk Score Trend when Industry Peer Average Score checkbox is unchecked and it is visible when checkbox is checked in Risk Score Trend widget
    Given User is in Risk360 Dashboard
    And User clicks the "Industry Peer Average Score" check box in Risk score trend widget
    And User clicks on Risk score trend graph to trigger the tooltip
    Then Verify that Risk Score tooltip is visible and "Industry Peer Avg" Risk Score is hidden in Risk score trend
    And User clicks the "Industry Peer Average Score" check box in Risk score trend widget
    And User clicks on Risk score trend graph to trigger the tooltip
    Then Verify that Risk Score tooltip is visible and "Industry Peer Avg" Risk Score is visible in Risk score trend

  Scenario: Verify that Zscaler Risk Score is hidden in Risk Score Trend when Risk Score checkbox is unchecked and it is visible when checkbox is checked in Risk Score Trend widget
    Given User is in Risk360 Dashboard
    And User clicks the "Risk Score" check box in Risk score trend widget
    And User clicks on Risk score trend graph to trigger the tooltip
    # Then Verify that Risk Score tooltip is visible and "Risk Score" Risk Score is hidden in Risk score trend
    And User clicks the "Risk Score" check box in Risk score trend widget
    And User clicks on Risk score trend graph to trigger the tooltip
    Then Verify that Risk Score tooltip is visible and "Risk Score" Risk Score is visible in Risk score trend
  
  Scenario: Verify Organization risk score tooltip and tooltip text
    Given User is in Risk360 Dashboard
    And User clicks on Organization Risk Score
    Then Verify the Organization risk score tooltip and tooltip text

  Scenario Outline: Verify factors count by entity and description in Contributing Factors by Entity widget
    Given User is in Risk360 Dashboard
    Then Verify the factors count by entity for entity - "<entity name>"
    Then Verify that the entity "<entity name>" has the description - "<entity description>"
    Examples: 
    |entity name | entity description                                                                                      |
    |Workforce   | An organization's employee base creates risk concentrated primarily around compromise                   |
    |3rd Parties | Third parties create risk related to lateral propagation and data loss                                  |
    |Applications| Applications are vulnerable to lateral propagation and expand an organization's external attack surface |
    |Assets      | Assets create risk distributed across the four stages of breach                                         |


    

