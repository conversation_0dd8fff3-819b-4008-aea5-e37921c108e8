
@risk360FinancialRisk

Feature: Verify Risk360 Financial Risk page

Scenario: Verify that user can set customized values in Financial Risk Settings drawer. Also verify that Save and Reset to Default buttons are disabled when the customized values are not set.
  Given User is in Risk360 Financial Risk page
  And User clicks on Financial Risk Settings button
  And User selects "Industry Vertical" as "Financial" in Financial Risk Settings drawer
  And User selects "Industry Annual Revenue Range ($)" as "1B to 10B" in Financial Risk Settings drawer
  And User enters Financial Loss lower bound as "1234"
  And User enters Financial Loss upper bound as "1234567"
  When User clicks on Save button in Financial Risk Settings drawer
  Then Verify toaster with message "Settings saved successfully. It may take up to 15 seconds to reflect the changes." in Financial Risk page
  When User clicks Reset To Default button in Financial Risk Settings drawer
  Then Verify toaster with message "Default changes were saved again. It may take up to 15 seconds to reflect the changes." in Financial Risk page
  Then Verify that Save button in Financial Risk Settings drawer is disabled
  Then Verify that Reset To Default button in Financial Risk Settings drawer is disabled
  Then User clicks on Financial Risk Settings Drawer close button

Scenario: Verify that error is thrown when user tries to set same values in Upper and Lower bound fields in Financial Risk Settings
  Given User is in Risk360 Financial Risk page
  And User clicks on Financial Risk Settings button
  And User selects "Industry Vertical" as "Agriculture" in Financial Risk Settings drawer
  And User selects "Industry Annual Revenue Range ($)" as "10B to 100B" in Financial Risk Settings drawer
  And User enters Financial Loss lower bound as "1234"
  And User enters Financial Loss upper bound as "1234"
  When User clicks on Save button in Financial Risk Settings drawer
  Then Verify toaster with message "Not the correct input." in Financial Risk page
  Then User clicks on Cancel button in Financial Risk Settings drawer

Scenario: Verify that error is thrown when user tries to set same Upper bound value lesser than the Lower bound value in Financial Risk Settings
  Given User is in Risk360 Financial Risk page
  And User clicks on Financial Risk Settings button
  And User selects "Industry Vertical" as "Agriculture" in Financial Risk Settings drawer
  And User selects "Industry Annual Revenue Range ($)" as "10B to 100B" in Financial Risk Settings drawer
  And User enters Financial Loss lower bound as "1234"
  And User enters Financial Loss upper bound as "1233"
  When User clicks on Save button in Financial Risk Settings drawer
  Then Verify toaster with message "Not the correct input." in Financial Risk page
  Then User clicks on Cancel button in Financial Risk Settings drawer

Scenario: Verify that error is thrown when user tries to enter alphabets or special characters in Upper and Lower bound fields
  Given User is in Risk360 Financial Risk page
  And User clicks on Financial Risk Settings button
  And User selects "Industry Vertical" as "Agriculture" in Financial Risk Settings drawer
  And User selects "Industry Annual Revenue Range ($)" as "10B to 100B" in Financial Risk Settings drawer
  And User enters Financial Loss lower bound as "123a"
  And User enters Financial Loss upper bound as "1234nup@"
  When User clicks on Save button in Financial Risk Settings drawer
  Then Verify toaster with message "Not the correct input." in Financial Risk page
  Then User clicks on Cancel button in Financial Risk Settings drawer

Scenario: Verify the tooltip and tooltip text of Financial Loss Range in Default Values section
  Given User is in Risk360 Financial Risk page
  When User clicks on Financial Risk Settings button
  Then Verify the tooltip and tooltip text of Financial Loss Range in Default Values section

Scenario: Verify the tooltip and tooltip text of Financial Loss Range in Customized Values section
  Given User is in Risk360 Financial Risk page
  When User clicks on Financial Risk Settings button
  Then Verify the tooltip and tooltip text of Financial Loss Range in Customized Values section

Scenario: Verify the tooltip and tooltip text of Customized Values in Customized Values section
  Given User is in Risk360 Financial Risk page
  When User clicks on Financial Risk Settings button
  Then Verify the tooltip and tooltip text of Customized Values in Customized Values section