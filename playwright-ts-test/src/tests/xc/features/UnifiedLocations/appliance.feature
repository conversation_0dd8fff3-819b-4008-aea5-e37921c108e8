
# Feature: Verify the unified Location Appliance functionality
#   # XC-7135 XC-7149
#   Scenario: To verify the Main view of the Appliance for physical devices
#     Given User is able to view "Infrastructure" in the global navigation
#     When User clicks on the appliance link
#     And User search "LR192306000255" in the appliance page
#     Then Verify the physical device details in the appliance

#   Scenario: To verify the Main view of the Appliance for virtual devices
#     Given User is able to view "Infrastructure" in the global navigation
#     When User clicks on the appliance link
#     And User search "BC_Test_Automation" in the appliance page
#     Then Verify the virtual device details in the appliance

#   Scenario: To verify navigation from appliances main page with appliance name
#     Given User is able to view "Infrastructure" in the global navigation
#     When User clicks on the appliance link
#     And User search "LR192306000255" in the appliance page
#     Then Verify the physical device details in the appliance
#     And Clicks on the name data "LR192306000255"
#     And Verify the "Name" data in appliance