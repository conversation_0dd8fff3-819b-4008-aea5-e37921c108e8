@UnifiedLocations
Feature: Verify the unified IPSec Location functionality
    # XC-8063,XC-8064,XC-8091,XC-8092,XC-8093, XC-8049, XC-8050, XC-8051
    Scenario: To verify the Add IPSec Location functionality with  Basic and Digest Enabled
        Given User is able to view "Infrastructure" in the global navigation
        When User clicks on the locations link
        And User clicks on the "Add IPSec/GRE Location" button
        And User enter the mandatory values with name "Location_Automation_11"
        And User select the "Exclude from Manual Location Groups" checkbox
        And User select the "Exclude from Dynamic Location Groups" checkbox
        Then User fill in the traffic type as "Guest Wi-Fi traffic" and other details
        Then Clicks on the Add button
        And User search "Location_Automation_11" in the search bar
        And Clicks on the name data "Location_Automation_11"
        And User click on the "Connection Options" tab
        And User fill the form as "caution and aup warning"
        Then Clicks on the "Connection Options" drawer save button
        Then User verify the details of the locations
        And User click on the "Overview" tab
        And Delete location from Overview

    #XC-8056, XC-8058, XC-8057, XC-8053, XC-8055 ,XC-8065 ,XC-8093, XC-8061, XC-8062, XC-8054
    <PERSON><PERSON>rio: To verify the Add IPSec Location functionality with IP Surrogate Enabled & non default values for Idle Time and Refresh Time
        Given User is able to view "Infrastructure" in the global navigation
        When User clicks on the locations link
        And User clicks on the "Add IPSec/GRE Location" button
        And User enter the mandatory values with name "Location_Automation_12"
        And User select the "Exclude from Manual Location Groups" checkbox
        And User select the "Exclude from Dynamic Location Groups" checkbox
        Then User fill in the traffic type as "Guest Wi-Fi traffic" and other details
        Then Clicks on the Add button
        And User search "Location_Automation_12" in the search bar
        And Clicks on the name data "Location_Automation_12"
        And User click on the "Connection Options" tab
        And User Enable the form as "Authentication and IP Surrogate Enabled"
        Then Clicks on the "Connection Options" drawer save button
        Then User verify the "card" of the locations
        And User click on the "Overview" tab
        And Delete location from Overview

    #XC-10258, XC-8059, XC-8052, XC-7098, XC-7095
    Scenario: To verify that all fields in Connection Options are disabled by default for a newly created location
        Given User is able to view "Infrastructure" in the global navigation
        When User clicks on the locations link
        And User clicks on the "Add IPSec/GRE Location" button
        And User enter the mandatory values with name "Location_Automation_13"
        And User select the "Exclude from Manual Location Groups" checkbox
        And User select the "Exclude from Dynamic Location Groups" checkbox
        Then User fill in the traffic type as "Guest Wi-Fi traffic" and other details
        Then Clicks on the Add button
        And User search "Location_Automation_13" in the search bar
        And Clicks on the name data "Location_Automation_13"
        And User click on the "Connection Options" tab
        Then User verify the default state of connection options card
        And User click on the "Overview" tab
        And Delete location from Overview

    # XC-8096, XC-8070
    Scenario: To verify create multiple Locations by giving different values for Time Zone , Country and City
        Given User is able to view "Infrastructure" in the global navigation
        When User clicks on the locations link
        And User clicks on the "Add IPSec/GRE Location" button
        And User enter the mandatory values with name "Location_Automation_14"
        And User select the "Exclude from Manual Location Groups" checkbox
        And User select the "Exclude from Dynamic Location Groups" checkbox
        Then User fill in the traffic type as "Guest Wi-Fi traffic" and other details
        Then Clicks on the Add button
        And User search "Location_Automation_14" in the search bar
        And Delete the location
        And User clicks on the "Add IPSec/GRE Location" button
        And User enter the mandatory values again with name "Location_Automation_15"
        And User select the "Exclude from Manual Location Groups" checkbox
        And User select the "Exclude from Dynamic Location Groups" checkbox
        Then User fill in the traffic type as "Guest Wi-Fi traffic" and other details
        Then Clicks on the Add button
        And User search "Location_Automation_15" in the search bar
        And Delete the location

    # XC-8066 XC-8067
    # Scenario: To verify the IOT Discovery and IOT Policy Control Enabled
    #     Given User is able to view "Infrastructure" in the global navigation
    #     When User clicks on the locations link
    #     And User clicks on the "Add IPSec/GRE Location" button
    #     And User enter the mandatory values with name "Location_Automation_17"
    #     And User select the "Exclude from Manual Location Groups" checkbox
    #     And User select the "Exclude from Dynamic Location Groups" checkbox
    #     Then User fill in the traffic type as "Guest Wi-Fi traffic" and other details
    #     Then Clicks on the Add button
    #     And User search "Location_Automation_17" in the search bar
    #     And Clicks on the name data "Location_Automation_17"
    #     And User click on the "Connection Options" tab
    #     And User Enable the toggle as "IOT Discovery and IOT Policy Control Enabled"
    #     Then Clicks on the "Connection Options" drawer save button
    #     Then User verify the IoT Discovery of the locations
    #     And User click on the "Overview" tab
    #     And Delete location from Overview

	# XC-7192
	# Scenario: To verify location name and description is edited successfully
	# 	Given User is able to view "Infrastructure" in the global navigation
    #     When User clicks on the locations link
    #     And User clicks on the "Add IPSec/GRE Location" button
    #     And User enter the mandatory values with name "Location_Automation_16"
    #     And User select the "Exclude from Manual Location Groups" checkbox
    #     And User select the "Exclude from Dynamic Location Groups" checkbox
    #     Then User fill in the traffic type as "Corporate user traffic" and other details
    #     Then Clicks on the Add button
    #     And User search "Location_Automation_16" in the search bar
	# 	And Clicks on the name data "Location_Automation_16"
	# 	And User clicks on the edit and fill in all the details with location name "Location_Automation_16"
	# 	Then Clicks on the "Location" drawer save button
	# 	And User click on the "Overview" tab
    #     And Delete location from Overview