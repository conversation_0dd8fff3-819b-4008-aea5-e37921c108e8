@UnifiedLocations
Feature: Verify the unified locations functionality
  # XC-7134 XC-7085
  Scenario: To verify the locations view and the search functionality in the locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And Verify the locations view
    And User search "UL_Test_Automation" in the search bar
    Then Verify the details in the locations
  # XC-7089
  Scenario: To verify search results according to type of locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "UL_Test_Automation" in the search bar
    And Clicks on the "Edge" tab
    And Verify the details in the locations
    Then Clicks on the "Cloud" tab
    And Verify the No result found message
  # XC-8137
  # Scenario: To verify the Overview in locations
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User search "UL_Test_Automation" in the search bar
  #   And Clicks on the name data "UL_Test_Automation"
  #   Then Verify the "Overview" with data "UL_Test_Automation" of the locations
  # XC-7099 XC-7096
  Scenario: To verify the Connection Types in locations
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User search "UL_Test_Automation" in the search bar
    And Clicks on the connection type "IPSec/GRE"
    Then Verify the "IPSec/GRE" with data "IPSec/GRE" of the locations
  # XC-8137 XC-7097 XC-7088
  # Scenario: To verify the Sublocations in locations
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User search "UL_Test_Automation" in the search bar
  #   And Clicks on the Sublocations data
  #   Then Verify the "Sublocations" with data "Sublocations" of the locations
  #   Then Verify sublocations count to be "2"
  # XC-7150 XC-7865 XC-8042
  Scenario: To verify the Add IPSec Location functionality with traffic type as Corporate user traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "IPSec Location Corporate user traffic" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "IPSec Location Corporate user traffic"
    And User select the "Exclude from Manual Location Groups" checkbox
    Then User fill in the traffic type as "Corporate user traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location Corporate user traffic"
    And Delete the location
  # XC-8047 XC-7866 XC-8040
  Scenario: To verify the Add IPSec Location functionality with traffic type as Guest Wi-Fi traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "IPSec Location Guest Wi-Fi traffic" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "IPSec Location Guest Wi-Fi traffic"
    And User select the "Exclude from Dynamic Location Groups" checkbox
    Then User fill in the traffic type as "Guest Wi-Fi traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location Guest Wi-Fi traffic"
    And Delete the location
  # XC-8043 XC-7869 XC-10296
  Scenario: To verify the Add IPSec Location functionality with traffic type as IoT traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "IPSec Location IoT traffic" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "IPSec Location IoT traffic"
    And User select the "Exclude from Manual Location Groups" checkbox
    And User select the "Exclude from Dynamic Location Groups" checkbox
    Then User fill in the traffic type as "IoT traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location IoT traffic"
    And Delete the location
  # XC-8041  XC-7870 XC-809
  Scenario: To verify the Add IPSec Location functionality with traffic type as Server traffic
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "IPSec Location Server traffic" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "IPSec Location Server traffic"
    Then User fill in the traffic type as "Server traffic" and other details
    Then Clicks on the Add button
    Then Verify the IPSec created "IPSec Location Server traffic"
    And Delete the location

  # Scenario: To verify Add Edge Location functionality
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User clicks on the "Add Edge Location" button
  #   Then Verify the user is redirected to the branch provisioning

  # Scenario: To verify Add Cloud Location functionality
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User clicks on the "Add Cloud Location" button
  #   Then Verify the user is redirected to the provisioning

  # Scenario: To verify the pagination of locations screen
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   Then Verify the rows per page and select the "100"

  # Scenario: To verify the Sync locations button is clickable
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   Then Verify the sync locations button is clickable

  # XC-8048 XC-7104
  # Scenario: To verify the Add IPSec Location functionality with Virtual Service Edge Clusters
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   Then Verify if location "UL_Automation_11" already exists
  #   And User clicks on the "Add IPSec/GRE Location" button
  #   And User enter the mandatory values with name "UL_Automation_11"
  #   And User select the "Exclude from Manual Location Groups" checkbox
  #   Then User fill in the traffic type as "Corporate user traffic" and other details
  #   Then Clicks on the Add button
  #   Then Verify the IPSec created "UL_Automation_11"
  #   And Clicks on the name data "UL_Automation_11"
  #   And User click on the "IPSec/GRE" tab
  #   And Verify the "Virtual Service Edge Clusters" data in IPSecGRE
  #   And User click on the "Overview" tab
  #   And Delete location from Overview

  # XC-8069
  Scenario: To verify create location duplicate
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values again with name "UL_Test_Automation"
    Then User fill in the traffic type as "Guest Wi-Fi traffic" and other details
    Then Clicks on the Add button
    Then Verify the duplicate name error message


  # XC-8045 XC-7101
  Scenario: To verify the Add IPSec Location functionality with Proxy Ports
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "UL_Automation_14" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "UL_Automation_14"
    Then User fill in the traffic type as "Corporate user traffic" and other details
    Then User fill in the proxy port
    Then Clicks on the Add button
    And User search "UL_Automation_14" in the search bar
    And Clicks on the name data "UL_Automation_14"
    And User click on the "IPSec/GRE" tab
    And Verify the "Proxy Ports" data in IPSecGRE
    And User click on the "Overview" tab
    And Delete location from Overview

  #XC-8077 XC-7100 XC-7102
  Scenario: To verify the Add IPSec Location functionality with Static IP & VPN Credentials
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "UL_Automation_11" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "UL_Automation_11"
    Then User fill in the traffic type as "Corporate user traffic" and other details
    Then User fill in the Static ip & VPN credentials
    Then Clicks on the Add button
    And User search "UL_Automation_11" in the search bar
    And Clicks on the name data "UL_Automation_11"
    And User click on the "IPSec/GRE" tab
    And Verify the "Static IPs & GRE" data in IPSecGRE
    And Verify the "VPN Credentials" data in IPSecGRE
    And User click on the "Overview" tab
    And Delete location from Overview

  # XC-8046 XC-7103
  Scenario: To verify the Add IPSec Location functionality with Virtual Service Edges
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "UL_Automation_11" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "UL_Automation_11"
    Then User fill in the traffic type as "Corporate user traffic" and other details
    Then User fill in the Virtual Service Edges
    Then Clicks on the Add button
    And User search "UL_Automation_11" in the search bar
    And Clicks on the name data "UL_Automation_11"
    And User click on the "IPSec/GRE" tab
    And Verify the "Virtual Service Edges" data in IPSecGRE
    And User click on the "Overview" tab
    And Delete location from Overview

  # XC-7098, XC-8094
  Scenario: To verify the default values in Overview details for a newly created location
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "UL_Automation_12" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User fill in the location name as "UL_Automation_12"
    Then User fill in the traffic type as "Corporate user traffic" and other details
    Then Clicks on the Add button
    And User search "UL_Automation_12" in the search bar
    And Clicks on the name data "UL_Automation_12"
    Then User verify the default values of overview details card
    And Delete location from Overview

  # XC-7091
  # Scenario: To verify empty state for sub locations
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   Then Verify if location "UL_Automation_12" already exists
  #   And User clicks on the "Add IPSec/GRE Location" button
  #   And User enter the mandatory values with name "UL_Automation_12"
  #   Then User fill in the traffic type as "Corporate user traffic" and other details
  #   Then Clicks on the Add button
  #   And User search "UL_Automation_12" in the search bar
  #   And Clicks on the name data "UL_Automation_12"
  #   And User click on the "Sublocations" tab
  #   Then Verify empty state for tab with data "No Sublocations"
  #   And User clicks on Add Sublocation button
  #   Then User close the "Add Sublocation" drawer
  #   And User click on the "Overview" tab
  #   And Delete location from Overview

  # XC-7090
  # Scenario: To verify empty state for appliances
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   Then Verify if location "UL_Automation_12" already exists
  #   And User clicks on the "Add IPSec/GRE Location" button
  #   And User enter the mandatory values with name "UL_Automation_12"
  #   Then User fill in the traffic type as "Corporate user traffic" and other details
  #   Then Clicks on the Add button
  #   And User search "UL_Automation_12" in the search bar
  #   And Clicks on the name data "UL_Automation_12"
  #   And User click on the "Appliances" tab
  #   Then Verify empty state for tab with data "No Appliances"
  #   # the below commented steps will be fixed once data-testid id is available on the required elements
  #   # And User clicks on Add Appliance button
  #   # Then User close the "Add Appliance" drawer
  #   And User click on the "Overview" tab
  #   And Delete location from Overview

  # XC-7093
  # Scenario: To verify locations appliances page display branch connector for VM
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User search "bc-zsb-loc" in the search bar
  #   And Clicks on the connection type "Appliances"
  #   And Verify branch connector details are visible

  # XC-7871
  Scenario: To verify the Add IPSec Location functionality with out giving any traffic type
    Given User is able to view "Infrastructure" in the global navigation
    When User clicks on the locations link
    Then Verify if location "IPSec Location without traffic type" already exists
    And User clicks on the "Add IPSec/GRE Location" button
    And User enter the mandatory values with name "IPSec Location without traffic type"
    And User select the "Exclude from Manual Location Groups" checkbox
    Then User fill in the Static ip & VPN credentials
    Then Clicks on the Add button
    Then Verify the "Request body is invalid" error occured

  # XC-8042
  # Scenario: To verify the Add IPSec Location functionality with different Manual Location Groups
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   Then Verify if location "UL_Automation_12" already exists
  #   And User clicks on the "Add IPSec/GRE Location" button
  #   And User enter the mandatory values with name "UL_Automation_12"
  #   And User select the multiple Manual Location Groups
  #   Then User fill in the traffic type as "Corporate user traffic" and other details
  #   Then Clicks on the Add button
  #   And User search "UL_Automation_12" in the search bar
  #   And Clicks on the name data "UL_Automation_12"
  #   And User verify the Manual Location Groups value in overview details card
  #   And Delete location from Overview

  # XC-8044
  # Scenario: To verify the Add IPSec Location functionality with GRE Tunnels
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User search "UL_Test_Automation" in the search bar
  #   And Clicks on the connection type "IPSec/GRE"
  #   And User verify the GRE Tunnel Info

  #XC-8104
  # Scenario: To verify the Add Sublocation functionality with a single Internal IP Address & also verify Sublocation Name and Description in the Sublocation Drawer
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User clicks on the "Add IPSec/GRE Location" button
  #   And User enter the mandatory values with name "Autolocation-IntIP"
  #   And User select the "Exclude from Manual Location Groups" checkbox
  #   Then User fill in the traffic type as "Corporate user traffic" and other details
  #   Then Clicks on the Add button
  #   Then Verify the IPSec created "Autolocation-IntIP"
  #   And User search "Autolocation-IntIP" in the search bar
  #   And Clicks on the name data "Autolocation-IntIP"
  #   Then User clicks on the Sublocation tab
  #   And User clicks on "Add Sublocation" button to add the first Sublocation
  #   Then User enter the mandatory values for Sublocation with name "Autosubloc-IntIP"
  #   And User fill in the Internal IP Address as "**********"
  #   And Clicks on the Add button in Sublocation drawer
  #   Then User search for Sublocation 'Autosubloc-IntIP' in the search bar
  #   And Clicks on the sublocation name 'Autosubloc-IntIP'
  #   And User verifies the Sublocation Name to be "Autosubloc-IntIP" in the drawer
  #   And User verifies the Sublocation Description to be "Sublocation Testing" in the drawer
  #   And User verifies the Sublocation Groups "Exclude from Manual Location Groups" to be "unchecked"
  #   And User verifies the Sublocation Groups "Exclude from Dynamic Location Groups" to be "unchecked"
  #   And User verifies the Internal IP Addresses entered to be "**********"
  #   And Exits from Sublocation Drawer
  #   Then User search for Sublocation 'Autosubloc-IntIP' in the search bar
  #   Then Delete the Sublocation
  #   And Click on Location Overview tab
  #   Then Delete "Autolocation-IntIP" from Location Overview page


  #XC-10725 #XC-8105
  # Scenario: To verify the Sublocation creation with with Parent Location as Server Traffic Type & Exclude from Manual Location groups to be Enabled.
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User clicks on the "Add IPSec/GRE Location" button
  #   And User enter the mandatory values with name "Autolocation-ServerTraffic"
  #   Then User fill in the traffic type as "Server traffic" and other details
  #   Then Clicks on the Add button
  #   Then Verify the IPSec created "Autolocation-ServerTraffic"
  #   And User search "Autolocation-ServerTraffic" in the search bar
  #   And Clicks on the name data "Autolocation-ServerTraffic"
  #   Then User clicks on the Sublocation tab
  #   And User clicks on "Add Sublocation" button to add the first Sublocation
  #   Then User enter the mandatory values for Sublocation with name "AutoSublocation-ServerTraffic"
  #   And User select the "Exclude from Manual Location Groups" checkbox for Sublocation
  #   And User fill in the Internal IP Address as "**********,**********,**********,**********,**********,**********,**********,**********,***********,***********,***********,***********,***********,***********"
  #   And Clicks on the Add button in Sublocation drawer
  #   Then User search for Sublocation 'AutoSublocation-ServerTraffic' in the search bar
  #   And Clicks on the sublocation name 'AutoSublocation-ServerTraffic'
  #   And User verifies the Sublocation Groups "Exclude from Manual Location Groups" to be "checked"
  #   And User verifies the Sublocation Groups "Exclude from Dynamic Location Groups" to be "unchecked"
  #   And User verifies the Sublocation Traffic Type to be "Server traffic"
  #   And User verifies the Internal IP Addresses entered to be "**********-***********"
  #   And Exits from Sublocation Drawer
  #   Then User search for Sublocation 'AutoSublocation-ServerTraffic' in the search bar
  #   Then Delete the Sublocation
  #   And Click on Location Overview tab
  #   Then Delete "Autolocation-ServerTraffic" from Location Overview page

  #XC-7097
  # Scenario: To verify the Sublocations Page view and its contents
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User search "MaxSubLocations" in the search bar
  #   And Clicks on the name data "MaxSubLocations"
  #   Then User clicks on the Sublocation tab
  #   And Verify the Sublocations View
  #   Then User search for Sublocation "Sublocation-For-Automation" in the search bar
  #   And Verify the data in Sublocations Index page for 'Sublocation-For-Automation'
  #   And User clear the Sublocation Search bar
  #   Then User search for Sublocation "Subloc-Disabled-For-Automation" in the search bar
  #   And Verify the data in Sublocations Index page for 'Subloc-Disabled-For-Automation'
  #   And User clear the Sublocation Search bar
  #   Then User search for Sublocation "other" in the search bar
  #   And Verify the data in Sublocations Index page for 'other'
  #   And User clear the Sublocation Search bar

  #XC-11059,XC-8109
  # Scenario: To verify the Duplicate Sublocaton Creation and Duplicate Internal IP Address Configuration(Verify that if an existing 
  # Sublocation name or Internal IP Address is given, Sublocation creation fails.
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User search "MaxSubLocations" in the search bar
  #   And Clicks on the name data "MaxSubLocations"
  #   Then User clicks on the Sublocation tab
  #   And Clicks on the Add button to add more Sublocations
  #   Then User enter the mandatory values for Sublocation with name "Sublocation-For-Automation"
  #   And User fill in the Internal IP Address as "***********"
  #   And Clicks on the Add button in Sublocation drawer
  #   And Check for Internal IP Address "***********" already exists message in popup
  #   Then User remove the duplicate Internal IP Address from the list
  #   And User fill in the Internal IP Address as "***********"
  #   And Clicks on the Add button in Sublocation drawer
  #   And Check for Sublocation "Sublocation-For-Automation" already exists message in popup

  #XC-8138
  # Scenario: To verify Sublocation Search functionality using Sublocation Name, Description and Internal IP Address
  #   Given User is able to view "Infrastructure" in the global navigation
  #   When User clicks on the locations link
  #   And User search "MaxSubLocations" in the search bar
  #   And Clicks on the name data "MaxSubLocations"
  #   And User clicks on the Sublocation tab
  #   Then Verify that Sublocation search results display "other" when user enters 'other' in the Search bar
  #   And User clear the Sublocation Search bar
  #   Then Verify that Sublocation search results display "Subloc-Disabled-For-Automation" when user enters '*************' in the Search bar
  #   And User clear the Sublocation Search bar
  #   Then Verify that Sublocation search results display "other" when user enters 'subloc11 test description' in the Search bar
  #   And User clear the Sublocation Search bar
