@transactionalactivity @xc
Feature: Verify the Cybersecurity Transactional Activity component
# XC-6179
@sanity
Scenario: Verify date dropdown filters in Transactional Activity Page
   When User is in Transactional Activity Page
   Then Verify the default date in date dropdown is "14 Days" in Transactional Activity Page
   Then Verify the date dropdown in Transactional Activity Page

# XC-6153 XC-6170 XC-6172
@sanity
Scenario: To verify the Transactional Activity tab and To verify the All Threats graph 
    When User is in Transactional Activity Page 
    Then Verify the title "All Threats" in Transactional Activity Page
@sanity   
Scenario: To verify the All Threats graph 
    When User is in Transactional Activity Page 
    Then Ensure that "All Threats" graph canvas of type "multiline-chart" is present in Transactional Activity Page 
@sanity
Scenario: To verify the Top Users Generating Threats horizontal bar graph
   When User is in Transactional Activity Page 
   Then Verify the title "Top Users Generating Threats" in Transactional Activity Page
   Then Ensure that "Top Users Generating Threats" graph canvas of type "horizontal-bar-chart" is present in Transactional Activity Page 


# XC-6173
@sanity
Scenario: To verify the Top Departments Generating Threats horizontal bar graph
   When User is in Transactional Activity Page 
   Then Verify the title "Top Departments Generating Threats" in Transactional Activity Page
   Then Ensure that "Top Departments Generating Threats" graph canvas of type "horizontal-bar-chart" is present in Transactional Activity Page 


# XC-6174
@sanity
Scenario: To verify the Your Policy Blocks graph
   When User is in Transactional Activity Page 
   Then Ensure that you are in "Policy Blocks" tab in Transactional Activity Page
   Then Ensure that "Your Policy Blocks" graph canvas of type "multiline-chart" is present in Transactional Activity Page
@sanity
Scenario: To verify the Top Application Class bar graph
   When User is in Transactional Activity Page 
   Then Ensure that you are in "Policy Blocks" tab in Transactional Activity Page
   Then Ensure that "Top Application Class" graph canvas of type "horizontal-bar-chart" is present in Transactional Activity Page

@sanity
Scenario: To verify the Top URL Categories Blocked bar graph
   When User is in Transactional Activity Page 
   Then Ensure that you are in "Policy Blocks" tab in Transactional Activity Page
   Then Ensure that "Top URL Categories Blocked" graph canvas of type "horizontal-bar-chart" is present in Transactional Activity Page
 

# XC-6177
@sanity
   Scenario: To verify the Top Cloud Applications Blocked bubble graph
   When User is in Transactional Activity Page 
   Then Ensure that you are in "Policy Blocks" tab in Transactional Activity Page
   Then Ensure that "Top Cloud Applications Blocked" graph canvas of type "bubbles-chart" is present in Transactional Activity Page


# XC-6178
@sanity
   Scenario: To verify the Top Blocked File Types no results found
   When User is in Transactional Activity Page 
   Then Ensure that you are in "Policy Blocks" tab in Transactional Activity Page
   Then Ensure that "Top Blocked File Types" graph canvas of type "horizontal-bar-chart" is present in Transactional Activity Page
