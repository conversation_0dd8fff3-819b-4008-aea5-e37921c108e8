@threatlocations @xc
Feature: Verify the Threat Locations component
# XC-6188
@sanity
Scenario: Verify date dropdown filters in Threat Locations Page
   When User is in Threat Locations Page
   Then Verify the default date in date dropdown is "14 Days" in Threat Locations Page
   Then Verify the date dropdown in Threat Locations Page
    
# XC-6161 XC-6190 XC-6189
@sanity
Scenario: To verify the Threat Locations widgets
    When User is in Threat Locations Page 
    Then Verify the title "Total Threats Trend" in Threat Locations Page
    Then Ensure that "Total Threats Trend" graph canvas of type "multiline-chart" is present in Threat Locations Page 

# XC-6191 XC-6192
@sanity
Scenario: To verify the Top Threat Categories graph
    When User is in Threat Locations Page 
    Then Verify the title "Top Threat Categories" in Threat Locations Page
    Then Ensure that "Top Threat Categories" graph canvas of type "bubble-chart" is present in Threat Locations Page 


# XC-6193
@sanity
Scenario: To verify the Top Users Generating Threats bar graph
    When User is in Threat Locations Page 
    Then Verify the title "Top Users Generating Threats" in Threat Locations Page
    Then Ensure that "Top Users Generating Threats" graph canvas of type "horizontal-bar-chart" is present in Threat Locations Page

# XC-6194
@sanity
Scenario: To verify the Top Applications Generating Threats bar graph
     When User is in Threat Locations Page 
     Then Verify the title "Top Applications Generating Threats" in Threat Locations Page
     Then Ensure that "Top Applications Generating Threats" graph canvas of type "horizontal-bar-chart" is present in Threat Locations Page