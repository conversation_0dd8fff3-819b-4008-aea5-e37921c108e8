@sandboxthreat @xc
Feature: Verify the sandbox threats dashboard
# XC-6180
@sanity
Scenario: Verify date dropdown filters in Sandbox Threats Page
   When User is in Sandbox Threats Page
   Then Verify the default date in date dropdown is "14 Days" in Sandbox Threats Page
   Then Verify the date dropdown in Sandbox Threats Page

# XC-4814
@regression
Scenario: To verify the Policy Actions & Verdicts for Files Known by Cloud Effect widget
  When User is in Sandbox Threats Page 
  Then Verify the title "Policy Actions & Verdicts for Files Known by Cloud Effect" in Sandbox Threats Page 
  Then Ensure that "Policy Actions & Verdicts for Files Known by Cloud Effect" graph canvas of type "vertical-bar-chart" is present in Sandbox Threats Page

#XC-6185
@sanity
Scenario: To verify the Policy Actions & Verdicts for Unknown Files widget
    When User is in Sandbox Threats Page 
    Then Verify the title "Policy Actions & Verdicts for Unknown Files" in Sandbox Threats Page 
    Then Ensure that "Policy Actions & Verdicts for Unknown Files" graph canvas of type "vertical-bar-chart" is present in Sandbox Threats Page 

# XC-6186
@sanity
Scenario: To verify the Top Users Generating Sandbox Threats widget
    When User is in Sandbox Threats Page 
    Then Verify the title "Top Users Generating Sandbox Threats" in Sandbox Threats Page 
    Then Ensure that "Top Users Generating Sandbox Threats" graph canvas of type "horizontal-bar-chart" is present in Sandbox Threats Page 

# XC-6187
@sanity
Scenario: To verify the Top Sandbox Incidents widget
    When User is in Sandbox Threats Page 
    Then Verify the title "Top Sandbox Incidents" in Sandbox Threats Page 
    Then Ensure that "Top Sandbox Incidents" table is present in Sandbox Threats page