@cybersecurity @xc
Feature: Verify the Cybersecurity dashboard

# XC-6149
@sanity
Scenario: Verify date dropdown filters in Cybersecurity Page
   When User is in Cybersecurity Page
   Then Verify the default date in date dropdown is "14 Days" in Cybersecurity Page
   Then Verify the date dropdown in Cybersecurity Page
@sanity
Scenario: To verify the transactions widget in Cybersecurity for malicious transactions
   When User is in Cybersecurity Page 
   Then Verify the title "Your Cybersecurity Transactions" in Cybersecurity Page
   Then Verify the Description "Malicious transactions are threats that have been detected and blocked by <PERSON><PERSON><PERSON>." in Cybersecurity Page
   Then Verify the title "Total Threats Blocked" in Cybersecurity Page
   Then Verify the value of "Total Threats Blocked" in Cybersecurity Page
   Then Ensure that "Total Threats Blocked Graph" graph canvas of type "multiline-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "Transactional Activity" Page while clicking on footer under "Total Threats Blocked Graph" in Cybersecurity Page

# XC-6154 
@sanity
Scenario: To verify the Advanced threats Categories bar graph
   When User is in Cybersecurity Page 
   Then Verify the title "Advanced Threat Categories" in Cybersecurity Page
   Then Ensure that "Advanced Threat Categories" graph canvas of type "multiline-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "Advanced Threats" Page while clicking on footer under "Advanced Threat Categories" in Cybersecurity Page
@sanity
Scenario: To verify the Top Threat Locations bar graph
   When User is in Cybersecurity Page 
   Then Verify the title "Top Threat Locations" in Cybersecurity Page
   Then Ensure that "Top Threat Locations" graph canvas of type "horizontal-bar-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "Threat Locations" Page while clicking on footer under "Top Threat Locations" in Cybersecurity Page

# XC-6166
@sanity
Scenario: To verify the SSL/TLS Inspection Review bar line chart
   When User is in Cybersecurity Page 
   Then Verify the title "Your SSL/TLS Inspection Review" in Cybersecurity Page
   Then Ensure that "Your SSL/TLS Inspection Review" graph canvas of type "bar-line-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "SSL/TLS Inspection" Page while clicking on footer under "Your SSL/TLS Inspection Review" in Cybersecurity Page

# XC-6162
@sanity
Scenario: To verify the Sandbox Threats widget
   When User is in Cybersecurity Page 
   Then Verify the title "Sandbox Threats" in Cybersecurity Page
   Then Ensure that "Sandbox Threats" graph canvas of type "sankey-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "Sandbox Threats" Page while clicking on footer under "Sandbox Threats" in Cybersecurity Page

# XC-6150
@sanity
Scenario: To verify the transactions widget in Cybersecurity for Policy Blocks
   When User is in Cybersecurity Page 
   Then Ensure that you are in "Policy Blocks" tab in Cybersecurity Page
   Then Verify the title "Your Cybersecurity Transactions" in Cybersecurity Page
   Then Verify the Description "Blocks that are caused by policies you have configured to limit exposure to certain types of web content." in Cybersecurity Page
   Then Verify the title "Total Policy Blocks" in Cybersecurity Page
   Then Verify the value of "Total Policy Blocks" in Cybersecurity Page
   Then Ensure that "Total Policy Blocks Graph" graph canvas of type "multiline-chart" is present in Cybersecurity Page 
   Then Ensure User can navigate to "Transactional Activity" Page while clicking on footer under "Total Policy Blocks Graph" in Cybersecurity Page

