import { Page } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import ThemePage from "../../pages/DarkTheme/darkTheme_page";

const { Given, When, Then } = createBdd();

let themePage: ThemePage;

Given('User navigates to the {string}', async ({ page }: { page: Page }, accountSettings:string) => {
  themePage = new ThemePage(page);
  await themePage.navigateToProfileSettings(accountSettings);
});

Given('User clicks on the {string} radio button', async ({ page }: { page: Page }, darkThemeOption:string) => {
  themePage = new ThemePage(page); 
  await themePage.selectDarkOption(darkThemeOption);  
});

When("User fetches the background theme color", async ({ page }: { page: Page }) => {
  themePage = new ThemePage(page); 
  await themePage.fetchBackgroundColor();
});

Then("Verify the theme is {string}", async ({ page }: { page: Page }, expectedTheme: string) => {
  themePage = new ThemePage(page);
  await themePage.verifyTheme(expectedTheme);
});
