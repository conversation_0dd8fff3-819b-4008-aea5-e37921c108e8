import { createBdd } from "playwright-bdd";
const { Before, When, Then } = createBdd();
import CybersecurityDashboard from "../../pages/Cybersecurity/cybersecurityDashboard_page";
import { Page } from "@playwright/test";

let cybersecurityDashboardPage: CybersecurityDashboard;

Before({ tags: "@cybersecurity" }, async ({ page }: { page: Page }) => {
  cybersecurityDashboardPage = new CybersecurityDashboard(page);
});

When("User is in Cybersecurity Page", async () => {
  await cybersecurityDashboardPage.goto();
  await cybersecurityDashboardPage.navigateTo();
  await cybersecurityDashboardPage.checkUrl();
});

Then("Verify the date dropdown in Cybersecurity Page", async () => {
  await cybersecurityDashboardPage.checkDateFilters();
});

Then("Verify the default date in date dropdown is {string} in Cybersecurity Page", async ({}, defaultDate) => {
  await cybersecurityDashboardPage.checkDefaultDate(defaultDate);
});

Then("Verify the title {string} in Cybersecurity Page", async ({}, title: string) => {
  await cybersecurityDashboardPage.checkContainerTitle(title);
});
Then("Ensure that {string} graph canvas of type {string} is present in Cybersecurity Page", async ({}, testId: string, graphType: string) => {
  await cybersecurityDashboardPage.checkGraph(testId, graphType);
});
Then("Ensure that {string} table is present in Cybersecurity page", async ({}, tableName: string) => {
    await cybersecurityDashboardPage.checkTable(tableName)
});
Then("Ensure that you are in {string} tab in Cybersecurity Page", async ({}, tabName: string) => {
  await cybersecurityDashboardPage.switchToTab(tabName);
});
Then("Ensure User can navigate to {string} Page while clicking on footer under {string} in Cybersecurity Page", async ({}, navigationPage: string, containerName: string) => {
  await cybersecurityDashboardPage.checkFooterNavigaion(navigationPage, containerName);
});
Then("Verify the Description {string} in Cybersecurity Page", async ({}, expectedDescription: string) => {
  await cybersecurityDashboardPage.checkContainerDescription(expectedDescription);
});
Then("Verify the value of {string} in Cybersecurity Page", async ({}, count: string) => {
  await cybersecurityDashboardPage.checkCount(count);
});
