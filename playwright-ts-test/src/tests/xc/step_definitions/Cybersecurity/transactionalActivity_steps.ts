import { Page } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import TransactionalActivity   from '../../pages/Cybersecurity/transactionalActivity_page';


const { Before, When, Then } = createBdd();

let transactionalActivityPage: TransactionalActivity;

Before({ tags: "@transactionalactivity" }, async ({ page }: { page: Page }) => {
  transactionalActivityPage = new TransactionalActivity(page);
});

When("User is in Transactional Activity Page", async () => {
  await transactionalActivityPage.goto();
  await transactionalActivityPage.navigateTo();
  await transactionalActivityPage.checkUrl();
});

Then("Verify the title {string} in Transactional Activity Page", async ({}, expectedTitle: string) => {
  await transactionalActivityPage.checkContainerTitle(expectedTitle);
});

Then("Ensure that {string} graph canvas of type {string} is present in Transactional Activity Page", async ({}, testId: string, graphType: string) => {
  await transactionalActivityPage.checkGraph(testId, graphType);
});

Then("Ensure that {string} table is present in Transactional Activity page", async ({}, tableName: string) => {
    await transactionalActivityPage.checkTable(tableName)
});

Then("Ensure that you are in {string} tab in Transactional Activity Page", async ({}, tabName: string) => {
  await transactionalActivityPage.switchToTab(tabName);
});


Then("Verify the date dropdown in Transactional Activity Page", async () => {
  await transactionalActivityPage.checkDateFilters();
});

Then("Verify the default date in date dropdown is {string} in Transactional Activity Page", async ({}, defaultDate) => {
  await transactionalActivityPage.checkDefaultDate(defaultDate);
});