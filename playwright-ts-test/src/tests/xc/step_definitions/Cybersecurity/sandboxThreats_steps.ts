import { Page } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import SandboxThreats  from '../../pages/Cybersecurity/sandboxThreats_page';
const { Before, When, Then } = createBdd();

let sandboxThreatsPage: SandboxThreats;

Before({ tags: "@sandboxthreat" }, async ({ page }: { page: Page }) => {
  sandboxThreatsPage = new SandboxThreats(page);
});

When("User is in Sandbox Threats Page", async () => {
  await sandboxThreatsPage.goto();
  await sandboxThreatsPage.navigateTo();
  await sandboxThreatsPage.checkUrl();
});

Then("Verify the title {string} in Sandbox Threats Page", async ({}, expectedTitle: string) => {
  await sandboxThreatsPage.checkContainerTitle(expectedTitle);
});

Then("Ensure that {string} graph canvas of type {string} is present in Sandbox Threats Page", async ({}, testId: string, graphType: string) => {
  await sandboxThreatsPage.checkGraph(testId, graphType);
});

Then("Ensure that {string} table is present in Sandbox Threats page", async ({}, tableName: string) => {
    await sandboxThreatsPage.checkTable(tableName)
});

Then("Verify the date dropdown in Sandbox Threats Page", async () => {
  await sandboxThreatsPage.checkDateFilters();
});

Then("Verify the default date in date dropdown is {string} in Sandbox Threats Page", async ({}, defaultDate) => {
  await sandboxThreatsPage.checkDefaultDate(defaultDate);
});
