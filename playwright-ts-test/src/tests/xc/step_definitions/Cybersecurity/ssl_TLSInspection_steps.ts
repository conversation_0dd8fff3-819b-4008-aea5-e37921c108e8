import { Page } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import SSL_TLSInspection from '../../pages/Cybersecurity/ssl_TLSInspection_page';
const { Before, When, Then } = createBdd();

let sSL_TLSInspectionPage: SSL_TLSInspection;

Before({ tags: "@ssl_tlsinspection" }, async ({ page }: { page: Page }) => {
  sSL_TLSInspectionPage = new SSL_TLSInspection(page);
});

When("User is in SSL\\/TLS Inspection Page", async () => {
  await sSL_TLSInspectionPage.goto();
  await sSL_TLSInspectionPage.navigateTo();
  await sSL_TLSInspectionPage.checkUrl();
});

Then("Verify the title {string} in SSL\\/TLS Inspection Page", async ({}, expectedTitle: string) => {
  await sSL_TLSInspectionPage.checkContainerTitle(expectedTitle);
});

Then("Verify the Description {string} in SSL\\/TLS Inspection Page", async ({}, expectedDescription: string) => {
  await sSL_TLSInspectionPage.checkContainerDescription(expectedDescription);
});

Then("Ensure that {string} graph canvas of type {string} is present in SSL\\/TLS Inspection Page", async ({}, testId: string, graphType: string) => {
  await sSL_TLSInspectionPage.checkGraph(testId, graphType);
});

Then("Ensure that ${string} table is present in SSL\\/TLS Inspection Page", async ({}, tableName: string) => {
    await sSL_TLSInspectionPage.checkTable(tableName)
});


Then("Verify the date dropdown in SSL\\/TLS Inspection Page", async () => {
  await sSL_TLSInspectionPage.checkDateFilters();
});

Then("Verify the default date in date dropdown is {string} in SSL\\/TLS Inspection Page", async ({}, defaultDate) => {
  await sSL_TLSInspectionPage.checkDefaultDate(defaultDate);
});