import { Page } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import ThreatLocations   from '../../pages/Cybersecurity/threatLocations_page';


const { Before, When, Then } = createBdd();
let threatLocationsPage: ThreatLocations;

Before({ tags: "@threatlocations" }, async ({ page }: { page: Page }) => {
  threatLocationsPage = new ThreatLocations(page);
});

When("User is in Threat Locations Page", async () => {
  await threatLocationsPage.goto();
  await threatLocationsPage.navigateTo();
  await threatLocationsPage.checkUrl();
});

Then("Verify the title {string} in Threat Locations Page", async ({}, expectedTitle: string) => {
  await threatLocationsPage.checkContainerTitle(expectedTitle);
});

Then("Ensure that {string} graph canvas of type {string} is present in Threat Locations Page", async ({}, testId: string, graphType: string) => {
  await threatLocationsPage.checkGraph(testId, graphType);
});

Then("Ensure that {string} table is present in Threat Locations page", async ({}, tableName: string) => {
    await threatLocationsPage.checkTable(tableName)
});

Then("Verify the date dropdown in Threat Locations Page", async () => {
  await threatLocationsPage.checkDateFilters();
});

Then("Verify the default date in date dropdown is {string} in Threat Locations Page", async ({}, defaultDate) => {
  await threatLocationsPage.checkDefaultDate(defaultDate);
});