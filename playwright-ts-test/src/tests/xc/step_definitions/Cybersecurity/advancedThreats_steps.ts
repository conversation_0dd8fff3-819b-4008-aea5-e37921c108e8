import { Page } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import advancedThreats  from '../../pages/Cybersecurity/advancedThreats_page';


const { Before, When, Then } = createBdd();
let advancedThreatsPage: advancedThreats;

Before({ tags: "@advancedthreat" }, async ({ page }: { page: Page }) => {
  advancedThreatsPage = new advancedThreats(page);
});

When("User is in Advanced Threats Page", async () => {
  await advancedThreatsPage.goto();
  await advancedThreatsPage.navigateTo();
  await advancedThreatsPage.checkUrl();
});
Then("Verify the title {string} in Advanced Threats Page", async ({}, expectedTitle: string) => {
  await advancedThreatsPage.checkContainerTitle(expectedTitle);
});

Then("Ensure that {string} graph canvas of type {string} is present in Advanced Threats Page", async ({}, testId: string, graphType: string) => {
  await advancedThreatsPage.checkGraph(testId, graphType);
});

Then("Ensure that {string} table is present in Advanced Threats page", async ({}, tableName: string) => {
    await advancedThreatsPage.checkTable(tableName)
});

Then("Verify the date dropdown in Advanced Threats Page", async () => {
  await advancedThreatsPage.checkDateFilters();
});

Then("Verify the default date in date dropdown is {string} in Advanced Threats Page", async ({}, defaultDate) => {
  await advancedThreatsPage.checkDefaultDate(defaultDate);
});
