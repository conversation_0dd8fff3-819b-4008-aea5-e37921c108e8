import { createBdd } from "playwright-bdd";
const { Before, When, Then, Given } = createBdd();
import Networking from "../../pages/Networking/networkingDashboard_page";
import { Page } from "@playwright/test";


let networkingPage: Networking;

Before({ tags: "@networking" }, async ({ page }: { page: Page }) => {
  networkingPage = new Networking(page);
});

When("User is in Networking Page", async () => {
  await networkingPage.goto();
  await networkingPage.navigateTo();
  await networkingPage.checkUrl();
});

Then("Verify the filters in Networking Page", async () => {
  await networkingPage.checkDateFilters();
});

Then("Verify the title {string} in Networking Page", async ({}, title: string) => {
  await networkingPage.checkContainerTitle(title);
});

Then("Ensure that you are in {string} tab in Networking Page", async ({}, tabName: string) => {
  await networkingPage.switchToTab(tabName);
});

Then("Verify the label and value of {string} under {string} in Networking Page", async ({}, labelName:string,  tabName: string) => {
  let valueType = "number";
  if(labelName.includes("Transactions"))
  {
    valueType = "number";
  }
  if(labelName.includes("Volume"))
    {
      valueType = "data";
    }
  await networkingPage.checkLabelWithValue(labelName, tabName, valueType);
});

Then("Ensure that {string} graph canvas of type {string} is present under {string} in Networking Page", async ({}, testId: string, graphType: string,  tabName: string) => {
  await networkingPage.checkLabelGraph(testId, tabName, graphType);
});

Then("Ensure that {string} graph canvas of type {string} is present in Networking Page", async ({}, testId: string, graphType: string) => {
  await networkingPage.checkGraph(testId, graphType);
});

Then("Ensure User can navigate to {string} Page while clicking on footer under {string} in Networking Page", async ({}, navigationPage: string, containerName: string) => {
  await networkingPage.checkFooterNavigaion(navigationPage, containerName);
});