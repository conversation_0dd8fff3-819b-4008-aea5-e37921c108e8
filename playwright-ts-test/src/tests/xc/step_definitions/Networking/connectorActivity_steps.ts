import { createBdd } from "playwright-bdd";
const { Before, When, Then } = createBdd();
import ConnectorActivity from "../../pages/Networking/connectorActivity_page";
import { Page } from "@playwright/test";


let connectorActivityPage: ConnectorActivity;

Before({ tags: "@connectoractivity" }, async ({ page }: { page: Page }) => {
  connectorActivityPage = new ConnectorActivity(page);
});

When("User is in Connector Activity Page", async () => {
  await connectorActivityPage.goto();
  await connectorActivityPage.navigateTo();
  await connectorActivityPage.checkUrl();
});

Then("Verify the filters under {string} in Connector Activity Page", async ({}, tabName: string) => {
  await connectorActivityPage.checkFilters(tabName);
});

Then("Verify the title {string} under {string} in Connector Activity Page", async ({}, title: string, tabName: string) => {
  await connectorActivityPage.checkContainerTitle(title, tabName);
});

Then("Ensure that you are in {string} tab in Connector Activity Page", async ({}, tabName: string) => {
  await connectorActivityPage.switchToTab(tabName);
});

Then("Ensure that {string} graph canvas of type {string} is present under {string} in Connector Activity Page", async ({}, graphName: string, graphType: string, tabName: string) => {
  await connectorActivityPage.checkGraph(graphName, graphType, tabName);
});

Then("Ensure that {string} table along with Searchbar is present under {string} in Connector Activity Page", async ({}, tableName: string, tabName: string) => {
  await connectorActivityPage.checkTableWithSearchbar(tableName, tabName);
});

Then("Verify the timestamp in Connector Activity Page", async () => {
  await connectorActivityPage.checkTimestamp();
});
