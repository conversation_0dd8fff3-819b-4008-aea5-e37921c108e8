import { Page } from '@playwright/test';
import { createBdd } from 'playwright-bdd';
import OnboardingSecureTraffic from '../../pages/Onboarding/step2_page';

const { When, Before, Then } = createBdd();

let OnboardingSecureTrafficPage: OnboardingSecureTraffic;

Before({ tags: "@onboarding-securetraffic" }, async ({ page }: { page: Page }) => {
  OnboardingSecureTrafficPage = new OnboardingSecureTraffic(page);
});

When('User enters into Secure Traffic Page', async () => {
    await OnboardingSecureTrafficPage.navigateTo();
    await OnboardingSecureTrafficPage.checkUrl();
});

Then('Verify the {string} stepper label on {string} of Secure Traffic Page', async ({}, label, step) => {
    await OnboardingSecureTrafficPage.checkStepperLabel(label, step);
});

Then('Verify user can able to see {string} Heading on {string} of Secure Traffic Page', async ({}, heading, step) => {
    await OnboardingSecureTrafficPage.checkHeader(heading, step);
});

Then('Verify user can able to see {string} Sub Heading on {string} of Secure Traffic Page', async ({}, subHeading, step) => {
    await OnboardingSecureTrafficPage.checkSubHeader(subHeading, step);
});

Then('Verify user can able to see {string} Description on {string} of Secure Traffic Page', async ({}, description, step) => {
    await OnboardingSecureTrafficPage.checkDescription(description, step);
});

Then('Verify user can able to see {string} Prompt on {string} of Secure Traffic Page', async ({}, prompt, step) => {
    await OnboardingSecureTrafficPage.checkPrompt(prompt, step);
});

Then('Verify user can able to see {string} Note on {string} of Secure Traffic Page', async ({}, note, step) => {
    await OnboardingSecureTrafficPage.checkNote(note, step);
});

Then('User checks tile on {string} of Secure Traffic Page', async ({}, step) => {
    await OnboardingSecureTrafficPage.checkTile(step);
});

Then('User Selects on {string} tile on {string} of Secure Traffic Page', async ({}, tile, step) => {
    await OnboardingSecureTrafficPage.clickonTile(tile, step);
});

Then('User clicks on {string} on {string} of Secure Traffic Page', async ({}, button, step) => {
    await OnboardingSecureTrafficPage.clickonFooterButton(button, step);
});

Then('check the VPN options on {string} of Secure Traffic Page', async ({}, step) => {
    await OnboardingSecureTrafficPage.checkVPNOptions();
});

Then('check the VPN configuration box on {string} of Secure Traffic Page', async ({}, step) => {
    await OnboardingSecureTrafficPage.checkVPNConfig(step);
});

Then('Verify Search Bar along with {string} Table on {string} of Secure Traffic Page', async ({}, table, step) => {
    await OnboardingSecureTrafficPage.checkTableWithSearchbar(table, step);
});

Then('User clicks username from table on {string} of Secure Traffic Page', async ({}, step) => {
    await OnboardingSecureTrafficPage.clickOnUserOnTable(step);
});

Then('Verify user can able to see {string} Label on {string} of Secure Traffic Page', async ({}, label, step) => {
    await OnboardingSecureTrafficPage.checkLabel(label, step);
});

Then('User Verify the Toggle on {string} of Secure Traffic Page', async ({}, step) => {
    await OnboardingSecureTrafficPage.checkToggle(step);
});

Then('Verify user can able to see {string} Toggle Label on {string} of Secure Traffic Page', async ({}, label, step) => {
    await OnboardingSecureTrafficPage.checkLabel(label, step);
});

Then('Verify Filters on {string} of Secure Traffic Page', async ({}, step) => {
    await OnboardingSecureTrafficPage.checkFilter(step);
});

