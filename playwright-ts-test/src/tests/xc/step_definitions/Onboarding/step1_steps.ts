import { Page } from '@playwright/test';
import { createBdd } from 'playwright-bdd';
import OnboardingSetUp from '../../pages/Onboarding/step1_page';

const { When, Then, Before } = createBdd();

let OnboardingSetUpPage: OnboardingSetUp;

Before({ tags: "@onboarding-step1" }, async ({ page }: { page: Page }) => {
  OnboardingSetUpPage = new OnboardingSetUp(page);
});

When('User enters into step1 Page', async () => {
    await OnboardingSetUpPage.navigateTo();
    await OnboardingSetUpPage.checkUrl();
});

Then('Verify user can able to see {string} Heading in step1 Page', async ({}, heading) => {
    await OnboardingSetUpPage.checkHeading(heading);
});

Then('Verify Filters in step1 Page', async () => {
    await OnboardingSetUpPage.checkFilter();
});

Then('Verify {string} Table along with Search Bar in step1 Page', async ({}, title) => {
    await OnboardingSetUpPage.checkTableWithSearchbar(title);
});

Then('Verify Upload CSV in step1 Page', async () => {
    await OnboardingSetUpPage.uploadCSV();
});

Then('Users Adds a User in step1 Page', async () => {
    await OnboardingSetUpPage.addUser();
});

Then('Users deletes a User in step1 Page', async () => {
    await OnboardingSetUpPage.deleteUser();
});
