import { expect, Page } from '@playwright/test';
import { createBdd } from 'playwright-bdd';
import OnboardingLanding from '../../pages/Onboarding/landing_page';

const { When, Then, Before } = createBdd();

let initialCount: number;
let newCount: number;

let OnboardingLandingPage: OnboardingLanding;

Before({ tags: "@landing" }, async ({ page }: { page: Page }) => {
  OnboardingLandingPage = new OnboardingLanding(page);
});

When('User is in Onboarding Landing Page', async () => {
    await OnboardingLandingPage.goto();
});

When('User checks Onboarding Landing Page URL', async () => {
    await OnboardingLandingPage.checkUrl();
});

Then('Verify user can able to see {string} Heading in Landing Page', async ({}, heading) => {
    await OnboardingLandingPage.checkHeading(heading);
});

Then('Verify user can able to see {string} Sub Heading in Landing Page', async ({}, subHeading) => {
    await OnboardingLandingPage.checkSubHeading(subHeading);
});

Then('User check for {string} Navigation in Landing Page', async ({}, title) => {
    await OnboardingLandingPage.checkNewTab(title);
});

Then('Verify Three steps as step 1 {string}, step 2 {string}, step 3 {string} in Landing Page', async ({}, step1Status, step2Status, step3Status) => {
    await OnboardingLandingPage.checkThreeSteps(step1Status, step2Status, step3Status);
});

Then('Verify the banner in Landing Page', async ({}) => {
    await OnboardingLandingPage.checkBanner();
});

Then('User gets Users Count in Onboarding Landing Page', async ({}) => {
    await OnboardingLandingPage.goto();
    await OnboardingLandingPage.setUserCounts();
    initialCount = await OnboardingLandingPage.getUserCounts();
});

Then('Users checks count after adding user in Onboarding Landing Page', async ({}) => {
    await OnboardingLandingPage.goto();
    await OnboardingLandingPage.setUserCounts();
    newCount = await OnboardingLandingPage.getUserCounts();
    expect(newCount - initialCount).toBe(1);
});

Then('Users checks count after deleting a user in Onboarding Landing Page', async ({}) => {
    await OnboardingLandingPage.goto();
    await OnboardingLandingPage.setUserCounts();
    newCount = await OnboardingLandingPage.getUserCounts();
    expect(initialCount - newCount).toBe(1);
});

