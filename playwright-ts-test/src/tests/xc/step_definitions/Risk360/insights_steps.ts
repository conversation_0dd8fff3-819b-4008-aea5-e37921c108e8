import { createBdd } from "playwright-bdd";
import { Page } from "@playwright/test";
import Risk360Insights from "../../pages/Risk360/insights_page";
import { scenarioState } from "./shared_state";

const { Before, Given, When, Then } = createBdd();
let risk360Insights: Risk360Insights;

Before({ tags: "@risk360Dashboard" }, async ({ page }: { page: Page }) => {
  risk360Insights = new Risk360Insights(page);
});

Then("Verify that user lands on Insights page", async () => {
    await risk360Insights.checkPageUrlAndTitle();
});

Then('Verify that correct Risk category filter is set in Insights page', async () => {
  // Read the value from the shared state object.
  if (!scenarioState.riskCategoryFilter) {
    throw new Error("Risk category was not captured in a previous step. Make sure the step order in your .feature file is correct.");
  }
  await risk360Insights.validateSelectedFilter(scenarioState.riskCategoryFilter);
});

Then('Verify that insight card of correct risk category is displayed in Insights page', async () => {
  if (!scenarioState.riskCategoryFilter) {
    throw new Error("Risk category was not captured in a previous step. Make sure the step order in your .feature file is correct.");
  }
  await risk360Insights.validateCategoryOfFirstInsightCard(scenarioState.riskCategoryFilter);
});
