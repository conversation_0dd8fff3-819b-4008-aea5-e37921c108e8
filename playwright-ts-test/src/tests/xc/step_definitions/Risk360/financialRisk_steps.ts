import { createBdd } from "playwright-bdd";
import { Page } from "@playwright/test";
import Risk360FinancialRisk from "../../pages/Risk360/financialRisk_page";

const { Before, Given, When, Then } = createBdd();
let risk360FinancialRisk: Risk360FinancialRisk;

Before({ tags: "@risk360Dashboard or @risk360FinancialRisk" }, async ({ page }: { page: Page }) => {
  risk360FinancialRisk = new Risk360FinancialRisk(page);
});

Then("Verify that user lands on Financial Risk page", async () => {
    await risk360FinancialRisk.checkPageUrlAndTitle();
});

Given('User is in Risk360 Financial Risk page', async () => {
  await risk360FinancialRisk.navigateToRisk360FinancialRiskPage();
});

Then('User clicks on Financial Risk Settings button', async () => {
  await risk360FinancialRisk.clickFinancialRiskSettingsButton();
});

Then('User selects {string} as {string} in Financial Risk Settings drawer' , async ( {page}, attributeName: string, attributeValue: string) => {
  await risk360FinancialRisk.setCustomizedValues(attributeName, attributeValue);
});

Then('User enters Financial Loss lower bound as {string}', async ( {page}, value: string) => {
  await risk360FinancialRisk.setFinancialLossLowerBound(value);
});

Then('User enters Financial Loss upper bound as {string}', async ( {page}, value: string) => {
  await risk360FinancialRisk.setFinancialLossUpperBound(value);
});

Then('User clicks on Save button in Financial Risk Settings drawer', async () => {
  await risk360FinancialRisk.clickFinancialRiskSettingsSaveButton();
});

Then('Verify toaster with message {string} in Financial Risk page', async ( {page}, message: string) => {
  await risk360FinancialRisk.validateToasterMessage(page, message);
});

Then('User clicks Reset To Default button in Financial Risk Settings drawer', async () => {
  await risk360FinancialRisk.clickResetToDefaultButton();
});

Then('User clicks on Financial Risk Settings Drawer close button', async () => {
  await risk360FinancialRisk.clickFinancialRiskSettingsDrawerCloseButton();
});

Then('User clicks on Cancel button in Financial Risk Settings drawer', async () => {
  await risk360FinancialRisk.clickFinancialRiskSettingsCancelButton();
});

Then('Verify that Save button in Financial Risk Settings drawer is disabled', async () => {
  await risk360FinancialRisk.verifySaveButtonIsDisabled();
});

Then('Verify that Reset To Default button in Financial Risk Settings drawer is disabled', async () => {
  await risk360FinancialRisk.verifyResetToDefaultButtonIsDisabled();
});

Then('Verify the tooltip and tooltip text of Financial Loss Range in Default Values section', async () => {
  await risk360FinancialRisk.verifyFinancialLossRangeTooltipTextInDefaultValuesSection();
});

Then('Verify the tooltip and tooltip text of Financial Loss Range in Customized Values section', async () => {
  await risk360FinancialRisk.verifyFinancialLossRangeTooltipTextInCustomizedValuesSection();
});

Then('Verify the tooltip and tooltip text of Customized Values in Customized Values section', async () => {
  await risk360FinancialRisk.verifyCustomizedValuesTooltipText();
});