import { createBdd } from "playwright-bdd";
import { Page } from "@playwright/test";
import Risk360Factors from "../../pages/Risk360/factors_page";

const { Before, Given, When, Then } = createBdd();
let risk360Factors: Risk360Factors;

Before({ tags: "@risk360Dashboard" }, async ({ page }: { page: Page }) => {
  risk360Factors = new Risk360Factors(page);
});

Then('Verify that user lands on Factors page', async () => {
  await risk360Factors.checkPageUrlAndTitle();
}); 

Then('Verify the Risk Score Category filter selected in Factors page {string}', async ( {page}, riskCategory: string) => {
  await risk360Factors.validateRiskCategoryFilterSelected(riskCategory);
}); 

