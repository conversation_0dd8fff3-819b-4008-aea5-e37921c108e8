import { createBdd } from "playwright-bdd";
import { Page } from "@playwright/test";
import Risk360Dashboard from "../../pages/Risk360/dashboard_page";
import { scenarioState } from "./shared_state";

const { Before, Given, When, Then } = createBdd();
let risk360Dashboard: Risk360Dashboard;

const CURRENT_MONTH = new Date();
CURRENT_MONTH.setMonth(CURRENT_MONTH.getMonth() - 1);
let previousMonth = CURRENT_MONTH.toLocaleString('default', { month: 'long' });
previousMonth = previousMonth.substring(0, 3);

const ORG_RISK_SEVERITY_EXPECTED_TOOLTIP_TEXT =
  'What does this score mean?Your organizational risk score allows you to quantify your risk exposure based on a rich set of underlying factors such as exposed servers, recent malware outbreaks, segmentation posture and data uploads to risky applications. You can study how the risk score has changed over time and compare your score against your industry peers.';

const zoomButtonTitle = {
  "Zoom In" : 'Zoom In',
  "Zoom Out" : 'Zoom Out',
};

const toolTipWrapper = {
  "Risk Score Trend" : ".recharts-tooltip-wrapper.recharts-tooltip-wrapper-left.recharts-tooltip-wrapper-top",
  "Risk Event Bubble" : '.leaflet-popup-content-wrapper',
  "Org Risk Score" : '.score-tooltip',
};

const riskScoreCheckBoxes: { [key: string]: string } = {
  "Risk Score" : '#zRiskScore-check-box',
  "Industry Peer Average Score" : '#peerRiskScore-check-box',
  "Customized Risk Score" : '#customRiskScore-check-box',
};

Before({ tags: "@risk360Dashboard" }, async ({ page }: { page: Page }) => {
  risk360Dashboard = new Risk360Dashboard(page);
  // Reset shared state before each scenario to prevent test contamination.
  scenarioState.riskCategoryFilter = null;
});

Given('User is in Risk360 Dashboard', async () => {
  await risk360Dashboard.navigateToRisk360Dashboard();
});

Then('User verifies the {string} risk score in Organization Risk Score widget', async ( {page}, category: string) => {
  await risk360Dashboard.validateOrgRiskScore(category);
});

When('User clicks on the risk category {string} in Organization Risk Score widget', async ( {page}, riskCategory: string) => {
  await risk360Dashboard.clickRiskScoreCategory(riskCategory);
});

When('User hovers on Finance icon in Organization Risk Score widget', async () => {
  await risk360Dashboard.hoverOnFinanceIcon();
});

When('User clicks on View Details button in Financial Risk tooltip', async () => {
  await risk360Dashboard.clickViewDetails();
});

When('User clicks on View All button in Contributing Factors by Entity', async () => {
  await risk360Dashboard.clickContributingFactorsByEntityViewAllButton();
});

When('User clicks on View All button in Top 10 Factors', async () => {
  await risk360Dashboard.clickTopTenFactorsViewAllButton();
});

When('User clicks on View All button in High Impact Recommendations', async () => {
  await risk360Dashboard.clickHighImpactRecommendationsViewAllButton();
});

When('User clicks on peer score settings icon', async () => {
  await risk360Dashboard.clickPeerScoreSettingsIcon();
});

When('User hovers on Add Custom Strategy button', async () => {
  await risk360Dashboard.hoverOnAddCustomStrategyButton();
});

Then('Verify the tooltip text for Feature not subscribed', async () => {
  await risk360Dashboard.verifyAddCustomStrategyDisabledTooltip();
});

Then('Verify that only Default strategy is displayed in Peer Score Settings table', async () => {
  await risk360Dashboard.verifyOnlyDefaultStrategyIsAvailable();
});

Then('Verify Default strategy tooltip text', async () => {
  await risk360Dashboard.verifyDefaultStrategyTooltipText();
});

Then('Verify that Default strategy cannot be deleted', async () => {
 await risk360Dashboard.verifyDefaultStrategyCannotBeDeleted();
});

When('User clicks on Edit button of Peer score strategy with name {string}', async ( {page}, strategyName: string) => {
  await risk360Dashboard.clickPeerScoreStrategyEditIcon(strategyName);
});

When('User sets the value for field {string} as {string} in peer score settings', async ( {page}, attributeName: string, attributeValue: string) => {
  await risk360Dashboard.setPeerScoreAttribute(attributeName, attributeValue);
});

When('User clicks on Save button of Peer score settings', async () => {
  await risk360Dashboard.clickPeerScoreSettingsSaveButton();
});

When('User clicks on Cancel button of Peer score settings', async () => {
  await risk360Dashboard.clickPeerScoreSettingsCancelButton();
});
Then('Verify toaster with message {string}', async ( {page}, message: string) => {
  await risk360Dashboard.validateToasterMessage(page, message);
});

Then('User closes the Peer score settings drawer', async () => {
  await risk360Dashboard.closePeerScoreSettingsDrawer();
});

Then('Verify that Top Risky Locations tile in Risk Events by Location widget displays the top risky locations in descending order', async () => {
  await risk360Dashboard.validateRiskValueDescendingOrder();
});

Then('Verify that Zoom Out button is in disabled state by default', async () => {
  await risk360Dashboard.validateZoomButtonIsDisabled(zoomButtonTitle["Zoom Out"]);
});

Then('User clicks on {string} button in Risk Events by Location map', async ( {page}, buttonTitle: string) => {
  await risk360Dashboard.clickZoomButton(buttonTitle);
});

When('User clicks on Risk Event bubble', async () => {
  await risk360Dashboard.clickRiskEventBubble();
});

Then('Verify that {string} Tooltip is visible', async ( {page}, toolTipWrapper: string) => {
  await risk360Dashboard.validateToolTipIsVisible(toolTipWrapper);
});

Then('Verify the Risk category in Risk event bubble tooltip', async () => {
  // Capture the risk category into the module-level variable to be used by subsequent steps.
  scenarioState.riskCategoryFilter = await risk360Dashboard.validateRiskCategoryInRiskEventBubble();
});

Then('User closes the Risk Event Bubble tooltip', async () => {
  await risk360Dashboard.closeRiskEventBubbleTooltip();
});

Then('User clicks on event count in Risk event bubble tooltip', async () => {
  await risk360Dashboard.clickRiskEventCount();
});

Then('Verify the tool tip text displayed for different risk categories', async () => {
  await risk360Dashboard.validateRiskCategoryToolTipText();
});

When('User clicks the {string} check box in Risk score trend widget', async ( {page}, riskScoreCheckBoxName: string) => {
  const riskScoreCheckBox = riskScoreCheckBoxes[riskScoreCheckBoxName];
  
  if (!riskScoreCheckBox) {
    throw new Error(`No locator found for check box name: ${riskScoreCheckBoxName}`);
  }
  await risk360Dashboard.clickCheckBox(riskScoreCheckBox);
});

When('User clicks on Risk score trend graph to trigger the tooltip', async () => {
  await risk360Dashboard.clickGraphAboveMonth(previousMonth);
});

Then('Verify that Risk Score tooltip is visible and {string} Risk Score is hidden in Risk score trend', async ( {page}, expectedText: string) => {
  await risk360Dashboard.validateToolTipIsVisible(toolTipWrapper["Risk Score Trend"]);
  await risk360Dashboard.validateTextAbsenceInToolTip(expectedText, toolTipWrapper["Risk Score Trend"]);
});

Then('Verify that Risk Score tooltip is visible and {string} Risk Score is visible in Risk score trend', async ( {page}, expectedText: string) => {
  await risk360Dashboard.validateToolTipIsVisible(toolTipWrapper["Risk Score Trend"]);
  await risk360Dashboard.validateTextPresenceInToolTip(expectedText, toolTipWrapper["Risk Score Trend"]);
});

When('User clicks on Organization Risk Score', async () => {
  await risk360Dashboard.clickOrgRiskSeverityTooltip();
});

Then('Verify the Organization risk score tooltip and tooltip text', async () => {
  await risk360Dashboard.validateToolTipIsVisible(toolTipWrapper["Org Risk Score"]);
  await risk360Dashboard.validateTextPresenceInToolTip(ORG_RISK_SEVERITY_EXPECTED_TOOLTIP_TEXT, toolTipWrapper["Org Risk Score"]);
});


Then('Verify the factors count by entity for entity - {string}', async ({}, entityName: string) => {
  await risk360Dashboard.validateContributingFactorsCountByEntity(entityName)
 });

Then('Verify that the entity {string} has the description - {string}', async ({}, entityName: string, entityDescription: string) => {
  await risk360Dashboard.validateEntityDescription(entityName, entityDescription);
});
