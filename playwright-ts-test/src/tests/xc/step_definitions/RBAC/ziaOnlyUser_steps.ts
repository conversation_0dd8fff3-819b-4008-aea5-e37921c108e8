import { createBdd } from "playwright-bdd";
const { Given, When, Then } = createBdd();
import ziaOnlyUser from '../../pages/RBAC/ziaOnlyUser_page';



Given('User capture the screenshot for ZIA {string} to {string}', async({page}, menu: string, tab: string) => {
    await page.waitForTimeout(15000);
    await ziaOnlyUser.captureScreenshot(page, menu, tab);
})

Then('Verify the difference between the screenshots for ZIA', async() => {
 await ziaOnlyUser.verifyScreenshot();
})

Given('User capture the screenshot for ZIA {string} to {string} then {string}',async({page}, menu, tab, verticalMenu) => {
 await ziaOnlyUser.captureScreenshotWithVerticalMenu(page, menu, tab, verticalMenu);
})
Given('User verify the Connector Activity is disabled, locked icon and text on hover for ZIA', async({page}) => {
 await ziaOnlyUser.verifyConnectorActivityTab(page);
})


