import { createBdd } from "playwright-bdd";
const { Given, When, Then } = createBdd();
import MaOnly<PERSON>ser from '../../pages/RBAC/maOnlyUser_page';


Given('User verify the {string} menu is not visible', async({page}, menu: string) => {
 await MaOnlyUser.isNotVisible(page, menu);
})

Given('User verify the {string} submenu under {string} is not visible', async({page}, submenu: string, menu: string) => {
 await MaOnlyUser.isNotVisible(page, menu, submenu);
})

Given('User is able to view the widgets heading {string}', async({page}, widgets: string) => {
 await MaOnlyUser.viewWidgetHeading(page, widgets);
})

When('User verify the {string} text along with {string}', async({page}, s, title) => {
 await MaOnlyUser.verifyAccessRestrictedText(page, s, title);
})


Given('User verify the Connector Activity is disabled, locked icon and text on hover', async({page}) => {
 await <PERSON>Only<PERSON>ser.verifyConnectorActivityTab(page);
})


When('User verify the Digital Experience is disabled, locked icon and text on hover', async({page}) => {
 await MaOnlyUser.verifyDigitalExperienceTab(page);
})


When('User verify the Cybersecurity is disabled, locked icon and text on hover', async({page}) => {
 await MaOnlyUser.verifyCybersecurityTab(page);
})

Given('User navigates to the Operational {string} tab', async({page}, tab) => {
 await MaOnlyUser.clickOperationalDevicesTab(page, tab);
})


When('User verify the {string} title, tooltip and user devices count', async({page}, s) => {
 await MaOnlyUser.verifyUserDevicesTitle(page, s);
})


Then('Verify the {string} graph canvas of type {string}', async({page}, testId: string, graphType: string) => {
  await page.waitForTimeout(3000);
  await MaOnlyUser.verifyDevicesGraph(page, testId, graphType);
})

When('User verify the {string} title in devices tab', async({page}, s) => {
 await MaOnlyUser.verifyTitle(page, s);
})

When('User verify the {string} title in operating system', async({page}, s: string) => {
 await MaOnlyUser.verifyTitle(page, s);
})

Given('User capture the screenshot for {string} to {string}', async({page}, menu: string, tab: string) => {
 await page.waitForTimeout(10000);
 await MaOnlyUser.captureScreenshot(page, menu, tab);
})


Then('Verify the difference between the screenshots', async() => {
 await MaOnlyUser.verifyScreenshot();
})

Given('User capture the screenshot for {string} to {string} then {string}', async({page}, menu, tab, verticalMenu) => {
 await MaOnlyUser.captureScreenshotWithVerticalMenu(page, menu, tab, verticalMenu);
})

Then('User clicks on the graph and verify the tab details', async({page}) => {
    await MaOnlyUser.verifyUserDevicesTab(page);
})