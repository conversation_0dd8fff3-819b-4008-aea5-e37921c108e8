import { createBdd } from "playwright-bdd";
const { Given, Then } = createBdd();
import ZTDSOnlyUser from '../../pages/RBAC/ztdsOnlyUser_page';

Then('Verify the difference between the screenshots for ZTDS user', async({}) => {
  await ZTDSOnlyUser.verifyScreenshot();
})

Then('User checks Switch To Existing Reports', async({page}) => {
  await ZTDSOnlyUser.verifySwitchToExistingReports(page);
})

Given('User capture the screenshot for {string} to {string} in ZTDS', async({page}, menu: string, tab: string) => {
 await page.waitForTimeout(15000);
 await ZTDSOnlyUser.captureScreenshot(page, menu, tab);
})

Given('User capture the screenshot for {string} to {string} then {string} in ZTDS', async({page}, menu, tab, verticalMenu) => {
 await ZTDSOnlyUser.captureScreenshotWithVerticalMenu(page, menu, tab, verticalMenu);
})