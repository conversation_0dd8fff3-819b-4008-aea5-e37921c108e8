import { createBdd } from "playwright-bdd";
const { Given, When, Then } = createBdd();
import ZIDOnlyUser from '../../pages/RBAC/zidOnlyUser_page';

// Then('User verify the Operational is disabled, locked icon and text on hover', async({page}) => {
//   await ZIDOnlyUser.verifyOperationalTab(page);
// })

Then('Verify the difference between the screenshots for ZID user', async() => {
  await ZIDOnlyUser.verifyScreenshot();
})

Given('User capture the screenshot for {string} to {string} in ZID', async({page}, menu: string, tab: string) => {
 await page.waitForTimeout(15000);
 await ZIDOnlyUser.captureScreenshot(page, menu, tab);
})

Given('User capture the screenshot for {string} to {string} then {string} in ZID', async({page}, menu, tab, verticalMenu) => {
 await <PERSON><PERSON>OnlyUser.captureScreenshotWithVerticalMenu(page, menu, tab, verticalMenu);
})

Given('User Checks for the {string} Option is Not Visible', async({page}, menu) => {
 await ZIDOnlyUser.checkIsMenuVisible(page, menu);
})
