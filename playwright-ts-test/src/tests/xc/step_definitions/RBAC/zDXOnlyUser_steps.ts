import { createBdd } from "playwright-bdd";
const { Given } = createBdd();
import ZdxOnlyUser from '../../pages/RBAC/zDXOnlyUser_page';


Given('User capture the screenshot for ZDX {string} to {string}', async({page}, menu: string, tab: string) => {
    await page.waitForTimeout(15000);
    await ZdxOnlyUser.captureScreenshot(page, menu, tab);
})

Given('User capture the screenshot for ZDX {string} to {string} then {string}', async({page}, menu, tab, verticalMenu) => {
    await ZdxOnlyUser.captureScreenshotWithVerticalMenu(page, menu, tab, verticalMenu);
})
