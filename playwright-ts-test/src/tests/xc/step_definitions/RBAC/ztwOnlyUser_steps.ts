import { expect, Page } from '@playwright/test';
import { createBdd } from "playwright-bdd";
const { Given, When, Then } = createBdd();
import * as fs from 'fs';
import ZTWOnlyUser from '../../pages/RBAC/ztwOnlyUser_page';

When('User verify the {string} text in networking widgets except Branch and Cloud Connectors', async ({ page }, s: string) => {
  await ZTWOnlyUser.verifyAccessRestrictedTextWithoutBCC(page, s);
});

Given('User verify the Cybersecurity is disabled, locked icon and text on hover in ZTW', async ({ page }) => {
  await ZTWOnlyUser.verifyCybersecurityTabZTW(page);
});

Then('User verify the Devices is disabled, locked icon and text on hover', async ({ page }) => {
  await ZTWOnlyUser.verifyDevicesTab(page);
});

Then('Verify the graph for the Branch and Cloud Connectors', async ({ page }) => {
  await ZTWOnlyUser.verifyBranchAndCloudConnectorsGraph(page);
});

Then('Verify the navigation of the View Connector Activity button to {string} screen', async ({ page }, s) => {
  await ZTWOnlyUser.clickViewConnectorActivityButton(page);
  await ZTWOnlyUser.verifyConnectorActivityScreen(page, s);
});

When('User navigates to the Connector Activity tab', async ({ page }) => {
  await ZTWOnlyUser.clickConnectorTab(page);
});

When('User verify the title {string}', async ({page}, s: string) => {
  await ZTWOnlyUser.verifyTitle(page, s);
});

Then('Verify the pie chart is loaded successfully', async ({page}) => {
  await ZTWOnlyUser.verifyTrafficVolumeAcrossService(page);
});

Then('Verify the pie chart is loaded successfully for Session Across Service', async ({page}) => {
  await ZTWOnlyUser.verifySessionAcrossService(page);
});

Then('Verify the table columns and branch connector data', async ({ page }) => {
  await ZTWOnlyUser.verifyConnectorTableAppliances(page);
});

Then('Verify the search in the table', async ({page} ) => {
  await ZTWOnlyUser.searchInput(page);
});

When('User switch to the {string}', async ({ page }: { page: Page }, s: string) => {
  await ZTWOnlyUser.switchToCloudConnectors(page, s);
});

Then('Verify the pie chart is loaded successfully for cloud connector', async ({page} ) => {
  await ZTWOnlyUser.verifyTrafficVolumeAcrossServiceCloudConnector(page);
});

Then('Verify the pie chart is loaded successfully for Session Across Service cloud connector', async ({ page }: { page: Page }) => {
  await ZTWOnlyUser.verifySessionAcrossServiceCloudConnector(page);
});

Then('Verify the table columns and branch connector data for cloud connector', async ({ page }: { page: Page }) => {
  await ZTWOnlyUser.verifyConnectorTableCloudConnector(page);
});

Then('Verify the widgets heading and {string} text', async ({ page }: { page: Page }, s: string) => {
  await ZTWOnlyUser.verifyDevicesAccessRestricted(page, s);
});

When('User navigates to the Appliances tab', async ({ page }: { page: Page }) => {
  await ZTWOnlyUser.navigateToAppliances(page);
});

Then('Verify the difference between the screenshots for ZTW user', async ({ page }: { page: Page }) => {
  await ZTWOnlyUser.verifyScreenshot();
});

Then('Verify the {string} graph canvas of type {string} is present in Appliances', async({page}, testId: string, graphType: string) => {
    await page.waitForTimeout(7000);
    await ZTWOnlyUser.verifyGraph(page, testId, graphType);
});

Then('Verify the {string} table in Appliances', async({page}, title: string) => {
    await ZTWOnlyUser.verifyAppliancesTable(page, title);
})

Then('Verify the filters in Operational Appliances page', async({page}) => {
    await ZTWOnlyUser.checkAppliancesFilters(page);
})

Given('User capture the screenshot for {string} to {string} in ZTW',  async({page}, menu: string, tab: string) => {
 await page.waitForTimeout(15000);
 await ZTWOnlyUser.captureScreenshot(page, menu, tab);
})

Given('User capture the screenshot for {string} to {string} then {string} in ZTW', async({page}, menu, tab, verticalMenu) => {
 await ZTWOnlyUser.captureScreenshotWithVerticalMenu(page, menu, tab, verticalMenu);
})