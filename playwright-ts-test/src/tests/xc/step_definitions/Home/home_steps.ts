import { Page } from "@playwright/test";
import { createBdd, DataTable } from "playwright-bdd";
import HomePage from "../../pages/Home/home_page";

const { When, Then, Before, After } = createBdd();

let homePage: HomePage;


Before({ tags: "@home" }, async ({ page }: { page: Page }) => {
    homePage = new HomePage(page);
});

After({ tags: "@home" }, async () => {
    await homePage.reset();
});
  
When("User is in Home Page", async ({ page }: { page: Page }) => {
    await homePage.goto();
    await homePage.reset();
    await homePage.checkUrl();
});

Then("Verify the Banner loads within 1 sec in Home Page", async () => {
    await homePage.checkBannerLoad();
})

Then("Verify the Banner in Home Page", async () => {
    await homePage.checkBanner();
});

Then("Verify the Banner updated automatically in Home Page", async () => {
    await homePage.checkBannerUpdate();
});

Then("Verify the back button functionality in Home Page", async () => {
    await homePage.checkBackButton();
});

Then("Verify the cancel button functionality in Home Page", async () => {
    await homePage.checkCancelButton();
});

Then("Verify the Customize button functionality in Home Page", async () => {
    await homePage.checkCustomize();
});

Then("Verify the Analytics Cards in Home Page", async () => {
    await homePage.checkAnalyticsCards();
});
    
Then("Verify the Recently Viewed Container in Home Page", async () => {
    await homePage.checkRecent();
});
    
Then("Verify the News Container in Home Page", async () => {
    await homePage.checkNews();
});
    
Then("Verify the Learn About Products Container in Home Page", async () => {
    await homePage.checkLearnAboutProducts();
});

Then("Verify the Video thumbnails and Product cards loads within 2 secs in Home Page", async () => {
    await homePage.checkLearnAboutProductsLoad();
})
    
Then("Verify the Resources Container in Home Page", async () => {
    await homePage.checkResources();
});
    
Then("Verify the Resize for all Containers in Home Page", async () => {
    await homePage.checkResize();
});
    
Then("Verify the Remove for all Containers in Home Page", async () => {
    await homePage.checkRemove();
});
    
Then('Verify the following Analytics Cards in Home Page:', async function ({}, dataTable: DataTable) {
  const cards: string[] = dataTable.raw().flat();
  await homePage.verifyRBACAnalyticsCardTiles(cards);
});
    
Then('Verify the Analytics Cards in Home Page - RBAC', async function () {
  await homePage.verifyRBACAnalyticsCard();
});
    
Then('Verify the Analytics Cards is not available in Home Page - RBAC', async function () {
  await homePage.verifyRBACAnalyticsCardNotVisible();
});
