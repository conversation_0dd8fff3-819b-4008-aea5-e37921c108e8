import { Page } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import SelfService from "../../pages/DigitalExperience/selfservice_page";

const { Before, When, Then } = createBdd();

let selfServicePage: SelfService;

Before({ tags: "@selfservice" }, async ({ page }: { page: Page }) => {
  selfServicePage = new SelfService(page);
});

When("User is in Self Service Page", async () => {
  await selfServicePage.goto();
  await selfServicePage.navigateTo();
  await selfServicePage.checkUrl();
});

Then("Verify the filters in Self Service page", async () => {
  await selfServicePage.checkFilters();
});

Then("Verify the date dropdown in Self Service page", async () => {
  await selfServicePage.checkDateFilters();
});

Then("Verify the title {string} in Self Service Page", async ({}, expectedTitle: string) => {
  await selfServicePage.checkContainerTitle(expectedTitle);
});

Then("Verify the value of {string} in Self Service Page", async ({}, sectionName: string) => {
  await selfServicePage.checkValue(sectionName);
});

Then("Ensure that {string} graph canvas of type {string} is present in Self Service Page", async ({}, testId: string, graphType: string) => {
  await selfServicePage.checkGraph(testId, graphType);
});

Then("Ensure that {string} table is present in Self Service page", async ({}, tableName: string) => {
  await selfServicePage.checkTable(tableName)
});

Then("Ensure that {string} Search Bar is present in Self Service page", async ({}, tableName: string) => {
  await selfServicePage.checkSearchBar(tableName)
});
