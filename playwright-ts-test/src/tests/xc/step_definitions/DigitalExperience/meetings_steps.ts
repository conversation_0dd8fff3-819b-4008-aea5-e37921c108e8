import { Page } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import Meetings from "../../pages/DigitalExperience/meetings_page";

const { Before, When, Then } = createBdd();

let meetingsPage: Meetings;

Before({ tags: "@meetings" }, async ({ page }: { page: Page }) => {
  meetingsPage = new Meetings(page);
});

When("User is in Meetings Page", async () => {
  await meetingsPage.goto();
  await meetingsPage.navigateTo();
  await meetingsPage.checkUrl();
});

Then("Verify the filters in Meetings page", async () => {
  await meetingsPage.checkDateFilters();
});

Then("Verify the date dropdown in Meetings page", async () => {
  await meetingsPage.checkDateFilters();
});

Then("Verify the title {string} in Meetings Page", async ({}, expectedTitle: string) => {
  await meetingsPage.checkContainerTitle(expectedTitle);
});

Then("Ensure that {string} table is present in Meetings page", async ({}, tableName: string) => {
    await meetingsPage.checkTable(tableName)
});

Then("Ensure that {string} Search Bar is present in Meetings page", async ({}, tableName: string) => {
    await meetingsPage.checkSearchBar(tableName)
});