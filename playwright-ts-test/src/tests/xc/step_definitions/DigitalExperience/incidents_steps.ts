import { Page } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import Incidents from "../../pages/DigitalExperience/incidents_page";

const { Before, When, Then } = createBdd();
let incidentsPage: Incidents;

Before({ tags: "@incidents" }, async ({ page }: { page: Page }) => {
  incidentsPage = new Incidents(page);
});

When("User is in Incidents Page", async () => {
  await incidentsPage.goto();
  await incidentsPage.navigateTo();
  await incidentsPage.checkUrl();
});

Then("Verify the date dropdown in Incidents page", async () => {
  await incidentsPage.checkDateFilters();
});

Then("Verify the Type filter in Incidents page", async () => {
  await incidentsPage.checkTypeFilter();
});

Then("Verify the title {string} in Incidents Page", async ({}, expectedTitle: string) => {
  await incidentsPage.checkContainerTitle(expectedTitle);
});

Then("Ensure that {string} graph canvas of type {string} is present in Incidents Page", async ({}, testId: string, graphType: string) => {
  await incidentsPage.checkGraph(testId, graphType);
});

Then("Ensure that {string} table is present in Incidents page", async ({}, tableName: string) => {
    await incidentsPage.checkTable(tableName)
  });

Then("Ensure that {string} Search Bar is present in Incidents page", async ({}, tableName: string) => {
  await incidentsPage.checkSearchBar(tableName)
});

Then("Verify the list title {string} under {string} in Incidents Page", async ({}, title: string, containerName: string) => {
  await incidentsPage.checklistHeader(title, containerName)
});

Then("Verify the list value {string} under {string} in Incidents Page", async ({}, title: string, containerName: string) => {
  await incidentsPage.checklistValue(title, containerName)
});