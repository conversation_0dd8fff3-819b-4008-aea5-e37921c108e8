import { createBdd } from "playwright-bdd";
import Dashboard from "../../pages/DigitalExperience/dashboard_page";
import { Page } from "@playwright/test";

const { Before, When, Then } = createBdd();
let dePage: Dashboard;

Before({ tags: "@dedashboard" }, async ({ page }: { page: Page }) => {
  dePage = new Dashboard(page);
});

When("User is in digital experience dashboard", async () => {
  await dePage.goto();
  await dePage.navigateTo();
  await dePage.checkUrl();
});

Then("Verify the filters in DE page", async () => {
  await dePage.checkFilters();
});

Then("Verify the date dropdown in DE page", async () => {
  await dePage.checkDateFilters();
});

Then("Verify the title {string} in DE page", async ({}, expectedTitle: string) => {
  await dePage.checkContainerTitle(expectedTitle);
});

Then("Ensure that {string} graph canvas of type {string} is present in DE page", async ({}, testId: string, graphType: string) => {
  await dePage.checkGraph(testId, graphType);
});

Then("Ensure User can navigate to {string} Page while clicking on footer under {string} in DE Page", async ({}, navigationPage: string, containerName: string) => {
  await dePage.checkFooterNavigaion(navigationPage, containerName);
});

Then("Ensure that you are in {string} tab in DE page", async ({}, tabName: string) => {
  await dePage.switchToTab(tabName);
});

Then("Ensure that {string} table is present in DE page", async ({}, tableName: string) => {
  await dePage.checkTable(tableName)
});

Then("Verify the {string} apps count in DE page", async ({}, containerName: string) => {
  await dePage.checkAppsCount(containerName)
});

Then("Verify the Total Incidents and Impacted users under {string} in DE page", async ({}, containerName: string) => {
  await dePage.checkIncidentsAndUsers(containerName)
});

Then("Ensure the {string}, {string} and {string} cards are present under {string} in DE page", async ({}, card1: string, card2: string, card3: string, containerName: string) => {
  await dePage.checkCards(card1, containerName);
  await dePage.checkCards(card2, containerName);
  await dePage.checkCards(card3, containerName);
});

Then("Ensure User can navigate to {string} Page while clicking on footer under {string} in {string} in DE Page", async ({}, navigationPage: string, cardName: string, containerName: string) => {
  await dePage.expandCardFooterNavigation(navigationPage, cardName, containerName);
});