import { Page } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import Application from "../../pages/DigitalExperience/application_page";

const { Before, When, Then } = createBdd();

let applicationPage: Application;

Before({ tags: "@applications" }, async ({ page }: { page: Page }) => {
  applicationPage = new Application(page);
});

When("User is in Application Page", async () => {
  await applicationPage.goto();
  await applicationPage.navigateTo();
  await applicationPage.checkUrl();
});

Then("Verify the filters in Application page", async () => {
 // await applicationPage.checkFilters();
  
});

Then("Verify the date dropdown in Application page", async () => {
  await applicationPage.checkDateFilters();
});

Then("Verify the title {string} in Application Page", async ({}, expectedTitle: string) => {
  await applicationPage.checkContainerTitle(expectedTitle);
});

Then("Ensure that {string} graph canvas of type {string} is present in Application Page", async ({}, testId: string, graphType: string) => {
  await applicationPage.checkGraph(testId, graphType);
});

Then("Ensure that {string} table is present in Application page", async ({}, tableName: string) => {
    await applicationPage.checkTable(tableName)
  });
