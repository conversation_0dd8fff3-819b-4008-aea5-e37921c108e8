import { createBdd } from "playwright-bdd";
const { Before, When, Then } = createBdd();
import Activity from "../../pages/DigitalExperience/activity_page";
import { Page } from "@playwright/test";

let activityPage: Activity;

Before({ tags: "@activity" }, async ({ page }: { page: Page }) => {
  activityPage = new Activity(page);
});

When("User is in Activity Page", async () => {
  await activityPage.goto();
  await activityPage.navigateTo();
  await activityPage.checkUrl();
});

Then("Verify the filters in Activity page", async () => {
  await activityPage.checkFilters();
});

Then("Verify the date dropdown in Activity page", async () => {
  await activityPage.checkDateFilters();
});

Then("Verify the title {string} in Activity Page", async ({}, expectedTitle: string) => {
  await activityPage.checkContainerTitle(expectedTitle);
});

Then("Verify the value of {string} in Activity Page", async ({}, sectionName: string) => {
  await activityPage.checkValue(sectionName);
});

Then("Ensure that {string} graph canvas of type {string} is present in Activity Page", async ({}, testId: string, graphType: string) => {
  await activityPage.checkGraph(testId, graphType);
});

Then("Verify the {string} users count in Activity Page", async ({}, containerName: string) => {
  await activityPage.checkUserDistributionCount(containerName);
});