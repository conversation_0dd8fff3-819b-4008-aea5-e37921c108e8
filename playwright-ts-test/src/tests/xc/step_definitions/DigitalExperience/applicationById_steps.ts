import { test, Page } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import ApplicationById from "../../pages/DigitalExperience/applicationById_page";
import Application from "../../pages/DigitalExperience/application_page";

const { Before, When, Then } = createBdd();
let applicationPage: Application;
let applicationByIdPage: ApplicationById;
let tableData: boolean;
let tableCheck: boolean = false;

Before({ tags: "@applicationbyid" }, async function ({ page }: { page: Page }) {
  if(!tableCheck)
  {
    applicationPage = new Application(page);
    await applicationPage.goto();
    await applicationPage.navigateTo();
    await applicationPage.checkUrl();
    tableData = await applicationPage.checkTableData("Application");
    tableCheck = true;
  }

  if (!tableData) {
    console.log("No data found in Application table – Skipping related tests");
    return test.skip();
  }
  else{
    applicationByIdPage = new ApplicationById(page);
  }
});

When("User is in Application By ID Page", async () => {
  await applicationByIdPage.goto();
  await applicationByIdPage.navigateTo();
  await applicationByIdPage.checkUrl();
});

Then("Verify the filters in Application By ID page", async () => {
  await applicationByIdPage.checkFilters();
});

Then("Verify the date dropdown in Application By ID page", async () => {
  await applicationByIdPage.checkDateFilters();
});

Then("Verify the title {string} in Application By ID Page", async ({}, expectedTitle: string ) => {
  await applicationByIdPage.checkContainerTitle(expectedTitle);
});

Then("Ensure that {string} graph canvas of type {string} is present in Application By ID Page", async ({}, testId: string, graphType: string ) => {
  await applicationByIdPage.checkGraph(testId, graphType);
});

Then("Ensure that {string} Progress List is present in Application By ID Page", async ({}, progressCntName: string ) => {
  await applicationByIdPage.checkProgressContainer(progressCntName);
});

Then("Ensure that {string} is present in Application By ID Page along with Table", async ({}, probeCntName: string ) => {
  await applicationByIdPage.checkProbesStatus(probeCntName);
});
