import { Page } from '@playwright/test';
import { createBdd } from "playwright-bdd";
import globalNavigationPage from "../../pages/GlobalNavigation/administration_page";
import { config } from "dotenv";

config();

const { Given, When, Then } = createBdd();

const { ONE_UI_BASE_URL: url } = process.env as { ONE_UI_BASE_URL?: string };

Given('User is able to view {string} in the global navigation', 
  async ({ page }: { page: Page }, s: string) => { 
  if (!url) {
    throw new Error('ONE_UI_BASE_URL is not defined in environment variables');
  }
  try {
    const response = await page.goto(url, { waitUntil: 'domcontentloaded' });
    if (!response?.ok()) {
      throw new Error(`Failed to load page: ${response?.status()} ${response?.statusText()}`);
    }
  } catch (error) {
    throw new Error(`Navigation failed: ${error instanceof Error ? error.message : String(error)}`);
  }
  await page.waitForTimeout(20000);
  await globalNavigationPage.verifyGlobalNavigationTitle(page, s);
});


When('User clicks on Account Management links under company profile and verify they are directed to respective screens', async({page}) => {
  await globalNavigationPage.clicksOnTheAccountManagement(page);
})

Then('User clicks on subscription and branding links and verify the screens', async({page}) => {
  await globalNavigationPage.clickSubscriptionAndBranding(page);
})

When('User clicks on the admin management and then {string}', async({page}, s) => {
  await globalNavigationPage.clickAdminManagement(page, s);
})

Then('User clicks on the link and verify the screens', async({page}) => {
  await globalNavigationPage.clickAdministratorManagementLinks(page);
})

Then('User verify the roles and Entitlements links', async({page}) => {
  await globalNavigationPage.clickRolesAndEntitlementsLinks(page);
})

Then('User verify the audit logs links', async({page}) => {
  await globalNavigationPage.clickAuditLogsLink(page);
})

When('User clicks on the Identity and selects ZIdentity', async({page}) => {
  await globalNavigationPage.clickIdentity(page, "ZIdentity");
})

Then('User verify the IDP Configuration, User Management, Domains, Passwords Authentication and Connectors links', async({page}) => {
  await globalNavigationPage.clickZIdentityLinks(page);
})

When('User clicks on the Identity and selects Internet and SaaS', async({page}) => {
  await globalNavigationPage.clickIdentity(page, "Internet & SaaS");
})

Then('User verify the Internet Authentication Setting, SCIM Event Logs, User Management and Identity Proxy links', async({page}) => {
  await globalNavigationPage.clickInternetAndSaaS(page);
})

When('User clicks on the Identity and selects Private Access', async({page}) => {
  await globalNavigationPage.clickIdentity(page, "Private Access");
})

Then('User verify the IDP Configuration, Device Authentication and Partner Login links', async({page}) => {
  await globalNavigationPage.clickPrivateAccess(page);
})

When('User clicks on the Entitlements', async({page}) => {
  await globalNavigationPage.clickEntitlements(page);
})

Then('User verify the Private Access, Digital Experience and Internet Access links', async({page}) => {
  await globalNavigationPage.clickEntitlementsClientConnectorServiceEntitlement(page);
})

When('User clicks on the API Configuration and OneAPI', async({page}) => {
  await globalNavigationPage.clickAPIConfiguration(page);
})

Then('User verify the OneAPI links', async({page}) => {
  await globalNavigationPage.clickOneAPILinks(page);
})

When('User clicks on the API Configuration and Legacy API', async({page}) => {
  await globalNavigationPage.clickLegacyAPI(page);
})

Then('User verify the Legacy API links', async({page}) => {
  await globalNavigationPage.clickLegacyAPILinks(page);
})

When('User clicks on the Alerts', async({page}) => {
  await globalNavigationPage.clickAlerts(page);
})
  
Then('User verify the Internet and SaaS, Digital Experience Monitoring, Private Access and Zero Trust Branch links', async({page}) => {
  await globalNavigationPage.clickAlertLinks(page);
})

When('User clicks on the Backup and Restore', async({page}) => {
  await globalNavigationPage.clickBackupAndRestore(page);
})

Then('User verify the Backup and Restore links', async({page}) => {
  await globalNavigationPage.clickInternetAndSaaSApplications(page);
})