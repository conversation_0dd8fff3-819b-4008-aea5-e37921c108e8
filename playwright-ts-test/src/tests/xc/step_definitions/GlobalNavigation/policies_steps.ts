import { expect } from "@playwright/test";
import { createBdd } from "playwright-bdd";

const { Given, When, Then } = createBdd();
let bearerToken: string, numberOfTransactionValue: number;
import policies_page from "../../pages/GlobalNavigation/policies_page";
import { config } from "dotenv";

config();

const { ONE_UI_BASE_URL: url } = process.env || "";

When('User click on {string} from global navigation bar',async ({page}) => {
    await policies_page.globalNavPoliciesURL(page);
})

Then('User verify all the global nav users under Servers are working fine', async({page}) => {
  await policies_page.policies_Links(page);
  
})

Then('User verify all the global nav users under App Segments are working fine',  async({page}) => {
  await policies_page.appSegments_Links(page);
  
})

Then('User verify all the global nav users under policies are working fine',  async({page}) => {
  await policies_page.servers_Links(page);
  
})

Then('User verify all the global nav users under Access Methods are working fine', async({page}) => {
  await policies_page.accessMethods_Links(page);
  
})

Then('User verify all the global nav users under User Portal Configuration are working fine', async({page}) => {
  await policies_page.userPortalConfiguration_Links(page);
  
})

Then('User verify all the global nav users under Private App Protection are working fine', async({page}) => {
  await policies_page.privateAppProtection(page);
  
})

Then('User verify all the global nav users under Resources are working fine', async({page}) => {
  await policies_page.resources_link(page);
  
})

Then('User verify all the global nav users under settings are working fine',async({page}) => {
  await policies_page.settings_link(page);
  
})

Then('User verify all the global nav users under Self Service are working fine', async({page}) => {
  await policies_page.selfService_link(page);
  
})

Then('User verify all the global nav users under Configuration are working fine', async({page}) => {
  await policies_page.configuration_link(page);
  
})

Then('User verify all the global nav users under End User Notifications are working fine',  async({page}) => {
  await policies_page.endUserNotifications_link(page);
  
})

Then('User verify all the global nav users under Connectors are working fine', async({page}) => {
  await policies_page.connectors_link(page);
  
})

Then('User click on policies from global navigation',  async({page}) => {
  if (!url) {
    throw new Error("ONE_UI_BASE_URL is not defined in environment variables");
}
await page.goto(url);
await page.waitForTimeout(30000);
  await policies_page.policies_globalNav(page);
})

Then('click on access control option', async({page}) => {
  await policies_page.accessControl(page);
})

Then('click on cybersecurity option', async({page}) => {
  await policies_page.cyberSecurity(page);
})

Then('click on Digital Experience Monitoring option', async({page}) => {
  await policies_page.digitalExperience(page);
})

Then('click on Common Configuration option', async({page}) => {
  await policies_page.commonConfiguration(page);
})

Then('User verify all the global nav users under SaaS Application Control are working fine', async({page}) => {
  await policies_page.saaSApplicationControl(page);
})

Then('User verify all the global nav users under File Type Control are working fine', async({page}) => {
  await policies_page.fileTypeControl(page);
})

Then('User verify all the global nav users under URL Control are working fine', async({page}) => {
  await policies_page.uRLControl(page);
})

Then('User verify all the global nav users under policies Firewall are working fine', async({page}) => {
  await policies_page.firewall_Links(page);
})


