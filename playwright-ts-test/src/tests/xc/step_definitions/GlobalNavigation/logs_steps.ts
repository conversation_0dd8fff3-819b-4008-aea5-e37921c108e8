import { expect } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import globalNavigationPage from "../../pages/GlobalNavigation/logs_page";

const { Given, When, Then } = createBdd();

When('User clicks on the Insights', async({page}) => {
  await globalNavigationPage.clickLogsInsights(page);
})

Then('User verify the Internet and SaaS, Private Applications, Branch and Cloud Connectors and Zero Trust Branch links', async({page}) => {
  await globalNavigationPage.clickInsightLinks(page);
})

When('User clicks on the Log Streaming', async({page}) => {
  await globalNavigationPage.clickLogsLogStreaming(page);
})

Then('User verify the Internet Log Streaming, and Private Log Streaming links', async({page}) => {
  await globalNavigationPage.clickLogStreamingLinks(page);
})
