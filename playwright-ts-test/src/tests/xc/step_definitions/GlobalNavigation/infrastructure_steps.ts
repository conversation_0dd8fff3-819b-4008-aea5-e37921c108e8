
import { expect } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import infrastructure_page from "../../pages/GlobalNavigation/infrastructure_page";
import { config } from "dotenv";

config();

const { ONE_UI_BASE_URL: url } = process.env || "";
const { Given, When, Then } = createBdd();

Then('click on Internet & SaaS access option', async({page}) => {
  await infrastructure_page.internetAndSaas(page);
})

Then('click on Traffic Forwarding and verify all the links are working fine', async({page}) => {
  await infrastructure_page.trafficForwarding(page);
})

Then('click on Network Policies and verify all the links are working fine', async({page}) => {
  await infrastructure_page.networkPolicies(page);
})

Then('click on Traffic Capture and verify all the links are working fine', async({page}) => {
  await infrastructure_page.trafficCapture(page);
})

Then('click on private access option', async({page}) => {
  await infrastructure_page.privateAccess(page);
})

Then('click on Component and verify all the links are working fine', async({page}) => {
  await infrastructure_page.componentLinks(page);
})

Then('click on Business Continuity and verify all the links are working fine', async({page}) => {
  await infrastructure_page.businessContinuityLinks(page);
})

Then('click on Client Connector Policies and verify all the links are working fine', async({page}) => {
  await infrastructure_page.clientConnectorPoliciesLinks(page);
})

Then('click on Locations and verify all the links are working fine', async({page}) => {
  await infrastructure_page.locations(page);
})

Then('click on Connectors option', async({page}) => {
  await infrastructure_page.connectors(page); 
})

Then('click on Client and verify all the links are working fine', async({page}) => {
  await infrastructure_page.client(page);
})

Then('click on Edge and verify all the links are working fine', async({page}) => {
  await infrastructure_page.edge(page);
})

Then('click on Cloud and verify all the links are working fine', async({page}) => {
  await infrastructure_page.cloud(page);
})

Then('click on Common Resources option', async({page}) => {
  await infrastructure_page.commonResources(page);
})

Then('click on Gateways and verify all the links are working fine', async({page}) => {
  await infrastructure_page.gateways(page);
})

Then('click on Application and verify all the links are working fine', async({page}) => {
  await infrastructure_page.application(page);
})

Then('click on Deployment and verify all the links are working fine', async({page}) => {
  await infrastructure_page.deployment(page);
})