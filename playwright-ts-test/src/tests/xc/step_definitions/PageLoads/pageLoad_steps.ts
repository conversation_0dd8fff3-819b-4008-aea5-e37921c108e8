import { Page } from '@playwright/test';
import { createBdd } from 'playwright-bdd';
import PageLoad from '../../pages/PageLoads/pageLoad_page';

const { When, Before } = createBdd();

let pageLoad: PageLoad;

Before({ tags: "@pageloads" }, async ({ page }: { page: Page }) => {
    pageLoad = new PageLoad(page);
});

When("User is in Landing Page", async()=> {
    await pageLoad.goto();
});

When("User check all left nav options", async()=> {
    await pageLoad.checkLeftNavAPIFailures();
});

When("User check all Top nav options", async()=> {
    await pageLoad.checkTopNavAPIFailures();
});
