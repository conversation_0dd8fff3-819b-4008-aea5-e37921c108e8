import { createBdd } from "playwright-bdd";
import Appliance from '../../pages/UnifiedLocations/appliance_page';
const { When, Then } = createBdd();

When('User clicks on the appliance link', async ({ page }) => {
   await Appliance.connectors(page);
})

When('User search {string} in the appliance page', async ({ page }, s: string) => {
    await page.waitForTimeout(6000);
    await Appliance.searchAppliance(page, s);
 })

Then('Verify the physical device details in the appliance', async ({ page }) => {
    await Appliance.verifyAppliancesData(page);
 })

 Then('Verify the virtual device details in the appliance', async ({ page }) => {
    await Appliance.verifyVMData(page);
 })

 Then('Verify the {string} data in appliance', async ({ page }, name: string) => {
    await Appliance.verifyApplianceDetails(page, name);
 })