  import { createBdd } from "playwright-bdd";
  import EditLocation from "../../pages/UnifiedLocations/editLocation_page";
  const { When, Then } = createBdd();

  Then("User select the Authentication Method as {string}", async ({ page }, s: string) => {
      await EditLocation.clickOnTab(page, s);
  });

  Then("User fill the form as {string}", async ({ page }, arg: string) => {
    await EditLocation.preAuthEnableToggle(page);
  });

  Then("User Enable the form as {string}", async ({ page }, arg: string) => {
    await EditLocation.postAuthEnableToggle(page);
  });

  Then("Clicks on the {string} drawer save button", async ({ page }, type: string) => {
    await EditLocation.clickSaveButton(page, type);
  });

  Then("User verify the details of the locations", async ({ page }) => {
    await EditLocation.preAuthDetailsVerification(page);
  });

  Then("User verify the {string} of the locations", async ({ page }) => {
    await EditLocation.postAuthDetailsVerification(page);
  });

  Then("User verify the default state of connection options card", async ({ page }) => {
      await EditLocation.verifyConnectionOptionDefaultState(page);
  });

  Then("User click on the {string} tab", async ({ page }, s: string) => {
    await EditLocation.clickOnTab(page, s);
  });

  Then("Delete location from Overview", async ({ page }) => {
    await EditLocation.deleteFromOverview(page);
  });

  When("User enter the mandatory values again with name {string}", async ({ page }, name: string) => {
      await EditLocation.fillMandatoryValuesAgain(page, name);
  });

  Then("User fill in the traffic type as {string} and other vpn details", async ({ page }, trafficType: string) => {
      await EditLocation.selectVPNAndProxyPort(page, trafficType);
  });

  Then("User verify the IPSecGRE details", async ({ page }) => {
    await EditLocation.verifyIPSecDetails(page);
  });

  Then("User Enable the toggle as {string}", async ({ page }, arg: string) => {
    await EditLocation.iotDiscoveryToggle(page);
  });
  Then("User verify the IoT Discovery of the locations", async ({ page }, arg: string) => {
    await EditLocation.iotVerification(page);
  });

