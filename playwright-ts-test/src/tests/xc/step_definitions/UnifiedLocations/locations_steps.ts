import { createBdd } from "playwright-bdd";
import Locations from '../../pages/UnifiedLocations/locations_page';
const { When, Then } = createBdd();
When('User clicks on the locations link', async ({ page }) => {
   await Locations.locations(page);
})
When('User search {string} in the search bar', async ({ page }, s: string) => {
   await page.waitForTimeout(6000);
   await Locations.searchLocations(page, s);
})
Then('Verify the locations view', async ({ page }) => {
   await Locations.verifyLocationsView(page);
})
Then('Verify the details in the locations', async ({ page }) => {
   await Locations.verifyLocationsData(page);
})
When('Clicks on the {string} tab', async ({ page }, tab: string) => {
   await Locations.toggleTab(page, tab);
})
Then('Verify the No result found message', async ({ page }) => {
   await Locations.verifyNoResultFound(page);
})
When('Clicks on the name data {string}', async ({ page }, name: string) => {
   await Locations.clickName(page, name);
})
Then('Verify the {string} with data {string} of the locations', async ({ page }, verticalTab: string, data: string) => {
   await Locations.verifyTabs(page, verticalTab, data);
})
When('Clicks on the connection type {string}', async ({ page }, label: string) => {
   await Locations.clickConnectionType(page, label);
})
When('Clicks on the Sublocations data', async ({ page }) => {
   await Locations.clickSublocations(page);
})
When('User clicks on the {string} button', async ({ page }, add: string) => {
   await page.waitForTimeout(6000);
   await Locations.clickAddLocation(page, add);
})
When('User enter the mandatory values with name {string}', async ({ page }, name: string) => {
   await Locations.fillMandatoryValues(page, name);
})
When('User select the {string} checkbox', async ({ page }, locationGroup: string) => {
   await Locations.checkLocationGroups(page, locationGroup);
})
Then('User fill in the traffic type as {string} and other details', async ({ page }, trafficType: string) => {
   await Locations.selectTrafficType(page, trafficType);
})
Then('Clicks on the Add button', async ({ page }) => {
   await Locations.clickAddButton(page);
})
Then('Verify the user is redirected to the branch provisioning', async ({ page }) => {
   await Locations.verifyRedirectionToBC(page);
})
Then('Verify the user is redirected to the provisioning', async ({ page }) => {
   await Locations.verifyRedirectionToCC(page);
})
Then('Verify the IPSec created {string}', async ({ page }, name: string) => {
   await Locations.verifyLocation(page, name);
})
Then('Delete the location', async ({ page }) => {
   await Locations.deleteLocation(page);
})
Then('Verify the rows per page and select the {string}', async ({ page }, row: string) => {
   await Locations.verifyPagination(page, row);
})
Then('Verify the sync locations button is clickable', async ({ page }) => {
   await Locations.clickSyncLocation(page);
})
Then('Verify the duplicate name error message', async ({ page }) => {
   await page.waitForTimeout(8000);
   await Locations.verifyDuplicateMessage(page);
})
Then('Verify the details mentioned in the Overview', async ({ page }) => {
   await Locations.verifyDetails(page);
})
Then('User clicks on the edit and fill in all the details with location name {string}', async ({ page }, name: string) => {
   await Locations.editFunctionality(page, name);
})
Then('User save the changes and verify the details', async ({ page }) => {
   await Locations.verifySaveChanges(page);
})
Then('User edit the IPSec GRE in locations', async ({ page }) => {
   await Locations.editIPSecFunctionality(page);
})
Then('Verify the updated changes', async ({ page }) => {
   await Locations.verifyIPSecDetails(page);
})
Then('Verify if location {string} already exists', async ({ page }, name: string) => {
   await Locations.verifyLocationExists(page, name);
})
Then('Verify the {string} data in IPSecGRE', async ({ page }, name: string) => {
   await Locations.verifyIPSecCard(page, name);
})
Then('User fill in the proxy port', async ({ page }) => {
   await Locations.selectProxyPort(page);
})
Then('User fill in the Static ip & VPN credentials', async ({ page }) => {
   await Locations.selectStaticIPAndVPNCredentials(page);
})
Then('User fill in the Virtual Service Edges', async ({ page }) => {
   await Locations.selectVirtualServiceEdges(page);
})
Then('Verify sublocations count to be {string}', async ({ page }, data: string) => {
   await Locations.verifySublocationsCount(page, data);
})
Then('User fill in the location name as {string}', async ({ page }, data: string) => {
   await Locations.fillLocationName(page, data);
})
Then("User verify the default values of overview details card", async ({ page }) => {
   await Locations.verifyLocationOverviewDefaultValues(page);
});
Then("Verify empty state for tab with data {string}", async ({ page }, data: string) => {
   await Locations.verifyEmptyStateForTab(page, data);
});
Then("User clicks on Add Sublocation button", async ({ page }) => {
   await Locations.clickAddSubLocation(page);
});
Then("User close the {string} drawer", async ({ page }, name: string) => {
   await Locations.closeDrawer(page, name);
});
Then("User clicks on Add Appliance button", async ({ page }) => {
   await Locations.clickAddAppliance(page);
});

Then("Verify branch connector details are visible", async ({ page }) => {
   await Locations.verifyBranchConnectorDetails(page);
});

Then("Verify the {string} error occured", async ({ page }, error: string) => {
   await Locations.verifyAlertError(page, error);
});

Then("User select the multiple Manual Location Groups", async ({ page }) => {
   await Locations.selecMultipletManulLocationGroups(page);
});

Then("User verify the Manual Location Groups value in overview details card", async ({ page }) => {
   await Locations.verifyManulLocationGroupsValue(page);
});

Then("User verify the GRE Tunnel Info", async ({ page }) => {
   await Locations.verifyIpSecGreTunnelInfo(page);
});

Then('User clicks on the Sublocation tab', async({page}, add: string) => {
   await Locations.clickSubLocationtab(page, add);
})

Then('User clicks on {string} button to add the first Sublocation', async({page}, add: string) => {
   await page.waitForTimeout(6000);
   await Locations.clickAddFirstSublocation(page, add);
})

Then('User enter the mandatory values for Sublocation with name {string}', async({page}, name: string) => {
   await Locations.fillSublocationMandatoryValues(page, name);

})
When('User select the {string} checkbox for Sublocation', async({page}, SublocationGroup: string) => {
   await Locations.checkSublocationGroups(page, SublocationGroup);
})
Then('User fill in the Internal IP Address as {string}', async({page}, ip: string) => {
   await Locations.fillInternalIPAddressSingleTime(page, ip);
})

Then('Clicks on the Add button in Sublocation drawer', async({page}) => {
   await page.waitForTimeout(6000);
   await Locations.clickAddButtonSublocationDrawer(page); 
  // await page.waitForTimeout(6000);
})

Then('User search for Sublocation {string} in the search bar', async({page}, s: string) => {
   await page.waitForTimeout(6000);
   await Locations.searchSublocations(page, s);
})

Then('Clicks on the sublocation name {string}', async({page}, name: string) => {
   await page.waitForTimeout(6000);
   await Locations.clickSublocationName(page, name);
})

Then('User verifies the Sublocation Name to be {string} in the drawer', async({page}, name: string) => {
   await page.waitForTimeout(6000);
   await Locations.verifySublocationName(page, name);
})

Then('User verifies the Sublocation Description to be {string} in the drawer', async({page}, name: string) => {
   await page.waitForTimeout(6000);
   await Locations.verifySublocationDescr(page, name);
})

Then('User verifies the Internal IP Addresses entered', async({page}, ip: string) => {
   await page.waitForTimeout(6000);
   await Locations.verifyInternalIpAddress(page, ip);
})

Then('User verifies the Internal IP Addresses entered to be {string}', async({page}, ip: string) => {
   await page.waitForTimeout(6000);
   await Locations.verifyInternalIpAddress(page, ip);
})

Then('User verifies the Sublocation Traffic Type to be {string}', async({page}, name: string) => {
  // await page.waitForTimeout(6000);
   await Locations.verifySublocationTrafficType(page, name);
})

Then('User verifies the Sublocation Groups {string} to be {string}', async({page}, name: string, status: string) => {
  // await page.waitForTimeout(6000);
   await Locations.verifySublocationGroups(page, name, status);
})

Then('Exits from Sublocation Drawer', async({page}) => {
   await Locations.cancelSublocationDrawer(page);
})

Then('Delete the Sublocation', async({page}) => {
  // await page.waitForTimeout(6000);
   await Locations.deleteSublocation(page);
})

Then('Click on Location Overview tab', async({page}) => {
   await Locations.clickOverviewTab(page);
})

Then('Delete {string} from Location Overview page', async({page}, name: string) => {
   await Locations.deleteLocationFromOverview(page, name);
})

Then('Verify the Sublocations View', async({page}) => {
   await Locations.verifySublocationsView(page);
})

Then('Verify the data in Sublocations Index page for {string}', async({page}, name:string) => {
   await Locations.verifySublocationsIndexData(page, name);
})

Then('Check for Internal IP Address {string} already exists message in popup', async({page}, name:string) => {
   //await page.waitForTimeout(8000);
   await Locations.checkDuplicateInternalIp(page, name);
})

Then('User remove the duplicate Internal IP Address from the list', async({page}) => {
   await Locations.removeFirstInternalIp(page);
})

Then('Check for Sublocation {string} already exists message in popup', async({page}, name:string) => {
   await Locations.checkDuplicateSubloc(page, name);
})

Then('Clicks on the Add button to add more Sublocations', async({page}) => {
   await Locations.clickAddMoreSublocation(page);
})

Then('User edit the Sublocation Name to {string}', async({page}, name:string) => {
   await Locations.editSublocationName(page, name);
})

Then('User edit the Sublocation Description to {string}', async({page}, name:string) => {
   await Locations.editSublocationDescription(page, name);
})

Then('User click on Save in Sublocation Drawer', async({page}) => {
   await Locations.clickSaveSublocation(page);
})

Then('Verify the Sublocation Name to be {string} in Sublocation page', async({page}, name:string) => {
   await Locations.verifySublocationNameInIndexPage(page, name);
})

Then('Verify the Sublocation Description to be {string} in Sublocation page', async({page}, name:string) => {
   await Locations.verifySublocationDescrInIndexPage(page, name);
})

Then('Verify that Sublocation search results display {string} when user enters {string} in the Search bar', async({page}, name:string, input:string) => {
   await Locations.verifySublocationSearch(page, name, input);
})

Then('User clear the Sublocation Search bar', async({page}) => {
   await Locations.clearSublocationSearch(page);
})





