import { expect } from '@playwright/test';
import { createBdd } from 'playwright-bdd';
import LoginPage from '../pages/login_page';
import BrowserStorageHelper from '../../../../resources/utils/BrowserStorageHelper';
import ApiHelper from '../../../../resources/utils/ApiHelper';
let globalToken: string | null = null;
import { config } from 'dotenv';
import PlaywrightActions from '../../../../resources/utils/PlaywrightActions';

const { Given, When, Then } = createBdd();

config();

const {
  ONEUI_USERNAME: username = '',
  ONEUI_PASSWORD: password = '',
  ONE_UI_BASE_URL: url = '',
  ONEUI_USERNAME_MA_ONLY: maOnlyUsername = '',
  ONEUI_USERNAME_ZID_ONLY: zidOnlyUsername = '',
  ONEUI_USERNAME_ZTDS_ONLY: ztdsOnlyUsername = '',
  ONEUI_USERNAME_ZTW_ONLY: ztwOnlyUsername = '',
  ONEUI_USERNAME_ZDX_ONLY: zdxOnlyUsername = '',
  ONEUI_USERNAME_ZIA_ONLY: ziaOnlyUsername = '',
  ONEUI_USERNAME_ZPA_ONLY: zpaOnlyUsername = ''
} = process.env;

let token;

Given('User open the URL', async ({ page }) => {
  await page.goto(url);
});

When('User enter the valid {string} username and password', async ({ page }, userLogin) => {
  await page.waitForLoadState('domcontentloaded');
  const user = returnUserEmail(userLogin);
  await LoginPage.login(page, user, password);
});

Then('User login to the console successfully', async ({ page }) => {
  await page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, {timeout: 40000});
  await page.waitForTimeout(10000);
  // await expect(page.getByTestId("nav-pills-tab-0")).toHaveText('Analytics');
});

Then('User waits till onboarding page gets loaded', async ({ page }) => {
  await page.waitForSelector(`[data-testid="three-steps-heading"]`, {timeout: 40000});
});

Then('user clicks on Analytics', async ({ page }) => {
  await page.getByTestId("nav-pills-tab-0").click();
  await page.waitForTimeout(10000);
  await PlaywrightActions.closePopup(page);
  await page.mouse.click(300, 500);
});

Then('User fetch the okta token', async ({ page }) => {
  const raw = await BrowserStorageHelper.getOktaToken(page);
  const token = raw.replace(/^Bearer\s+/i, '');
  globalToken = token;
  ApiHelper.setBearerToken(token);
});

function returnUserEmail(userName: string): string {
  return (userName === 'MA Only User') ? maOnlyUsername
    : (userName === 'ZID Only User') ? zidOnlyUsername
    : (userName === 'ZTDS Only User') ? ztdsOnlyUsername
    : (userName === 'ZTW Only User') ? ztwOnlyUsername
    : (userName === 'ZDX Only User') ? zdxOnlyUsername
    : (userName === 'ZIA Only User') ? ziaOnlyUsername
    : (userName === 'ZPA Only User') ? zpaOnlyUsername
    : username;
}