import { createBdd } from "playwright-bdd";
import { Page } from "@playwright/test";
import SaasSecurity from "../../pages/DataProtection/saassecurity_page";

const { Before, When, Then } = createBdd();
let dpPage: SaasSecurity;

Before({ tags: "@dpdashboard" }, async ({ page }: { page: Page }) => {
  dpPage = new SaasSecurity(page);
});

When("User is in data channels saas security", async () => {
  await dpPage.navigateToDP();
  await dpPage.checkPageUrl();
});

Then("Verify the date filter in saas security page", async () => {
  await dpPage.checkDeDateFilter();
});

Then(
  "Verify the {string} bar chart widget in saas security page",
  async ({ page }, s: string) => {
    await dpPage.checkSaasIncidents(page, s);
  },
);

Then("Verify the empty state for top users", async ({ page }) => {
  await dpPage.checkEmptyStateTopUsers(page);
});

Then(
  "Verify the {string} users table widget in saas security page",
  async ({ page }, s: string) => {
    await dpPage.checkTopUsersSaasIncidents(page, s);
  },
);

Then(
  "Verify the {string} saas applications table widget in saas security page",
  async ({ page }, s: string) => {
    await dpPage.checkSaasApplicationsDataExposure(page, s);
  },
);

Then("Verify information icon against each legend", async ({ page }) => {
  await dpPage.checkSaasIncidentsIconEachLegend(page);
});
