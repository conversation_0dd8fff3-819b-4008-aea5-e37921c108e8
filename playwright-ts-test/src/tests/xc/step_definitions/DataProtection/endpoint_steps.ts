import { createBdd } from "playwright-bdd";
import { Page } from "@playwright/test";
import Endpoints from "../../pages/DataProtection/endpoints_page";

const { Before, When, Then } = createBdd();
let dpPage: Endpoints;

Before({ tags: "@dpdashboard" }, async ({ page }: { page: Page }) => {
  dpPage = new Endpoints(page);
});

When("User is in data channels endpoints page", async () => {
  await dpPage.navigateToDP();
  await dpPage.checkPageUrl();
});

Then("Verify the date filter in endpoints page", async () => {
  await dpPage.checkDeDateFilter();
});

Then(
  "Verify the {string} donut chart widget in endpoints page",
  async ({ page }, s: string) => {
    await dpPage.checkTotalEndpointIncidents(page, s);
  },
);

Then(
  "Verify the {string} users table widget in endpoints page",
  async ({ page }, s: string) => {
    await dpPage.TopUsersEndpointIncidents(page, s);
  },
);

Then(
  "Verify the {string} bar chart widget in endpoints page",
  async ({ page }, s: string) => {
    await dpPage.checkTopEndpointsSensitiveDataBeingExfiltrated(page, s);
  },
);

Then(
  "Verify Empty endpoint incidents data",
  async ({ page }) => {
    await dpPage.checkEmptyStateEndpointIncidents(page);
  },
);

Then(
  "Verify Click View Endpoint Activity",
  async ({ page }) => {
    await dpPage.checkClickViewEndpointActivity(page);
  },
);