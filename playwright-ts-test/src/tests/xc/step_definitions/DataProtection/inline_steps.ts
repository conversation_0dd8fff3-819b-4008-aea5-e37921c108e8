import { createBdd } from "playwright-bdd";
import { Page } from "@playwright/test";
import Inline from "../../pages/DataProtection/inline_page";

const { Before, When, Then } = createBdd();
let dpPage: Inline;

Before({ tags: "@dpdashboard" }, async ({ page }: { page: Page }) => {
  dpPage = new Inline(page);
});

When("User is in data channels inline page", async () => {
  await dpPage.navigateToDP();
  await dpPage.checkPageUrl();
});

Then("Verify the date filter in inline page", async () => {
  await dpPage.checkDeDateFilter();
});

Then(
  "Verify the {string} bar chart widget in inline page",
  async ({ page }, s: string) => {
    await dpPage.checkTopSensitiveDataTypes(page, s);
  },
);

Then(
  "Verify the {string} donut chart widget in inline page",
  async ({ page }, s: string) => {
    await dpPage.checkSensitiveGenAIApplications(page, s);
  },
);

Then(
  "Verify the {string} lowest usage widget in inline page",
  async ({ page }, s: string) => {
    await dpPage.checkTopHighRiskApplicationsEliminate(page, s);
  },
);

When(
  "Verify the {string} lowest usage table in inline page",
  async ({ page }, title: string) => {
    await dpPage.verifyTopHighRiskApplicationsEliminateTable(page, title);
  },
);

Then(
  "Verify the colors used for Critical, High, Medium risks",
  async ({ page }) => {
    await dpPage.verifyColorCodingInRiskLevels(page);
  },
);

Then(
  "Verify the Application names with users count should be available",
  async ({ page }) => {
    await dpPage.verifyApplicationNamesUsersCount(page);
  },
);

Then(
  "Verify the {string} Action Edit Policy",
  async ({ page }, title: string) => {
    await dpPage.verifyActionEditPolicy(page, title);
  },
);

Then(
  "Verify the {string} most usage widget in inline page",
  async ({ page }, s: string) => {
    await dpPage.checkTopHighRiskApplicationsSecure(page, s);
  },
);

Then(
  "Verify the {string} most usage table in inline page",
  async ({ page }, title: string) => {
    await dpPage.verifyTopHighRiskApplicationsSecureTable(page, title);
  },
);

Then(
  "Verify the {string} spider node chart widget in inline page",
  async ({ page }, s: string) => {
    await dpPage.checkSensitiveFilesMLCategories(page, s);
  },
);

Then(
  "Verify Time Range filter",
  async ({ page }) => {
    await dpPage.checkTimeRangeFilter(page);
  },
);

Then(
  "Verify Click View All GenAI Activity",
  async ({ page }) => {
    await dpPage.checkClickViewAllGenAIActivity(page);
  },
);

Then(
  "Verify Click View All Data Discovery",
  async ({ page }) => {
    await dpPage.checkClickViewAllDataDiscovery(page);
  },
);
