import { createBdd } from "playwright-bdd";
import Dashboard from "../../pages/DataProtection/dashboard_page";
import { Page } from "@playwright/test";

const { Before, When, Then } = createBdd();
let dpPage: Dashboard;

Before({ tags: "@dpdashboard" }, async ({ page }: { page: Page }) => {
  dpPage = new Dashboard(page);
});

When("User is in data protection dashboard", async () => {
  await dpPage.navigateToDP();
  await dpPage.checkPageUrl();
});

Then("Verify the filters in DP page", async () => {
  await dpPage.checkDeFilters();
});

Then("Verify the date dropdown in DP page", async () => {
  await dpPage.checkDeDateFilter();
});

Then("Verify the Total Incidents widget in DP page", async () => {
  await dpPage.checkTotalIncidents();
});

Then("Verify the Users with Most Incidents widget in DP page", async () => {
  await dpPage.checkUsersMostIncidents();
});

Then("Verify the All Channels Data widget in DP page", async () => {
  await dpPage.checkAllChannelsData();
});

Then("Verify the Top Sensitive Data At Rest widget in DP page", async () => {
  await dpPage.checkSensitiveDataAtRest();
});

Then(
  "Verify presence of classification options: AI Classification and DLP Engines",
  async ({ page }) => {
    await dpPage.checkSensitiveDataAtRestSegment(page);
  },
);

Then(
  "Verify dropdowns Channel, ML Category display options",
  async ({ page }) => {
    await dpPage.checkSensitiveDataAtRestDropdowns(page);
  },
);

Then(
  "Verify Admin can clear Channel, ML Category filters",
  async ({ page }) => {
    await dpPage.checkSensitiveDataAtRestClearFilters(page);
  },
);

Then("Verify Empty total incidents data", async ({ page }) => {
  await dpPage.checkEmptyStateTotalIncidents(page);
});

Then("Verify page rendring time", async ({ page }) => {
  await dpPage.checkPageRendringTime(page);
});
