import { createBdd } from "playwright-bdd";
import { Page } from "@playwright/test";
import Email from "../../pages/DataProtection/email_page";

const { Before, When, Then } = createBdd();
let dpPage: Email;

Before({ tags: "@dpdashboard" }, async ({ page }: { page: Page }) => {
  dpPage = new Email(page);
});

When("User is in data channels email page", async () => {
  await dpPage.navigateToDP();
  await dpPage.checkPageUrl();
});

Then("Verify the date filter in email page", async () => {
  await dpPage.checkDeDateFilter();
});

Then(
  "Verify the {string} bar chart widget in email page",
  async ({ page }, s: string) => {
    await dpPage.checkTopDomainsSensitiveDataBeingSentTo(page, s);
  },
);

Then(
  "Verify the {string} table widget in email page",
  async ({ page }, s: string) => {
    await dpPage.checkTopUsersEmailIncidents(page, s);
  },
);
