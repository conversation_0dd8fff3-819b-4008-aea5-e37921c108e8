import { expect } from '@playwright/test';
import { createBdd } from 'playwright-bdd';
import LoginPage from '../pages/login_page.js';
import browserStorageHelper from '../../../../resources/utils/BrowserStorageHelper.js';
import { config } from 'dotenv';

const { Given, When, Then } = createBdd();

config();

const {
  ONEUI_USERNAME: username = '',
  ONEUI_PASSWORD: password = '',
  ONE_UI_BASE_URL: url = '',
  ONEUI_USERNAME_MA_ONLY: maOnlyUsername = '',
  ONEUI_USERNAME_ZID_ONLY: zidOnlyUsername = '',
  ONEUI_USERNAME_ZTDS_ONLY: ztdsOnlyUsername = '',
  ONEUI_USERNAME_ZTW_ONLY: ztwOnlyUsername = '',
  ONEUI_USERNAME_ZDX_ONLY: zdxOnlyUsername = '',
  ONEUI_USERNAME_ZIA_ONLY: ziaOnlyUsername = '',
  ONEUI_USERNAME_ZPA_ONLY: zpaOnlyUsername = ''
} = process.env;

let token;

//needed
Given('User open the ZTW URL', async ({ page }) => {
  await page.goto(url);
});

//needed
When('User enter the valid ZTW {string} username and password', async ({ page }, userLogin) => {
  await page.goto(url);
  await page.waitForLoadState('domcontentloaded');
  const user = returnUserEmail(userLogin);
  await LoginPage.login(page, user, password);
});

Then('User login to the console successfully', async ({ page }) => {
  await page.waitForTimeout(10000);
  const verifyAnalytics = await page.waitForSelector(`(//button[@data-testid="nav-pills-tab-0"])`, {timeout: 10000});
  await expect(page.getByTestId("nav-pills-tab-0")).toHaveText('Analytics');
  const verifyNetworkLabel = await page.getByTestId('networking-page-header');
  expect(verifyNetworkLabel).toHaveText('Networking');
});

Then('User fetch the okta token', async ({ page }) => {
  token = await browserStorageHelper.getOktaToken(page);
  console.log('token: ', token);
});

function returnUserEmail(userName: string): string {
  return (userName === 'MA Only User') ? maOnlyUsername
    : (userName === 'ZID Only User') ? zidOnlyUsername
    : (userName === 'ZTDS Only User') ? ztdsOnlyUsername
    : (userName === 'ZTW Only User') ? ztwOnlyUsername
    : (userName === 'ZDX Only User') ? zdxOnlyUsername
    : (userName === 'ZIA Only User') ? ziaOnlyUsername
    : (userName === 'ZPA Only User') ? zpaOnlyUsername
    : username;
}