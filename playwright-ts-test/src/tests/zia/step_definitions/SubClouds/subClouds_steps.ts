import { createBdd } from "playwright-bdd";
import { Page, expect } from "@playwright/test";
import { SubCloudsPage } from "../../pages/SubClouds/subClouds_page"; 

const { Before, Given, Then } = createBdd();

let subCloudsPage: SubCloudsPage;

// Setup for before steps
Before({ tags: "@subclouds" }, async ({ page }: { page: Page }) => {
  subCloudsPage = new SubCloudsPage(page);
});

// Background Step
Given("User navigates to the SubClouds screen", async () => {
  await subCloudsPage.navigateToSubCloudsPage();
});

// Step: Verify the page title
Then("I should see the page title as {string}", async ({}, expectedTitle: string) => {
  const actualTitle = await subCloudsPage.getPageTitle();
  expect(actualTitle).toBe(expectedTitle);
});

// Step: Verify the search bar is present
Then("I should see the search bar present on the page", async () => {
  const isSearchBarVisible = await subCloudsPage.isSearchBarVisible();
  expect(isSearchBarVisible).toBeTruthy();
});
