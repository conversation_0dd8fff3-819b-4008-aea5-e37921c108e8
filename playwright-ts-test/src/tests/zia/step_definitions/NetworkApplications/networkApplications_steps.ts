import { createBdd } from "playwright-bdd";
import { Page, expect } from "@playwright/test";
import NetworkApplicationsPage from "../../pages/NetworkApplications/networkApplications_page";

const { Before, Given, When, Then } = createBdd();

let networkApplicationsPage: NetworkApplicationsPage;

Before({ tags: "@network_applications" }, async () => {
  networkApplicationsPage = new NetworkApplicationsPage();
});

// Background Step
Given("User navigates to the Network Applications screen", async ({ page }: { page: Page }) => {
  await networkApplicationsPage.navigateToNetworkApplications(page);
});

// Search Functionality
Then("I should be able to search for applications using valid keywords", async ({ page }: { page: Page }) => {
  await networkApplicationsPage.verifySearchForApplications(page);
});

Then("I should be able to search within the DNS application groups", async ({ page }: { page: Page }) => {
  await networkApplicationsPage.verifySearchInDnsApplicationGroups(page);
});

Then("I should be able to search for application groups using valid keywords", async ({ page }: { page: Page }) => {
  await networkApplicationsPage.verifySearchForApplicationGroups(page);
});

// Sorting Functionality
Then("I should be able to sort applications by columns such as name and description", async ({ page }: { page: Page }) => {
  await networkApplicationsPage.verifySortingByColumns(page, "APPLICATIONS");
});

Then("I should be able to sort DNS application groups by columns such as name and description", async ({ page }: { page: Page }) => {
  await networkApplicationsPage.verifySortingByColumns(page, "DNS_APPLICATION_GROUP");
});

Then("I should be able to sort application groups by columns such as name and description", async ({ page }: { page: Page }) => {
  await networkApplicationsPage.verifySortingByColumns(page, "APPLICATION_GROUPS");
});

// Application Group Management
When( "I add an application group with name {string} and description {string}", async ({ page }: { page: Page }, name: string, description: string) => {
    await networkApplicationsPage.addAnApplicationGroup(page, name, description);
  }
);

When(
  "I add a DNS application group with name {string} and description {string}",
  async ({ page }: { page: Page }, name: string, description: string) => {
    await networkApplicationsPage.addApplicationGroup(page, name, description);
  }
);


Then( "I should be able to select {string}, click {string}, and save the group", async ({ page }: { page: Page }, application: string, action: string) => {
    await networkApplicationsPage.selectApplicationAndSaveGroup(page, application, action);
    
  }
);

Then('I should be able to select {string}, click {string}, and save the DNS group', async ({ page }: { page: Page }, application: string, action: string) => {
  await networkApplicationsPage.selectApplicationAndSaveDNSGroup(page, application, action);
});

Then( "I should be able to select {string} also, click {string}, and save the group", async ({ page }: { page: Page }, application: string, action: string) => {
  await networkApplicationsPage.selectOneMoreApplicationAndSaveGroup(page, application, action);
});

Then('I should be able to select {string} also, click {string}, and save the DNS group',async ({ page }: { page: Page }, application: string, action: string) => {
  await networkApplicationsPage.selectOneMoreDNSApplicationAndSaveGroup(page, application, action);
});

When("I edit the group for {string}", async ({ page }: { page: Page }, name: string) => {
  await networkApplicationsPage.editDNSGroup(page, name);
});

Then("I should see {string} is still present", async ({ page }: { page: Page }, application: string) => {
  await networkApplicationsPage.verifyApplicationIsPresent(page, application);
});

Then("I should see both {string} and {string} are present", async ({ page }: { page: Page }, application1: string, application2: string) => {
    await networkApplicationsPage.verifyMultipleApplicationsArePresent(page, application1, application2);
  }
);

Then("I should be able to delete the group {string}", async ({ page }: { page: Page }, application: string) => {
  await networkApplicationsPage.delete(page, application);
});

Then('I should be able to delete the DNS application group {string}',  async ({ page }: { page: Page }, application: string) => {
  await networkApplicationsPage.deleteGroup(page, application);
});