import { createBdd } from "playwright-bdd";
import NanologStreamingServicePageInstance from "../../pages/NanologStreamingService/nanologStreamingService_page";
import { Page } from "@playwright/test";

const { Before, Given, Then, When } = createBdd();

let nssPage: typeof NanologStreamingServicePageInstance;

Before({ tags: "@nanolog" }, async ({ page }: { page: Page }) => {
  nssPage = NanologStreamingServicePageInstance;
});

// Common Given step to navigate to NSS Feeds screen
Given("User navigates to the NSS Feeds screen", async ({ page }) => {
  await nssPage.navigateToNSSFeeds(page);
});

// New step definition for adding basic feed details
When('Add NSS Feed with name {string} type {string} log type {string} siem ip {string} and siem port {string}', async ({ page }, feedName: string, nssType: string, logType: string, siemIp: string, siemPort: string) => {
  // Navigate to NSS Feeds tab first (as per original logic flow)
  const nssFeedOption = page.locator(nssPage.fields.nssFeedsTab);
  await nssFeedOption.click();
  await page.waitForTimeout(2000);
  const addNssFeedButton = await page.locator(nssPage.fields.addNssFeedButton);
  await addNssFeedButton.click();
  await page.waitForTimeout(2000);

  if (nssType === "NSS for Web") {
    await nssPage.fillNSSFeedForWebForm(page, feedName, logType, siemIp, siemPort);
  } else {
    // Placeholder for other NSS types if they need parameterized form filling
    throw new Error(`Parameterized step for NSS Type "${nssType}" is not yet fully implemented for form filling.`);
  }
});


Then('verify {string} NSS Server for type {string} is exists.', async ({ page },s: string, s2: string) => {
  await nssPage.checkNssServer(page, s, s2);

})

Then("Verify adding NSS Feed {string} with type NSS for Firewall, Cloud & Branch Connector", async ({ page }, feedName: string) => {
  await nssPage.addNSSFeedForFirewall(page, feedName);
});

Then("Verify adding NSS Feed {string} for SaaS Security with Log Type SaaS Security", async ({ page }, feedName: string) => {
  await nssPage.addNSSFeedForSaaSSecurity_SaaSLog(page, feedName);
});


Then('Verify adding NSS Feed {string} with type NSS for Endpoint DLP', async ({ page }, feedName: string) => {
  await nssPage.addNSSFeedFor_endpointDLP(page, feedName);
})

Then('Verify adding NSS Feed {string} with type NSS for Email DLP', async ({ page }, feedName: string) => {
  await nssPage.addNSSFeedFor_emailDLP(page, feedName);
})


Then("Verify adding NSS Feed {string} with Log Type SaaS Security Activity", async ({ page }, feedName: string) => {
  await nssPage.addNSSFeedForSaaSSecurity_ActivityLog(page, feedName);
});

Then("Verify adding NSS Feed {string} with Log Type Tunnel", async ({ page }, feedName: string) => {
  await nssPage.addNSSFeedForTunnelLog(page, feedName);
});

Then("Verify adding NSS Feed {string} with type NSS for Firewall, Cloud & Branch Connector and Log Type DNS", async ({ page }, feedName: string) => {
  await nssPage.addNSSFeedForFirewallDNSLogWithAllFiltersAndCancel(page, feedName);
});

Then("Verify adding NSS Feed {string} with type NSS for Firewall, Cloud & Branch Connector and Log Type Alerts", async ({ page }, feedName: string) => {
  await nssPage.addNSSFeedForFirewall_AlertsLog(page, feedName);
});

Then("Verify adding NSS Feed {string} with type NSS for Firewall, Cloud & Branch Connector and Log Type Firewall Logs", async ({ page }, feedName: string) => {
  await nssPage.addNSSFeedForFirewall_FirewallLogs(page, feedName);
});

When('Search the test feed {string} from the NSS Feed grid',  async ({ page }, feedName: string) => {
  await nssPage.searchTestFeed(page, feedName);
})

Then('Delete the NSS Feed {string} by using API', async ({ page }, feedName: string) => {
  await nssPage.deleteFeed(page, feedName);
})

Then('Delete the NSS Feed {string} by using UI', async ({ page }, feedName: string) => {
  await nssPage.deleteTestFeed(page, feedName);
})

// New step definition for saving the form
Then('Save the NSS Feed form', async ({ page }) => {
  await nssPage.saveForm(page);
});

// New step definition for verifying changes saved message
Then('Verify changes are saved successfully', async ({ page }) => {
  await nssPage.verifyChangesSaved(page);
});

Then('Verify feed {string} is visible in the grid', async ({ page }, feedName: string) => {
  await nssPage.verifyFeedVisibleInGrid(page, feedName);
});
