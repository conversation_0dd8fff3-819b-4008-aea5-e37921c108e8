import { createBdd } from "playwright-bdd";
import BandwidthControlPage from "../../pages/BandwidthControl/bandwidthControl_page";
import { Page } from "@playwright/test";
import { DataTable } from "@cucumber/cucumber";

const { Before, Given, Then } = createBdd();
let bandwidthControlPage: typeof BandwidthControlPage;

Before({ tags: "@bandwidth_control" }, async ({ page }: { page: Page }) => {
  bandwidthControlPage = BandwidthControlPage;
});

Given("User navigates to the Bandwidth Control screen", async ({ page }) => {
  await bandwidthControlPage.navigateToBandwidthControl(page);
});

Then('Verify Bandwidth Control search functionality with search term {string} and expected filter text {string}',async ({ page }, searchTerm: string, expectedFilterText: string) => {
  await bandwidthControlPage.verifySearchFunctionality(page, searchTerm, expectedFilterText);
});

Then('verify that the {string} record exists', async ({ page }, ruleName: string) => {
  await bandwidthControlPage.verifyRecordExists(page, ruleName);
})

Then(`Verify Bandwidth Control sort functionality for rule {string}`, async ({ page }, ruleName: string) => {
  await bandwidthControlPage.verifySortFunctionality(page, ruleName);
});

Then('Verify Bandwidth Control rule label click for rule {string}', async ({ page }, ruleName: string) => {
  await bandwidthControlPage.verifyRuleLabelClick(page, ruleName);
});

Then('Verify Bandwidth Control rule order click expecting first rule order {string}', async ({ page }, expectedFirstRuleOrder: string) => {
  await bandwidthControlPage.verifyRuleOrderClick(page, expectedFirstRuleOrder);
});


Then('Verify Bandwidth Control default rule selection for rule {string} with initial min bandwidth {string} max bandwidth {string} expecting {string} and reset min bandwidth {string} max bandwidth {string} expecting {string}', async (
  { page },
  ruleNameToSearch: string,
  initialMinBw: string,
  initialMaxBw: string,
  expectedInitialBwRange: string,
  resetMinBw: string,
  resetMaxBw: string,
  expectedResetBwRange: string
) => { // NOSONAR // Missing closing parenthesis was here
  await bandwidthControlPage.verifyDefaultRuleSelection(page, ruleNameToSearch, initialMinBw, initialMaxBw, expectedInitialBwRange, resetMinBw, resetMaxBw, expectedResetBwRange);
});

Then('Verify Bandwidth Control add new bandwidth rule all fields with the following labels:', async ({ page }, dataTable: any) => {
  const expectedLabels: { [key: string]: string } = {};
  dataTable.hashes().forEach((row: { "Label Type": string; "Expected Text": string }) => {
    expectedLabels[row["Label Type"]] = row["Expected Text"];
  });
  await bandwidthControlPage.verifyAddNewBandwidthRuleAllFields(page, expectedLabels);
});

Then('Verify Bandwidth Control add new bandwidth rule with the following details:', async ({ page }: { page: Page }, dataTable: DataTable) => {
  const ruleDetailsArray = dataTable.hashes();
  if (ruleDetailsArray.length === 0) {
    throw new Error("Data table for adding new bandwidth rule is empty.");
  }
  const ruleDetails = ruleDetailsArray[0];
  await bandwidthControlPage.verifyAddNewBandwidthRule(
    page,
    ruleDetails["Rule Order"],
    ruleDetails["Rule Name"],
    ruleDetails["Admin Rank"],
    ruleDetails["Rule Status"],
    ruleDetails["Bandwidth Class"],
    ruleDetails["Location Group"]
  );
});

Then('Verify Bandwidth Control copy bandwidth rule created with the following details:', async ({ page }: { page: Page }, dataTable: DataTable) => {
  const ruleDetailsArray = dataTable.hashes();
  if (ruleDetailsArray.length === 0) {
    throw new Error("Data table for copying bandwidth rule is empty.");
  }
  const ruleDetails = ruleDetailsArray[0];
  // First, ensure the rule to be copied exists or is created
  await bandwidthControlPage.verifyAddNewBandwidthRule(page, ruleDetails["Rule Order"], ruleDetails["Rule Name"], ruleDetails["Admin Rank"], ruleDetails["Rule Status"], ruleDetails["Bandwidth Class"], ruleDetails["Location Group"]);
  // Then, perform the copy operation and verification
  await bandwidthControlPage.verifyCopyBandwidthRule(page);
});

Then('Verify Bandwidth Control add classes in wizard for a new rule with order {string}, name {string}, admin rank {string}, status {string}',  async ({ page }, order: string, name: string, rank: string, status: string) => {
  await bandwidthControlPage.verifyAddClassesInWizard(page, order, name, rank, status);
});

Then('Verify Bandwidth Control dropdown search in wizard for location group {string} and protocol {string}', async ({ page }, locationGroupSearchTerm: string, protocolSearchTerm: string) => {
  await bandwidthControlPage.verifyDropdownSearchInWizard(page, locationGroupSearchTerm, protocolSearchTerm);
});

Then('Verify Bandwidth Control click recommended policy link {string} and verify popup title {string} and content {string}', async ({ page }, linkText: string, popupTitle: string, popupContent: string) => {
  await bandwidthControlPage.verifyClickRecommendedPolicy(page, linkText, popupTitle, popupContent);
});
