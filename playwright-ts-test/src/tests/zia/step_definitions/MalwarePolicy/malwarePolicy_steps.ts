import { createBdd } from "playwright-bdd";
import MalwarePolicyInstance from "../../pages/MalwarePolicy/malwarePolicy_page";
import { Page } from "@playwright/test";

const { Before, Given, Then } = createBdd();

let malwarePolicyPage: typeof MalwarePolicyInstance; 

Before({ tags: "@malware_policy" }, async ({ page }: { page: Page }) => {
  malwarePolicyPage = MalwarePolicyInstance; 
});

// Common Given step to navigate to Malware Policy
Given("User navigates to the Malware Policy screen", async ({ page }) => {
  await malwarePolicyPage.navigateToMalwarePolicy(page);
});

// Step definition for Inspect Inbound Traffic
Then("Verify the toggle {string} feature", async ({ page }, trafficDirection: "Inspect Inbound Traffic" | "Inspect Outbound Traffic") => {
  await malwarePolicyPage.inspectTrafficToggle(page, trafficDirection);
});

// Step definition for Inspect HTTP
Then("Verify the toggle for protocol {string}", async ({ page }: { page: Page }, protocolName: string) => {
  await malwarePolicyPage.verifyProtocolToggle(page, protocolName);
});

// Step definition for toggling Password-Protected Files
Then("Verify the toggle for file exception {string}", async ({ page }: { page: Page }, fileExceptionType: string) => {
  await malwarePolicyPage.verifyFileExceptionToggle(page, fileExceptionType);
});

// Step definition for Do Not Scan Content from URLs
Then("Verify the toggle Do Not Scan Content from these URLs feature", async ({ page }) => {
  await malwarePolicyPage.verifyDoNotScanContentSectionTitle(page);
});

// Step definition for deleting URLs from Do Not Scan
Then("Verify the ability to Delete Do Not Scan Content from these URLs", async ({ page }) => {
  await malwarePolicyPage.addAndRemoveUrlFromDoNotScanList(page, "test.net"); // Example URL
});

// Step definition for removing all Do Not Scan content
Then("Verify the ability to Remove All Do Not Scan Content from these URLs", async ({ page }) => {
  await malwarePolicyPage.Remove_All_Do_Not_Scan_Content_From_URLs(page, "test.net");
});
// Step definition for removing page-specific Do Not Scan content
Then("Verify the ability to Remove Page Do Not Scan Content from these URLs", async ({ page }) => {
  await malwarePolicyPage.Remove_Page_Do_Not_Scan_Content_From_URLs(page,  "test.net");
});

// Step definition for toggling Unwanted Applications
Then("Verify the toggle for threat type {string}", async ({ page }: { page: Page }, threatType: string) => {
  await malwarePolicyPage.verifyThreatTypeToggle(page, threatType);
});

// Step definition for toggling Inspect Inbound Traffic with cancel action
Then("Verify the toggle Inspect Inbound Traffic and ability to cancel the action", async ({ page }) => {
  await malwarePolicyPage.inspectTrafficToggleAndCancel(page, "Inspect Inbound Traffic");
});