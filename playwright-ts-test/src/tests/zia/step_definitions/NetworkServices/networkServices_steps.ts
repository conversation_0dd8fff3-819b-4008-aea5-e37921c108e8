import { createBdd } from "playwright-bdd";
import { Page, expect } from "@playwright/test";
import NetworkServicesPage from "../../pages/NetworkServices/networkServices_page";

const { Before, Given, When, Then } = createBdd();

let networkServicesPage: NetworkServicesPage;

Before({ tags: "@network_services_management" }, async () => {
  networkServicesPage = new NetworkServicesPage();
});

Given("User navigates to the Network Services screen", async ({ page }: { page: Page }) => {
  await networkServicesPage.navigateToNetworkServices(page);
});

// XC-10338 Steps
Given("I delete the Network Service {string} if it exists", async ({ page }: { page: Page }, name: string) => {
  await networkServicesPage.deleteNetworkService(page, name);
});

When("I add a Network Service with name {string}, TCP port {string}, and UDP port {string}", async ({ page }: { page: Page }, name: string, tcpPort: string, udpPort: string) => {
  await networkServicesPage.addNetworkService(page, name, tcpPort, udpPort);
});

Then("the Network Service {string} should be present", async ({ page }: { page: Page }, name: string) => {
  await networkServicesPage.verifyNetworkServicePresent(page, name);
});

When("I search for Network Service by port {string}", async ({ page }: { page: Page }, port: string) => {
  await networkServicesPage.searchNetworkServiceByPort(page, port);
});

Then("the Network Service {string} should be visible", async ({ page }: { page: Page }, name: string) => {
  // Assuming search by port also makes the name visible
  await networkServicesPage.verifyNetworkServicePresent(page, name);
});

When("I edit the Network Service {string} to change its description to {string}", async ({ page }: { page: Page }, name: string, newDescription: string) => {
  await networkServicesPage.editNetworkService(page, name, newDescription);
});


When("I delete the Network Service {string}", async ({ page }: { page: Page }, name: string) => {
  await networkServicesPage.deleteNetworkService(page, name);
});

Then("the Network Service {string} should not be present", async ({ page }: { page: Page }, name: string) => {
  await networkServicesPage.verifyNetworkServiceNotPresent(page, name);
});

// XC-10339 Steps
Then("I should be able to sort Network Services by name and description", async ({ page }: { page: Page }) => {
  await networkServicesPage.sortNetworkServicesByNameAndDescription(page);
});

Then("I should be able to filter Network Services by protocol {string}", async ({ page }: { page: Page }, protocol: string) => {
  await networkServicesPage.filterNetworkServicesByProtocol(page, protocol);
});

// XC-10340 Steps
Given("I delete the Network Service Group {string} if it exists", async ({ page }: { page: Page }, name: string) => {
  await networkServicesPage.deleteNetworkServiceGroup(page, name);
});

When("I add a Network Service Group with name {string} and select service {string}", async ({ page }: { page: Page }, name: string, service: string) => {
  await networkServicesPage.addNetworkServiceGroup(page, name, service);
});

Then("the Network Service Group {string} should be present", async ({ page }: { page: Page }, name: string) => {
  await networkServicesPage.verifyNetworkServiceGroupPresent(page, name);
});

When("I edit the Network Service Group {string} to add description {string}", async ({ page }: { page: Page }, name: string, description: string) => {
  await networkServicesPage.editNetworkServiceGroup(page, name, description);
});

When("I delete the Network Service Group {string}", async ({ page }: { page: Page }, name: string) => {
  await networkServicesPage.deleteNetworkServiceGroup(page, name);
});

Then("the Network Service Group {string} should not be present", async ({ page }: { page: Page }, name: string) => {
  await networkServicesPage.verifyNetworkServiceGroupNotPresent(page, name);
});

// XC-10341 Steps
Then("I should be able to search for Network Service Group {string}", async ({ page }: { page: Page }, name: string) => {
  await networkServicesPage.searchNetworkServiceGroup(page, name);
});

Then("I should be able to sort Network Service Groups by name and description", async ({ page }: { page: Page }) => {
  await networkServicesPage.sortNetworkServiceGroupsByNameAndDescription(page);
});