import { createBdd } from "playwright-bdd";
import { Page } from "@playwright/test";
import { DataTable } from '@cucumber/cucumber';
import StaticIPsGRETunnelsPage from "../../pages/StaticIPsAndGRETunnels/staticIPsAndGRETunnels"; 

const { Before, Given, When,Then } = createBdd();

// Common Given step to navigate to Email Tenants screen
Given("User navigates to the Static IPs & GRE Tunnels screen", async ({ page }) => {

  await StaticIPsGRETunnelsPage.navigateToStaticIPsAndGRETunnels(page);
});
Given("Click On Add Static IP Button", async ({ page }) => {

  await StaticIPsGRETunnelsPage.clickOnAddStaticIPButton(page);
});
Then('Verify User Trying to Edit Static IP', async ({ page }: { page: Page }, dataTable: DataTable) => {
  // Uses hashes()[0] because the table has headers in the first row and one data row.
   console.log(`Number of Rows :`+dataTable.hashes().length);
  for (let i = 0; i < dataTable.hashes().length; i++) {
    const details = dataTable.hashes()[i];
    //const row = tableData[i];
    console.log(`Validating Row ${i + 1}:`);
    await StaticIPsGRETunnelsPage.provideInputFieldTextBoxInStaticIP(page,details['labelName'],details['inputText']);
  }
  await StaticIPsGRETunnelsPage.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,'Edit Static IP','Next');
  await StaticIPsGRETunnelsPage.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,'Edit Static IP','Next');
  await StaticIPsGRETunnelsPage.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,'Edit Static IP','Save');
});
Then("Verify User Trying to Create Static IP {string} and Description {string} Inside Wizard", async ({ page }: { page: Page }, ipAddress: string, descriptionText:string) => {
  
    await StaticIPsGRETunnelsPage.clickOnStaticIPGRETunnelSearchBoxOption(page,'Static IP Address',1,ipAddress);
    await StaticIPsGRETunnelsPage.provideInputFieldTextBoxInStaticIP(page,'Description',descriptionText);
});
Then("Verify User Provide Region For Static IP Configuration", async ({ page }) => {
  
    await StaticIPsGRETunnelsPage.fillRegionDetailsInStaticIP(page);
});
Then("Verify User Providing Address {string} And Port Number {string} to Create Exchange Tenant", async ({ page }: { page: Page }, labelName: string, inputText:string) => {
  
    await StaticIPsGRETunnelsPage.fillGRETunnelConfigurationWizard(page);
    await StaticIPsGRETunnelsPage.provideInputFieldTextBoxInStaticIP(page,'Next Hop Address',labelName);
    await StaticIPsGRETunnelsPage.provideInputFieldTextBoxInStaticIP(page,'Port Number',inputText);
});
Then("Click On Save Button And Verify Notification Message", async ({ page }) => {
  
  await StaticIPsGRETunnelsPage.clickOnCurrentTextButton(page);
  await StaticIPsGRETunnelsPage.captureSavedNotificationMessage(page);
});
When("Search the created Static IPs {string}", async ({ page }: { page: Page }, searchText:string) => {
  
  await StaticIPsGRETunnelsPage.clickOnStaticIPGRETunnelSearchBoxOption(page,'Search.',1,searchText);
});
Then("Click On StaticIP {string} Menu Button {string} Option In Static IPs & GRE Tunnels", async ({ page }: { page: Page }, ipAddress: string, menuOption:string) => {
  
  await StaticIPsGRETunnelsPage.clickOnMenuOptionForStaticIPAndGRETunnel(page,ipAddress,menuOption);
});
When("Capture Wizard Header {string} And Click On Footer {string} Button For Email Tenant", async ({ page }: { page: Page }, wizardHeaderName: string, footerButtonAction:string) => {
  
  await StaticIPsGRETunnelsPage.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,wizardHeaderName,footerButtonAction);
});
Then("Click On {string} Header Save & Verify Notification Message In Static IPs & GRE Tunnels", async ({ page }: { page: Page }, wizardHeaderName: string) => {
  
  await StaticIPsGRETunnelsPage.clickOnSaveButtonAndCaptureNotificationMessage(page,wizardHeaderName);
});
Given("Click On Add GRE Tunnel Configuration Button And Verify Wizard Header", async ({ page }) => {

  await StaticIPsGRETunnelsPage.clickOnAddGRETunnelsButton(page);
});
When("Click On Static IP Address Dropdown & Select StaticIP {string} In GRE Tunnels", async ({ page }: { page: Page }, ipAddress: string) => {
  
  await StaticIPsGRETunnelsPage.selectStaticIPInGRETunnels(page,'Static IP Address',1,ipAddress,'Add GRE Tunnels');
});
Then("Provide Data Center In Add GRE Tunnel Configuration", async ({ page }) => {
  
  await StaticIPsGRETunnelsPage.chooseDataCenterInGRETunnels(page,'Primary Data Center VIP',1);
});
Then("Select Internal IP Range In Add GRE Tunnel Configuration", async ({ page }) => {
  
  await StaticIPsGRETunnelsPage.selectInternalGREIPRangeInGRETunnels(page,'Primary Data Center VIP',1);
});
Then('Verify User Perform Update GRE Tunnel Configuration', async ({ page }: { page: Page }, dataTable: DataTable) => {

  // Uses hashes()[0] because the table has headers in the first row and one data row.
   console.log(`Number of Rows :`+dataTable.hashes().length);
  for (let i = 0; i < dataTable.hashes().length; i++) {
    const details = dataTable.hashes()[i];
    //const row = tableData[i];
    console.log(`Validating Row ${i + 1}:`);
    await StaticIPsGRETunnelsPage.provideInputFieldTextBoxInStaticIP(page,details['labelName'],details['inputText']);
  }
  await StaticIPsGRETunnelsPage.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,'Edit GRE Tunnel','Next');
});
When("Click On GRE Tunnel Tab & Search the created GRE Tunnels {string}", async ({ page }: { page: Page }, searchText:string) => {
  
  await StaticIPsGRETunnelsPage.clickOnGRETunnelTab(page);
  await StaticIPsGRETunnelsPage.clickOnStaticIPGRETunnelSearchBoxOption(page,'Search.',1,searchText);
});
Then("Verify Update Data Center deatils In GRE Tunnel Configuration", async ({ page }) => {
  
  await StaticIPsGRETunnelsPage.changeDomesticPreferenceToggleForDataCenter(page,'Domestic Preference');
  await StaticIPsGRETunnelsPage.clickOnStaticIPLabelDropdown(page,'Primary Data Center VIP',1);
  await StaticIPsGRETunnelsPage.clickOnCurrentTextButton(page);
  await StaticIPsGRETunnelsPage.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,'Edit GRE Tunnel','Next');
});
Then("Verify Update Internal GRE IP Range In GRE Tunnel Configuration", async ({ page }) => {
  
  await StaticIPsGRETunnelsPage.changeDomesticPreferenceToggleForDataCenter(page,'Is Unnumbered IP');
  await StaticIPsGRETunnelsPage.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,'Edit GRE Tunnel','Next');
});
Then("Click On {string} Header Delete & Verify Notification Message In Static IPs & GRE Tunnels", async ({ page }: { page: Page }, wizardHeaderName: string) => {
  
  await StaticIPsGRETunnelsPage.clickOnDeleteButtonAndCaptureNotificationMessage(page,wizardHeaderName);
});
Then("Click Import CSV Button & Verify Notification Message In Static IPs & GRE Tunnels", async ({ page }: { page: Page }) => {
  
  await StaticIPsGRETunnelsPage.clickOnImportGRETunnelButtonAndCaptureNotificationMessage(page);
});