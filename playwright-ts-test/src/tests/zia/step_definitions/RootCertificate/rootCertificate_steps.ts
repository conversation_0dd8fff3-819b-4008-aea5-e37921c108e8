import { createBdd } from "playwright-bdd";
import { Page, expect } from "@playwright/test";
import RootCertificatesPage from "../../pages/RootCertificate/rootCertificate_page";

const { Before, Given, When, Then } = createBdd();

let rootCertificatesPage: RootCertificatesPage;

Before({ tags: "@root_certificate_management" }, async ({ page }: { page: Page }) => {
  rootCertificatesPage = new RootCertificatesPage(page);
});

Given("User navigates to the Root Certificates page", async ({ page }: { page: Page }) => {
  await rootCertificatesPage.navigateToRootCertificates();
});

When('I add a root certificate {string} with all types and upload {string}', async ({ page }: { page: Page }, name: string, file: string) => {
    await rootCertificatesPage.addRootCertificate(page, name, file);
})


Then('the certificate {string} should be saved', async ({ page }: { page: Page }, name: string) => {
    await rootCertificatesPage.verifyAddedRootCertificate(page, name);
})

When('I download, edit, and uncheck one type for {string} and save it', async ({ page }: { page: Page }, name: string) => {
    await rootCertificatesPage.downloadRootCertificate(page, name);
})


When('I delete the certificate {string}.', async ({ page }: { page: Page }, name: string) => {
    await rootCertificatesPage.deleteRootCertificateIfExists(page, name);
})
  
Then('{string} should no longer be visible', async ({ page }: { page: Page }, name: string) => {
  await rootCertificatesPage.verifyDeletedRootCertificate(page, name);
})