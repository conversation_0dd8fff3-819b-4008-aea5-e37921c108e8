import { createBdd } from "playwright-bdd";
import { DataTable } from "@cucumber/cucumber";
import { RiskProfilePage } from "../../pages/RiskProfiles/riskProfile_page";

const { Before, Given, When, Then } = createBdd();

let riskProfilePage: RiskProfilePage;

Before({ tags: "@risk_profiles" }, async ({ page }) => {
  riskProfilePage = new RiskProfilePage(page);
});

// Background Step
Given("I am on the {string} administration page", async ({}, pageName: string) => {
  await riskProfilePage.navigateToRiskProfilesPage();
});

// Add Step
When("I add a risk profile with the following details:", async ({}, dataTable: DataTable) => {
  const details = dataTable.hashes()[0];
  await riskProfilePage.addRiskProfile(details);
});

//clone add 
When("I add a risk profile for clone scenario with the following details:", async ({}, dataTable: DataTable) => {
  const details = dataTable.hashes()[0];
  await riskProfilePage.addRiskProfileForClone(details);
});

// Edit/Update Step (uses a regex to match both "update" and "edit")
When("I update the risk profile {string} with the following details:", async ({}, profileName: string, dataTable: DataTable) => {
  const detailsToUpdate = dataTable.hashes()[0];
  await riskProfilePage.editRiskProfile(profileName, detailsToUpdate);
});

// Delete Step
Then("I delete the risk profile {string}", async ({}, profileName: string) => {
  await riskProfilePage.deleteRiskProfile(profileName);
});

// Copy Step
When("I copy the risk profile {string}", async ({}, profileName: string) => {
  await riskProfilePage.copyRiskProfile(profileName);
});

// Search Step
When("I search for {string}", async ({}, profileName: string) => {
  await riskProfilePage.searchForClonedName(profileName);
});

// Verify Search Results Count Step
Then("I should see {string} risk profile in the results", async ({}, expectedCountStr: string) => {
  const expectedCount = parseInt(expectedCountStr, 10);
  await riskProfilePage.verifyResultCount(expectedCount);
});

// Clear Search Step
When("I clear the search filter", async () => {
  await riskProfilePage.clearSearch();
});
