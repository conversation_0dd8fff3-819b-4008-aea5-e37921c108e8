import { createBdd } from 'playwright-bdd';
const { Given, When, Then } = createBdd();
import FTPControl from '../../pages/FTPControl/ftpControl_page';

Given('User navigates to the FTP Control screen', async({page}) => {
    await FTPControl.navigateToFTPControl(page);
})
When('User verify the {string} heading', async({page}, title: string) => {
    await FTPControl.verifyTitle(page, title);
})

When('Verify the title {string}', async({page}, title: string) => {
    await FTPControl.verifySpanHeading(page, title);
})

Then('Change the {string} toggle', async({page}, toggle: string) => {
    await FTPControl.toggleAllowFTPOverHTTP(page, toggle);
})

Then('Click on {string} button', async({page}, button: string) => {
    await FTPControl.clickButton(page, button);
})

Then('Click on the {string} button', async({page}, button: string) => {
    await FTPControl.clickButton1(page, button);
})

Then('User select the option in Allowed URL Categories', async({page}) => {
    await FTPControl.selectOptionAllowedURLCategories(page);
})

Then('Change the {string} toggle for URL Category toggle case',  async({page}, toggle: string) => {
    await FTPControl.toggleAllowFTPOverHTTP1(page, toggle);
})

Then('User deselect the option in Allowed URL Categories', async({page}) => {
    await FTPControl.deSelectOptionAllowedURLCategories(page);
})

When('User clicks on the Recommended Policy option', async({page}) => {
    await FTPControl.clickRecommendedPolicy(page);
})

Then('Verify the pop up and close', async({page}) => {
    await FTPControl.closeRecommendedPolicyPopUp(page);
})