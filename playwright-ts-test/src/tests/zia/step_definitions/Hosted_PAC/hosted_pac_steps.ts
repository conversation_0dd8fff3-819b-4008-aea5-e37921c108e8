import { createBdd } from "playwright-bdd";
import { Page, expect } from "@playwright/test";
import { DataTable } from "@cucumber/cucumber";
import { HostedPacFilesPage } from "../../pages/Hosted_PAC/hosted_pac_page"; // Assuming this page object exists

const { Before, Given, When, Then } = createBdd();

let hostedPacFilesPage: HostedPacFilesPage;

Before({ tags: "@hosted_pac_files" }, async ({ page }) => {
  hostedPacFilesPage = new HostedPacFilesPage(page);
});

// Background Step
Given("User navigates to the Hosted PAC Files page", async () => {
  await hostedPacFilesPage.navigateToHostedPacFilesPage();
});

// @XC-10235 Scenario: Add, branch, and delete a PAC file
When("I create and deploy a new PAC file with the following details:", async ({ page }: { page: Page }, dataTable: DataTable) => {
  const details = dataTable.hashes()[0];
  await hostedPacFilesPage.createAndDeployPacFile(details);
});

Then("the PAC file {string} should be in the grid",  async ({}, pacFileName: string) => {
  await hostedPacFilesPage.verifyPacFileInGrid(pacFileName);
});

When("I create a new branch for the PAC file {string} with comment {string}", async ({}, pacFileName: string, comment: string) => {
  await hostedPacFilesPage.createBranchForPacFile(pacFileName, comment);
});

Then("I should be on the file history page for {string}", async ({}, pacFileName: string) => {
  await hostedPacFilesPage.verifyFileHistoryPage(pacFileName);
});

When("I delete the PAC file {string} from the main grid",  async ({}, pacFileName: string) => {
  await hostedPacFilesPage.deletePacFileFromMainGrid(pacFileName);
});

Then("the PAC file {string} should not be in the grid", async ({}, pacFileName: string) => {
  await hostedPacFilesPage.verifyPacFileNotInGrid(pacFileName);
});


When("I choose to export the PAC file {string} from the grid", async ({}, pacFileName: string) => {
  await hostedPacFilesPage.chooseToExportPacFile(pacFileName);
});

// @XC-10272 Scenario: Manage versions of a PAC file
When("I manage versions for the PAC file {string}", async ({}, pacFileName: string) => {
  await hostedPacFilesPage.manageVersionsForPacFile(pacFileName);
});

Then("I can view the deployed version", async () => {
  await hostedPacFilesPage.canViewDeployedVersion();
});

Then("I can see the correct export options for the latest version", async () => {
  await hostedPacFilesPage.canSeeCorrectExportOptionsForLatestVersion();
});

Then("I can create a new branch with comment {string}", async ({}, comment: string) => {
  await hostedPacFilesPage.canCreateNewBranchWithComment(comment);
});

Then("I can initiate and cancel a version comparison", async () => {
  await hostedPacFilesPage.canInitiateAndCancelVersionComparison();
});

Then("I navigate back to the PAC file list", async () => {
  await hostedPacFilesPage.navigateBackToPacFileList();
});


Then('sort the grid ascending to descending and {string} should be at top',  async ({}, pacName: string) => {
  await hostedPacFilesPage.sortGrid(pacName);
})
