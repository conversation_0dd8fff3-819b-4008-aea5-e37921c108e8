import { createBdd } from "playwright-bdd";
import { Page } from "@playwright/test";
import EzAgentPage from "../../pages/EzAgentConfiguration/ezAgentConfiguration_page";

const { Before, Given, When, Then } = createBdd();
let ezAgentPage: typeof EzAgentPage;

Before({ tags: "@ez_Agent" }, async () => {
    ezAgentPage = EzAgentPage;
});

Given('User navigates to the EZ Agent Configurations', async ({ page }: { page: Page }) => {
    await ezAgentPage.navigateToEzAgentConfiguration(page);
  });
  
  Then('Verify title {string}',async ({ page }: { page: Page }, title: string) => {
    await ezAgentPage.checkTitle(page, title);
  })
  
  