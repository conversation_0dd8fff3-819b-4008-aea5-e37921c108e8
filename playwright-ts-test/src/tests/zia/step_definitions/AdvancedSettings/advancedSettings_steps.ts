import { createBdd } from 'playwright-bdd';
const { Given, When, Then } = createBdd();
import AdvancedSettings from '../../pages/AdvancedSettings/advancedSettings_page';

Given('User navigates to the Advanced Settings screen', async({page}) => {
    console.log("User navigates to the Advanced Settings screen");
    await AdvancedSettings.navigateToAdvancedSettings(page);
})

When('User verify the heading {string}', async({page}, heading: string) => {
    await AdvancedSettings.verifyHeading(page, heading);
})

Then('Change the Enable Admin Ranking toggle', async({page}) => {
    await AdvancedSettings.toggleEnableAdminRanking(page);
})

When('Verify the Advanced setting subHeading {string}', async({page}, subHeading: string) => {
    await page.waitForTimeout(7000);
    await AdvancedSettings.verifySubHeading(page, subHeading);
})

Then('Enable the Enable Admin Ranking toggle', async({page}) => {
    await AdvancedSettings.enableToggleEnableAdminRanking(page);
})

Then('User clicks on {string} button', async({page}, button: string) => {
    await AdvancedSettings.clickButton(page, button);
})

Then('Change the Allow Cascading to URL Filtering toggle', async({page}) => {
    console.log('Change the Allow Cascading to URL Filtering toggle');
    await AdvancedSettings.disableToggleAllowCascadingURLFiltering(page);
})

Then('Enable the Allow Cascading to URL Filtering toggle', async({page}) => {
    console.log('Enable the Allow Cascading to URL Filtering toggle');
    await AdvancedSettings.enableToggleAllowCascadingURLFiltering(page);
})

Then('User enter session timeout {string}', async({page}, timeout: string) => {
    await AdvancedSettings.enterTimeout(page, timeout);
})

When('User enter the details for authentication exemptions', async({page}) => {
    await AdvancedSettings.enterAuthenticationExemption(page);
})

Then('User de-selects the details for authentication exemptions', async({page}) => {
    await AdvancedSettings.deSelectAuthenticationExemption(page);
})

When('User enter the details for Kerberos authentication exemptions', async({page}) => {
    await AdvancedSettings.enterKerberosAuthenticationExemption(page);
})

Then('User de-selects the details for Kerberos authentication exemptions', async({page}) => {
    await AdvancedSettings.deSelectKerberosAuthenticationExemption(page);
})

When('User enable the toggle button', async({page}) => {
    await AdvancedSettings.enableToggles(page);
})

Then('User disable the toggle button', async({page}) => {
    await AdvancedSettings.disableTogglesButton(page);
})

When('User select the options in the drop down', async({page}) => {
    await AdvancedSettings.selectDropDowns(page);
})

When('User enable the option and select values in the option', async({page}) => {
    await AdvancedSettings.preferSNIOption(page);
})