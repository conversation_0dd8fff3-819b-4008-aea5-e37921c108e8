import { createBdd } from 'playwright-bdd';
import { Page, expect } from '@playwright/test';
import { DataTable } from '@cucumber/cucumber';
import { VpnCredentialsPage } from '../../pages/VpnCredentialsManagement/vpnCredentialsManagement_page';

// Initialize BDD hooks
const { Before, Given, When, Then } = createBdd();

// Declare a variable to hold the VpnCredentialsPage instance
let vpnCredentialsPage: VpnCredentialsPage;

// Initialize the VpnCredentialsPage instance before tests tagged with @vpn_credentials
Before({ tags: "@vpn_credentials" }, async () => { // Assuming you'll use a tag like @vpn_credentials for these scenarios
  vpnCredentialsPage = new VpnCredentialsPage();
});

// Background Step
Given("User navigates to the VPN Credentials Management", async ({ page }: { page: Page }) => {
  await vpnCredentialsPage.navigateToVpnCredentialsPage(page);
});

// @XC-10231
When('I add a new VPN credential with the following details:', async ({ page }: { page: Page }, dataTable: DataTable) => {
  // Uses hashes()[0] because the table has headers in the first row and one data row.
  const details = dataTable.hashes()[0]; 

  await vpnCredentialsPage.clickAddCredentialButton(page);
  await vpnCredentialsPage.enterUserId(page, details['User ID']);
  await vpnCredentialsPage.verifyAndSelectAddressType(page, details['Type'] as 'FQDN' | 'IP');
  await vpnCredentialsPage.enterPassword(page, details['Password']);
  await vpnCredentialsPage.enterConfirmPassword(page, details['Password']);
  await vpnCredentialsPage.clickSaveButton(page);
});

Given('the VPN credential with User ID {string} does not exist', async ({ page }: { page: Page }, userId: string) => {
  await vpnCredentialsPage.ensureCredentialDoesNotExist(page, userId);
});

Then('search the {string}', async ({ page }: { page: Page }, credentialName: string) => {
  await vpnCredentialsPage.seachRecord(page, credentialName);
})

When('I edit the VPN credential {string}, changing its password to {string}', async ({ page }: { page: Page }, credentialName: string, newPassword: string) => {
  await vpnCredentialsPage.clickEditCredential(page, credentialName);
  await vpnCredentialsPage.enterPassword(page, newPassword); 
  await vpnCredentialsPage.enterConfirmPassword(page, newPassword);
  await vpnCredentialsPage.clickSaveButton(page);
});


When('I delete the VPN credential {string}', async ({ page }: { page: Page }, credentialName: string) => {
  await vpnCredentialsPage.seachRecord(page, credentialName);
  await vpnCredentialsPage.clickEditCredential(page, credentialName);
  await vpnCredentialsPage.clickDeleteButtonInEditForm(page);
  await vpnCredentialsPage.confirmDeletion(page);
});

When('I open the {string} dialog',  async ({ page }: { page: Page }, importCred: string) => {
  await vpnCredentialsPage.clickImportCredentialsButton(page, importCred);
});

Then('the dialog appears with an enabled {string} button', async ({ page }: { page: Page }) => {
  await page.locator('//div[@id="IMPORT_VPN_CREDENTIALS_FROM_CSV_FILE"]//span[contains(text(),"Import VPN Credentials")]').isVisible({timeout: 5000});
  await page.waitForTimeout(2000);
});

Then('when I cancel it, the dialog closes', async ({ page }: { page: Page }) => {
  await vpnCredentialsPage.clickCancelInImportDialog(page);

});

When('I click the {string} button on the VPN credentials page', async ({ page }: { page: Page }) => {
  // This step definition remains the same
  await vpnCredentialsPage.clickDownloadCsvButton(page);
});

Then('a CSV file for VPN credentials should be downloaded successfully', async ({ page }: { page: Page }) => {
  await vpnCredentialsPage.verifyDownloadInitiated(page);
});

Given('the user click on the Authentication Type filter dropdown and select {string} value from it', async ({ page }: { page: Page }, importCred: string) => {
  await vpnCredentialsPage.authTypeFilter(page, importCred);
})