import { Page, expect } from "@playwright/test";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

interface EventLogsLocators {
  // Navigation
  pageTitle: string;

  // Time Range
  timeRangeMainButton: string; 
  timeRangeOptionButton: (option: string) => string; 
  timeRangeCustomButton: string;
  timeRangeApplyButton: string;
  customTimeRangeApply: string;

  // Filters
  categoryFilterSectionText: string;
  categoryFilterDropdownTrigger: string;
  categoryOption: (option: string) => string;
  categorySelectedDisplayButton: (selection: string) => string;

  subCategoryFilterSectionText: string;
  subCategoryFilterDropdownTrigger: string; 
  subCategoryOption: (option: string) => string;
  subCategoryDoneButton: string;

  resultFilterSectionText: string;
  resultFilterDropdownTrigger: string; 
  resultOption: (option: string) => string;

  // Search
  searchFieldDropdownButton: (currentField: string) => string;
  searchFieldOption: (field: string) => string;
  searchInput: string; 
  searchActionButton: string;

  // Download
  downloadButton: string;
}

class EventLogsPage {
  private readonly locators: EventLogsLocators;

  constructor() {
    this.locators = {
      pageTitle: "Event Logs", 

      timeRangeMainButton: '//div[@id="Time Range"]/following-sibling::div/button/span[1]', 
      timeRangeOptionButton: (option: string) => `button[name="${option}"]`,
      timeRangeCustomButton: `button[name="Custom"]`,
      timeRangeApplyButton: `button[name="Apply"]`,
      customTimeRangeApply: '//button[@class="apply"]',

      categoryFilterSectionText: "Category", 
      categoryFilterDropdownTrigger: `div:filter({ hasText: /^CategoryAllAll.*$/ }) >> internal:role=button[name="All"i]`, // Example, needs refinement
      categoryOption: (option: string) => `role=option[name="${option}"]`,
      categorySelectedDisplayButton: (selection: string) => `button[name="${selection} "]`,

      subCategoryFilterSectionText: "Sub-Category",
      subCategoryFilterDropdownTrigger: `div:filter({ hasText: /^All.*DoneCancel$/ }) >> internal:role=button[name="All"i]`, // Example, needs refinement
      subCategoryOption: (option: string) => `text=${option}`,
      subCategoryDoneButton: `button[name="Done"]`,

      resultFilterSectionText: "Result", 
      resultFilterDropdownTrigger: `div[id="Result"] >> button[name*=""]`,
      resultOption: (option: string) => `role=option[name="${option}"]`,

      searchFieldDropdownButton: (currentField: string) => `//button[contains(@class, "drop-down-selected-value") and contains(., "${currentField}")]`,
      searchFieldOption: (field: string) => `role=option[name="${field}"]`,
      searchInput: `input[placeholder="Search"]`, 
      searchActionButton: `button[name="Search"]`,

      downloadButton: '//span[@title="Download CSV"]//i',
    };
  }

  async navigateToEventLogs(page: Page): Promise<void> {
    console.log("Executing navigateToEventLogs...");
    
    console.log("The Url used is : - " + url);
    if (!url) {
    throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
    }
    
    const eventLogsPath = "administration/event-logs";
    let targetUrl = "";
    
    if (url.includes("console")) {
    targetUrl = url + "internet-saas#" + eventLogsPath;
    await page.goto(targetUrl);
    console.log("Page navigation initiated (console URL).");
    await page.waitForTimeout(20000);
    } else {
    targetUrl = url + "#" + eventLogsPath;
    await page.goto(targetUrl);
    console.log("Page navigation initiated (non-console URL).");
    await page.waitForTimeout(15000);
    }
  
    console.log("Successfully navigated to Event Logs screen.");
    }

  async selectTimeRangeOption(page: Page, timeRangeOption: string): Promise<void> {
    console.log(`Selecting time range option: ${timeRangeOption}`);
    const timeRangeTitle = await page.getByText('Time Range');
    await expect(timeRangeTitle).toContainText('Time Range');

    const currentSelectionButton = page.locator('//div[@id="Time Range"]/following-sibling::div/button'); 
    await page.waitForTimeout(2000); // Added wait
    if (await currentSelectionButton.isVisible()){
        await currentSelectionButton.click();
    } else { 
        console.log("The Time Range Selection drop-down is not visible.")
    }
    await page.waitForTimeout(2000);
    await page.getByRole('button', { name: timeRangeOption, exact: true }).click();
    console.log(`Selected time range: ${timeRangeOption}`);
  }

  async clickApplyButtonForTimeRange(page: Page): Promise<void> {
    console.log("Clicking 'Apply' button for time range.");
    await page.waitForTimeout(4000);
    await page.locator(this.locators.customTimeRangeApply).click();
  }

  async verifyLogsForTimeRange(page: Page, expectedTimeRange: string): Promise<void> {
    console.log(`Verifying logs for time range: ${expectedTimeRange}`);
    await expect(page.locator('body')).toContainText(expectedTimeRange, { timeout: 10000 });
  }

  async selectCustomTimeRange(page: Page, _startDateDesc: string, _endDateDesc: string): Promise<void> {
    console.log("Selecting custom time range...");
    const timeRangeTitle = await page.getByText('Time Range');
    await expect(timeRangeTitle).toContainText('Time Range');
    
    await page.waitForTimeout(2000); // Added wait
    await page.locator(this.locators.timeRangeMainButton).click();
    await page.waitForTimeout(2000);
    // await page.waitForTimeout(2000); // Already present before next click
    await page.locator("//button/span[contains(text(),'Custom')]").click();
    console.log("Clicked 'Custom'. Date selection logic needs to be implemented.");
    await page.waitForTimeout(2000);
  }


  async selectFilterOption(page: Page, option: string, filterName: string): Promise<void> {
    console.log(`Selecting "${option}" from the "${filterName}" filter dropdown`);
    await page.waitForTimeout(2000); // Added wait
    await page.getByTitle(this.locators.pageTitle).click(); // As per snippet pattern

    if (filterName === "Category") {
      await page.waitForTimeout(2000); // Added wait
      await page.locator('//div[@id="Category"]/following-sibling::div/div/button').click();
      await page.waitForTimeout(2000);
      // await page.waitForTimeout(2000); // Already present before next click
      await page.locator('//span[@title="Provisioning"]').click();
    } else if (filterName === "Sub-Category") {
      await page.waitForTimeout(2000); // Added wait
      await page.locator('//div[@id="Sub-Category"]/following-sibling::div/button/span[1]').click();
      await page.waitForTimeout(2000);
      // await page.waitForTimeout(2000); // Already present before next click
      await page.locator('//input[@id="SCIM"]').click();

    } else if (filterName === "Result") {
        await page.waitForTimeout(2000);
      // await page.waitForTimeout(2000); // Already present before next click
      await page.locator('//div[@id="Result"]/following-sibling::div/div/button').click();
      await page.waitForTimeout(4000);
      // await page.waitForTimeout(4000); // Already present before next click
      await page.locator(`//span[@title="${option}"]`).click(); 
    }
  }

  async applyFilters(page: Page): Promise<void> {
    console.log("Applying filters (if applicable)...");
    if (await page.getByRole('button', { name: 'Done' }).isVisible()) {
        // Check if it's the sub-category "Done"
        const subCategoryDoneButton = page.locator('//div[@type="button"][contains(text(),"Done")]');
        if (await subCategoryDoneButton.isVisible()) {
            await page.waitForTimeout(2000);
            await subCategoryDoneButton.click();
            console.log("Clicked 'Done' for Sub-Category filter.");
        }
    }
  }

  async verifyLogsFilteredBy(page: Page, selectedFilterName: string,  filteredName: string): Promise<void> {
    console.log(`Verifying logs are filtered by selected ${selectedFilterName}`);
    await expect(page.locator(`//div[@id="${selectedFilterName}"]/following-sibling::div`)).toContainText(filteredName, { timeout: 10000 }); 
  }

  async verifyLogsFilteredByResultStatus(page: Page, filterType: string, status: string): Promise<void> {
    console.log(`Verifying logs are filtered by ${filterType} status ${status}`);
    await expect(page.locator(`//div[@id="${filterType}"]/following-sibling::div/div/button/div`)).toContainText(status, { timeout: 5000 }); 
    
  }

  async searchByField(page: Page, searchFilter: string, searchValue: string): Promise<void> {
    console.log(`Searching by filter "${searchFilter}" with value "${searchValue}"`);
    const pageTitle = await page.getByTitle(this.locators.pageTitle).isVisible();
    if (!pageTitle) throw new Error("Page title not found");

    const currentSearchFieldButton = page.locator('(//button[@class="drop-down-selected-value "])[4]//div');
    await page.waitForTimeout(2000);
    await currentSearchFieldButton.first().click();
    await page.locator(`//button/span[contains(text(),"${searchFilter}")]`)

    console.log(`Search value "${searchValue}" to be entered. Input field interaction missing from snippets.`);

    await page.locator('//input[@placeholder="Search..."]').click();
    await page.waitForTimeout(2000);
    await page.locator('//input[@placeholder="Search..."]').fill(searchValue);
    await page.waitForTimeout(2000);
    await page.locator('//span[@aria-label="Search"]').click();


  }

  async clickDownloadButton(page: Page): Promise<void> {
    console.log("Clicking the download button for event logs");
    await expect(page.getByTitle(this.locators.pageTitle)).toBeVisible();
    // await page.waitForTimeout(2000); // Already present before next click
    await page.waitForTimeout(2000);
    await page.locator(this.locators.downloadButton).click();
  }

  async verifyDownloadInitiated(page: Page): Promise<void> {
    console.log("Verifying download process initiated successfully");
    const downloadPromise = page.waitForEvent('download');
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toBeTruthy(); 
    console.log(`Download initiated: ${download.suggestedFilename()}`);
  }
}

export default new EventLogsPage();