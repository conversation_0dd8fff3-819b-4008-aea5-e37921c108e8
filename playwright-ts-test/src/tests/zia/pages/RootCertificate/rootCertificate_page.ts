import { Page, expect } from "@playwright/test";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

interface RootCertificatesLocators {
  pageTitle: string;
  addCertificateButton: string;
  nameInput: string;
}

export default class RootCertificatesPage {
  private readonly page: Page;
  private readonly locators: RootCertificatesLocators;

  constructor(page: Page) {
    this.page = page;
    this.locators = {
      pageTitle: '//div/span[contains(text(),"Root Certificates")]',
      addCertificateButton: '//span[contains(text(),"Add Root Certificate")]',
      nameInput: '//input[@aria-label="displayName"]'
    };
  }

  // Navigate to the Root Certificates Page
  async navigateToRootCertificates() {
    console.log("Executing navigateToRootCertificates...");

    console.log("The Url used is: " + url);
    if (!url) {
        throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
    }

    const rootCertificatesPath = "administration/root-certificate";
    let targetUrl = "";

    if (url.includes("console")) {
        targetUrl = url + "internet-saas#" + rootCertificatesPath;
        await this.page.goto(targetUrl);
        console.log("Page navigation initiated (console URL).");
        await this.page.waitForTimeout(45000); 
    } else {
        targetUrl = url + "#" + rootCertificatesPath;
        await this.page.goto(targetUrl);
        console.log("Page navigation initiated (non-console URL).");
        await this.page.waitForTimeout(19000); 
    }

    await expect(this.page).toHaveURL(new RegExp(rootCertificatesPath.replace(/\//g, '\\/')));
    await expect(this.page.locator(this.locators.pageTitle)).toBeVisible({ timeout: 20000 });
    console.log("Successfully navigated to Root Certificates page.");
}

  // Add a Root Certificate
  async addRootCertificate(page: Page, name: string, filePath: string) {
    console.log(`Adding certificate "${name}" with file "${filePath}"...`);
    const title = await this.page.locator('//div/span[contains(text(),"Root Certificates")]');
    await expect(title).toBeVisible();
    await this.page.locator(this.locators.addCertificateButton).click();
    await this.page.locator(this.locators.nameInput).click();
    await this.page.locator(this.locators.nameInput).fill(name);    

    await page.locator('//div[@data-testid="multiDropdown-type"]/div[2]/div/div/span[2]').click();
    await page.waitForTimeout(1000);
    await page.locator('//span[contains(text(),"Clear Selection")]').click();
    await page.waitForTimeout(1000);

    // Check all checkboxes
    const checkboxes = await page.locator('//label/input');
    const checkboxCount = await checkboxes.count();

    for (let i = 0; i < checkboxCount; i++) {
        await checkboxes.nth(i).click();
    }

    await page.locator('//span[contains(text(),"Selected Items ( ' + checkboxCount + ' )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(1000);

    // Locate the correct file input element and upload the file
    const fileUploader = await page.locator('//form[@data-testid="fileUploader-cert"]//input[@type="file"]'); 
    await fileUploader.setInputFiles(`fixture/ZIA/${filePath}`);
    
    // Save the certificate
    await page.locator('//span[contains(text(),"Save")]').click();
    console.log('Certificate has been saved successfully.');
}

async searchRoot (page: Page, name: string){
    console.log(`Searching for certificate "${name}"...`);
    const search = await page.locator('//input[@aria-label="Filter"]');
    await search.isVisible();
    await search.fill(name);
    await page.waitForTimeout(1000);
    await page.locator('//button[@aria-label="Apply filter"]').click();
    await page.waitForTimeout(1000);
}
async verifyAddedRootCertificate(page: Page, name: string) {
    console.log(`Verifying added certificate "${name}"...`);
    await this.searchRoot(page, name);
    await page.waitForTimeout(1000);
    await page.locator(`//div[contains(text(),"${name}")]`).isVisible();
    await page.waitForTimeout(1000);
}

async downloadRootCertificate(page: Page, name: string) {
    console.log(`Downloading certificate "${name}"...`);
    await page.waitForTimeout(1000);
    await page.locator('//button[@aria-label="EDIT"]').isVisible();
    await page.locator('//button[@aria-label="EDIT"]').click();
    await page.waitForTimeout(1000);

    const downloadButton = await page.locator('//span[@title="Edit Root Certificate"]');
    await downloadButton.isVisible();
    await page.waitForTimeout(1000);

    await page.locator('//div[@data-testid="multiDropdown-type"]/div[2]/div/div/span[2]').click();
    await page.waitForTimeout(1000);
    await page.locator('//span[contains(text(),"Clear Selection")]').click();
    await page.waitForTimeout(1000);

    await page.locator('(//label/input)[1]').click();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Save")]').click();
}

async deleteRootCertificateIfExists(page: Page, name: string) {
  console.log(`Searching and deleting certificate "${name}" if it exists...`);

  // Search for the root certificate using the searchRoot function
  console.log(`Searching for certificate "${name}"...`);
  const search = await page.locator('//input[@aria-label="Filter"]');
  await expect(search).toBeVisible();
  await search.fill(name);
  await page.waitForTimeout(1000);
  await page.locator('//button[@aria-label="Apply filter"]').click();
  await page.waitForTimeout(1000);

  // Check if the certificate is present
  const certificateLocator = page.locator(`//div[contains(text(),"${name}")]`);
  const isCertificateVisible = await certificateLocator.isVisible();

  if (isCertificateVisible) {
      console.log(`Certificate "${name}" found. Proceeding to delete...`);
      
      // Perform the delete if the certificate exists
      await expect(page.locator('//button[@aria-label="EDIT"]')).toBeVisible();
      await page.locator('//button[@aria-label="EDIT"]').click();
      await page.waitForTimeout(1000);

      const downloadButton = await page.locator('//span[@title="Edit Root Certificate"]');
      await expect(downloadButton).toBeVisible();
      await page.waitForTimeout(1000);

      const deleteButton = await page.locator('//span[contains(text(),"Delete")]');
      await expect(deleteButton).toBeVisible();
      await deleteButton.click();

      const confirmButton = await page.locator('//span[contains(text(),"Confirm")]');
      await expect(confirmButton).toBeVisible();
      await confirmButton.click();
      await page.waitForTimeout(4000);

      console.log("Check Popup");
      const modalCloseButtonLocator = page.locator('//div[@data-testid="modal-header"]/span/i');
      const isModalCloseButtonVisible = await modalCloseButtonLocator.isVisible();
      if (isModalCloseButtonVisible) {
          console.log("Modal popup found, closing it...");
          await modalCloseButtonLocator.click();
      } else {
          console.log("Modal popup not found, continuing with execution...");
      }

      console.log(`Certificate "${name}" successfully deleted.`);
  } else {
      console.log(`No record found for certificate "${name}". Continuing with execution.`);
  }

  await page.waitForTimeout(5000);
}

async verifyDeletedRootCertificate(page: Page, name: string) {
  console.log(`Verifying deleted certificate "${name}"...`);
  await page.waitForTimeout(1000);

  // Search for the certificate using the searchRoot method
  await this.searchRoot(page, name);
  await page.waitForTimeout(1000);

  // Verify the certificate is no longer visible after deletion
  const certificateLocator = page.locator(`//div[contains(text(),"${name}")]`);
  await expect(certificateLocator).not.toBeVisible();
  console.log(`Certificate "${name}" has been successfully verified as deleted.`);
}

}