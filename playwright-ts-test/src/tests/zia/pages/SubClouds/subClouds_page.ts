import { Page, expect } from "@playwright/test";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

export class SubCloudsPage {
  private page: Page;

  // Fields for locators
  fields: {
    pageTitle: string;
    searchBar: string;
  };

  constructor(page: Page) {
    this.page = page;

    // Define locators specific to the SubClouds screen
    this.fields = {
      pageTitle: '//div[@id="page-content"]//span[contains(text(),"Subclouds")]', 
      searchBar: '//input[@placeholder="Search..."]'
    };
  }

  async navigateToSubCloudsPage(): Promise<void> {
    console.log("Executing navigateToSubCloudsPage...");

    console.log("The Url used is : - " + url);
    if (!url) {
        throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
    }

    const subCloudsPath = "administration/subclouds";
    let targetUrl = "";

    // Determine the URL format based on whether it includes "console"
    if (url.includes("console")) {
        targetUrl = url + "internet-saas#" + subCloudsPath;
        await this.page.goto(targetUrl, { waitUntil: 'networkidle' });
        console.log("Page navigation initiated (console URL).");
        await this.page.waitForTimeout(45000); // Adjusted timeout for console URLs
    } else {
        targetUrl = url + "#" + subCloudsPath;
        await this.page.goto(targetUrl, { waitUntil: 'networkidle' });
        console.log("Page navigation initiated (non-console URL).");
        await this.page.waitForTimeout(19000); // Adjusted timeout for non-console URLs
    }

    // Validate the navigation
    await expect(this.page).toHaveURL(new RegExp(subCloudsPath.replace(/\//g, '\\/'))); // Regex ensures correct path structure in the URL
    await expect(this.page.locator(this.fields.pageTitle)).toBeVisible({ timeout: 20000 }); // Page title is used as a key validation
    console.log("Successfully navigated to SubClouds page.");
}

  // Method to get the page title
  async getPageTitle(): Promise<string> {
    console.log("Fetching page title...");
    const titleElement = this.page.locator(this.fields.pageTitle);
    await titleElement.isVisible();
    const titleText = await titleElement.innerText();
    console.log("Page title is:", titleText);
    return titleText;
  }

  // Method to verify the presence of the search bar
  async isSearchBarVisible(): Promise<boolean> {
    console.log("Checking if the search bar is visible...");
    const searchBar = this.page.locator(this.fields.searchBar);
    const isVisible = await searchBar.isVisible();
    console.log("Search bar visibility:", isVisible);
    return isVisible;
  }
}