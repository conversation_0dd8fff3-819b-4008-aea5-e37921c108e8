import { Page, expect } from "@playwright/test";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

class SecureBrowserPage {
  fields: {
    pageTitle: string;
    smartIsolationToggle: string;
    usersTab: string;
    groupTab: string;
    warningsToggleTestId: string;
    valueDropDown: string;
    groupDropDown: string;
    clearSelectedItem: string;
    status: string;
    browserControl: string;
    enableCheckUserNotificationToggle: string,
    howOftenToCheck: string,
    smartIsolateLabel: string;
    enableSmartAILabel: string;
    cancelButton: string;
    allChangesCanceledMessage: string;
    selectedItems1: string;
    dropdownSearchInput: string;
    selectedItems0: string;
    doneButton: string;
    saveButton: string;
    allChangesSavedMessage: string;
    browserIsolationProfileLabel: string;
    profileDropdownUndefined: string;
    defaultIsolationProfileListItem: string;
    browserVulnerabilityProtectionLabel: string;
    enableChecksUserNotificationLabel: string;
    howOftenToCheckLabel: string;
    dailyFrequencyListItem: string;
    monthlyFrequencyListItem: string;
    weeklyFrequencyListItem: string;
    disableNotificationForPluginsLabel: string;
    pluginsDropdown: string;
    disableNotificationForApplicationsLabel: string;
    applicationsDropdown: string;
    clearSelectedItemsButton: string;
    applicationsDropdownSelectedValue: string;
    browserBlockingLabel: string;
    allowAllBrowsersToggleStatus: string;
    microsoftBrowsersLabel: string;
  };

  constructor() {
    this.fields = {
      howOftenToCheck: "//span[@data-testid='dropdown-pluginCheckFrequency']//div/span",
      pageTitle: "//div[@class='page-title ']//span[text()='Alerts']",
      smartIsolationToggle: '(//span[@data-testid="toggleSwitch-enableSmartBrowserIsolation"]//span)[2]/i',
      usersTab: "//span[text()='Users']",
      groupTab: "//span[text()='Groups']",
      warningsToggleTestId: "toggleSwitch-enableWarnings",
      valueDropDown: '(//div[@data-testid="multi-dropdown"])[1]',
      groupDropDown: '(//div[@data-testid="multi-dropdown"])[2]',
      clearSelectedItem: '//span[contains(text(),"Clear Selection")]',
      status: "//span[@data-testid='toggleSwitch-enableSmartBrowserIsolation']//div",
      enableCheckUserNotificationToggle: "//span[@data-testid='toggleSwitch-enableWarnings']//div",
      browserControl: "//span[text()='Browser Control']",
      smartIsolateLabel: "//span[contains(text(),'Smart Isolate')]",
      enableSmartAILabel: "//span[contains(text(), 'Enable AI/ML based Smart')]",
      cancelButton: '//button[text()="Cancel"]',
      allChangesCanceledMessage: '//span[contains(text(),"All changes have been canceled.")]',
      selectedItems1: '//span[contains(text(),"Selected Items ( 1 )")]',
      dropdownSearchInput: '//input[@placeholder="Search..."]',
      selectedItems0: '//span[contains(text(),"Selected Items ( 0 )")]',
      doneButton: '//span[contains(text(),"Done")]',
      saveButton: "//button[text()='Save']",
      allChangesSavedMessage: '//span[contains(text(),"All changes have been saved.")]',
      browserIsolationProfileLabel: '//span[contains(text(),"Browser Isolation Profile")]',
      profileDropdownUndefined: '//span[@data-testid="dropdown-undefined"]',
      defaultIsolationProfileListItem: '//ul/li[contains(text(),"Default Isolation Profile")]',
      browserVulnerabilityProtectionLabel: "//span[text()='Browser Vulnerability Protection']",
      enableChecksUserNotificationLabel: "//span[text()='Enable Checks & User Notification']",
      howOftenToCheckLabel: '//span[contains(text(), "How Often to Check")]',
      dailyFrequencyListItem: "//ul/li[contains(text(),'Daily')]",
      monthlyFrequencyListItem: "//ul/li[contains(text(),'Monthly')]",
      weeklyFrequencyListItem: "//ul/li[contains(text(),'Weekly')]",
      disableNotificationForPluginsLabel: '//span[contains(text(),"Disable Notification for Plugins")]',
      pluginsDropdown: '(//div[@data-testid="multiDropdown-bypassPlugins"]//div)[2]',
      disableNotificationForApplicationsLabel: '//span[contains(text(),"Disable Notification for Applications")]',
      applicationsDropdown: '(//div[@data-testid="multiDropdown-bypassApplications"]//div)[2]/div',
      clearSelectedItemsButton: '//span[contains(text(),"Clear") and contains(text(),"Selection")]',
      applicationsDropdownSelectedValue: '(//div[@data-testid="multiDropdown-bypassApplications"]//div)[1]',
      browserBlockingLabel: '//span[text()="Browser Blocking"]',
      allowAllBrowsersToggleStatus: '//span[@data-testid="toggleSwitch-allowAllBrowsers"]//div',
      microsoftBrowsersLabel: '//span[contains(text(),"Microsoft Browsers")]',
    };
  }

  async navigateToSecureBrowser(page: Page): Promise<void> {
    console.log("Executing navigateToSecureBrowser...");

    console.log("The Url used is : - " + url);
    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
    }

    const secureBrowserPath = "policy/web/browser-control";
    let targetUrl = "";

    if (url.includes("console")) {
      targetUrl = url + "internet-saas#" + secureBrowserPath;
      await page.goto(targetUrl, { waitUntil: 'networkidle' });
      console.log("Page navigation initiated (console URL).");
      await page.waitForTimeout(25000);
    } else {
      targetUrl = url + "#" + secureBrowserPath;
      await page.goto(targetUrl, { waitUntil: 'networkidle' });
      console.log("Page navigation initiated (non-console URL).");
      await page.waitForTimeout(19000);
    }
    await expect(page).toHaveURL(new RegExp(secureBrowserPath.replace(/\//g, '\\/')));
    await expect(page.locator(this.fields.browserControl).first()).toBeVisible({ timeout: 20000 }); // Using browserControl as the key element
    console.log("Successfully navigated to Secure Browser (Browser Control) screen.");
  }



  async toggleSmartIsolation(page: Page): Promise<void> {
    console.log("Executing toggleSmartIsolation...");
    await page.waitForTimeout(3000);

    const label = page.locator(this.fields.smartIsolateLabel);
    await expect(label).toContainText("Smart Isolate"); // Text is part of the locator
    console.log("Verified 'Smart Isolate' label.");
    await label.click();
    console.log("Clicked 'Smart Isolate' label.");

    await page.waitForTimeout(2000);

    const toggle = page.locator(this.fields.smartIsolationToggle);

    // Ensure the toggle element is present before proceeding
    await page.waitForSelector(this.fields.smartIsolationToggle);
    await page.waitForTimeout(1000);

    // Fetch the current value of the aria-checked attribute
    const currentState = await page.locator(this.fields.status).getAttribute('aria-checked');
    console.log("Current Smart Isolation toggle status: ", currentState);
    await page.waitForTimeout(5000);

    if (currentState === 'false') {
      console.log("Toggle is false, attempting to click it to true.");
      // Click the toggle and verify it turns on
      await page.waitForTimeout(1000);
      await toggle.click();
      console.log("Clicked toggle.");
      const currentStatus = await page.locator(this.fields.status).getAttribute('aria-checked');
      await expect(currentStatus).toContain("true");
      console.log("Verified toggle is now true.");
    } else if (currentState === 'true') {
      // Click the toggle, verify it turns off, and toggle it back on
      console.log("The AI/ML based Smart Browser Isolation is already enabled");
    } else {
      // Throw an error for any unexpected attribute value
      console.error(`Unexpected aria-checked value for Smart Isolation toggle: ${currentState}`);
      throw new Error(`Unexpected aria-checked value: ${currentState}`);
    }
    console.log("toggleSmartIsolation completed.");
  }



  async toggleSmartIsolationToUnchecked(page: Page): Promise<void> {
    console.log("Executing toggleSmartIsolationToUnchecked...");
    await page.waitForTimeout(3000);

    const label = page.locator(this.fields.smartIsolateLabel);
    await expect(label).toContainText("Smart Isolate"); // Text is part of the locator
    console.log("Verified 'Smart Isolate' label.");
    await label.click();
    console.log("Clicked 'Smart Isolate' label.");

    await page.waitForTimeout(2000);

    const toggle = page.locator(this.fields.smartIsolationToggle);

    // Ensure the toggle element is present before proceeding
    await page.waitForSelector(this.fields.smartIsolationToggle);
    await page.waitForTimeout(1000);

    // Fetch the current value of the aria-checked attribute
    const currentState = await page.locator(this.fields.status).getAttribute('aria-checked');
    console.log("Current Smart Isolation toggle status: ", currentState);
    await page.waitForTimeout(5000);

    if (currentState === 'false') {
      // Click the toggle, verify it turns off, and toggle it back on
      console.log("The AI/ML based Smart Browser Isolation is already disabled.");
    } else if (currentState === 'true') {
      console.log("Toggle is true, attempting to click it to false.");
      // Click the toggle and verify it turns on
      await page.waitForTimeout(3000);
      // Using force:true as a precaution if other elements (like tooltips) intercept the click.
      // This is a workaround; ideally, the overlaying element should be handled or the click target refined.
      await page.locator(this.fields.smartIsolationToggle).click({ force: true }); 
      console.log("Clicked toggle (toggleSmartIsolationToUnchecked).");
      const currentStatus = await page.locator(this.fields.status).getAttribute('aria-checked');
      await expect(currentStatus).toContain("false");
      console.log("Verified toggle is now false.");
    } else {
      // Throw an error for any unexpected attribute value
      console.error(`Unexpected aria-checked value for Smart Isolation toggle: ${currentState}`);
      throw new Error(`Unexpected aria-checked value: ${currentState}`);
    }
    console.log("toggleSmartIsolationToUnchecked completed.");
  }

  async cancelSmartIsolationToggle(page: Page): Promise<void> {
    console.log("Executing cancelSmartIsolationToggle...");
    await page.waitForTimeout(1500);

    const label = page.locator(this.fields.enableSmartAILabel);
    await expect(label).toContainText("Enable AI/ML based Smart"); // Text is part of the locator
    console.log("Verified 'Enable AI/ML based Smart' label.");
    await page.waitForTimeout(1500);

    await page.locator(this.fields.smartIsolationToggle).isVisible(); // Check if toggle part is visible
    console.log("Smart Isolation toggle component is visible.");
    await page.locator(this.fields.usersTab).isVisible();
    console.log("Users tab is visible.");
    await page.waitForTimeout(2000);

    await page.locator(this.fields.cancelButton).click();
    console.log("Clicked 'Cancel' button.");
    const notifyCancel = page.locator(this.fields.allChangesCanceledMessage);
    await expect(notifyCancel).toContainText("All changes have been canceled."); // Text is part of the locator
    console.log("Verified 'All changes have been canceled' message.");
    await page.waitForTimeout(3000);

    const currentState = await page.locator(this.fields.status).getAttribute('aria-checked');
    console.log("Current Smart Isolation toggle status after cancel: ", currentState);
    await page.waitForTimeout(2000);
    const currentStatus = await page.locator(this.fields.status).getAttribute('aria-checked');
    // After clicking Cancel, the toggle should revert to its state before any uncommitted changes.
    // If toggleSmartIsolationToUnchecked made it 'false' (from an original 'true'), Cancel should revert it to 'true'.
    await expect(currentStatus).toContain("true"); 
    console.log("Verified toggle is true after cancel (reverted pending change).");
    console.log("cancelSmartIsolationToggle completed.");
  }

  async searchUsers(page: Page, userEmail: string): Promise<void> {
    console.log(`Executing searchUsers with userEmail: ${userEmail}...`);
    // Navigate to Users tab
    await page.waitForTimeout(1000);
    await page.locator(this.fields.usersTab).click();
    console.log("Navigated to Users Tab.");

    // Open the user selection dropdown/modal
    await page.locator(this.fields.valueDropDown).click();
    console.log("Opened Users selection.");

    // Check if a user is already selected
    const confirmSelectedUser = page.locator(this.fields.selectedItems1);
    if (await confirmSelectedUser.isVisible()) {
      // If a user is already selected, clear the selection
      console.log("A user is already selected, clearing selection.");
      await page.locator(this.fields.clearSelectedItem).click();
      console.log("Cleared previously selected user.");
    }

    // Perform the search for the desired user
    const search = page.locator(this.fields.dropdownSearchInput);
    await page.waitForTimeout(1000);
    await search.click();
    console.log("Clicked on the search input box.");

    // await page.waitForTimeout(1000);
    // await search.fill(userEmail);
    // console.log(`Searching for user: ${userEmail}`);

    await page.waitForTimeout(2000);

    // Select the user from search results
    await page.locator('(//input[@type="checkbox"])[3]').click();
    console.log(`Selected searched user: ${userEmail}`);

    // Validate that the user has been selected
    const confirmSelection = page.locator(this.fields.selectedItems1);
    await expect(confirmSelection).toContainText('Selected Items ( 1 )');
    console.log(`Confirmed the selection of user: ${userEmail}`);
    await page.waitForTimeout(1000);

    // Clear user selection after validation
    await page.locator(this.fields.clearSelectedItem).click();
    console.log("Cleared the user selection.");
    await page.waitForTimeout(1000);

    // Confirm that the user selection has been cleared
    const confirmClearSelection = page.locator(this.fields.selectedItems0);
    await expect(confirmClearSelection).toContainText('Selected Items ( 0 )'); 
    console.log("Confirmed that no users are selected.");
    await page.waitForTimeout(1000);

    // Finalize by clicking the "Done" button
    await page.locator(this.fields.doneButton).click();
    console.log("Closed user selection modal.");
    console.log("searchUsers completed.");
  }

  async selectUsers(page: Page, userEmail: string): Promise<void> {
    console.log(`Executing selectUsers for user: ${userEmail}...`);
    // Navigate to Users tab
    await page.waitForTimeout(1000);
    await page.locator(this.fields.usersTab).click();
    console.log("Navigated to Users Tab.");

    // Open the user selection dropdown/modal
    console.log("Opening Users selection dropdown.");
    await page.locator(this.fields.valueDropDown).click();
    console.log("Opened Users selection.");

    // Check if a user is already selected and clear it if necessary
    const confirmSelectedUser = page.locator(this.fields.selectedItems1);
    if (await confirmSelectedUser.isVisible()) {
      // If a user is already selected, clear the selection
      await page.locator(this.fields.clearSelectedItem).click();
      console.log("Cleared previously selected user.");
    }

    // Continue with selecting the desired user by name
    const search = page.locator(this.fields.dropdownSearchInput);
    await page.waitForTimeout(1000);

    await search.click();
    console.log("Clicked on search box.");

    await page.waitForTimeout(1000);

    // await search.fill(userEmail);
    // console.log(`Searched for user: ${userEmail}.`);

    await page.waitForTimeout(2000);

    await page.locator('(//input[@type="checkbox"])[3]').click();
    // console.log(`Selected the user: ${userEmail}.`);

    // Validate that the user has been selected
    const confirmSelectedUserAgain = page.locator(this.fields.selectedItems1);
    await expect(confirmSelectedUserAgain).toContainText('Selected Items ( 1 )'); // Text is part of the locator
    console.log(`Confirmed user selection: ${userEmail}.`);

    await page.waitForTimeout(1000);

    // Confirm selection by clicking 'Done'
    await page.locator(this.fields.doneButton).click();
    console.log("Clicked 'Done' to confirm user selection.");

    // Save the selection
    await page.waitForTimeout(1000);
    await page.locator(this.fields.saveButton).click();
    console.log("Clicked 'Save' button.");
    await page.waitForTimeout(2000);
    const saveMessage = page.locator(this.fields.allChangesSavedMessage);
    await expect(saveMessage).toBeVisible();
    console.log("Changes saved successfully.");

    console.log("Clearing selected user to reset state.");
    // Clear user selection to reset state
    await page.locator(this.fields.valueDropDown).click();
    await page.locator(this.fields.clearSelectedItem).click();
    console.log("Cleared current user selection.");
    await page.waitForTimeout(1000);

    // Confirm clearing of the selection
    const confirmClearSelection = page.locator(this.fields.selectedItems0);
    await expect(confirmClearSelection).toContainText('Selected Items ( 0 )'); // Text is part of the locator
    console.log("Confirmed that no users are selected.");
    await page.waitForTimeout(1000);

    // Finalize by clicking the "Done" button after clearing
    await page.locator(this.fields.doneButton).click();
    console.log("Clicked 'Done' after clearing selection.");
    const confirmUnSelectedUser = await page.locator(this.fields.valueDropDown);
    await expect(confirmUnSelectedUser).toContainText("---");
    console.log("User selection was cleared and confirmed.");
    console.log("selectUsers completed.");
  }

  async selectGroups(page: Page, groupName: string): Promise<void> {
    console.log(`Executing selectGroups for group: ${groupName}...`);
    // Navigate to Groups tab
    await page.waitForTimeout(1000); // Optionally adjust timeout for loading
    await page.locator(this.fields.groupTab).click();
    console.log("Navigated to Groups Tab.");

    // Open the group selection modal/pop-up
    console.log("Opening Groups selection dropdown.");
    await page.locator(this.fields.groupDropDown).click();
    console.log("Opened Groups selection.");

    // Check if a group is already selected and clear it if necessary
    const confirmSelectedUser = page.locator(this.fields.selectedItems1);
    if (await confirmSelectedUser.isVisible()) {
      // If a group is already selected, clear the selection
      await page.locator(this.fields.clearSelectedItem).click();
      console.log("Cleared previously selected group.");
    }

    // Continue with selecting the desired group by name
    const search = page.locator(this.fields.dropdownSearchInput);
    await page.waitForTimeout(1000);

    await search.click();
    console.log("Clicked on search box.");

    await page.waitForTimeout(1000);

    // await search.fill(groupName);
    // console.log(`Searched for group: ${groupName}.`);

    await page.waitForTimeout(2000);

    await page.locator('(//input[@type="checkbox"])[3]').click();
    // console.log(`Selected the group: ${groupName}.`);

    // Validate that the group has been selected
    const confirmSelectedGroup = page.locator(this.fields.selectedItems1);
    await expect(confirmSelectedGroup).toContainText('Selected Items ( 1 )'); // Text is part of the locator
    console.log(`Confirmed group selection: ${groupName}.`);

    await page.waitForTimeout(1000);

    // Confirm selection by clicking 'Done'
    await page.locator(this.fields.doneButton).click();
    console.log("Clicked 'Done' to confirm group selection.");

    // Save the selection
    await page.waitForTimeout(1000);
    await page.locator(this.fields.saveButton).click();
    console.log("Clicked 'Save' button.");
    await page.waitForTimeout(2000);
    const saveMessage = page.locator(this.fields.allChangesSavedMessage);
    await expect(saveMessage).toBeVisible();
    console.log("Changes saved successfully.");

    console.log("Clearing selected group to reset state.");
    // Clear group selection to reset state
    await page.locator(this.fields.groupDropDown).click();
    await page.locator(this.fields.clearSelectedItem).click();
    console.log("Cleared current group selection.");
    await page.waitForTimeout(1000);

    // Confirm clearing of the selection
    const confirmClearSelection = page.locator(this.fields.selectedItems0);
    await expect(confirmClearSelection).toContainText('Selected Items ( 0 )'); // Text is part of the locator
    console.log("Confirmed that no groups are selected.");
    await page.waitForTimeout(1000);

    // Finalize by clicking the "Done" button after clearing
    await page.locator(this.fields.doneButton).click();
    console.log("Clicked 'Done' after clearing selection.");
    const confirmUnSelectedGroup = await page.locator(this.fields.groupDropDown);
    await expect(confirmUnSelectedGroup).toContainText("---");
    console.log("Group selection was cleared and confirmed.");
    console.log("selectGroups completed.");
  }

  async searchGroups(page: Page, groupName: string): Promise<void> {
    console.log(`Executing searchGroups with groupName: ${groupName}...`);
    // Navigate to Groups tab
    await page.waitForTimeout(1000); // Optionally adjust timeout for loading
    await page.locator(this.fields.groupTab).click();
    console.log("Navigated to Groups Tab.");

    await page.waitForTimeout(2000);
    // Open the group selection modal/pop-up
    console.log("Opening Groups selection dropdown.");
    await page.locator(this.fields.groupDropDown).click();
    console.log("Opened Groups selection.");

    await page.waitForTimeout(2000);
    // Check if a group is already selected
    const confirmSelectedGroup = page.locator(this.fields.selectedItems1);
    if (await confirmSelectedGroup.isVisible()) {
      // If a group is already selected, clear the selection
      console.log("A group is already selected, clearing selection.");
      await page.locator(this.fields.clearSelectedItem).click();
      console.log("Cleared previously selected group.");
    }

    await page.waitForTimeout(2000);
    // Perform the search for the desired group
    const search = page.locator(this.fields.dropdownSearchInput);
    await page.waitForTimeout(1000);
    await search.click();
    console.log("Clicked on the search input box.");

    // await page.waitForTimeout(1000);
    // await search.fill(groupName);
    // console.log(`Searching for group: ${groupName}`);

    await page.waitForTimeout(2000);
    // Select the group from search results
    await page.locator('(//input[@type="checkbox"])[3]').click();
    // console.log(`Selected searched group: ${groupName}`);

    await page.waitForTimeout(2000);
    // Validate that the group has been selected
    const confirmSelection = page.locator(this.fields.selectedItems1);
    await expect(confirmSelection).toContainText('Selected Items ( 1 )'); // Text is part of the locator
    console.log(`Confirmed the selection of group: ${groupName}`);
    await page.waitForTimeout(1000);

    // Clear group selection after validation
    await page.locator(this.fields.clearSelectedItem).click();
    console.log("Cleared the group selection.");
    await page.waitForTimeout(2000);

    // Confirm that the group selection has been cleared
    const confirmClearSelection = page.locator(this.fields.selectedItems0);
    await expect(confirmClearSelection).toContainText('Selected Items ( 0 )'); // Text is part of the locator
    console.log("Confirmed that no groups are selected.");
    await page.waitForTimeout(1000);

    // Finalize by clicking the "Done" button
    await page.locator(this.fields.doneButton).click();
    console.log("Closed group selection modal.");
    console.log("searchGroups completed.");
  }

  async verifyDefaultProfile(page: Page): Promise<void> {
    console.log("Executing verifyDefaultProfile...");
    await page.waitForTimeout(1000);
    const label = page.locator(this.fields.browserIsolationProfileLabel);
    await expect(label).toContainText("Browser Isolation Profile"); // Text is part of the locator
    console.log("Verified 'Browser Isolation Profile' label.");
    await page.waitForTimeout(1000);
    await page.locator(this.fields.profileDropdownUndefined).click();
    console.log("Clicked profile dropdown.");
    await page.waitForTimeout(1000);
    await page.locator(this.fields.defaultIsolationProfileListItem).click();
    console.log("Selected 'Default Isolation Profile'.");
    const value = page.locator(this.fields.profileDropdownUndefined);
    await expect(value).toContainText("Default Isolation Profile"); // Text is part of the locator
    console.log("Verified dropdown shows 'Default Isolation Profile'.");
    // Save the selection
    await page.waitForTimeout(1000);
    await page.locator(this.fields.saveButton).click();
    console.log("Clicked 'Save' button.");
    await page.waitForTimeout(2000);
    const saveMessage = page.locator(this.fields.allChangesSavedMessage);
    await expect(saveMessage).toBeVisible();
    console.log("Changes saved successfully.");
    await page.waitForTimeout(5000);

    console.log("Verify the saved value for Browser Isolation Profile field");
    const value1 = page.locator(this.fields.profileDropdownUndefined);
    await expect(value1).toContainText("Default Isolation Profile"); // Text is part of the locator
    console.log("Verified saved profile is 'Default Isolation Profile'.");
    console.log("verifyDefaultProfile completed.");
  }

  async toggleEnableWarnings(page: Page): Promise<void> {
    console.log("Executing toggleEnableWarnings...");
    await page.waitForTimeout(2000);
    // Ensure the "Browser Control" tab is visible and clickable before interacting
    const BrowserControlTab = page.locator(this.fields.browserControl);
    await expect(BrowserControlTab).toBeVisible({ timeout: 15000 }); // Increased timeout
    await expect(BrowserControlTab).toContainText("Browser Control"); // Text is part of the locator
    console.log("Verified 'Browser Control' tab.");
    if (!await BrowserControlTab.evaluate(node => node.classList.contains('active'))) { // Click only if not already active
      await BrowserControlTab.click();
    }
    console.log("Clicked 'Browser Control' tab.");

    await page.waitForTimeout(2000);
    const label1 = page.locator(this.fields.browserVulnerabilityProtectionLabel);
    await expect(label1).toContainText("Browser Vulnerability Protection"); // Text is part of the locator
    console.log("Verified 'Browser Vulnerability Protection' label.");
    await page.waitForTimeout(2000);
    const label2 = page.locator(this.fields.enableChecksUserNotificationLabel);
    await expect(label2).toContainText("Enable Checks & User Notification"); // Text is part of the locator
    console.log("Verified 'Enable Checks & User Notification' label.");
    await page.waitForTimeout(2000);
    const toggle = page.locator(`(//span[@data-testid="${this.fields.warningsToggleTestId}"]//span)[2]`);

    // Ensure the toggle element is present before proceeding
    await page.waitForTimeout(2000);
    await page.waitForSelector(`(//span[@data-testid="${this.fields.warningsToggleTestId}"]//span)[2]`);
    await page.waitForTimeout(1000);

    // Fetch the current value of the aria-checked attribute
    await page.waitForTimeout(2000);
    const currentState = await page.locator(this.fields.enableCheckUserNotificationToggle).getAttribute('aria-checked');
    console.log("Current 'Enable Checks & User Notification' toggle status: ", currentState);
    await page.waitForTimeout(5000);

    if (currentState === 'false') {
      console.log("Toggle is false, attempting to click it to true.");
      // Click the toggle and verify it turns on
      await page.waitForTimeout(1000);
      await toggle.click();
      console.log("Clicked toggle.");
      const currentStatus = await page.locator(this.fields.enableCheckUserNotificationToggle).getAttribute('aria-checked');
      await expect(currentStatus).toContain("true");
      console.log("Verified toggle is now true.");
    } else if (currentState === 'true') {
      // Click the toggle, verify it turns off, and toggle it back on
      console.log("The 'Enable Checks & User Notification' toggle is already enabled.");
    } else {
      // Throw an error for any unexpected attribute value
      console.error(`Unexpected aria-checked value for 'Enable Checks & User Notification' toggle: ${currentState}`);
      throw new Error(`Unexpected aria-checked value: ${currentState}`);
    }

    await page.waitForTimeout(2000);
    const verify = page.locator(this.fields.howOftenToCheckLabel);
    await expect(verify).toContainText("How Often to Check"); // Text is part of the locator
    console.log("Verified 'How Often to Check' label is visible.");
    console.log("toggleEnableWarnings completed.");
  }

  async selectCheckFrequency(page: Page): Promise<void> {
    console.log("Executing selectCheckFrequency...");
    await page.waitForTimeout(1000);
    const verify = page.locator(this.fields.howOftenToCheckLabel);
    await expect(verify).toContainText("How Often to Check"); // Text is part of the locator
    console.log("Verified 'How Often to Check' label.");
    await page.locator(this.fields.howOftenToCheck).click();
    console.log("Clicked 'How Often to Check' dropdown.");
    await page.waitForTimeout(2000);

    const dailyValue = page.locator(this.fields.dailyFrequencyListItem);
    await expect(dailyValue).toContainText("Daily"); // Text is part of the locator
    console.log("Verified 'Daily' option is present.");
    await page.waitForTimeout(2000);

    const monthlyValue = page.locator(this.fields.monthlyFrequencyListItem);
    await expect(monthlyValue).toContainText("Monthly"); // Text is part of the locator
    console.log("Verified 'Monthly' option is present.");
    await page.waitForTimeout(2000);

    const weeklyValue = page.locator(this.fields.weeklyFrequencyListItem);
    await expect(weeklyValue).toContainText("Weekly"); // Text is part of the locator
    console.log("Verified 'Weekly' option is present.");
    await page.waitForTimeout(2000);

    // Verify the updated value for Daily in drop-down
    await page.waitForTimeout(2000);
    await dailyValue.click();
    console.log("Selected 'Daily'.");
    const currentValue = await page.locator(this.fields.howOftenToCheck);
    await expect(currentValue).toContainText("Daily"); // Text is part of the locator
    console.log("Verified dropdown shows 'Daily'.");

    // Verify the updated value for Monthly in drop-down
    await page.waitForTimeout(2000);
    await page.locator(this.fields.howOftenToCheck).click(); // Re-open dropdown
    console.log("Re-clicked 'How Often to Check' dropdown to select Monthly.");
    await monthlyValue.click(); // Corrected to click monthlyValue as per apparent intent
    console.log("Selected 'Monthly'.");
    const currentValue1 = await page.locator(this.fields.howOftenToCheck);
    await expect(currentValue1).toContainText("Monthly"); // Text is part of the locator
    console.log("Verified dropdown shows 'Monthly'.");
    console.log("selectCheckFrequency completed.");
  }

  async toggleDisablePlugins(page: Page, pluginName: string): Promise<void> {
    console.log(`Executing toggleDisablePlugins for plugin: ${pluginName}...`);
    await page.waitForTimeout(2000);
    const disableForPlugin = page.locator(this.fields.disableNotificationForPluginsLabel);
    await expect(disableForPlugin).toContainText("Disable Notification for Plugins"); // Text is part of the locator
    console.log("Verified 'Disable Notification for Plugins' label.");

    await page.waitForTimeout(2000);
    await page.locator(this.fields.pluginsDropdown).click();
    console.log("Clicked plugins dropdown.");

    // Check if a user is already selected and clear it if necessary
    const confirmSelectedUser = page.locator(this.fields.selectedItems1);
    if (await confirmSelectedUser.isVisible()) {
      // If a user is already selected, clear the selection
      console.log("A plugin is already selected, clearing selection.");
      await page.locator(this.fields.clearSelectedItem).click();
      console.log("Cleared previously selected user.");
    }

    // Continue with selecting the desired user by name
    const search = page.locator(this.fields.dropdownSearchInput);
    await page.waitForTimeout(1000);

    await search.click();
    console.log("Clicked on search box.");

    await page.waitForTimeout(1000);

    await search.fill(pluginName);
    console.log(`Searched for Plugin: ${pluginName}`);

    await page.waitForTimeout(2000);

    await page.locator(`//label/div[contains(text(), "${pluginName}")]`).click();
    console.log(`Selected the plugin: ${pluginName}`);

    // Validate that the user has been selected
    const confirmSelectedUserAgain = page.locator(this.fields.selectedItems1);
    await expect(confirmSelectedUserAgain).toContainText('Selected Items ( 1 )'); // Text is part of the locator
    console.log(`Confirmed plugin selection: ${pluginName}`);

    await page.waitForTimeout(1000);

    // Confirm selection by clicking 'Done'
    await page.locator(this.fields.doneButton).click();
    console.log("Clicked 'Done' to confirm plugin selection.");

    // Save the selection
    await page.waitForTimeout(1000);
    await page.locator(this.fields.saveButton).click();
    console.log("Clicked 'Save' button.");
    await page.waitForTimeout(2000);
    const saveMessage = page.locator(this.fields.allChangesSavedMessage);
    await expect(saveMessage).toBeVisible();
    console.log("Changes saved successfully.");

    console.log("Clearing selected plugin to reset state.");
    // Clear user selection to reset state
    await page.locator(this.fields.valueDropDown).click();
    await page.waitForTimeout(1000);

    await page.locator('//span[contains(text(),"Clear Selection")]').click();
    await page.waitForTimeout(1000);

    // Confirm clearing of the selection
    const confirmClearSelection = page.locator(this.fields.selectedItems0);
    await expect(confirmClearSelection).toContainText('Selected Items ( 0 )');
    console.log("Confirmed that no plugin are selected.");
    await page.waitForTimeout(1000);

    // Finalize by clicking the "Done" button after clearing
    await page.locator(this.fields.doneButton).click();
    console.log("Clicked 'Done' after clearing selection.");
  }

  async searchPlugins(page: Page, pluginName: string): Promise<void> {
    console.log(`Executing searchPlugins with pluginName: ${pluginName}...`);
    await page.waitForTimeout(2000);
    const disableForPlugin = page.locator(this.fields.disableNotificationForPluginsLabel);
    await expect(disableForPlugin).toContainText("Disable Notification for Plugins"); // Text is part of the locator
    console.log("Verified 'Disable Notification for Plugins' label.");

    await page.waitForTimeout(2000);
    await page.locator(this.fields.pluginsDropdown).click();
    console.log("Clicked plugins dropdown.");

    // Check if a user is already selected and clear it if necessary
    await page.waitForTimeout(2000);
    const confirmSelectedUser = page.locator(this.fields.selectedItems1);
    if (await confirmSelectedUser.isVisible()) {
      // If a user is already selected, clear the selection
      await page.locator(this.fields.clearSelectedItem).click();
      console.log("Cleared previously selected user.");
    }

    // Continue with selecting the desired user by name
    const search = page.locator(this.fields.dropdownSearchInput);
    await page.waitForTimeout(1000);

    await page.waitForTimeout(2000);
    await search.click();
    console.log("Clicked on search box.");

    await page.waitForTimeout(1000);
    await search.fill(pluginName); 
    console.log(`Searched for Plugins: ${pluginName}`);

    await page.waitForTimeout(2000);
    await page.locator(`//label/div[contains(text(), "${pluginName}")]`).click();
    console.log(`Selected the plugin: ${pluginName}`);

    // Validate that the user has been selected
    await page.waitForTimeout(2000);
    const confirmSelectedUserAgain = page.locator(this.fields.selectedItems1);
    await expect(confirmSelectedUserAgain).toContainText('Selected Items ( 1 )'); // Text is part of the locator
    console.log(`Confirmed plugin selection: ${pluginName}`);

    await page.waitForTimeout(1000);

    await page.locator(this.fields.clearSelectedItem).click();
    console.log("Cleared current user selection.");
    await page.waitForTimeout(1000);

    // Confirm clearing of the selection
    await page.waitForTimeout(2000);
    const confirmClearSelection = page.locator(this.fields.selectedItems0);
    await expect(confirmClearSelection).toContainText('Selected Items ( 0 )'); // Text is part of the locator
    console.log("Confirmed that no plugin are selected.");
    console.log("searchPlugins completed.");
  }

  async searchApplications(page: Page, appName: string): Promise<void> {
    console.log(`Executing searchApplications with appName: ${appName}...`);
    // Wait for the page to stabilize
    await page.waitForTimeout(2000);
    
    // Open the dropdown for applications
    await page.waitForTimeout(2000);
    await page.locator(this.fields.applicationsDropdown).click();
    console.log('Clicked on the dropdown for applications.');

    // Check if an application is already selected and clear it if necessary
    await page.waitForTimeout(2000);
    const confirmSelectedApp = page.locator(this.fields.selectedItems1);
    if (await confirmSelectedApp.isVisible()) {
      console.log("An application is already selected, clearing selection.");
      await page.waitForTimeout(2000);
      const clearButton = page.locator(this.fields.clearSelectedItemsButton);
      await page.waitForTimeout(2000);
      await clearButton.click();
      console.log("Cleared previously selected application.");
    }

    // Search for the desired application by name
    await page.waitForTimeout(2000);
    const searchBox = page.locator(this.fields.dropdownSearchInput);
    await searchBox.click();
    console.log("Clicked on the search box for applications.");

    // Fill the application name and wait for results to load
    await page.waitForTimeout(2000);
    await searchBox.fill(appName);
    console.log(`Searched for application: ${appName}`);
    await page.waitForTimeout(2000);

    // Select the application from the list
    await page.waitForTimeout(2000);
    await page.locator(`//label/div[contains(text(), "${appName}")]`).click();
    console.log(`Selected the application: ${appName}.`);

    // Confirm the selection
    await page.waitForTimeout(2000);
    const confirmSelectedAppAgain = page.locator(this.fields.selectedItems1);
    await expect(confirmSelectedAppAgain).toContainText('Selected Items ( 1 )');
    console.log(`Confirmation: Application "${appName}" is selected.`);

    // Click 'Done' to confirm and save the selection
    await page.waitForTimeout(2000);
    await page.locator(this.fields.doneButton).click();
    console.log("Clicked 'Done' to confirm application selection.");

    // Save the changes
    await page.locator(this.fields.saveButton).click();
    console.log("Clicked 'Save' button.")
    await page.waitForTimeout(3000);
    const saveMessage = page.locator(this.fields.allChangesSavedMessage);
    await expect(saveMessage).toBeVisible();
    console.log("Changes saved successfully.");
  }

  async toggleDisableApplications(page: Page): Promise<void> {
    console.log("Executing toggleDisableApplications...");
    // Locate and validate the "Disable Notification for Applications" label
    await page.waitForTimeout(2000);
    const label = page.locator(this.fields.disableNotificationForApplicationsLabel);
    await expect(label).toContainText('Disable Notification for Applications'); 
    console.log('Found "Disable Notification for Applications" label.');

    // Open the dropdown for applications
    await page.waitForTimeout(2000);
    await page.locator(this.fields.applicationsDropdown).click();
    console.log('Clicked on the dropdown for applications.');

    // Check if an application is already selected
    await page.waitForTimeout(2000);
    const confirmSelectedApp = page.locator(this.fields.selectedItems1);
    if (await confirmSelectedApp.isVisible()) {
      // Clear the currently selected application
      console.log("An application is already selected, clearing selection.");
      const clearButton = page.locator(this.fields.clearSelectedItemsButton);
      await expect(clearButton).toBeVisible({ timeout: 10000 });
      await expect(clearButton).toBeEnabled({ timeout: 10000 });
      await clearButton.click();
      console.log("Cleared currently selected application.");
      await page.waitForTimeout(2000);

      // Confirm clearing of selection
      await page.waitForTimeout(2000);
      const confirmClearSelection = page.locator(this.fields.selectedItems0);
      await expect(confirmClearSelection).toContainText('Selected Items ( 0 )'); 
      console.log("Confirmed that no application is selected.");
    } else {
      console.log("No application was initially selected.");
    }

    // Finalize by clicking the "Done" button
    await page.locator(this.fields.doneButton).click();
    console.log("Clicked 'Done' after clearing selection.");
    console.log("toggleDisableApplications completed.");
  }

  async toggleAllowAll(page: Page): Promise<void> {
    // Locate and validate the "Browser Blocking" label
    const label = page.locator('//span[text()="Browser Blocking"]');
    await expect(label).toContainText('Browser Blocking');
    console.log('Found and validated the "Browser Blocking" label.');

    // Click on the "Browser Blocking" to expand the section
    await label.click();
    console.log('Clicked on the "Browser Blocking" label.');

    // Ensure the toggle element is present before proceeding
    const toggle = page.locator(this.fields.allowAllBrowsersToggleStatus.replace('//div', '//span[2]')); // Assuming span[2] is the clickable part
    await page.waitForSelector(this.fields.allowAllBrowsersToggleStatus.replace('//div', '//span[2]'));
    console.log("Located the toggle switch for 'Allow All Browsers'.");

    // Check the current state of the toggle using the aria-checked attribute
    const currentState = await page.locator(this.fields.allowAllBrowsersToggleStatus).getAttribute('aria-checked');
    console.log('Current State of "Allow All Browsers" toggle: ', currentState);
    await page.waitForTimeout(2000);

    if (currentState === 'false') {
      // Click to enable the toggle if it is currently disabled
      await toggle.click();
      console.log("Clicked to enable 'Allow All Browsers' toggle.");

      // Verify the toggle is enabled
      await page.waitForTimeout(2000);
      const verifyStateAfterToggle = await page.locator(this.fields.allowAllBrowsersToggleStatus).getAttribute('aria-checked');
      await expect(verifyStateAfterToggle).toContain('true');
      console.log('Verified that "Allow All Browsers" toggle is now enabled.');

      // Ensure the "Microsoft Browsers" element is NOT visible (it should be hidden)
      await page.waitForTimeout(2000);
      const microsoftBrowsers = page.locator(this.fields.microsoftBrowsersLabel);
      const isVisible = await microsoftBrowsers.isVisible();
      if (isVisible) {
        throw new Error('Microsoft Browsers section is still visible after enabling "Allow All Browsers".');
      }
      console.log('Verified that "Microsoft Browsers" is not visible when toggle is enabled.');
    } else if (currentState === 'true') {
      // If the toggle is already enabled
      console.log('The "Allow All Browsers" toggle is already enabled.');

      // Verify the "Microsoft Browsers" section is not visible
      await page.waitForTimeout(2000);
      const microsoftBrowsers = page.locator(this.fields.microsoftBrowsersLabel);
      const isVisible = await microsoftBrowsers.isVisible();
      if (isVisible) {
        throw new Error('Microsoft Browsers section is still visible, but "Allow All Browsers" toggle is already enabled.');
      }
      console.log('Verified that "Microsoft Browsers" section is not visible (as expected).');
    } else {
      // Handle unexpected aria-checked attribute values
      throw new Error(`Unexpected value for 'aria-checked': ${currentState}`);
    }
    console.log("toggleAllowAll completed.");
  }

  async manageBrowserVersionsSearch(page: Page, browserName: string, versionPattern: string): Promise<void> {
    console.log(`Executing manageBrowserVersionsSearch for Browser: ${browserName}, Version: ${versionPattern}...`);
    await page.waitForTimeout(2000);

    // Locate and validate the "Browser Blocking" label
    const label = page.locator(this.fields.browserBlockingLabel);
    await expect(label).toContainText('Browser Blocking'); // Text is part of the locator
    console.log('Found and validated the "Browser Blocking" label.');

    // Click on the "Browser Blocking" to expand the section
    await page.waitForTimeout(2000);
    await label.click();
    console.log('Clicked on the "Browser Blocking" label.');
    // Ensure the toggle element is present before proceeding
    await page.waitForTimeout(2000);
    const toggle = page.locator(this.fields.allowAllBrowsersToggleStatus.replace('//div', '//span[2]'));
    await page.waitForSelector(this.fields.allowAllBrowsersToggleStatus.replace('//div', '//span[2]'));
    console.log("Located the toggle switch for 'Allow All Browsers'.");

    // Check the current state of the toggle using the aria-checked attribute
    await page.waitForTimeout(2000);
    const currentState = await page.locator(this.fields.allowAllBrowsersToggleStatus).getAttribute('aria-checked');
    console.log('Current State of "Allow All Browsers" toggle: ', currentState);
    await page.waitForTimeout(2000);

    if (currentState === 'true') {
      console.log("Toggle is true, attempting to click it to false.");
      await page.waitForTimeout(2000);
      await toggle.click({ force: true }); // Using force: true as a precaution
      console.log("Clicked to disable 'Allow All Browsers' toggle.");

      // Verify the toggle is now disabled
      // Use expect().toHaveAttribute() for more reliable waiting on attribute change
      await expect(page.locator(this.fields.allowAllBrowsersToggleStatus)).toHaveAttribute('aria-checked', 'false', { timeout: 5000 });
      const finalState = await page.locator(this.fields.allowAllBrowsersToggleStatus).getAttribute('aria-checked');
      console.log(`Verified that "Allow All Browsers" toggle is now disabled. Current state: ${finalState}`);
    } else if (currentState === 'false') {
      console.log('The "Allow All Browsers" toggle is already disabled, proceeding to browser selection.');
    } else {
      // Handle unexpected aria-checked attribute values
      throw new Error(`Unexpected value for 'aria-checked': ${currentState}`);
    }
    // After ensuring "Allow All Browsers" is OFF, the specific browser sections should be visible.
    // Add a small wait for UI to update if necessary.
    await page.waitForTimeout(1000); 

    await page.waitForTimeout(2000);
    const label1 = await page.locator(`//span[text()='${browserName}']`);
    await expect(label1).toBeVisible({ timeout: 10000 }); // Ensure the browser section label is visible
    await expect(label1).toContainText(browserName);
    console.log(`Verified '${browserName}' label.`);

    await page.waitForTimeout(2000);
    const browser = await page.locator(`(//span[contains(normalize-space(text()),'${browserName}')]//following::div[@data-testid="multi-dropdown"][1])[1]`);
    await browser.isVisible();
    await browser.click();
    console.log(`Clicked dropdown for ${browserName}.`);

    await page.locator(this.fields.clearSelectedItem).click();
    console.log("Cleared previously selected item.");
    
    // Perform the search for the desired browser version
    const search = page.locator(this.fields.dropdownSearchInput);
    await page.waitForTimeout(1000);
    await search.click();
    console.log("Clicked on the search input box.");

    await page.waitForTimeout(1000);
    await search.fill(versionPattern);
    console.log(`Searching for browser version: ${versionPattern}`);

    await page.waitForTimeout(2000);

    // Select the browser version from search results
    await page.waitForTimeout(2000);
    await page.locator(`//label/div[contains(text(), "${versionPattern}")]`).click();
    console.log(`Selected searched browser version: ${versionPattern}`);

    // Validate that the browser version has been selected
    await page.waitForTimeout(2000);
    const confirmSelection = page.locator(this.fields.selectedItems1);
    await expect(confirmSelection).toContainText("Selected Items ( 1 )"); 
    console.log(`Confirmed the selection of browser version: ${versionPattern}`);
    await page.waitForTimeout(1000);

    // Clear browser version selection after validation
    await page.waitForTimeout(2000);
    await page.locator(this.fields.clearSelectedItem).click();
    console.log("Cleared the browser version selection.");
    await page.waitForTimeout(1000);

    // Confirm that the browser version selection has been cleared
    await page.waitForTimeout(2000);
    const confirmClearSelection = page.locator(this.fields.selectedItems0);
    await expect(confirmClearSelection).toContainText('Selected Items ( 0 )'); // Text is part of the locator
    console.log("Confirmed that no users are selected.");
    await page.waitForTimeout(1000);

    // Finalize by clicking the "Done" button
    await page.locator(this.fields.doneButton).click();
    console.log("Closed browser version selection modal.");
    console.log(`manageBrowserVersionsSearch for ${browserName} completed.`);
  }



  async manageBrowserVersionsSelect(page: Page, browserName: string, versionPattern: string): Promise<void> {
    console.log(`Executing manageBrowserVersionsSelect for Browser: ${browserName}, Version: ${versionPattern}...`);
    // Locate and validate the "Browser Blocking" label
    await page.waitForTimeout(2000);
    const label = page.locator(this.fields.browserBlockingLabel);
    await expect(label).toContainText('Browser Blocking'); 
    console.log('Found and validated the "Browser Blocking" label.');

    // Click on the "Browser Blocking" to expand the section
    await page.waitForTimeout(2000);
    await label.click();
    console.log('Clicked on the "Browser Blocking" label.');
    // Ensure the toggle element is present before proceeding
    await page.waitForTimeout(2000);
    const toggle = page.locator(this.fields.allowAllBrowsersToggleStatus.replace('//div', '//span[2]'));
    await page.waitForSelector(this.fields.allowAllBrowsersToggleStatus.replace('//div', '//span[2]'));
    console.log("Located the toggle switch for 'Allow All Browsers'.");

    // Check the current state of the toggle using the aria-checked attribute
    await page.waitForTimeout(2000);
    const currentState = await page.locator(this.fields.allowAllBrowsersToggleStatus).getAttribute('aria-checked');
    console.log('Current State of "Allow All Browsers" toggle: ', currentState);
    await page.waitForTimeout(2000);

    if (currentState === 'true') {
      console.log("Toggle is true, attempting to click it to false.");
      await page.waitForTimeout(2000);
      await toggle.click({ force: true });
      console.log("Clicked to disable 'Allow All Browsers' toggle.");

      // Verify the toggle is now disabled
      await page.waitForTimeout(2000);
      // Use expect().toHaveAttribute() for more reliable waiting on attribute change
      await expect(page.locator(this.fields.allowAllBrowsersToggleStatus)).toHaveAttribute('aria-checked', 'false', { timeout: 5000 });
      const finalState = await page.locator(this.fields.allowAllBrowsersToggleStatus).getAttribute('aria-checked');
      console.log(`Verified that "Allow All Browsers" toggle is now disabled. Current state: ${finalState}`);
    } else if (currentState === 'false') {
      console.log('The "Allow All Browsers" toggle is already disabled, proceeding to browser selection.');
    } else {
      // Handle unexpected aria-checked attribute values
      throw new Error(`Unexpected value for 'aria-checked': ${currentState}`);
    }
    // After ensuring "Allow All Browsers" is OFF, the specific browser sections should be visible.
    // Add a small wait for UI to update if necessary.
    await page.waitForTimeout(1000);

    await page.waitForTimeout(2000);
    const label1 = await page.locator(`//span[text()='${browserName}']`);
    await expect(label1).toBeVisible({ timeout: 10000 });
    await expect(label1).toContainText(browserName);
    console.log(`Verified '${browserName}' label.`);

    await page.waitForTimeout(2000);
    const browser = await page.locator(`(//span[contains(normalize-space(text()),'${browserName}')]//following::div[@data-testid="multi-dropdown"][1])[1]`);
    await browser.isVisible();
    await browser.click();
    console.log(`Clicked dropdown for ${browserName}.`);

    await page.locator(this.fields.clearSelectedItem).click();
    console.log("Cleared previously selected item.");
  
    // Perform the search for the desired browser version
    await page.waitForTimeout(2000);
    const search = page.locator(this.fields.dropdownSearchInput);
    await page.waitForTimeout(1000);
    await search.click();
    console.log("Clicked on the search input box.");

    await page.waitForTimeout(1000);
    await search.fill(versionPattern);
    console.log(`Searching for browser version: ${versionPattern}`);

    await page.waitForTimeout(2000);

    // Select the browser version from search results
    await page.waitForTimeout(5000);
    await page.locator(`//label/div[contains(text(), "${versionPattern}")]`).click();
    console.log(`Selected searched browser version: ${versionPattern}`);

    // Validate that the browser version has been selected
    await page.waitForTimeout(2000);
    const confirmSelection = page.locator(this.fields.selectedItems1);
    await expect(confirmSelection).toContainText('Selected Items ( 1 )'); 
    console.log(`Confirmed the selection of browser version: ${versionPattern}`);
    await page.waitForTimeout(1000);

    // Confirm selection by clicking 'Done'
    await page.waitForTimeout(2000);
    await page.locator(this.fields.doneButton).click();
    console.log("Clicked 'Done' to confirm group selection.");

    // Save the selection
    await page.waitForTimeout(1000);
    await page.locator(this.fields.saveButton).click();
    console.log("Clicked 'Save' button.");
    await page.waitForTimeout(2000);
    const saveMessage = page.locator(this.fields.allChangesSavedMessage);
    await expect(saveMessage).toBeVisible();
    console.log("Changes saved successfully.");
  }
}

export default new SecureBrowserPage();
