import { Page, expect, Locator, Download } from "@playwright/test";
import { config } from "dotenv";

config();

interface VpnCredentialsLocators {
    // Main page
    pageTitle: string;
    addCredentialButton: string; // From clickAddCredentialButton
    addCredentialModalTitle: string; // From clickAddCredentialButton
    // User ID input
    userIdLabel: string; // From enterUserId
    userIdInput: string; // From enterUserId
    // Address Type
    fqdnToggleButton: string; // From verifyAndSelectAddressType
    ipToggleButton: string; // From verifyAndSelectAddressType
    // Password
    newPreSharedKeyLabel: string; // From enterPassword
    passwordInput1: string; // From enterPassword (first password field)
    confirmNewPreSharedKeyLabel: string; // From enterConfirmPassword
    passwordInput2: string; // From enterConfirmPassword (second password field)
    // Save
    saveButton: string; // From clickSaveButton
    allChangesSavedMessage: string; // From clickSaveButton
    // Search/Filter
    filterInput: string; // From seachRecord & ensureCredentialDoesNotExist
    applyFilterButton: string; // From seachRecord & ensureCredentialDoesNotExist
    // Edit
    editIcon: string; // From clickEditCredential (generic edit icon)
    editVpnCredentialModalTitle: string; // From clickEditCredential
    // Delete
    deleteButtonInForm: string; // From clickDeleteButtonInEditForm
    confirmButtonInModalFooter: string; // From clickDeleteButtonInEditForm
    noMatchingDataMessage: string; // For checking if search yields no results
    itemDeletedMessage: string; // From confirmDeletion
    // Import/Export/Filter (from previous context, ensure these are needed and correct)
    importButton?: string;
    importDialogTitle?: string;
    importDialog?: string;
    chooseFileButtonInDialog?: string;
    cancelButtonInDialog?: string;
    downloadCsvButton?: string;
    authTypeFilterDropdown?: string;
    allVisibleCredentialRowsAuthType?: string;
}

export class VpnCredentialsPage {
    public readonly locators: VpnCredentialsLocators; // Made public for step def access if needed
    private activeDownloadPromise: Promise<Download> | null = null;

    constructor() {
        // Replace with your actual selectors
        this.locators = {
            pageTitle: '//span[@title="VPN Credentials"]',
            addCredentialButton: '//span[contains(text(),"Add VPN Credential")]',
            addCredentialModalTitle: '//span[@title="Add VPN Credential"]',
            userIdLabel: '//span[contains(text(),"User ID")]',
            userIdInput: '//div[@data-testid="textBox-userId"]/input',
            fqdnToggleButton: '//button[@aria-label="FQDN"]/span',
            ipToggleButton: '//button[@aria-label="IP"]/span',
            newPreSharedKeyLabel: '//div[@id="NEW_PRE_SHARED_KEY"]/span',
            passwordInput1: '(//input[@type="password"])[1]',
            confirmNewPreSharedKeyLabel: '//div[@id="CONFIRM_NEW_PRE_SHARED_KEY"]/span',
            passwordInput2: '(//input[@type="password"])[2]',
            saveButton: '//span[contains(text(),"Save")]',
            allChangesSavedMessage: '//span[contains(text(),"All changes have been saved.")]',
            filterInput: '//input[@aria-label="Filter"]',
            applyFilterButton: '//button[@aria-label="Apply filter"]',
            editIcon: '//i[@aria-label="EDIT"]',
            editVpnCredentialModalTitle: '//span[@title="Edit VPN Credential"]',
            deleteButtonInForm: '//span[contains(text(),"Delete")]',
            confirmButtonInModalFooter: '//div[@data-testid="modal-footer"]//span[contains(text(),"Confirm")]',
            noMatchingDataMessage: '//div[contains(text(),"No matching data found")]', // Added locator
            itemDeletedMessage: '//span[contains(text(),"The item has been deleted.")]',
        
        };
    }

    async navigateToVpnCredentialsPage(page: Page) {
        console.log("Executing navigateTo VPN Credentials...");
        // Read base URL from environment variable, similar to PartnerIntegrationsPage
        const uiBaseUrl: string = process.env.ONE_UI_BASE_URL ?? "";

        if (!uiBaseUrl) {
            throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
        }
        const vpnCredentialsPath = "administration/vpn-credentials";
        let targetUrl = "";

        if (uiBaseUrl.includes("console")) {
            // Assuming a similar structure for console URLs if applicable, adjust if different
            targetUrl = `${uiBaseUrl}internet-saas#${vpnCredentialsPath}`;
            await page.goto(targetUrl);
            // Consider replacing waitForTimeout with a more robust wait, e.g., waiting for a specific element
            // await page.waitForTimeout(30000); 
            await page.waitForLoadState('networkidle', { timeout: 30000 }); // Example of a more robust wait
        } else {
            targetUrl = `${uiBaseUrl}#${vpnCredentialsPath}`;
            await page.goto(targetUrl);
            // Consider replacing waitForTimeout with a more robust wait
            // await page.waitForTimeout(20000); 
            await page.waitForLoadState('networkidle', { timeout: 20000 }); // Example of a more robust wait
        }
        
        await expect(page).toHaveURL(new RegExp(vpnCredentialsPath.replace(/\//g, '\\/')), { timeout: 10000 });
        await expect(page.locator(this.locators.pageTitle).first()).toBeVisible({ timeout: 15000 });
        console.log("Successfully navigated to VPN Credentials page.");
    }

    async clickAddCredentialButton(page: Page) {
        await page.locator('//span[@title="VPN Credentials"]').isVisible();
        await page.waitForTimeout(2000);

        const addVpnCredentialsLink = await page.locator('//span[contains(text(),"Add VPN Credential")]');
        await expect(addVpnCredentialsLink).toBeVisible();
        await page.waitForTimeout(2000);
        await expect(addVpnCredentialsLink).toContainText("Add VPN Credential");
        await page.waitForTimeout(2000);
        await addVpnCredentialsLink.click();

        await page.locator('//span[@title="Add VPN Credential"]').isVisible();
        await page.waitForTimeout(2000);

    }


    async enterUserId(page: Page, userId: string) {
        const userIdLabel = await page.locator('//span[contains(text(),"User ID")]');
        await expect(userIdLabel).toBeVisible();
        await page.waitForTimeout(2000);
        await expect(userIdLabel).toContainText("User ID");
        await page.waitForTimeout(2000);

        const userIdTextbox = await page.locator('//div[@data-testid="textBox-userId"]/input');
        await expect(userIdTextbox).toBeVisible();
        await page.waitForTimeout(2000);
        await userIdTextbox.fill(userId);
        await page.waitForTimeout(2000);
        console.log(`Entered user ID: ${userId}`);
    }

    async verifyAndSelectAddressType(page: Page, addressType: 'FQDN' | 'IP') {
        const fqdnToggle = page.locator('//button[@aria-label="FQDN"]/span');
        const ipToggle = page.locator('//button[@aria-label="IP"]/span');
        await expect(fqdnToggle).toBeVisible();
        await expect(ipToggle).toBeVisible();

        if (addressType === 'FQDN') {
            await fqdnToggle.click();
        } else {
            await ipToggle.click();
        }
        console.log(`Verified address type toggle and selected '${addressType}'`);
    }

    async enterPassword(page: Page, password: string) {
        await page.locator('//div[@id="NEW_PRE_SHARED_KEY"]/span').isVisible();
        await page.waitForTimeout(2000);

        const passwordTextbox = await page.locator('(//input[@type="password"])[1]');
        await expect(passwordTextbox).toBeVisible();
        await page.waitForTimeout(2000);
        await passwordTextbox.fill(password);
        await page.waitForTimeout(2000);
        console.log(`Entered password is ${password}`);
    }

    async enterConfirmPassword(page: Page, password: string) {
        await page.locator('//div[@id="CONFIRM_NEW_PRE_SHARED_KEY"]/span');
        await page.waitForTimeout(2000);

        const confirmPasswordTextbox = await page.locator('(//input[@type="password"])[2]');
        await expect(confirmPasswordTextbox).toBeVisible();
        await page.waitForTimeout(2000);
        await confirmPasswordTextbox.fill(password);
        await page.waitForTimeout(2000);
        console.log(`Entered confirm password is ${password}`);
    }

    async clickSaveButton(page: Page) {
        await page.waitForTimeout(2000);
        await page.locator('//span[contains(text(),"Save")]').click();
        await page.waitForTimeout(2000);

        const successMessage = await page.locator('//span[contains(text(),"All changes have been saved.")]');
        await expect(successMessage).toBeVisible();
        await page.waitForTimeout(2000);
    }


    async ensureCredentialDoesNotExist(page: Page, userId: string): Promise<void> {
        console.log(`Ensuring VPN credential with User ID "${userId}" does not exist...`);
        await this.seachRecord(page, userId); 
    
        const noDataMessageLocator = page.locator(this.locators.noMatchingDataMessage);
        let recordFound = true; 
    
        if (await noDataMessageLocator.isVisible({ timeout: 2000 }).catch(() => false)) { 
            console.log(`"No matching data found" message displayed for User ID "${userId}". Credential does not exist.`);
            recordFound = false;
        }
    
        if (recordFound) {
            try {
                await this.clickEditCredential(page, userId); 
                console.log(`VPN credential with User ID "${userId}" found and edit modal opened. Proceeding with deletion.`);
                await this.clickDeleteButtonInEditForm(page); 
                await this.confirmDeletion(page); 
                console.log(`VPN credential with User ID "${userId}" has been deleted.`);
            } catch (error) {
                console.log(`VPN credential with User ID "${userId}" could not be edited after search (and no 'No Data' message seen). Assuming it does not exist or is not uniquely identifiable for deletion. Error: ${error}`);
            }
        }

        try {
            const searchField = page.locator(this.locators.filterInput);
            if (await searchField.isVisible({ timeout: 1000 }).catch(() => false)) {
                await searchField.fill('');
                await page.locator(this.locators.applyFilterButton).click();
                await page.waitForLoadState('networkidle', {timeout: 5000}); 
                console.log("Search filter cleared.");
            } else {
                console.log("Search filter input not visible, skipping clear.");
            }
        } catch (clearError) {
            console.error("Error while trying to clear search filter:", clearError);
        }
    }


    async seachRecord(page: Page, credentialName: string) {
        const searchField = page.locator(this.locators.filterInput);
        await expect(searchField).toBeVisible();
        await page.waitForTimeout(2000);
        await searchField.click();
        await page.waitForTimeout(2000);
        await searchField.fill(credentialName);
        await page.waitForTimeout(2000); // Kept as per request

        await page.locator(this.locators.applyFilterButton).click();
        await page.waitForTimeout(2000); // Kept as per request
        console.log(`Search performed for: ${credentialName}`);
    }

    async clickEditCredential(page: Page, credentialName: string) {
        console.log(`Edit the ${credentialName} to update the data`)
        //click on Edit button
        await page.waitForTimeout(2000);
        await page.locator(this.locators.editIcon).click(); // Using the defined locator

        await expect(page.locator(this.locators.editVpnCredentialModalTitle)).toBeVisible();
    }

    async clickDeleteButtonInEditForm(page: Page) {
        await page.waitForTimeout(2000);
        await page.locator('//span[contains(text(),"Delete")]').click();
        await page.waitForTimeout(2000);
        await page.locator('//div[@data-testid="modal-footer"]//span[contains(text(),"Confirm")]').isVisible();
        // The above line only checks visibility, it doesn't store the locator for clicking.
        // Let's define it and click.
        const confirmButton = page.locator(this.locators.confirmButtonInModalFooter);
        await expect(confirmButton).toBeVisible();
        await page.waitForTimeout(2000);
        await confirmButton.click();
        await page.waitForTimeout(2000);
    }

    // This method was originally for verifying the deletion message.
    // The ensureCredentialDoesNotExist calls this after the deletion process.
    async confirmDeletion(page: Page) {
        const deleteSuccessMessage = page.locator(this.locators.itemDeletedMessage);
        await expect(deleteSuccessMessage).toContainText("The item has been deleted.");
        await page.waitForTimeout(2000);
        console.log("Deletion success message confirmed.");
    }

    async clickImportCredentialsButton(page: Page, importCred: string) {
        const importButton = await page.locator(`//span[contains(text(),"${importCred}")]`);
        await expect(importButton).toBeVisible();
        await page.waitForTimeout(2000);
        await importButton.click();
        await page.waitForTimeout(2000);

        await page.locator('//span[@title="Import VPN Credentials"]').isVisible({timeout: 5000});
    }


    async clickCancelInImportDialog(page: Page) {
        await page.waitForTimeout(2000);
        await page.locator('//span[contains(text(),"Cancel")]').click();
        await page.waitForTimeout(2000);
    }

    async clickDownloadCsvButton(page: Page) {
        const downloadButton = page.locator('//span[contains(text(),"Sample Import CSV file")]');
        await expect(downloadButton).toBeVisible();
        await expect(downloadButton).toContainText("Sample Import CSV file");
        
        // Start waiting for the download event BEFORE clicking the button
        console.log("Setting up listener for download event...");
        this.activeDownloadPromise = page.waitForEvent('download');

        await downloadButton.click();
        console.log("Clicked 'Sample Import CSV file' button to initiate download.");
    }

  async verifyDownloadInitiated(page: Page): Promise<void> {
    if (!this.activeDownloadPromise) {
        throw new Error("Download was not initiated by clickDownloadCsvButton or the promise has already been consumed.");
    }
    console.log("Verifying download process initiated successfully");
    // Await the promise that was set when the download button was clicked
    const download = await this.activeDownloadPromise;
    expect(download.suggestedFilename()).toBeTruthy(); 
    console.log(`Download initiated: ${download.suggestedFilename()}`);
    // Clear the promise after it has been used
    this.activeDownloadPromise = null;
  }

  async authTypeFilter(page: Page, authTypeFilter: string) {
    await page.locator('//div/span[@role="button"]/span[2]').click();
    await page.waitForTimeout(2000);
    await page.locator('//li[@id="UFQDN"]').click();

    await page.locator('(//div[contains(text(),"FQDN")])[1]').isVisible();
    await page.waitForTimeout(2000);

  }
    // async selectAuthTypeFilter(page: Page, authType: string) {
    //     await page.locator(this.locators.authTypeFilterDropdown).selectOption({ label: authType });
    //     console.log(`Selected auth type '${authType}' from filter: ${this.locators.authTypeFilterDropdown}`);
    //     // Replace with a more robust wait, e.g., waiting for a specific element in the grid to update or a loading spinner.
    //     await page.waitForLoadState('domcontentloaded'); // Or 'networkidle'
    // }

    // async getVisibleCredentialsAuthTypes(page: Page): Promise<string[]> {
    //     await page.waitForSelector(this.locators.allVisibleCredentialRowsAuthType, { state: 'attached', timeout: 5000 });
    //     const authTypeElements = await page.locator(this.locators.allVisibleCredentialRowsAuthType).all();
    //     const authTypes: string[] = [];
    //     for (const el of authTypeElements) {
    //         if (await el.isVisible()) { // Ensure we only get text from visible elements
    //             authTypes.push(await el.textContent() || "");
    //         }
    //     }
    //     console.log(`Visible credentials auth types from ${this.locators.allVisibleCredentialRowsAuthType}: ${authTypes}`);
    //     return authTypes.map(type => type.trim()).filter(type => type.length > 0);
    // }
}
