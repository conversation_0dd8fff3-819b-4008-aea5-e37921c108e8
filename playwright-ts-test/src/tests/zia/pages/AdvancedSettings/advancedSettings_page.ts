import { expect, Page } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions.js';
import { config } from 'dotenv';

config(); 

const url: string = process.env.ONE_UI_BASE_URL ?? "";

const Selectors: Record<string, string> = {
   Save: "-js-save button big primary",
   Cancel: "-js-cancel button big no-bkgrd"
};

class AdvancedSettings {
    fields: {
        saveButton: string;
        subHeading: (subHeadings: string) => string;
        cancelButton: string;
      };

    constructor() {
        this.fields = {
          subHeading: (subHeadings) => `//span[text()='${subHeadings}']`,
          saveButton: "//span[text()='Save']",
          cancelButton: "//span[text()='Cancel']",

        };
    }

    async navigateToAdvancedSettings(page: Page): Promise<void>{
          console.log("The Url used is : - "+url);
            if (!url) {
              throw new Error('ONE_UI_BASE_URL environment variable is not set');
            }
            if (url.includes("console")) {
              await page.goto(url+"internet-saas#administration/advanced-network-settings");
              await page.waitForTimeout(35000);
              }
            else{
              await page.goto(url+"#administration/advanced-network-settings");
              await page.waitForTimeout(19000);
            }
    }

    async verifyHeading(page: Page, heading: string): Promise<void>{
        await page.locator(`//span[text()='${heading}' and @class='-js-title-text']`).hover();
        const value = await page.locator(`//span[text()='${heading}' and @class='-js-title-text']`);
        expect(value).toHaveText(heading);
    }

    async verifySubHeading(page: Page, subHeading: string): Promise<void>{
      const subheadingPolicies = await page.locator(this.fields.subHeading(subHeading));
      expect(subheadingPolicies).toHaveText(subHeading);
  }

    async toggleEnableAdminRanking(page: Page): Promise<void>{
        await page.locator('.toggle-button').first().isVisible();
        await page.locator('.toggle-button').first().click();
        await page.waitForTimeout(2000);
    }

    async enableToggleEnableAdminRanking(page: Page): Promise<void>{
      await page.waitForTimeout(1000);
      await page.locator('//span[@data-testid="toggleSwitch-enableAdminRankAccess"]/div/span[1]').isVisible();
      await page.locator('//span[@data-testid="toggleSwitch-enableAdminRankAccess"]/div/span[1]').click();
      await page.waitForTimeout(2000);
  }

    async clickButton(page: Page, button: string): Promise<void>{
        await page.waitForTimeout(6000);
        const value = await page.locator(this.fields.saveButton);
        expect(value).toHaveText(button);
        await page.locator(this.fields.saveButton).click();
        if(await page.getByText('Confirm').isVisible())
          {
            await page.getByText('OK', { exact: true }).click();
          }
        await page.waitForTimeout(5000);  
        await page.locator("//span[contains(text(),'All changes have been')]").waitFor();
        const messgae = await page.locator("//span[contains(text(),'All changes have been')]");
        expect(messgae).toContainText('All changes have been');
    }

    async disableToggleAllowCascadingURLFiltering(page: Page): Promise<void>{
      await page.waitForTimeout(2000);
      await page.locator('//div[@id="ALLOW_CASCADING_URL_FILTERING"]/following-sibling::span/div/span[1]').click();
      await page.waitForTimeout(2000);
    }

    async enableToggleAllowCascadingURLFiltering(page: Page): Promise<void>{
      await page.waitForTimeout(7000);
      await page.locator('//div[@id="ALLOW_CASCADING_URL_FILTERING"]/following-sibling::span/div/span[1]').click();
      await page.locator('div').filter({ hasText: /^Allow Cascading to URL Filtering$/ }).locator('span').isVisible();
      await page.getByText('Confirm').click();
      await page.waitForTimeout(2000);
    }

    async enterTimeout(page: Page, timeout: string): Promise<void>{
      await page.waitForTimeout(7000);
      await page.getByRole('textbox', { name: 'Enter Text' }).click();
      await page.getByRole('textbox', { name: 'Enter Text' }).fill(timeout);
    }

    async enterAuthenticationExemption(page: Page): Promise<void>{
      await page.locator("(//span[@class='dropdown-button-label -js-dropdown-button-label'])[1]").click();
      await page.waitForTimeout(4000);
      await page.locator("(//input[@placeholder='Search...'])[1]").click();
      await page.locator("(//input[@placeholder='Search...'])[1]").fill('Vehicles');
      await page.locator("(//input[@placeholder='Search...'])[1]").press('Enter');
      await page.waitForTimeout(4000);
      await page.locator("//li[@class='multiselect-list-item -js-dropdown-list-item child']").click();
      await page.locator("(//span[text()='Done'])[1]").click();
      await page.getByRole('textbox', { name: 'Add Items' }).first().click();
      await page.getByRole('textbox', { name: 'Add Items' }).first().fill('google.com');
      await page.getByText('Add Items').first().click();
      await page.getByText('google.com').isVisible();
      await page.locator("(//span[@class='dropdown-button-label -js-dropdown-button-label'])[2]").click();
      await page.locator("//li[text()='ChatGPT']").click();
      await page.locator("(//span[text()='Done'])[2]").click();
      await page.waitForTimeout(4000);
    }

    async deSelectAuthenticationExemption(page: Page): Promise<void>{
      await page.waitForTimeout(7000);
      await page.getByText('Vehicles').first().click();
      await page.locator('.dropdown-panel-footer > span:nth-child(3)').first().click();
      await page.locator('.dropdown-panel-footer > span').first().click();
      await page.locator('.list-builder-list-item > .fas').click();
      await page.getByText('ChatGPT').first().click();
      await page.locator('div:nth-child(3) > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span:nth-child(3)').first().click();
      await page.locator('div:nth-child(3) > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
    }

    async enterKerberosAuthenticationExemption(page: Page): Promise<void>{
      await page.locator('div:nth-child(5) > .form-input-rows > div > .form-input > .dropdown > .dropdown-button').first().click();
      await page.waitForTimeout(5000);
      await page.getByRole('checkbox').first().check();
      await page.locator('div:nth-child(5) > .form-input-rows > div > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.getByRole('textbox', { name: 'Add Items' }).nth(1).click();
      await page.getByRole('textbox', { name: 'Add Items' }).nth(1).fill('google.com');
      await page.getByText('Add Items').nth(1).click();
      await page.getByText('google.com').isVisible();
      await page.locator('div:nth-child(5) > .form-input-rows > div:nth-child(3) > .form-input > .dropdown > .dropdown-button').click();
      await page.waitForTimeout(5000);
      await page.getByRole('listitem').filter({ hasText: 'ChatGPT' }).getByRole('checkbox').check();
      await page.locator('div:nth-child(5) > .form-input-rows > div:nth-child(3) > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
    }

    async deSelectKerberosAuthenticationExemption(page: Page): Promise<void>{
      await page.waitForTimeout(7000);
      await page.getByText('Adult Sex Education; Adult').first().click();
      await page.locator('div:nth-child(5) > .form-input-rows > div > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span:nth-child(3)').first().click();
      await page.locator('div:nth-child(5) > .form-input-rows > div > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.locator('.list-builder-list-item > .fas').click();
      await page.getByText('ChatGPT').first().click();
      await page.locator('div:nth-child(5) > .form-input-rows > div:nth-child(3) > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span:nth-child(3)').click();
      await page.locator('div:nth-child(5) > .form-input-rows > div:nth-child(3) > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
    }

    async enableToggles(page: Page): Promise<void>{
      await page.getByText('Log Internal IPs from XFF').isVisible();
      await page.locator('div:nth-child(6) > .form-input-rows > .form-input-row > .form-input > .toggle > .toggle-button').click();
      await page.getByText('Enforce Surrogate IP').isVisible();
      await page.locator('div:nth-child(7) > .form-input-rows > .form-input-row > .form-input > .toggle > .toggle-button').click();
      await page.getByText('Enable Policy For').isVisible();
      await page.locator('div:nth-child(8) > .form-input-rows > .form-input-row > .form-input > .toggle > .toggle-button').click();
      await page.getByText('Inspect Tunneled HTTP Traffic').isVisible();
      await page.locator("(//span[@class='toggle-button'])[4]").click();
      await page.getByText('Block Tunneling to Non-HTTP/').isVisible();
      await page.locator("(//span[@class='toggle-button off'])[3]").click();
      await page.getByText('Block Non-RFC Compliant HTTP').isVisible();
      await page.locator("(//span[@class='toggle-button off'])[4]").click();
      await page.getByText('Block Domain Fronting').isVisible();
      await page.locator("(//span[@class='toggle-button'])[5]").click();
      await page.getByText('HTTP', { exact: true }).nth(2).isVisible();
      await page.locator('div:nth-child(16) > .form-input-rows > div > .form-input > .toggle > .toggle-button').first().click();
      await page.getByText('HTTPS', { exact: true }).nth(2).isVisible();
      await page.locator('div:nth-child(16) > .form-input-rows > div:nth-child(2) > .form-input > .toggle > .toggle-button').click();
      await page.getByText('FTP', { exact: true }).nth(2).isVisible();
      await page.locator('div:nth-child(16) > .form-input-rows > div:nth-child(3) > .form-input > .toggle > .toggle-button').click();
      await page.getByText('DNS', { exact: true }).nth(2).isVisible();
      await page.locator('div:nth-child(4) > .form-input > .toggle > .toggle-button').click();
      await page.getByText('RTSP', { exact: true }).nth(2).isVisible();
      await page.locator('div:nth-child(5) > .form-input > .toggle > .toggle-button').click();
      await page.getByText('PPTP', { exact: true }).nth(2).isVisible();
      await page.locator('div:nth-child(6) > .form-input > .toggle > .toggle-button').click();
      await page.getByText('Enable Office 365 One Click').isVisible();
      await page.locator('div:nth-child(2) > .toggle > .toggle-button').click();
      await page.getByText('Insert XFF Header for ZPA').isVisible();
      await page.locator('div:nth-child(18) > .form-input-rows > .form-input-row > .form-input > .toggle > .toggle-button').click();
      await page.getByText('Optimize DNS Resolution').isVisible();
      await page.locator('div:nth-child(19) > .form-input-rows > div > .form-input > .toggle > .toggle-button').first().click();
      await page.getByText('Enable Firewall for Z-Tunnel').isVisible();
      await page.locator('div:nth-child(20) > .form-input-rows > .form-input-row > .form-input > .toggle > span:nth-child(2) > .fas').click();
    }

    async disableTogglesButton(page: Page): Promise<void>{
      await page.waitForTimeout(7000);
      await page.locator('div:nth-child(6) > .form-input-rows > .form-input-row > .form-input > .toggle > .toggle-button').click();
      await page.locator('div:nth-child(7) > .form-input-rows > .form-input-row > .form-input > .toggle > .toggle-button').click();
      await page.locator('div:nth-child(8) > .form-input-rows > .form-input-row > .form-input > .toggle > .toggle-button').click();
      await page.locator('div:nth-child(9) > .form-input-rows > div > .form-input > .toggle > .toggle-button').first().click();
      await page.locator('div:nth-child(2) > .form-input > .toggle > .toggle-button').first().click();
      await page.locator('div:nth-child(3) > .form-input > .toggle > .toggle-button').first().click();
      await page.locator('div:nth-child(3) > .form-input > .toggle > span:nth-child(2) > .fas').first().click
      await page.locator('div:nth-child(16) > .form-input-rows > div > .form-input > .toggle > .toggle-button').first().click();
      await page.locator('div:nth-child(16) > .form-input-rows > div:nth-child(2) > .form-input > .toggle > .toggle-button').click();
      await page.locator('div:nth-child(16) > .form-input-rows > div:nth-child(3) > .form-input > .toggle > .toggle-button').click();
      await page.locator('div:nth-child(4) > .form-input > .toggle > .toggle-button').click();
      await page.locator('div:nth-child(5) > .form-input > .toggle > .toggle-button').click();
      await page.locator('div:nth-child(6) > .form-input > .toggle > .toggle-button').click();
      await page.locator('div:nth-child(2) > .toggle > .toggle-button').click();
      await page.locator('div:nth-child(18) > .form-input-rows > .form-input-row > .form-input > .toggle > .toggle-button').click();
      await page.locator('div:nth-child(20) > .form-input-rows > .form-input-row > .form-input > .toggle > span > .fas').first().click();
    }

    async selectDropDowns(page : Page): Promise<void>{
      await page.getByText('HTTP Services').isVisible();
      await page.getByText('HTTP', { exact: true }).first().click();
      await page.getByText('AIM').click();
      await page.locator('div:nth-child(11) > .form-input-rows > div > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.getByText('HTTPS Services').isVisible();
      await page.getByText('HTTPS', { exact: true }).nth(1).click();
      await page.getByRole('listitem').filter({ hasText: 'AIM' }).getByRole('checkbox').check();
      await page.locator('.form-input-rows > div:nth-child(2) > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.getByText('DNS Services').isVisible();
      await page.getByText('DNS', { exact: true }).nth(2).click();
      await page.getByRole('listitem').filter({ hasText: 'AIM' }).getByRole('checkbox').check();
      await page.locator('div:nth-child(12) > .form-input-rows > .form-input-row > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.getByText('FTP Services').isVisible();
      await page.locator('span').filter({ hasText: /^FTP$/ }).nth(1).click();
      await page.getByRole('listitem').filter({ hasText: 'AIM' }).getByRole('checkbox').check();
      await page.locator('div:nth-child(13) > .form-input-rows > .form-input-row > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.getByText('RTSP Services').isVisible();
      await page.locator('span').filter({ hasText: /^RTSP$/ }).nth(1).click();
      await page.getByRole('listitem').filter({ hasText: 'AIM' }).getByRole('checkbox').check();
      await page.locator('div:nth-child(14) > .form-input-rows > .form-input-row > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.getByText('PPTP Services').isVisible();
      await page.locator('span').filter({ hasText: /^PPTP$/ }).nth(1).click();
      await page.getByRole('listitem').filter({ hasText: 'AIM' }).getByRole('checkbox').check();
      await page.locator('div:nth-child(15) > .form-input-rows > .form-input-row > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.getByText('Optimize These URL Categories', { exact: true }).isVisible();
      await page.locator('.-js-dns-sub-section > div > .form-input > .dropdown > .dropdown-button > .dropdown-button-label').first().click();
      await page.getByRole('listitem').filter({ hasText: 'Adult Sex Education' }).getByRole('checkbox').check();
      await page.locator('.-js-dns-sub-section > div > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.getByText('Optimize These Cloud Applications', { exact: true }).isVisible();
      await page.locator('.-js-dns-sub-section > div:nth-child(2) > .form-input > .dropdown > .dropdown-button > .dropdown-button-label').click();
      await page.getByText('ChatGPT').click();
      await page.locator('.-js-dns-sub-section > div:nth-child(2) > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.getByText('Do Not Optimize These URL').isVisible();
      await page.locator('.do-not-resolve-block > div > .form-input > .dropdown > .dropdown-button > .dropdown-button-label').first().click();
      await page.getByRole('listitem').filter({ hasText: 'Adult Sex Education' }).getByRole('checkbox').check();
      await page.locator('.do-not-resolve-block > div > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.getByText('Do Not Optimize These Cloud').isVisible();
      await page.locator('.do-not-resolve-block > div:nth-child(2) > .form-input > .dropdown > .dropdown-button').click();
      await page.getByRole('listitem').filter({ hasText: 'ChatGPT' }).getByRole('checkbox').check();
      await page.locator("(//span[text()='Done'])[last()]").click();
    }

    async preferSNIOption(page: Page): Promise<void>{
      await page.getByText('Prefer SNI Over CONNECT Host for DNS Resolution', { exact: true }).isVisible();
      await page.locator('div:nth-child(19) > .form-input-rows > div:nth-child(3) > .form-input > .toggle > span:nth-child(2) > .fas').click();
      await page.getByText('URL Category Exemptions for Prefer SNI Over CONNECT Host for DNS Resolution').click();
      await page.getByText('Zscaler Proxy IPs').nth(2).click();
      await page.getByRole('listitem').filter({ hasText: 'Adult Sex Education' }).getByRole('checkbox').check();
      await page.locator('.-js-dns-over-conn-section > div > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
      await page.getByText('Exempted Cloud Applications for Prefer SNI Over CONNECT Host for DNS Resolution').isVisible();
      await page.locator('.-js-dns-over-conn-section > div:nth-child(2) > .form-input > .dropdown > .dropdown-button > .dropdown-button-label').click();
      await page.getByRole('listitem').filter({ hasText: 'ChatGPT' }).getByRole('checkbox').check();
      await page.locator('.-js-dns-over-conn-section > div:nth-child(2) > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
    }
}

export default new AdvancedSettings();