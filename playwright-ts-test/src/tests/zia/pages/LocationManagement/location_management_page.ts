import { Page, expect, Locator, Download } from "@playwright/test";
import { DataTable } from "@cucumber/cucumber";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

interface LocationManagementLocators {
    // Navigation & General
    pageTitle: string; // For verifying navigation

    // Add Location Form
    addLocationButton: string;
    addLocationModalTitle: string;
    locationNameInput: string;
    countryDropdown: string;
    cityStateProvinceInput: string;
    timeZoneDropdown: string;
    excludeManualGroupsToggle: string;
    excludeDynamicGroupsToggle: string;
    locationTypeDropdown: string;
    staticIpGreTunnelDropdown: string;
    useXffToggle: string;
    enforceAuthToggle: string;
    enableIpSurrogateToggle: string;
    enforceSurrogateIpKnownBrowsersToggle: string;
    enableKerberosAuthToggle: string;
    enforceFirewallControlToggle: string;
    enableIpsControlToggle: string;
    enforceBandwidthControlToggle: string;
    bandwidthDownloadInput: string;
    bandwidthUploadInput: string;
    saveButton: string;
    successMessage: string;
    descriptionInput: string; // In edit form

    // Dropdown Panel (generic parts, might need more specific ones)
    dropdownPanel: string;
    dropdownPanelSearchInput: string;
    dropdownOptionFirst: string; // For 'select_first'
    dropdownOptionByText: (text: string) => string;

    // Grid
    gridRowByName: (name: string) => string;
    editButtonForRow: (name: string) => string; // Corrected definition below
    deleteButtonForRow: (name: string) => string;
    deleteConfirmationButton: string;
    locationDescriptionInGrid: (name: string) => string; // For verifying description in grid

    // Location Groups
    locationGroupsTab: string;
    addManualGroupButton: string;
    addDynamicGroupButton: string; // In case you implement dynamic groups later
    manualGroupNameInput: string;
    groupDescriptionInput: string;
    groupFormNextButton: string;
    groupFormSaveButton: string;
    groupGridRowByName: (name: string) => string;
    groupGridDescriptionByName: (name: string) => string;
    editGroupButtonForRow: (name: string) => string;
    deleteGroupButtonOnForm: string;
    confirmGroupDeletionButton: string;
    groupSearchInput: string;
    locationGroupSearch: string;
}

export default class LocationManagementPage {
    private readonly locators: LocationManagementLocators;

    constructor() {
        this.locators = {
            pageTitle: "(//span[contains(text(),'Location Management')])[2]",
            addLocationButton: '(//i[@class="fas fa-plus-circle"])[1]',
            addLocationModalTitle: '//span[contains(text(),"Add Location")]',
            locationNameInput: '(//input[@placeholder="Enter Text"])[1]',
            countryDropdown: 'label:has-text("Country") + div .dropdown-button',
            cityStateProvinceInput: 'label:has-text("City, State, Province") + div input[type="text"]',
            timeZoneDropdown: 'label:has-text("Time Zone") + div .dropdown-button',
            excludeManualGroupsToggle: '//span[contains(text(),"Exclude from Manual Location Groups")]/parent::*/following-sibling::*/span/span[2]',
            excludeDynamicGroupsToggle: '//span[contains(text(),"Exclude from Dynamic Location Groups")]/parent::*/following-sibling::*/span/span[2]',
            locationTypeDropdown: 'label:has-text("Location Type") + div .dropdown-button',
            staticIpGreTunnelDropdown: 'label:has-text("Static IP Address or GRE Tunnel") + div .dropdown-button',
            useXffToggle: '//span[contains(text(),"Use XFF from Client Request")]/parent::*/following-sibling::*/span/span[2]',
            enforceAuthToggle: '//span[contains(text(),"Enforce Authentication")]/parent::*/following-sibling::*/span/span[2]',
            enableIpSurrogateToggle: '//span[contains(text(),"Enable IP Surrogate")]/parent::*/following-sibling::*/span/span[2]',
            enforceSurrogateIpKnownBrowsersToggle: '//span[contains(text(),"Enforce Surrogate IP for Known Browsers")]/parent::*/following-sibling::*/span/span[2]',
            enableKerberosAuthToggle: 'label:has-text("Enable Kerberos Authentication") + div .toggle input[type="checkbox"]',
            enforceFirewallControlToggle: '//span[contains(text(),"Enforce Firewall Control")]/parent::*/following-sibling::*/span/span[2]',
            enableIpsControlToggle: '//span[contains(text(),"Enable IPS Control")]/parent::*/following-sibling::*/span/span[2]',
            enforceBandwidthControlToggle: 'label:has-text("Enforce Bandwidth Control") + div .toggle input[type="checkbox"]',
            bandwidthDownloadInput: '//span[contains(text(),"Download (Mbps)")]',
            bandwidthUploadInput: '//span[contains(text(),"Upload (Mbps)")]',
            saveButton: '//span[contains(text(),"Save")]',
            successMessage: '//span[contains(text(),"All changes have been saved.")]',
            descriptionInput: '//div/textarea',

            // Dropdown panel parts
            dropdownPanel: '+ .dropdown-panel', // Assumes panel is a sibling after the button
            dropdownPanelSearchInput: 'input[placeholder="Search..."]',
            dropdownOptionFirst: '.dropdown-option >> visible=true >> nth=0', // First visible option
            dropdownOptionByText: (text: string) => `.dropdown-option:has-text("${text}")`,

            // Grid locators
            gridRowByName: (name: string) => `//span[contains(text(),"${name}")]`,
            // Corrected locator pattern for edit button specific to a row
            editButtonForRow: (name: string) => `//span[contains(text(),"${name}")]/ancestor::div[contains(@class,"row") or @role="row"][1]//span[@title="Edit"]`,
            deleteButtonForRow: (name: string) => `${this.locators.gridRowByName(name)} button:has-text("Delete")`,
            deleteConfirmationButton: 'button:has-text("Confirm Delete")', // Example
            locationDescriptionInGrid: (name: string) => `${this.locators.gridRowByName(name)} .location-description-column`, // Example

            // Location Groups
            locationGroupsTab: '//span[contains(text(),"Location Groups")]',
            addManualGroupButton: '//span[contains(text(),"Add Manual Group")]',
            addDynamicGroupButton: '//span[contains(text(),"Add Dynamic Group")]',
            manualGroupNameInput: '//input[@aria-label="name"]', // Assuming a common name input
            groupDescriptionInput: '//textarea[@aria-label="description"]', // Assuming a common description textarea
            groupFormNextButton: '//span[contains(text(),"Next")]',
            groupFormSaveButton: '//span[contains(text(),"Save")]', // Assuming a common save button
            groupGridRowByName: (name: string) => `//div[@role="grid"]//span[contains(text(),"${name}")]`, // Adjust if grid structure is different
            groupGridDescriptionByName: (name: string) => `//div[@role="grid"]//span[contains(text(),"${name}")]/ancestor::div[@role="row"]//span[contains(@class,"description-column")]`, // Example, adjust based on actual HTML
            editGroupButtonForRow: (name: string) => `//span[contains(text(),"${name}")]/ancestor::div[@role="row"]//button[@aria-label="Edit"]`, // Example
            deleteGroupButtonOnForm: '//span[contains(text(),"Delete")]', // Assuming delete button on edit form
            confirmGroupDeletionButton: '//span[contains(text(),"Confirm")]', // Assuming a common confirm button
            groupSearchInput: '(//input[@placeholder="Search..."])[1]',
            locationGroupSearch: '(//input[@placeholder="Search..."])[3]',
        };
    }

    async navigateToLocationManagement(page: Page) {
        console.log("Executing navigateToLocationManagement...");

        console.log("The Url used is : - " + url);
        if (!url) {
            throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
        }

        const locationManagementPath = "administration/locations";
        let targetUrl = "";

        if (url.includes("console")) {
            targetUrl = url + "internet-saas#" + locationManagementPath;
            await page.goto(targetUrl);
            console.log("Page navigation initiated (console URL).");
            await page.waitForTimeout(45000);
        } else {
            targetUrl = url + "#" + locationManagementPath;
            await page.goto(targetUrl);
            console.log("Page navigation initiated (non-console URL).");
            await page.waitForTimeout(30000);
        }
        await expect(page).toHaveURL(new RegExp(locationManagementPath.replace(/\//g, '\\/')));
        await expect(page.locator(this.locators.pageTitle)).toBeVisible();
        console.log("Successfully navigated to Location Management page.");
    }


    private async setToggle(page: Page, toggleCheckboxLocator: string, value: string) {
        await page.waitForTimeout(5000);
        const toggleCheckbox = page.locator(toggleCheckboxLocator);
        await expect(toggleCheckbox).toBeVisible();
        await page.waitForTimeout(2000);
        await toggleCheckbox.click();
        console.log("Toggle set to:", value);
    }

    async addLocation(page: Page, details: DataTable) {
        await page.waitForTimeout(2000);
        await page.locator(this.locators.addLocationButton).click();
        await page.waitForTimeout(2000);
        const addLocationTitle = await page.locator(this.locators.addLocationModalTitle);
        await expect(addLocationTitle).toBeVisible();
        await expect(addLocationTitle).toContainText('Add Location');

        const data = details.rowsHash();

        // Fill in the form fields based on the DataTable
        for (const field in data) {
            const value = data[field];

            switch (field) {
                case 'Name':
                    await page.waitForTimeout(2000);
                    await page.locator(this.locators.locationNameInput).fill(value);
                    break;
                case 'Country':
                    await page.waitForTimeout(2000);
                    const dropdownButton = page.locator('//span[@data-help-property="country"]/following-sibling::span/div/span/span[1]');
                    await expect(dropdownButton).toBeVisible();
                    await dropdownButton.click();
                    await page.waitForTimeout(2000);
                    const countrySearch = await page.locator('//span[@data-help-property="country"]/following-sibling::span/div/div/div/span/span[1]/input');
                    await expect(countrySearch).toBeVisible();
                    await countrySearch.click();
                    await page.waitForTimeout(2000);
                    await (countrySearch).fill(value)
                    await page.waitForTimeout(2000);
                    await page.locator('//span[@data-help-property="country"]/following-sibling::span/div/div/div/span/span[2]');
                    await page.waitForTimeout(2000);
                    await page.locator('//li[@data-id="UNITED_KINGDOM"]').click();
                    await page.waitForTimeout(2000);
                    break;
                case 'City, State, Province':
                    await page.waitForTimeout(2000);
                    const city = await page.locator('//span[@data-help-property="state"]/following-sibling::span/input');
                    await expect(city).toBeVisible();
                    await city.fill(value);
                    await page.waitForTimeout(2000);
                    break;
                case 'Time Zone':
                    await page.waitForTimeout(2000);
                    const tzDropdown = await page.locator('//span[@data-help-property="tz"]/following-sibling::span/div/span/span[1]');
                    await expect(tzDropdown).toBeVisible();
                    await tzDropdown.click();
                    await page.waitForTimeout(2000);
                    const tzSearch = await page.locator('//span[@data-help-property="tz"]/following-sibling::span/div/div/div/span/span[1]/input');
                    await expect(tzSearch).toBeVisible();
                    await tzSearch.click();
                    await page.waitForTimeout(2000);
                    await (tzSearch).fill(value);
                    await page.waitForTimeout(2000);
                    await page.locator('//li[@data-id="UNITED_KINGDOM_EUROPE_LONDON"]').click();
                    await page.waitForTimeout(2000);
                    break;
                case 'Exclude from Manual Location Groups':
                    await page.waitForTimeout(2000);
                    await this.setToggle(page, this.locators.excludeManualGroupsToggle, value);
                    await page.waitForTimeout(2000);
                    await page.locator(`//span[contains(text(),"${value}")]/parent::*/following-sibling::*/span/span[@class="toggle-label on"]`);
                    await page.waitForTimeout(2000);
                    break;
                case 'Exclude from Dynamic Location Groups':
                    await page.waitForTimeout(2000);
                    await this.setToggle(page, this.locators.excludeDynamicGroupsToggle, value);
                    await page.waitForTimeout(2000);
                    await page.locator(`//span[contains(text(),"${value}")]/parent::*/following-sibling::*/span/span[@class="toggle-label on"]`);
                    await page.waitForTimeout(2000);
                    break;
                case 'Location Type':
                    await page.waitForTimeout(2000);
                    await page.locator('//div[@data-help-property="profile"]/following-sibling::div/div/span/span[1]').click();

                    await page.waitForTimeout(2000);
                    const locationSearch = await page.locator('//div[@data-help-property="profile"]/following-sibling::div/div/div/div/span/span[1]/input');
                    await expect(locationSearch).toBeVisible();
                    await locationSearch.click();
                    await page.waitForTimeout(5000);
                    await (locationSearch).fill(value)
                    await page.waitForTimeout(2000);
                    // await page.locator('//div[@data-help-property="profile"]/following-sibling::div/div/div/div/span/span[2]');
                    // await page.waitForTimeout(2000);
                    await page.locator('//li[@data-id="IOT"]').dblclick();
                    await page.waitForTimeout(2000);
                    break;
                case 'Static IP Address or GRE Tunnel':
                    const addressTitle = await page.locator('//div[contains(text(),"Addressing")]');
                    expect(addressTitle).toContainText('Addressing');
                    console.log('Click on Static IP Address or GRE Tunnel');
                    await page.waitForTimeout(2000);
                    await page.locator('div:nth-child(3) > div:nth-child(2) > div > .form-input > .dropdown > .dropdown-button > .dropdown-button-label').first().click();
                    await page.waitForTimeout(2000);
                    await page.locator('div:nth-child(3) > div:nth-child(2) > div > .form-input > .dropdown > .dropdown-button').first().click();
                    await page.waitForTimeout(2000);
                    await page.getByText('************').click();
                    await page.locator('div:nth-child(3) > div:nth-child(2) > div > .form-input > .dropdown > .dropdown-panel > .dropdown-panel-footer > span').first().click();
                    await page.waitForTimeout(2000);

                    break;
                case 'Use XFF from Client Request':
                    await page.waitForTimeout(2000);
                    await this.setToggle(page, this.locators.useXffToggle, value);
                    await page.waitForTimeout(2000);
                    await page.locator(`//span[contains(text(),"${value}")]/parent::*/following-sibling::*/span/span[@class="toggle-label on"]`);
                    await page.waitForTimeout(2000);
                    break;
                case 'Enforce Authentication':
                    await page.waitForTimeout(2000);
                    await this.setToggle(page, this.locators.enforceAuthToggle, value);
                    await page.waitForTimeout(2000);
                    await page.locator(`//span[contains(text(),"${value}")]/parent::*/following-sibling::*/span/span[@class="toggle-label on"]`);
                    await page.waitForTimeout(2000);
                    break;
                case 'Enable IP Surrogate':
                    await page.waitForTimeout(2000);
                    await this.setToggle(page, this.locators.enableIpSurrogateToggle, value);
                    await page.waitForTimeout(2000);
                    await page.locator(`//span[contains(text(),"${value}")]/parent::*/following-sibling::*/span/span[@class="toggle-label on"]`);
                    await page.waitForTimeout(2000);
                    break;
                case 'Enforce Surrogate IP for Known Browsers':
                    await page.waitForTimeout(2000);
                    await this.setToggle(page, this.locators.enforceSurrogateIpKnownBrowsersToggle, value);
                    await page.waitForTimeout(2000);
                    await page.locator(`//span[contains(text(),"${value}")]/parent::*/following-sibling::*/span/span[@class="toggle-label on"]`);
                    await page.waitForTimeout(2000);
                    break;
                case 'Enforce Firewall Control':
                    await page.waitForTimeout(2000);
                    await this.setToggle(page, this.locators.enforceFirewallControlToggle, value);
                    await page.waitForTimeout(2000);
                    await page.locator(`//span[contains(text(),"${value}")]/parent::*/following-sibling::*/span/span[@class="toggle-label on"]`);
                    await page.waitForTimeout(2000);
                    break;
                case 'Enable IPS Control':
                    await page.waitForTimeout(2000);
                    await this.setToggle(page, this.locators.enableIpsControlToggle, value);
                    await page.waitForTimeout(2000);
                    await page.locator(`//span[contains(text(),"${value}")]/parent::*/following-sibling::*/span/span[@class="toggle-label on"]`);
                    await page.waitForTimeout(2000);
                    break;
                case 'Enforce Bandwidth Control':
                    await page.waitForTimeout(2000);
                    await page.locator('//span[@data-key="ENABLE"]//span').click();
                    break;
                case 'Bandwidth Download (Mbps)':
                    await page.waitForTimeout(2000);
                    const bandwidthText = await page.locator(this.locators.bandwidthDownloadInput);
                    expect(bandwidthText).toContainText('Download (Mbps)');
                    await page.locator('//span[@data-help-property="dnBandwidth"]/following-sibling::span/input').fill(value);
                    break;
                case 'Bandwidth Upload (Mbps)':
                    await page.waitForTimeout(2000);
                    const uploadText = await page.locator(this.locators.bandwidthUploadInput);
                    expect(uploadText).toContainText('Upload (Mbps)');
                    await page.locator('//span[@data-help-property="upBandwidth"]/following-sibling::span/input').fill(value);
                    break;
                default:
                    await page.waitForTimeout(2000);
                    console.warn(`Field "${field}" not recognized or implemented.`);
            }
        }
        await page.locator(this.locators.saveButton).click();
        await page.waitForLoadState('domcontentloaded');
    }


    async deleteLocation(page: Page, locationName: string) {
        // Ensure the location is visible (e.g., after a search)
        const locationRow = page.locator(this.locators.gridRowByName(locationName)).first();
        await expect(locationRow).toBeVisible({ timeout: 10000 });

        // Use a row-specific locator for the edit button
        // This assumes the edit button is a span with title="Edit" within the row context
        const editButtonLocator = `//span[contains(text(),"${locationName}")]/ancestor::div[contains(@class,"row") or @role="row"][1]//span[@title="Edit"]`;
        const editButton = page.locator(editButtonLocator);
        await expect(editButton).toBeVisible();
        await editButton.click();

        const editPopupTitle = await page.locator('//span[contains(text(),"Edit Location")]');
        expect(editPopupTitle).toContainText('Edit Location');

        const deleteButton = await page.locator('//span[contains(text(),"Delete")]');
        expect(deleteButton).toBeVisible();
        await deleteButton.click();

        const confirmDelete = await page.locator('//span[contains(text(),"Confirm")]');
        expect(confirmDelete).toBeVisible();
        await confirmDelete.click();
        await page.waitForTimeout(4000);

        const successDelete = await page.locator('//span[contains(text(),"The item has been deleted.")]');
        expect(successDelete).toContainText('The item has been deleted.');
        await page.waitForTimeout(4000);
    }

    async editLocation(page: Page, locationName: string, description: string) {
        // Ensure the location is visible (e.g., after a search)
        const locationRow = page.locator(this.locators.gridRowByName(locationName)).first();
        await expect(locationRow).toBeVisible({ timeout: 10000 });

        // Use a row-specific locator for the edit button
        const editButtonLocator = `//span[contains(text(),"${locationName}")]/ancestor::div[contains(@class,"row") or @role="row"][1]//span[@title="Edit"]`;
        const editButton = page.locator(editButtonLocator);
        await expect(editButton).toBeVisible();
        await editButton.click();

        const editPopupTitle = await page.locator('//span[contains(text(),"Edit Location")]');
        expect(editPopupTitle).toContainText('Edit Location');
        // Find the description field and fill it
        await page.waitForTimeout(2000);
        await expect(page.locator(this.locators.descriptionInput)).toBeVisible();
        await page.waitForTimeout(2000);
        await page.locator(this.locators.descriptionInput).fill(description);
        await page.waitForTimeout(2000);
        // Click save
        await page.locator(this.locators.saveButton).click();
        // Wait for save to complete
        await page.waitForLoadState('domcontentloaded');
        await page.waitForTimeout(2000);
    }

    async isLocationVisibleInGrid(page: Page, locationName: string){
        console.log(`Checking if location "${locationName}" exists...`);
        const locationSearch = page.locator('(//input[@placeholder="Search..."])[1]');
        await locationSearch.click();
        await page.waitForTimeout(2000);
        await locationSearch.fill(locationName);
        await page.waitForTimeout(2000);
        await page.locator('(//span[@class="search-container"]//span[2])[1]').click();
        await page.waitForTimeout(5000);

        // Define the locator for the location row using the dynamic locationName
        const locationRow = page.locator(this.locators.gridRowByName(locationName)).first();

        // Check if this dynamically identified location row is visible
        if (await locationRow.isVisible()) {
            console.log(`Location "${locationName}" found.`);
            // Ensure the location is visible (e.g., after a search) - this was in the original logic
            await expect(locationRow).toBeVisible({ timeout: 10000 });

            // Use a row-specific locator for the edit button
            const editButtonLocator = `//span[contains(text(),"${locationName}")]/ancestor::div[contains(@class,"row") or @role="row"][1]//span[@title="Edit"]`;
            const editButton = page.locator(editButtonLocator);
            await expect(editButton).toBeVisible();
            await editButton.click();

            const editPopupTitle = await page.locator('//span[contains(text(),"Edit Location")]');
            await expect(editPopupTitle).toContainText('Edit Location'); // Corrected: Added await
            await page.waitForTimeout(2000);

            await page.locator('//span[contains(text(),"Delete")]').click();
            await page.waitForTimeout(2000);

            await page.locator('//span[contains(text(),"Confirm")]').click();
            await page.waitForTimeout(2000);

            const confirmDelete = await page.locator('//span[contains(text(),"The item has been deleted.")]');
            await expect(confirmDelete).toContainText('The item has been deleted.');
            await page.waitForTimeout(2000);
        } else {
            console.log(`Location "${locationName}" not found.`);
        }
    }

    async checkLocationVisibleInGrid(page: Page, locationName: string) {
        console.log(`Checking if location "${locationName}" exists...`);
        const locationSearch = page.locator('(//input[@placeholder="Search..."])[1]');
        await locationSearch.click();
        await page.waitForTimeout(2000);
        await locationSearch.fill(locationName);
        await page.waitForTimeout(2000);
        await page.locator('(//span[@class="search-container"]//span[2])[1]').click();
        await page.waitForTimeout(5000);

        const locationRow = await page.locator(`//span[contains(text(),"${locationName}")]`);
        await expect(locationRow).toBeVisible();
        await page.waitForTimeout(2000);
        }    

    async clickImportLocationButton(page: Page, importButton: string) {
        await page.waitForTimeout(2000);
        const importBtn = await page.locator('//i[@class="fas fa-arrow-circle-right"]');
        await expect(importBtn).toBeVisible();
        await importBtn.click();
    }

    async getImportLocationsModal(page: Page, modalTitle: string) {
        await page.waitForTimeout(2000);
        const importLocationTitle = await page.locator(`//span[contains(text(),"${modalTitle}")]`);
        await expect(importLocationTitle).toBeVisible();
        await expect(importLocationTitle).toContainText(modalTitle);
        await page.waitForTimeout(2000);
    }

    async getUploadFileButtonInImportModalAndSimulateCancel(page: Page, buttonName: string) {
        await page.waitForTimeout(2000);
        await page.locator('//input[@type="file"]').isVisible();
        // await page.waitForTimeout(2000);
        // await page.locator('//input[@type="file"]').click();
        await page.waitForTimeout(2000);
        console.log(`Clicked the button identified by class "${buttonName}" which should open the OS file dialog.`);
        await page.waitForTimeout(1000);
    }

    async clickCancelButtonOnImportModal(page: Page, buttonName: string) {
        await page.waitForTimeout(2000);
        const cancelButton = await page.locator(`//span[contains(text(),"${buttonName}")]`);
        await expect(cancelButton).toBeVisible();
        await cancelButton.click();
    }

    async clickDownloadSampleCsvFileButton(page: Page): Promise<Download> {
        const downloadIconLocator = '//span/i[@class="fas fa-file"]';
        const downloadButton = page.locator(downloadIconLocator);

        await expect(downloadButton, `Download button/icon with locator "${downloadIconLocator}" should be visible.`).toBeVisible();

        // Start waiting for the download event BEFORE clicking
        const downloadPromise = page.waitForEvent('download');

        await downloadButton.click();

        // Wait for the download to start and get the Download object
        const download = await downloadPromise;

        console.log(`Download initiated for file: ${download.suggestedFilename()}`);
        return download;
    }

    async clickDownloadDownloadFileButton(page: Page): Promise<Download> {
        const downloadIconLocator = '//span/i[@class="fas fa-download"]';
        const downloadButton = page.locator(downloadIconLocator);

        await expect(downloadButton, `Download button/icon with locator "${downloadIconLocator}" should be visible.`).toBeVisible();

        // Start waiting for the download event BEFORE clicking
        const downloadPromise = page.waitForEvent('download');

        await downloadButton.click();

        // Wait for the download to start and get the Download object
        const download = await downloadPromise;

        console.log(`Download initiated for file: ${download.suggestedFilename()}`);
        return download;
    }

    // --- Location Group Methods ---

    async navigateToLocationGroupsTab(page: Page) {
        console.log("Navigating to Location Groups tab...");
        await page.locator(this.locators.locationGroupsTab).click();
        await page.waitForTimeout(2000); // Wait for tab content to load
        // Add a verification that the tab is active if possible, e.g., by checking an attribute or a unique element on the tab
        console.log("Successfully navigated to Location Groups tab.");
    }

    async ensureLocationGroupDoesNotExist(page: Page, groupName: string, groupType: "Manual" | "Dynamic") {
        console.log(`Ensuring location group "${groupName}" of type "${groupType}" does not exist...`);
        // This might involve searching for the group and deleting it if found.
        // For simplicity, we'll assume a search and delete flow similar to locations.
        const searchInput = page.locator(this.locators.groupSearchInput);
        await searchInput.fill(groupName);
        await page.waitForTimeout(1000); // Wait for search results

        const groupRow = page.locator(this.locators.groupGridRowByName(groupName));
        if (await groupRow.isVisible()) {
            console.log(`Location group "${groupName}" found. Deleting it.`);
            await this.deleteLocationGroup(page, groupName, true); // Pass a flag to indicate it's part of a pre-check
        } else {
            console.log(`Location group "${groupName}" not found. No deletion needed.`);
        }
        await searchInput.fill(''); // Clear search
        await page.waitForTimeout(1000);
    }

    async createManualLocationGroup(page: Page, groupName: string) {
        console.log(`Creating manual location group: ${groupName}`);
        await page.locator(this.locators.addManualGroupButton).click();
        await expect(page.locator('//span[contains(text(),"Add Manual Group")]').nth(1)).toBeVisible(); // Modal title

        await page.locator(this.locators.manualGroupNameInput).fill(groupName);
        await page.locator(this.locators.groupFormNextButton).click();
        // Assuming no locations are selected for now for simplicity
        await page.locator(this.locators.groupFormSaveButton).click();
        await page.waitForTimeout(3000); // Wait for save and grid update
    }

    async isLocationGroupVisibleInGrid(page: Page, groupName: string): Promise<boolean> {
        console.log(`Checking if location group "${groupName}" is visible in the grid...`);
        const searchInput = page.locator(this.locators.groupSearchInput);
        await searchInput.fill(groupName);
        await page.waitForTimeout(2000); // Wait for search results

        const groupRow = page.locator(this.locators.groupGridRowByName(groupName));
        const isVisible = await groupRow.isVisible();

        await searchInput.fill(''); // Clear search
        await page.waitForTimeout(1000);
        return isVisible;
    }

    async updateLocationGroupDescription(page: Page, groupName: string, description: string) {
        console.log(`Updating description for location group "${groupName}" to "${description}"`);
        const searchInput = page.locator(this.locators.groupSearchInput);
        await searchInput.fill(groupName);
        await page.waitForTimeout(1000);

        await page.locator(this.locators.editGroupButtonForRow(groupName)).click();
        await expect(page.locator('//span[contains(text(),"Edit Manual Group")]')).toBeVisible(); // Modal title

        await page.locator(this.locators.groupDescriptionInput).fill(description);
        await page.locator(this.locators.groupFormNextButton).click();
        await page.locator(this.locators.groupFormSaveButton).click();
        await page.waitForTimeout(3000); // Wait for save and grid update

        await searchInput.fill(''); // Clear search
        await page.waitForTimeout(1000);
    }

    async verifyLocationGroupDescription(page: Page, groupName: string, expectedDescription: string): Promise<void> {
        console.log(`Verifying description for location group "${groupName}"`);
        const searchInput = page.locator(this.locators.groupSearchInput);
        await searchInput.fill(groupName);
        await page.waitForTimeout(2000);

        // This locator needs to be precise for your grid structure to get the description cell for the specific group
        const descriptionLocator = page.locator(`//span[contains(text(),"${groupName}")]/ancestor::div[@role="row"]//td[@aria-colindex="2"]//span`); // Example: assumes description is in the second column
        await expect(descriptionLocator).toBeVisible();
        await expect(descriptionLocator).toHaveText(expectedDescription);

        await searchInput.fill(''); // Clear search
        await page.waitForTimeout(1000);
    }

    async deleteLocationGroup(page: Page, groupName: string, isPreCheck: boolean = false) {
        console.log(`Deleting location group: ${groupName}`);
        if (!isPreCheck) { // Only search if not part of a pre-check that already searched
            const searchInput = page.locator(this.locators.groupSearchInput);
            await searchInput.fill(groupName);
            await page.waitForTimeout(1000);
        }

        await page.locator(this.locators.editGroupButtonForRow(groupName)).click();
        // Verify edit modal title (e.g., "Edit Manual Group" or "Edit Dynamic Group")
        await expect(page.locator('//span[contains(text(),"Edit Manual Group")]').or(page.locator('//span[contains(text(),"Edit Dynamic Group")]'))).toBeVisible();

        await page.locator(this.locators.deleteGroupButtonOnForm).click();
        await expect(page.locator('//span[contains(text(),"Confirm Deletion")]')).toBeVisible(); // Confirmation modal title
        await page.locator(this.locators.confirmGroupDeletionButton).click();
        await page.waitForTimeout(4000); // Wait for deletion and grid update
        // Optionally, verify a success message for deletion if one appears
        console.log(`Location group "${groupName}" deleted.`);
    }
    async clickAddManualGroupButton(page: Page) {
        await page.waitForTimeout(4000);

        const pageTitleText = await page.locator(this.locators.pageTitle);
        await expect(pageTitleText).toContainText('Location Management');

        const locationGroupOption = await page.locator('//li[@data-label="LOCATION_GROUPS"]/span[1]');
        await expect(locationGroupOption).toBeVisible();
        await locationGroupOption.click();
        await page.waitForTimeout(2000);

        const addManualGroup = await page.locator('//span[contains(text(),"Add") and contains(text(),"Manual Group")]');
        await expect(addManualGroup).toBeVisible();
        await addManualGroup.click();
        await page.waitForTimeout(2000);
    }

    async clickAddDynynamicGroupButton(page: Page) {
        const pageTitleText = await page.locator(this.locators.pageTitle);
        await expect(pageTitleText).toContainText('Location Management');

        const locationGroupOption = await page.locator('//li[@data-label="LOCATION_GROUPS"]/span[1]');
        await expect(locationGroupOption).toBeVisible();
        await locationGroupOption.click();
        await page.waitForTimeout(2000);

        const addDynamicGroup = await page.locator('//span[contains(text(),"Add") and contains(text(),"Dynamic Group")]');
        await expect(addDynamicGroup).toBeVisible();
        await addDynamicGroup.click();
        await page.waitForTimeout(2000);
    }

    async addManualGroup(page: Page, groupName: string): Promise<void> {
        const addManualGroup_PopupTitle = await page.locator('//span[contains(text(),"Add Manual Group")]');
        await expect(addManualGroup_PopupTitle).toBeVisible();
        await expect(addManualGroup_PopupTitle).toContainText('Add Manual Group');

        const optionGroupInformation = await page.locator('//span[contains(text(),"Group Information")]');
        await expect(optionGroupInformation).toContainText('Group Information');
        await page.waitForTimeout(2000);

        await page.locator('//input[@placeholder="Enter Text"]').click();
        await page.waitForTimeout(2000);
        await page.locator('//input[@placeholder="Enter Text"]').fill(groupName);
        await page.waitForTimeout(2000);

        await page.locator('//a[@data-action-id="next"]').click();
        await page.waitForTimeout(2000);

        const optionSelectLocation = await page.locator('//span[contains(text(),"Select Locations")]');
        await expect(optionSelectLocation).toContainText('Select Locations');
        await page.waitForTimeout(2000);

        await page.locator('//a[@data-action-id="save"]').click();
        await page.waitForTimeout(2000);
    }

    async verifyAddedManualGroup(page: Page) {
        await page.waitForTimeout(4000);
        const successMessage = await page.locator(this.locators.successMessage);
        await expect(successMessage).toContainText('All changes have been saved.');
        await page.waitForTimeout(2000);
    }

    async searchManualGroup(page: Page, groupName: string): Promise<void> {
        await page.waitForTimeout(2000);
        const searchInput = await page.locator(this.locators.locationGroupSearch);
        await expect(searchInput).toBeVisible();
        await searchInput.click();
        await page.waitForTimeout(2000);

        await searchInput.fill(groupName);
        await page.waitForTimeout(2000);
        await page.getByRole('textbox', { name: 'Search...' }).press('Enter');

        await page.waitForTimeout(2000);
        const results = await page.locator(`//span[contains(text(),"${groupName}")]`);
        await expect(results).toBeVisible();
        await page.waitForTimeout(2000);
    }


    async searchManualGroup_And_Verify_Description(page: Page, groupDescription: string): Promise<void> {
        await page.waitForTimeout(2000);
        const searchInput = await page.locator(this.locators.locationGroupSearch);
        await expect(searchInput).toBeVisible();
        await searchInput.click();
        await page.waitForTimeout(2000);

        await searchInput.fill("Automation_ManualGroup");
        await page.waitForTimeout(2000);
        await searchInput.press('Enter');

        await page.waitForTimeout(2000);
        const results1 = await page.locator(`//span[contains(text(),"${groupDescription}")]`);
        await expect(results1).toBeVisible();
        await page.waitForTimeout(2000);

    }



    async editManualGroup(page: Page) {
        await page.waitForTimeout(2000);
        await page.locator('//div[@data-id="view1011_1"]//span[@title="Edit"]').click();

        const editPopupTitle = await page.locator('//span[contains(text(),"Edit Manual Group")]');
        await expect(editPopupTitle).toContainText('Edit Manual Group');
        await page.waitForTimeout(2000);

        const description = await page.locator('//div[@data-help-property="comments"]/span');
        await expect(description).toBeVisible();
        await page.waitForTimeout(2000);

        await page.locator('//textarea').click();
        await page.waitForTimeout(2000);
        await page.locator('//textarea').fill('Automation_Description');
        await page.waitForTimeout(2000);

        await page.locator('//a[@data-action-id="next"]').click();
        await page.waitForTimeout(2000);

        const optionSelectLocation = await page.locator('//span[contains(text(),"Select Locations")]');
        await expect(optionSelectLocation).toContainText('Select Locations');
        await page.waitForTimeout(2000);

        await page.locator('//a[@data-action-id="save"]').click();
        await page.waitForTimeout(2000);
    }

    async deleteManualGroup(page: Page) {
        await page.waitForTimeout(2000);
        await page.locator('//div[@data-id="view1011_1"]//span[@title="Edit"]').click();

        const editPopupTitle = await page.locator('//span[contains(text(),"Edit Manual Group")]');
        await expect(editPopupTitle).toContainText('Edit Manual Group');
        await page.waitForTimeout(2000);

        await page.locator('//a[@data-action-id="delete"]').click();
        await page.waitForTimeout(2000);

        await page.locator('//span[contains(text(),"Confirm")]').click();
        await page.waitForTimeout(2000);

        const deleteSuccessMessage = await page.locator('//span[contains(text(),"The item has been deleted.")]');
        await expect(deleteSuccessMessage).toContainText('The item has been deleted.');
        await page.waitForTimeout(2000);
    }



    async checkDynamicGroupDoesNotExists(page: Page, groupName: string): Promise<void> {
        const pageTitleText = await page.locator(this.locators.pageTitle);
        await expect(pageTitleText).toContainText('Location Management');

        const locationGroupOption = await page.locator('//li[@data-label="LOCATION_GROUPS"]/span[1]');
        await expect(locationGroupOption).toBeVisible();
        await locationGroupOption.click();
        await page.waitForTimeout(2000);

        const searchInput = await page.locator(this.locators.locationGroupSearch);
        await expect(searchInput).toBeVisible();
        await searchInput.click();
        await page.waitForTimeout(2000);

        await searchInput.fill(groupName);
        await page.waitForTimeout(2000);
        await page.getByRole('textbox', { name: 'Search...' }).press('Enter');

        await page.waitForTimeout(2000);
        const results = await page.locator(`//span[contains(text(),"${groupName}")]`);
        await page.waitForTimeout(2000);
        if (await results.isVisible()) {
            await page.waitForTimeout(2000);
            // Use the specific edit button for the group
            await page.locator('//div[@data-id="view1011_1"]//span[@title="Edit"]').click();
            await page.waitForTimeout(2000);

            const editPopupTitle = await page.locator('//span[contains(text(),"Edit Dynamic Group")]');
            await expect(editPopupTitle).toContainText('Edit Dynamic Group');
            await page.waitForTimeout(2000);

            await page.locator('//a[@data-action-id="delete"]').click();
            await page.waitForTimeout(2000);

            await page.locator('//span[contains(text(),"Confirm")]').click();
            await page.waitForTimeout(2000);

            const deleteSuccessMessage = await page.locator('//span[contains(text(),"The item has been deleted.")]');
            await expect(deleteSuccessMessage).toContainText('The item has been deleted.');
        }
        else {
            await page.waitForTimeout(2000);
            console.log("No Test Data is available.");
        }
    }

    async confirmDeleteletionOfGroup(page: Page, groupName: string): Promise<void> {
        await page.waitForTimeout(2000);
        const searchInput = await page.locator(this.locators.locationGroupSearch);
        await expect(searchInput).toBeVisible();
        await searchInput.click();
        await page.waitForTimeout(2000);

        await searchInput.fill(groupName);
        await page.waitForTimeout(2000);
        await page.getByRole('textbox', { name: 'Search...' }).press('Enter');

        await page.waitForTimeout(2000);
        const results = await page.locator(`//span[contains(text(),"${groupName}")]`);
        await expect(results).not.toBeVisible();
        await page.waitForTimeout(2000);
    }


    async addDynamicGroup(page: Page, groupName: string): Promise<void> {
        const addDynamicGroup_PopupTitle = await page.locator('//span[contains(text(),"Add Dynamic Group")]');
        await expect(addDynamicGroup_PopupTitle).toBeVisible();
        await expect(addDynamicGroup_PopupTitle).toContainText('Add Dynamic Group');

        const optionGroupInformation = await page.locator('//span[contains(text(),"Group Information")]');
        await expect(optionGroupInformation).toContainText('Group Information');
        await page.waitForTimeout(2000);

        await page.locator('//input[@placeholder="Enter Text"]').click();
        await page.waitForTimeout(2000);
        await page.locator('//input[@placeholder="Enter Text"]').fill(groupName);
        await page.waitForTimeout(2000);

        const newCondition = await page.locator('//span[contains(text()," Add New Condition")]');
        await expect(newCondition).toBeVisible();
        await page.waitForTimeout(2000);
        await newCondition.click();
        await page.waitForTimeout(2000);
        await page.locator('//li[@data-name="City/State/Province"]').click();
        await page.waitForTimeout(2000);
        await page.locator('(//input[@placeholder="Enter Text"])[2]').click();
        await page.waitForTimeout(2000);
        await page.locator('(//input[@placeholder="Enter Text"])[2]').fill('London');
        await page.waitForTimeout(2000);

        await page.locator('//a[@data-action-id="next"]').click();
        await page.waitForTimeout(2000);

        const optionPreviewLocation = await page.locator('//span[contains(text(),"Preview Locations")]');
        await expect(optionPreviewLocation).toContainText('Preview Locations');
        await page.waitForTimeout(2000);

        await page.locator('//a[@data-action-id="save"]').click();
        await page.waitForTimeout(2000);

    }
    async verifyAddedDynamicGroup(page: Page) {
        await page.waitForTimeout(4000);
        const successMessage = await page.locator(this.locators.successMessage);
        await expect(successMessage).toContainText('All changes have been saved.');
        await page.waitForTimeout(2000);
    }

    async searchDynamicGroup(page: Page, groupName: string): Promise<void> {
        await page.waitForTimeout(2000);
        const searchInput = await page.locator(this.locators.locationGroupSearch);
        await expect(searchInput).toBeVisible();
        await searchInput.click();
        await page.waitForTimeout(2000);

        await searchInput.fill(groupName);
        await page.waitForTimeout(2000);
        await page.getByRole('textbox', { name: 'Search...' }).press('Enter');

        await page.waitForTimeout(2000);
        const results = await page.locator(`//span[contains(text(),"${groupName}")]`);
        await expect(results).toBeVisible();
        await page.waitForTimeout(2000);
    }

    async editDynamicGroup(page: Page) {
        await page.waitForTimeout(2000);
        await page.locator('//div[@data-id="view1011_1"]//span[@title="Edit"]').click();

        const editPopupTitle = await page.locator('//span[contains(text(),"Edit Dynamic Group")]');
        await expect(editPopupTitle).toContainText('Edit Dynamic Group');
        await page.waitForTimeout(2000);

        const description = await page.locator('//div[@data-help-property="comments"]/span');
        await expect(description).toBeVisible();
        await page.waitForTimeout(2000);

        await page.locator('//textarea').click();
        await page.waitForTimeout(2000);
        await page.locator('//textarea').fill('Automation_Description');
        await page.waitForTimeout(2000);

        await page.locator('//a[@data-action-id="next"]').click();
        await page.waitForTimeout(2000);

        const optionPreviewLocation = await page.locator('//span[contains(text(),"Preview Locations")]');
        await expect(optionPreviewLocation).toContainText('Preview Locations');
        await page.waitForTimeout(2000);

        await page.locator('//a[@data-action-id="save"]').click();
        await page.waitForTimeout(2000);
    }

    async deleteDynamicGroup(page: Page) {
        await page.waitForTimeout(2000);
        await page.locator('//div[@data-id="view1011_1"]//span[@title="Edit"]').click();

        const editPopupTitle = await page.locator('//span[contains(text(),"Edit Dynamic Group")]');
        await expect(editPopupTitle).toContainText('Edit Dynamic Group');
        await page.waitForTimeout(2000);

        await page.locator('//a[@data-action-id="delete"]').click();
        await page.waitForTimeout(2000);

        await page.locator('//span[contains(text(),"Confirm")]').click();
        await page.waitForTimeout(2000);

        const deleteSuccessMessage = await page.locator('//span[contains(text(),"The item has been deleted.")]');
        await expect(deleteSuccessMessage).toContainText('The item has been deleted.');
        await page.waitForTimeout(2000);
    }

    async searchDynamicGroup_And_Verify_Description(page: Page, groupDescription: string): Promise<void> {
        const searchInput = await page.locator(this.locators.locationGroupSearch);
        await expect(searchInput).toBeVisible();
        await searchInput.click();
        await page.waitForTimeout(2000);

        await searchInput.fill("Automation_DynamicGroup");
        await page.waitForTimeout(2000);
        await page.getByRole('textbox', { name: 'Search...' }).press('Enter');

        await page.waitForTimeout(2000);
        const results1 = await page.locator(`//span[contains(text(),"${groupDescription}")]`);
        await expect(results1).toBeVisible();
        await page.waitForTimeout(2000);
    }

    async checkManualGroupDoesNotExists(page: Page, groupName: string): Promise<void> {
        const pageTitleText = await page.locator(this.locators.pageTitle);
        await expect(pageTitleText).toContainText('Location Management');

        const locationGroupOption = await page.locator('//li[@data-label="LOCATION_GROUPS"]/span[1]');
        await expect(locationGroupOption).toBeVisible();
        await locationGroupOption.click();
        await page.waitForTimeout(2000);

        const searchInput = await page.locator(this.locators.locationGroupSearch);
        await expect(searchInput).toBeVisible();
        await searchInput.click();
        await page.waitForTimeout(2000);

        await searchInput.fill(groupName);
        await page.waitForTimeout(2000);
        await page.getByRole('textbox', { name: 'Search...' }).press('Enter');

        await page.waitForTimeout(2000);
        const results = await page.locator(`//span[contains(text(),"${groupName}")]`);
        await page.waitForTimeout(2000);
        if (await results.isVisible()) {
            await page.waitForTimeout(2000);
            // Use the specific edit button for the group
            await page.locator('//div[@data-id="view1011_1"]//span[@title="Edit"]').click();

            const editPopupTitle = await page.locator('//span[contains(text(),"Edit Manual Group")]');
            await expect(editPopupTitle).toContainText('Edit Manual Group');

            await page.locator('//a[@data-action-id="delete"]').click();
            await page.waitForTimeout(2000);

            await page.locator('//span[contains(text(),"Confirm")]').click();
            await page.waitForTimeout(2000);

            const deleteSuccessMessage = await page.locator('//span[contains(text(),"The item has been deleted.")]');
            await expect(deleteSuccessMessage).toContainText('The item has been deleted.');
        }
        else {
            await page.waitForTimeout(2000);
            console.log("No Test Data is available.");
        }
    }

}