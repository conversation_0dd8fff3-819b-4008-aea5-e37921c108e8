import { Page, expect } from "@playwright/test";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

export default class NetworkServicesPage {
  // Locators
  private readonly networkServicesTab = '//span[@title="Network Services"]';
  private readonly serviceGroupsTab = '//li[@data-label="SERVICEGROUPS"]/span'; 
  private readonly addNetworkServiceButton = '//span[contains(text(),"Add Network Service")]'; 
  private readonly addServiceGroupButton = '//span[contains(text(),"Add Network Service Group")]'; 
  private readonly searchInput = '//input[@aria-label="Filter"]';
  private readonly applyFilterButton = '//button[@aria-label="Apply filter"]';
  private readonly nameInput = '//input[@aria-label="name"]';
  private readonly descriptionInput = '//textarea[@aria-label="description"]';
  private readonly saveButton = '//span[contains(text(),"Save")]';
  private readonly deleteButton = '//span[contains(text(),"Delete")]';
  private readonly confirmDeleteButton = '//span[contains(text(),"Confirm")]';
  private readonly tcpPortInput = '//textarea[@aria-labelledby="TCP_DEST_PORTS"]'; 
  private readonly udpPortInput = '//textarea[@aria-labelledby="UDP_DEST_PORTS"]'; 
  private readonly protocolDropdown = '//div[@class="dropdown"]/span/span[2]'; 
  private readonly serviceDropdown = '//div[@data-testid="multi-dropdown"]/div/div/span[2]'; 
  private readonly searchResultsLocator = (name: string) => `//div[@data-testid="cell-name"]//div[contains(text(),"${name}")]`;
  private readonly noResultsLocator = '//div[contains(text(),"No matching data found")]';
  private readonly sortingHeader = (columnName: string) => `//div[@data-testid="columnHeader-${columnName.toLowerCase()}"]//div[contains(text(),"${columnName}")]`;
  private readonly sortButton = (columnName: string) => `//div[@data-testid="columnHeader-${columnName.toLowerCase()}"]//div[@class="suffix"]/button`;
  private readonly editButton = '//i[@aria-label="EDIT"]';
  private readonly successMessage = '//span[contains(text(),"All changes have been saved.")]';
  private readonly itemDeletedMessage = '//span[contains(text(),"The item has been deleted.")]';

  constructor() {}

  async navigateToNetworkServices(page: Page): Promise<void> {
    console.log("Executing navigateToNetworkServices...");
  
    const networkServicesPath = "administration/network-services";
    let targetUrl = "";
  
    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
    }
  
    if (url.includes("console")) {
      targetUrl = url + "internet-saas#" + networkServicesPath;
      await page.goto(targetUrl);
      console.log("Page navigation initiated (console URL) to Network Services.");
      await page.waitForTimeout(80000); 
    } else {
      targetUrl = url + "#" + networkServicesPath;
      await page.goto(targetUrl);
      console.log("Page navigation initiated (non-console URL) to Network Services.");
      await page.waitForTimeout(30000);
    }
  
    await expect(page).toHaveURL(new RegExp(networkServicesPath.replace(/\//g, '\\/')));
    console.log("Successfully navigated to Network Services screen.");
  }

  async deleteNetworkService(page: Page, name: string): Promise<void> {
    console.log(`Attempting to delete Network Service: ${name}`);
    await page.locator(this.networkServicesTab).isVisible();
    await page.waitForTimeout(1000);

    const isPresent = await this.searchAndVerifyPresence(page, name);
    if (isPresent) {
      await page.locator(this.editButton).click();
      await page.waitForTimeout(2000);
      await page.locator(this.deleteButton).click();
      await page.waitForTimeout(1000);
      await page.locator(this.confirmDeleteButton).click();
      await page.waitForTimeout(2000);
      await expect(page.locator(this.itemDeletedMessage)).toBeVisible();
      console.log(`Network Service ${name} deleted.`);
    } else {
      console.log(`Network Service ${name} not found, no deletion needed.`);
    }
  }

  async deleteNetworkServiceGroup(page: Page, name: string): Promise<void> {
    console.log(`Attempting to delete Network Service Group: ${name}`);
    await page.waitForTimeout(1000); 
    await page.locator(this.serviceGroupsTab).click();
    await page.waitForTimeout(1000); 

    const isPresent = await this.searchAndVerifyPresence(page, name);
    if (isPresent) {
      await page.locator(this.editButton).click();
      await page.waitForTimeout(2000);
      await page.locator(this.deleteButton).click();
      await page.waitForTimeout(1000);
      await page.locator(this.confirmDeleteButton).click();
      await page.waitForTimeout(2000);
      await expect(page.locator(this.itemDeletedMessage)).toBeVisible();
      console.log(`Network Service Group ${name} deleted.`);
    } else {
      console.log(`Network Service Group ${name} not found, no deletion needed.`);
    }
  }

  private async searchAndVerifyPresence(page: Page, name: string): Promise<boolean> {
    await page.waitForTimeout(1000);
    await page.locator(this.searchInput).fill(name);
    await page.waitForTimeout(1000);
    await page.locator(this.applyFilterButton).click();
    await page.waitForTimeout(2000);
    const isVisible = await page.locator(this.searchResultsLocator(name)).first().isVisible();
    if (isVisible) {
      await expect(page.locator(this.searchResultsLocator(name)).first()).toContainText(name);
      return true;
    } else {
      // Check for "No matching data found" message
      const noResultsVisible = await page.locator(this.noResultsLocator).isVisible();
      if (noResultsVisible) {
        await expect(page.locator(this.noResultsLocator)).toBeVisible();
      }
      return false;
    }
  }

  async addNetworkService(page: Page, name: string, tcpPort: string, udpPort: string): Promise<void> {
    console.log(`Adding Network Service: ${name}`);
    await page.locator(this.networkServicesTab).isVisible();
    await page.waitForTimeout(1000);
    await page.locator(this.addNetworkServiceButton).click();
    await page.waitForTimeout(2000);
    await page.locator(this.nameInput).fill(name);
    await page.locator(this.tcpPortInput).fill(tcpPort);

    await page.locator(this.udpPortInput).fill(udpPort);
    await page.locator(this.saveButton).click();
    await page.waitForTimeout(2000);
    await expect(page.locator(this.successMessage)).toBeVisible();
    console.log(`Network Service ${name} added.`);
  }

  async editNetworkService(page: Page, name: string, newDescription: string): Promise<void> {
    console.log(`Editing Network Service: ${name}`);
    await page.locator(this.networkServicesTab).isVisible();
    await page.waitForTimeout(1000);
    await this.searchAndVerifyPresence(page, name);
    await page.locator(this.editButton).click();
    await page.waitForTimeout(2000);
    await page.locator(this.descriptionInput).fill(newDescription);
    await page.locator(this.saveButton).click();
    await page.waitForTimeout(2000);
    await expect(page.locator(this.successMessage)).toBeVisible();
    console.log(`Network Service ${name} edited.`);
  }

  async verifyNetworkServicePresent(page: Page, name: string): Promise<void> {
    await page.locator(this.networkServicesTab).isVisible();
    await page.waitForTimeout(1000);
    await expect(await this.searchAndVerifyPresence(page, name)).toBeTruthy();
    console.log(`Network Service ${name} is present.`);
  }

  async verifyNetworkServiceNotPresent(page: Page, name: string): Promise<void> {
    await page.locator(this.networkServicesTab).isVisible();
    await page.waitForTimeout(1000);
    await expect(await this.searchAndVerifyPresence(page, name)).toBeFalsy();
    console.log(`Network Service ${name} is not present.`);
  }

  async searchNetworkServiceByPort(page: Page, port: string): Promise<void> {
    console.log(`Searching Network Service by port: ${port}`);
    await page.locator(this.networkServicesTab).isVisible();
    await page.waitForTimeout(1000);
    await page.locator(this.searchInput).fill(port);
    await page.waitForTimeout(1000);
    await page.locator(this.applyFilterButton).click();
    await page.waitForTimeout(2000);
    // Assuming search by port shows the service name in the name column or a relevant column
    await expect(page.locator(`//div[contains(text(),"${port}")]`).first()).toBeVisible();
    console.log(`Network Service with port ${port} found.`);
  }


  async sortNetworkServicesByNameAndDescription(page: Page): Promise<void> {
    console.log("Verifying sorting for Network Services by Name and Description...");
    await page.locator(this.networkServicesTab).isVisible();
    await page.waitForTimeout(1000);

    // Sort by Name
    let sortNameButton = page.locator(this.sortButton("name"));
    await expect(sortNameButton).toBeVisible();
    await sortNameButton.click(); // Ascending
    await page.waitForTimeout(2000);
    await sortNameButton.click(); // Descending
    await page.waitForTimeout(2000);
    console.log("Sorted Network Services by Name.");

    // Sort by Description (assuming a description column exists and is sortable)
    let sortDescriptionButton = page.locator(this.sortButton("description"));
    if (await sortDescriptionButton.isVisible()) {
      await sortDescriptionButton.click(); // Ascending
      await page.waitForTimeout(2000);
      await sortDescriptionButton.click(); // Descending
      await page.waitForTimeout(2000);
      console.log("Sorted Network Services by Description.");
    } else {
      console.log("Description column sort button not found for Network Services, skipping.");
    }
  }

  async filterNetworkServicesByProtocol(page: Page, protocol: string): Promise<void> {
    console.log(`Filtering Network Services by protocol: ${protocol}`);
    await page.locator(this.networkServicesTab).isVisible();
    await page.waitForTimeout(1000);

    await page.locator(this.protocolDropdown).click();
    await page.waitForTimeout(1000);
    await page.locator(`//li[text()="${protocol}"]`).click();
    await page.waitForTimeout(2000);
    console.log(`Network Services filtered by protocol ${protocol}.`);
  }

  async addNetworkServiceGroup(page: Page, name: string, service: string): Promise<void> {
    console.log(`Adding Network Service Group: ${name}`);
    await page.locator(this.serviceGroupsTab).click();
    await page.waitForTimeout(1000);
    await page.locator(this.addServiceGroupButton).click();
    await page.waitForTimeout(2000);
    await page.locator(this.nameInput).fill(name);
    await page.locator(this.serviceDropdown).click();
    
    await page.locator('//span[contains(text(),"Clear Selection")]').click();
    const search = await page.locator('//div[@data-testid="multi-dropdown"]//input[@placeholder="Search..."]');
    await search.isVisible();
    await page.waitForTimeout(1000);
    await search.click();
    await search.fill(`${service}`);

    await page.locator(`//label/div[contains(text(),"${service}")]`).click();
    const selectedItems = page.locator(`//span[contains(text(),"Selected Items ( 1 )")]`);
    await selectedItems.isVisible();
    
    await page.locator('//span[contains(text(),"Done")]').click(); 
    await page.locator(this.saveButton).click();
    await page.waitForTimeout(2000);
    await expect(page.locator(this.successMessage)).toBeVisible();
    console.log(`Network Service Group ${name} added.`);
  }

  async editNetworkServiceGroup(page: Page, name: string, description: string): Promise<void> {
    console.log(`Editing Network Service Group: ${name}`);
    await page.locator(this.serviceGroupsTab).click();
    await page.waitForTimeout(1000);
    await this.searchAndVerifyPresence(page, name);
    await page.locator(this.editButton).click();
    await page.waitForTimeout(2000);
    await page.locator(this.descriptionInput).fill(description);
    await page.locator(this.saveButton).click();
    await page.waitForTimeout(2000);
    await expect(page.locator(this.successMessage)).toBeVisible();
    console.log(`Network Service Group ${name} edited.`);
  }

  async verifyNetworkServiceGroupPresent(page: Page, name: string): Promise<void> {
    await page.locator(this.serviceGroupsTab).click();
    await page.waitForTimeout(1000);
    await expect(await this.searchAndVerifyPresence(page, name)).toBeTruthy();
    console.log(`Network Service Group ${name} is present.`);
  }

  async verifyNetworkServiceGroupNotPresent(page: Page, name: string): Promise<void> {
    await page.locator(this.serviceGroupsTab).click();
    await page.waitForTimeout(1000);
    await expect(await this.searchAndVerifyPresence(page, name)).toBeFalsy();
    console.log(`Network Service Group ${name} is not present.`);
  }

  async searchNetworkServiceGroup(page: Page, name: string): Promise<void> {
    console.log(`Searching Network Service Group: ${name}`);
    await page.locator(this.serviceGroupsTab).click();
    await page.waitForTimeout(1000);
    await expect(await this.searchAndVerifyPresence(page, name)).toBeTruthy();
    console.log(`Network Service Group ${name} found.`);
  }

  async sortNetworkServiceGroupsByNameAndDescription(page: Page): Promise<void> {
    console.log("Verifying sorting for Network Service Groups by Name and Description...");
    await page.locator(this.serviceGroupsTab).click();
    await page.waitForTimeout(1000);

    // Sort by Name
    let sortNameButton = page.locator(this.sortButton("name"));
    await expect(sortNameButton).toBeVisible();
    await sortNameButton.click(); // Ascending
    await page.waitForTimeout(2000);
    await sortNameButton.click(); // Descending
    await page.waitForTimeout(2000);
    console.log("Sorted Network Service Groups by Name.");

    // Sort by Description (assuming a description column exists and is sortable)
    let sortDescriptionButton = page.locator(this.sortButton("description"));
    if (await sortDescriptionButton.isVisible()) {
      await sortDescriptionButton.click(); // Ascending
      await page.waitForTimeout(2000);
      await sortDescriptionButton.click(); // Descending
      await page.waitForTimeout(2000);
      console.log("Sorted Network Service Groups by Description.");
    } else {
      console.log("Description column sort button not found for Network Service Groups, skipping.");
    }
  }
}