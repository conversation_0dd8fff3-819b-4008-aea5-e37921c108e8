import { Page, expect } from "@playwright/test";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

interface MalwarePolicyFields {
  alert: string;
  passwordProtectedToggleButtonState: string;
  unscannableToggleButtonState: string;
  trafficInspectionTitle: string;
  inspectInboundTrafficTitle: string;
  inspectInboundTrafficToggle: string; // CSS selector
  malwarePolicyNav: string;
  securityExceptionsTab: string;
  passwordProtectedFilesTitle: string;
  unscannableFilesTitle: string;
  doNotScanContentTitle: string;
  testNetUrlItem: string;
  removeItemIcon: string;
  noMatchingItemsFoundText: string;
  textAreaForAddItems: string;
  addItemsButtonInListBuilder: string;
  bodyLocator: string; // CSS selector
  removeButtonText: string;
  removeAllDropdownItem: string;
  removePageDropdownItem: string;
  cancelButtonText: string;
  allChangesCanceledNotification: string;
}
// Add mappings for descriptive names to locators
interface MalwarePolicyFieldMappings extends MalwarePolicyFields {
  fileExceptionToggleMappings: Record<string, { testId: string; activeStateLocator: string }>;
  protocolToggleMappings: Record<string, string>; // Maps protocol name to testId
  threatTypeToggleMappings: Record<string, string>; // Maps threat type name to testId
}

class MalwarePolicy {
  fields: MalwarePolicyFieldMappings;

  constructor() {
    this.fields = {
      // Existing fields
      alert: "//div[@class='page-title ']//span[text()='Alerts']",
      passwordProtectedToggleButtonState: '//div[@data-testid="radioButton-blockPasswordProtectedArchiveFiles"]//button[contains(@class, "radio-button") and contains(@class, "active")]',
      unscannableToggleButtonState: '//div[@data-testid="radioButton-blockUnscannableFiles"]//button[contains(@class, "radio-button") and contains(@class, "active")]',
      trafficInspectionTitle: "//span[contains(text(), 'Traffic Inspection')]",
      inspectInboundTrafficTitle: "//span[contains(text(), 'Inspect Inbound Traffic')]",
      inspectInboundTrafficToggle: "[data-testid='toggleSwitch-inspectInbound'] div[role='checkbox']",
      malwarePolicyNav: "(//span[contains(text(), 'Malware Policy')])[1]",
      securityExceptionsTab: "(//span[contains(text(), 'Security Exceptions')])[1]",
      passwordProtectedFilesTitle: "//span[contains(text(), 'Password-Protected Files')]",
      unscannableFilesTitle: "//span[contains(text(), 'Unscannable Files')]",
      doNotScanContentTitle: "//span[contains(text(), 'Do Not Scan Content from')]",
      testNetUrlItem: '//span[contains(text(),"test.net")]',
      removeItemIcon: '//i[@aria-label="Remove "]',
      noMatchingItemsFoundText: '//div[contains(text(),"No matching items found")]',
      textAreaForAddItems: '//textarea',
      addItemsButtonInListBuilder: '//div[@data-testid="listBuilder-bypassUrlsDetailsList"]//button[normalize-space(.)="Add Items"]',
      bodyLocator: 'body',
      removeButtonText: '//span[contains(text(),"Remove")]',
      removeAllDropdownItem: '//ul/li[contains(text(),"Remove All")]',
      removePageDropdownItem: '//ul/li[contains(text(),"Remove Page")]',
      cancelButtonText: '//span[contains(text(),"Cancel")]',
      allChangesCanceledNotification: '//span[contains(text(),"All changes have been canceled.")]',
      // Initialize mappings
      fileExceptionToggleMappings: {
        "Password-Protected Files": {
          testId: "radioButton-blockPasswordProtectedArchiveFiles",
          activeStateLocator: "//div[@data-testid='radioButton-blockPasswordProtectedArchiveFiles']//button[contains(@class, 'radio-button') and contains(@class, 'active')]"
        },
        "Unscannable Files": {
          testId: "radioButton-blockUnscannableFiles",
          activeStateLocator: "//div[@data-testid='radioButton-blockUnscannableFiles']//button[contains(@class, 'radio-button') and contains(@class, 'active')]"
        }
      },
      protocolToggleMappings: {
        "Inspect HTTP": 'toggleSwitch-inspectHttp',
        "Inspect FTP over HTTP": 'toggleSwitch-inspectFtpOverHttp',
        "Inspect FTP": 'toggleSwitch-inspectFtp'
      },
      threatTypeToggleMappings: {
        "Unwanted Applications": "unwantedApplicationsBlocked",
        "Trojans": "trojanBlocked",
        "Worms": "wormBlocked",
        "Ransomware": "ransomwareBlocked",
        "Remote Access Tool": "remoteAccessToolBlocked",
        "Other Viruses": "virusBlocked",
        "Adware": "adwareBlocked",
        "Spyware": "spywareBlocked"
      }
    };
  }

  async navigateToMalwarePolicy(page: Page): Promise<void> {
    console.log("Executing navigateToMalwarePolicy...");

    console.log("The Url used is : - " + url);
    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
    }

    const malwarePolicyPath = "policy/web/malware-protection";
    let targetUrl = "";

    if (url.includes("console")) {
      targetUrl = url + "internet-saas#" + malwarePolicyPath;
      await page.goto(targetUrl);
      console.log("Page navigation initiated (console URL).");
      await page.waitForTimeout(25000);
    } else {
      targetUrl = url + "#" + malwarePolicyPath;
      await page.goto(targetUrl);
      console.log("Page navigation initiated (non-console URL).");
      await page.waitForTimeout(19000);
    }
    await expect(page).toHaveURL(new RegExp(malwarePolicyPath.replace(/\//g, '\\/')));
    console.log("Successfully navigated to Malware Policy screen.");
  }

  async inspectTrafficToggle(page: Page, trafficDirection: "Inspect Inbound Traffic" | "Inspect Outbound Traffic"): Promise<void> {
    console.log("Executing Inspect_Inbound_Traffic...");
    await page.waitForTimeout(2000);
    await expect(page.locator(this.fields.trafficInspectionTitle)).toContainText("Traffic Inspection");
    console.log("Verified 'Traffic Inspection' title.");
    await expect(page.locator(`//span[contains(text(), '${trafficDirection}')]`)).toContainText(trafficDirection);
    console.log(`Verified '${trafficDirection}' title.`);
    await page.waitForTimeout(2000);

    const toggle = page.locator(this.fields.inspectInboundTrafficToggle);
    const currentState = await toggle.getAttribute("aria-checked");
    console.log(`Inspect Inbound Traffic toggle current state: ${currentState}`);
    await page.waitForTimeout(2000);

    if (currentState === "false") {
      console.log("Current state is false, clicking to toggle to true.");
      await toggle.click();
      await expect(toggle).toHaveAttribute("aria-checked", "true");
      console.log("Toggled to true.");
    } else if (currentState === "true") {
      console.log("Current state is true, clicking to toggle to false, then back to true.");
      await toggle.click();
      await page.waitForTimeout(1000);
      await expect(toggle).toHaveAttribute("aria-checked", "false");
      console.log("Toggled to false.");
      await page.waitForTimeout(1000);
      await toggle.click();
      await page.waitForTimeout(1000);
      await expect(toggle).toHaveAttribute("aria-checked", "true");
      console.log("Toggled back to true.");
    } else {
      await page.waitForTimeout(1000);
      throw new Error("Unexpected aria-checked value: " + currentState);
    }
    console.log("Inspect_Inbound_Traffic execution finished.");
  }

  async navigateToSecurityExceptions(page: Page): Promise<void> {
    console.log("Executing navigateToSecurityExceptions...");
    await page.waitForTimeout(2000);
    const label = page.getByRole("tab", { name: "Security Exceptions" });
    await expect(label).toContainText("Security Exceptions");
    console.log("Verified 'Security Exceptions' tab text.");
    await page.waitForTimeout(2000);
    console.log("Clicking 'Security Exceptions' tab.");
    await label.locator("span").click();
    await page.waitForTimeout(1000);
    await expect(label).toHaveAttribute("aria-selected", "true");
  }

  async verifyFileExceptionToggle(page: Page, fileExceptionType: string): Promise<void> {
    console.log(`Executing toggleFileException for ${fileExceptionType}...`);
    const mapping = this.fields.fileExceptionToggleMappings[fileExceptionType];
    if (!mapping) {
      throw new Error(`No locator mapping found for file exception: "${fileExceptionType}"`);
    }

    await page.waitForTimeout(2000);
    const securityExceptions = page.locator(this.fields.securityExceptionsTab);
    await securityExceptions.isVisible();
    console.log("Clicking 'Security Exceptions' tab.");
    await securityExceptions.click();
    await page.waitForTimeout(2000);
    await expect(page.locator(`//span[contains(text(), '${fileExceptionType}')]`)).toContainText(fileExceptionType);
    console.log(`Verified '${fileExceptionType}' title.`);
    await page.waitForTimeout(2000);

    const allowBtn = page.getByTestId(mapping.testId).getByRole("button", { name: "Allow" });
    const blockBtn = page.getByTestId(mapping.testId).getByRole("button", { name: "Block" });

    console.log(`Clicking 'Allow' for ${fileExceptionType}.`);
    await allowBtn.click();
    await page.waitForTimeout(2000);
    console.log(`Clicking 'Block' for ${fileExceptionType}.`);
    await blockBtn.click();
    await page.waitForTimeout(2000);
    // Verify the 'Block' button is now active, or the 'Allow' button is not.
    // The activeStateLocator points to the element that is visible when 'Block' is active.
    const toggleState = page.locator(mapping.activeStateLocator);
    console.log(`Current Status of ${fileExceptionType} Toggle Button: `, await toggleState.textContent());
    await page.waitForTimeout(2000);
    await expect(toggleState).toContainText("Block");
    console.log("Verified Unscannable Files is set to 'Block'.");
    console.log("Unscannable_Files execution finished.");
  }

  async verifyDoNotScanContentSectionTitle(page: Page): Promise<void> {
    console.log("Executing verifyDoNotScanContentSectionTitle...");
    await page.waitForTimeout(2000);
    const securityExceptions = page.locator(this.fields.securityExceptionsTab);
    await securityExceptions.isVisible();
    console.log("Clicking 'Security Exceptions' tab.");
    await securityExceptions.click();
    
  await page.waitForTimeout(2000);
  // Verify section header text is visible
  const label = page.locator(this.fields.doNotScanContentTitle);
  await expect(label).toContainText("Do Not Scan Content from");
  console.log("Verified 'Do Not Scan Content from' section title.");
  console.log("verifyDoNotScanContentSectionTitle execution finished.");
  }

  async addAndRemoveUrlFromDoNotScanList(page: Page, urlToAddAndRemove: string): Promise<void> {
    console.log(`Executing addAndRemoveUrlFromDoNotScanList for URL: ${urlToAddAndRemove}...`);
    await page.waitForTimeout(2000);
    const securityExceptions = page.locator(this.fields.securityExceptionsTab);
    await securityExceptions.isVisible();
    console.log("Clicking 'Security Exceptions' tab.");
    await securityExceptions.click();
    
  await page.waitForTimeout(2000);
  // Verify section header text is visible
  const label = page.locator(this.fields.doNotScanContentTitle);
  await expect(label).toContainText("Do Not Scan Content from");
  console.log("Verified 'Do Not Scan Content from' section title.");

  await page.evaluate(() => {
    window.scrollTo(0, document.body.scrollHeight);
  });
  await page.waitForTimeout(2000);
  // Interact with the textbox and searchbox
  const textbox = page.locator('//div[@data-testid="listBuilder-bypassUrlsDetailsList"]/textarea');
  await textbox.click();
  await textbox.fill(urlToAddAndRemove);
  await page.waitForTimeout(1000);
  await page.locator('//div[@data-testid="listBuilder-bypassUrlsDetailsList"]/button').click();
  await page.waitForTimeout(2000);
  // Verify the item was added
  const searchedItem = page.locator(`//span[contains(text(),"${urlToAddAndRemove}")]`);
  const isVisible = await searchedItem.isVisible();
  console.log(`Is '${urlToAddAndRemove}' item visible? ${isVisible}`);
  await page.waitForTimeout(2000);
  if (isVisible) {
    console.log(`'${urlToAddAndRemove}' is visible, attempting to remove it.`);
    // If item is present, remove it
    await page.locator(this.fields.removeItemIcon).click();
    await expect(page.locator(this.fields.noMatchingItemsFoundText))
      .not.toBeVisible();
    console.log(`Verified '${urlToAddAndRemove}' removed, 'No matching items found' displayed.`);
  } else {
    console.log(`'${urlToAddAndRemove}' is not visible. Adding it and then removing.`);
    await page.waitForTimeout(2000);
    // If item is not found in the first place
    await expect(page.locator(this.fields.noMatchingItemsFoundText))
      .toContainText("No matching items found");
    console.log("Verified 'No matching items found' initially.");
      await page.waitForTimeout(2000);
    await page.locator(this.fields.textAreaForAddItems).click();
    await page.locator(this.fields.textAreaForAddItems).fill(urlToAddAndRemove);
    console.log(`Filled textarea with '${urlToAddAndRemove}'.`);
    await page.locator(this.fields.addItemsButtonInListBuilder).click(); // Assuming this is the correct add button
    console.log("Clicked 'Add Items' button for the list builder.");
    await page.waitForTimeout(3000);
    await page.locator(this.fields.removeItemIcon).click();
    console.log(`Clicked remove icon for the newly added '${urlToAddAndRemove}'.`);
    await expect(page.locator(this.fields.noMatchingItemsFoundText))
      .toContainText("No matching items found");
    console.log(`Verified '${urlToAddAndRemove}' removed after adding, 'No matching items found' displayed.`);
  }
  console.log("Delete_Do_Not_Scan_Content_From_URLs execution finished.");
  }

  async Remove_All_Do_Not_Scan_Content_From_URLs(page: Page, urlToAddAndRemove: string): Promise<void> {
    console.log("Executing Remove_All_Do_Not_Scan_Content_From_URLs...");
    await page.waitForTimeout(2000);

    // Open "Security Exceptions"
    const securityExceptions = page.locator(this.fields.securityExceptionsTab);
    await securityExceptions.waitFor({ state: 'visible' });
    console.log("Clicking 'Security Exceptions' tab.");
    await securityExceptions.click();
    await page.waitForTimeout(2000);

    // Click somewhere on the body to ensure dropdown visibility
    console.log("Clicking body to ensure dropdown visibility.");
    await page.locator(this.fields.bodyLocator).click();

    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    console.log("Scrolled to bottom of the page.");
    await page.waitForTimeout(2000);

      // Interact with the textbox and searchbox
  const textbox = page.locator('//div[@data-testid="listBuilder-bypassUrlsDetailsList"]/textarea');
  await textbox.click();
  await textbox.fill(urlToAddAndRemove);
  await page.locator('//div[@data-testid="listBuilder-bypassUrlsDetailsList"]/button').click();
  await page.waitForTimeout(2000);

    const remove = page.locator(this.fields.removeButtonText);
    await expect(remove).toBeVisible();
    console.log("Clicking 'Remove' button.");
    await remove.click();
    await page.waitForTimeout(2000);
    const removeAll = page.locator(this.fields.removeAllDropdownItem);
    await expect(removeAll).toBeVisible();
    console.log("Clicking 'Remove All' from dropdown.");
    await removeAll.click();
    await page.waitForTimeout(2000);
    // First confirmation popup - click Cancel
    await page.getByText('Confirmation: Remove All').waitFor({ state: 'visible' });
    await page.getByText('Please confirm that you want').waitFor({ state: 'visible' });
    console.log("Clicking 'Cancel' on 'Remove All' confirmation.");
    await page.getByLabel('SECURITY_EXCEPTIONS').getByText('Cancel').click();
  
    // Open "Remove" dropdown and click "Remove All" again
    const remove1 = page.locator(this.fields.removeButtonText);
    await expect(remove1).toBeVisible();
    console.log("Clicking 'Remove' button again.");
    await remove1.click();
  
    const removeAll1 = page.locator(this.fields.removeAllDropdownItem);
    await expect(removeAll1).toBeVisible();
    console.log("Clicking 'Remove All' from dropdown again.");
    await removeAll1.click();
  
    // Final confirmation - click Confirm
    console.log("Clicking 'Confirm' on 'Remove All' confirmation.");
    await page.getByRole('button', { name: 'Confirm' }).click();
  
    // Final assertion: "Remove" button should no longer be visible
    const remove2 = page.locator(this.fields.removeButtonText);
    await expect(remove2).not.toBeVisible();
    console.log("Verified 'Remove' button is no longer visible.");
    console.log("Remove_All_Do_Not_Scan_Content_From_URLs execution finished.");
  }
  


  async Remove_Page_Do_Not_Scan_Content_From_URLs(page: Page, urlToAddAndRemove: string): Promise<void> {
    console.log("Executing Remove_Page_Do_Not_Scan_Content_From_URLs...");
    await page.waitForTimeout(2000);

    // Open "Security Exceptions"
    const securityExceptions = page.locator(this.fields.securityExceptionsTab);
    await securityExceptions.waitFor({ state: 'visible' });
    console.log("Clicking 'Security Exceptions' tab.");
    await securityExceptions.click();
    await page.waitForTimeout(2000);

    // Click on body to ensure dropdown visibility
    console.log("Clicking body to ensure dropdown visibility.");
    await page.locator(this.fields.bodyLocator).click();
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    console.log("Scrolled to bottom of the page.");
    await page.waitForTimeout(2000);

  // Interact with the textbox and searchbox
  const textbox = page.locator('//div[@data-testid="listBuilder-bypassUrlsDetailsList"]/textarea');
  await textbox.click();
  await textbox.fill(urlToAddAndRemove);
  await page.locator('//div[@data-testid="listBuilder-bypassUrlsDetailsList"]/button').click();
  await page.waitForTimeout(2000);
  
    // Open Remove dropdown
    const remove = page.locator(this.fields.removeButtonText);
    await expect(remove).toBeVisible();
    console.log("Clicking 'Remove' button.");
    await remove.click();
    await page.waitForTimeout(2000);

    // Click "Remove Page"
    const removePage = page.locator(this.fields.removePageDropdownItem);
    await expect(removePage).toBeVisible();
    console.log("Clicking 'Remove Page' from dropdown.");
    await removePage.click();
    await page.waitForTimeout(2000);

    // First confirmation popup - click Cancel
    await page.getByText('Confirmation: Remove Page').waitFor({ state: 'visible' });
    await page.getByText('Please confirm that you want').waitFor({ state: 'visible' });
    console.log("Clicking 'Cancel' on 'Remove Page' confirmation.");
    await page.getByLabel('SECURITY_EXCEPTIONS').getByText('Cancel').click();
    await page.waitForTimeout(2000);

    // Reopen Remove dropdown and select "Remove All"
    await expect(remove).toBeVisible();
    console.log("Clicking 'Remove' button again.");
    await remove.click();
    await page.waitForTimeout(2000);

    const removeAll = page.locator(this.fields.removeAllDropdownItem); // Intentionally clicking Remove All as per original code
    await expect(removeAll).toBeVisible();
    console.log("Clicking 'Remove All' from dropdown (as per original logic after 'Remove Page' cancel).");
    await removeAll.click();
    await page.waitForTimeout(2000);

  
    // Final confirmation - click Confirm
    console.log("Clicking 'Confirm' on 'Remove All' confirmation.");
    await page.getByRole('button', { name: 'Confirm' }).click();
    await page.waitForTimeout(2000);

    // Final assertion: "Remove" button should no longer be visible
    await expect(remove).not.toBeVisible();
    console.log("Verified 'Remove' button is no longer visible.");
    console.log("Remove_Page_Do_Not_Scan_Content_From_URLs execution finished.");
  }
  


  async Add_Do_Not_Scan_Content(page: Page, content: string): Promise<void> {
    console.log(`Executing Add_Do_Not_Scan_Content with content: "${content}"...`);
    await page.waitForTimeout(2000);

    const textbox = page.getByRole("textbox", { name: "Add Items" });
    console.log("Clicking 'Add Items' textbox.");
    await textbox.click();
    console.log(`Filling textbox with: "${content}".`);
    await textbox.fill(content);
    await page.waitForTimeout(2000);

    console.log("Clicking 'Add Items' button.");
    await page.getByRole("button", { name: "Add Items" }).click();
    console.log(`Filling searchbox with: "${content}".`);
    await page.getByRole("searchbox", { name: "Search..." }).fill(content);
    await page.waitForTimeout(2000);

    console.log("Pressing Enter in searchbox.");
    await page.getByRole("searchbox", { name: "Search..." }).press("Enter");
    await page.waitForTimeout(2000);

    console.log(`Clicking text element: "${content}".`);
    await page.getByText(content).click();
    await page.waitForTimeout(2000);

    await expect(page.getByText(content)).toContainText(content);
    console.log(`Verified text element "${content}" is present.`);
    console.log("Add_Do_Not_Scan_Content execution finished.");
  }

  async saveChanges(page: Page): Promise<void> {
    console.log("Executing saveChanges...");
    await page.waitForTimeout(2000);
    console.log("Clicking 'Save' button.");
    await page.getByRole("button", { name: "Save" }).click();
    await page.waitForTimeout(2000);

    await expect(page.getByText("All changes have been saved.")).toContainText("All changes have been saved.");
    console.log("Verified 'All changes have been saved.' message.");
    console.log("saveChanges execution finished.");
  }

  async inspectTrafficToggleAndCancel(page: Page, trafficDirection: "Inspect Inbound Traffic"): Promise<void> {
    console.log(`Executing inspectTrafficToggleAndCancel for ${trafficDirection}...`);
    await page.waitForTimeout(2000);
    await expect(page.locator(this.fields.trafficInspectionTitle)).toContainText("Traffic Inspection");
    console.log("Verified 'Traffic Inspection' title.");
    await expect(page.locator(this.fields.inspectInboundTrafficTitle)).toContainText("Inspect Inbound Traffic");
    console.log("Verified 'Inspect Inbound Traffic' title.");
    await page.waitForTimeout(2000);

    const toggle = page.locator(this.fields.inspectInboundTrafficToggle);
    let currentState = await toggle.getAttribute("aria-checked");
    console.log(`Inspect Inbound Traffic toggle current state: ${currentState}`);
    await page.waitForTimeout(2000);

    if (currentState === "false") {
      console.log("Current state is false, clicking to toggle to true.");
      await toggle.click();
      await expect(toggle).toHaveAttribute("aria-checked", "true");
      console.log("Toggled to true.");
      currentState = "true"; // Update current state for cancel check
    } else if (currentState === "true") {
      console.log("Current state is true, clicking to toggle to false.");
      await toggle.click();
      await expect(toggle).toHaveAttribute("aria-checked", "false");
      console.log("Toggled to false.");
      currentState = "false"; // Update current state for cancel check
      await toggle.click(); // Toggle back to true to ensure a change is made before cancel
      await expect(toggle).toHaveAttribute("aria-checked", "true");
      console.log("Toggled back to true.");
    } else {
      throw new Error("Unexpected aria-checked value: " + currentState);
    }
    await page.waitForTimeout(2000);
    console.log("Clicking 'Cancel' button.");
    await page.locator(this.fields.cancelButtonText).click();
    const notifyCancel = page.locator(this.fields.allChangesCanceledNotification);
    await expect(notifyCancel).toContainText("All changes have been canceled.");
    console.log("Verified 'All changes have been canceled.' notification.");

    // Verify the toggle is back to its original state before the last toggle action (if it was changed)
    // This part is tricky because the "original" state depends on what it was before this method was called.
    // For simplicity, we'll check if it's the opposite of what it was after the first toggle in this method.
    // If it was toggled to true, after cancel it should be false (assuming it was false initially).
    // If it was toggled to false, after cancel it should be true (assuming it was true initially).
    // This needs careful thought based on the actual desired state after cancel.
    // For now, we just verify the cancel message.
    console.log("Inspect_Inbound_Traffic_Cancel execution finished.");
  }

  async verifyProtocolToggle(page: Page, protocolName: string): Promise<void> {
    console.log(`Verifying toggle for protocol: ${protocolName}`);
    const testId = this.fields.protocolToggleMappings[protocolName];
    if (!testId) {
      throw new Error(`No locator mapping found for protocol: "${protocolName}"`);
    }
    const toggleSwitch = page.getByTestId(testId);
    const toggleElement = page.locator(`//span[@data-testid='${testId}']//div`); 

    // Initial 3-second wait as requested
    await page.waitForTimeout(3000);

    await expect(toggleSwitch).toBeVisible();
    console.log(`Toggle switch for "${protocolName}" is visible.`);

    let initialStateIsActive = await toggleElement.getAttribute('aria-checked') === 'true';
    console.log(`Initial state for "${protocolName}" is active: ${initialStateIsActive}`);

    if (initialStateIsActive) {
      console.log(`The toggle for "${protocolName}" is already enabled.`);
      // Assert its current state to fulfill the verification step
      await expect(toggleElement).toHaveAttribute('aria-checked', 'true', { timeout: 5000 });
    } else {
      // If false, click to make it true and verify
      console.log(`Toggle for "${protocolName}" is false, attempting to click it to true.`);
      
      // Perform the click action
      await toggleElement.click({ force: true });

      await expect(toggleElement).toHaveAttribute('aria-checked', 'true', { timeout: 10000 });
      console.log(`"${protocolName}" is now active.`);
    }
  }

  async verifyThreatTypeToggle(page: Page, threatType: string): Promise<void> {
    console.log(`Verifying toggle for threat type: ${threatType}`);
    const testId = this.fields.threatTypeToggleMappings[threatType];
    if (!testId) {
      throw new Error(`No locator mapping found for threat type: "${threatType}"`);
    }
    // Locate the Block and Allow buttons using testId
    const blockBtn = page.getByTestId(testId).getByRole("button", { name: "Block" });
    const allowBtn = page.getByTestId(testId).getByRole("button", { name: "Allow" });

    // Ensure Block button is the target state.
    // We will click Allow first, then Block, to ensure the Block button is interacted with and becomes active.
    const isBlockActiveInitially = await blockBtn.evaluate(node => node.classList.contains('active'));

    if (isBlockActiveInitially) {
      console.log(`Block button for ${threatType} is already active. Toggling to Allow and back to Block to ensure interaction.`);
      await allowBtn.click();
      await page.waitForTimeout(500); // Short wait for UI to process Allow click
      await blockBtn.click();
      await page.waitForTimeout(1000); // Wait for UI to update after Block click
    } else {
      console.log(`Block button for ${threatType} is not initially active (or Allow is active). Clicking Allow then Block.`);
      await allowBtn.click();
      await page.waitForTimeout(500); // Short wait for UI to process Allow click
      await blockBtn.click();
      await page.waitForTimeout(1000); // Wait for UI to update after Block click
    }

    // Verify Block button is now active by checking its class
    await expect(blockBtn).toHaveClass(/active/, { timeout: 5000 });
    // Also ensure Allow button is not active
    await expect(allowBtn).not.toHaveClass(/active/, { timeout: 5000 });
    console.log(`Verified Block button for testId: "${testId}" is active and Allow is not.`);
  }
}

export default new MalwarePolicy();
