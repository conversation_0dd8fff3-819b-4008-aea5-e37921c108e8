import { Page, expect } from "@playwright/test";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

export default class NetworkApplicationsPage {
  constructor() { }

  async navigateToNetworkApplications(page: Page): Promise<void> {
    console.log("Executing navigateToNetworkApplications...");

    const networkApplicationsPath = "administration/network-applications";
    let targetUrl = "";

    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
    }

    if (url.includes("console")) {
      targetUrl = url + "internet-saas#" + networkApplicationsPath;
      await page.goto(targetUrl);
      console.log("Page navigation initiated (console URL) to Network Applications.");
      await page.waitForTimeout(80000);
    } else {
      targetUrl = url + "#" + networkApplicationsPath;
      await page.goto(targetUrl);
      console.log("Page navigation initiated (non-console URL) to Network Applications.");
      await page.waitForTimeout(20000);
    }

    await expect(page).toHaveURL(new RegExp(networkApplicationsPath.replace(/\//g, '\\/')));
    console.log("Successfully navigated to Network Applications screen.");
  }

  async verifySearchForApplications(page: Page): Promise<void> {
    console.log("Verifying search functionality for applications...");

    // 1. Click on the Applications tab to navigate
    const applicationsTab = page.locator('//li[@data-label="APPLICATIONS"]/span');
    await expect(applicationsTab).toBeVisible();
    await applicationsTab.click();
    await page.waitForTimeout(1000); // Wait for tab content to load

    const searchInput = page.locator('//input[@aria-label="Filter"]');
    await expect(searchInput).toBeVisible();
    await searchInput.fill("Acer");
    await page.waitForTimeout(2000);
    const applyFilterButton = page.locator('//button[@aria-label="Apply filter"]');
    await applyFilterButton.click(); // Ensure the filter is applied
    await page.waitForTimeout(2000); // Wait for results to load

    const searchResultsLocator = page.locator('//div[@data-testid="cell-name"]//div[contains(text(),"Acer")]');
    const noResultsLocator = page.locator('//div[contains(text(),"No matching data found")]');

    const isSearchResultsVisible = await searchResultsLocator.first().isVisible();
    const isNoResultsVisible = await noResultsLocator.isVisible();

    if (isSearchResultsVisible) {
      await expect(searchResultsLocator.first()).toContainText("Acer");
      console.log("Search functionality for applications verified: Results found.");
    } else if (isNoResultsVisible) {
      await expect(noResultsLocator).toContainText("No matching data found");
      console.log("Search functionality for applications verified: No matching data found.");
    } else {
      throw new Error("Search did not yield expected results or no data message.");
    }
  }

  async verifySearchInDnsApplicationGroups(page: Page): Promise<void> {
    console.log("Verifying search functionality within DNS application groups...");

    // 1. Click on the DNS Application Groups tab to navigate
    const dnsTab = page.locator('//li[@data-label="DNS_APPLICATION_GROUP"]/span');
    await expect(dnsTab).toBeVisible();
    await dnsTab.click();
    await page.waitForTimeout(1000); // Wait for tab content to potentially load

    // 2. Use the search filter
    const searchInput = page.locator('//input[@aria-label="Filter"]');
    await expect(searchInput).toBeVisible();
    const searchTerm = "Test DNS Group"; // Using a sample search term
    await searchInput.fill(searchTerm);
    await page.waitForTimeout(2000);

    // 3. Click the apply filter button
    const applyFilterButton = page.locator('//button[@aria-label="Apply filter"]');
    await applyFilterButton.click();
    await page.waitForTimeout(2000); // Wait for results to load

    // 4. Check for results or "no data" message, similar to verifySearchForApplications
    const searchResultsLocator = page.locator(`//div[@data-testid="cell-name"]//div[contains(text(),"${searchTerm}")]`);
    const noResultsLocator = page.locator('//div[contains(text(),"No matching data found")]');

    const isSearchResultsVisible = await searchResultsLocator.first().isVisible();
    const isNoResultsVisible = await noResultsLocator.isVisible();

    if (isSearchResultsVisible) {
      await expect(searchResultsLocator.first()).toContainText(searchTerm);
      console.log("Search functionality for DNS application groups verified: Results found.");
    } else if (isNoResultsVisible) {
      await expect(noResultsLocator).toContainText("No matching data found");
      console.log("Search functionality for DNS application groups verified: No matching data found.");
    } else {
      throw new Error("Search for DNS Application Groups did not yield expected results or a 'no data' message.");
    }
  }


  async verifySearchForApplicationGroups(page: Page): Promise<void> {
    console.log("Verifying search functionality for application groups...");

    // 1. Click on the Application Groups tab to navigate
    // Using exact text match to distinguish from "DNS Application Groups"
    const appGroupsTab = page.locator('//span[text()="Application Groups"]');
    await expect(appGroupsTab).toBeVisible();
    await appGroupsTab.click();
    await page.waitForTimeout(1000); // Wait for tab content to load

    // 2. Use the search filter
    const searchInput = page.locator('//input[@aria-label="Filter"]');
    await expect(searchInput).toBeVisible();
    const searchTerm = "Microsoft Office365";
    await searchInput.fill(searchTerm);
    await page.waitForTimeout(2000);

    // 3. Click the apply filter button
    const applyFilterButton = page.locator('//button[@aria-label="Apply filter"]');
    await applyFilterButton.click();
    await page.waitForTimeout(2000); // Wait for results to load

    // 4. Check for results or "no data" message
    const searchResultsLocator = page.locator(`//div[@data-testid="cell-name"]//div[contains(text(),"${searchTerm}")]`);
    const noResultsLocator = page.locator('//div[contains(text(),"No matching data found")]');

    const isSearchResultsVisible = await searchResultsLocator.first().isVisible();
    const isNoResultsVisible = await noResultsLocator.isVisible();

    if (isSearchResultsVisible) {
      await expect(searchResultsLocator.first()).toContainText(searchTerm);
      console.log("Search functionality for application groups verified: Results found.");
    } else if (isNoResultsVisible) {
      await expect(noResultsLocator).toContainText("No matching data found");
      console.log("Search functionality for application groups verified: No matching data found.");
    } else {
      throw new Error("Search for Application Groups did not yield expected results or a 'no data' message.");
    }
  }

  async verifySortingByColumns(page: Page, type: string): Promise<void> {
    console.log(`Verifying sorting functionality for ${type}...`);

    const tab = page.locator(`//li[@data-label="${type}"]/span`);
    await expect(tab).toBeVisible();
    await tab.click();
    await page.waitForTimeout(1000); // Wait for tab content to potentially load

    const sortingHeader = page.locator('//div[@data-testid="columnHeader-name"]//div[contains(text(),"Name")]');
    const noResultsLocator = page.locator('//div[contains(text(),"No matching data found")]');

    const isSortingHeaderVisible = await sortingHeader.isVisible();
    const isNoResultsVisible = await noResultsLocator.isVisible();

    if (isSortingHeaderVisible) {
      console.log("Sorting header is visible. Proceeding with sort action.");
      const sortButton = page.locator('//div[@data-testid="columnHeader-name"]//button[@aria-label="Sort"]');
      await expect(sortButton).toBeVisible();
      await sortButton.click();
      await page.waitForTimeout(2000); // Wait for sort to apply
      console.log(`Sorting by ${type} (Name column) was successful.`);
    } else if (isNoResultsVisible) {
      console.log("No data found to sort, which is an expected outcome.");
      await expect(noResultsLocator).toContainText("No matching data found");
    } else {
      throw new Error(`Could not find the sortable header for '${type}' or the 'No matching data found' message.`);
    }
  }

  
  async deleteGroup(page: Page, name: string): Promise<void> {
    await page.waitForTimeout(2000);
    // Navigate to the DNS Application Group tab first
    await page.locator('//li[@data-label="DNS_APPLICATION_GROUP"]/span').click();

    await page.waitForTimeout(2000);
    const searchInput = page.locator('//input[@aria-label="Filter"]');
    await expect(searchInput).toBeVisible();
    await searchInput.fill(`${name}`);
    await page.waitForTimeout(2000);
    const applyFilterButton = page.locator('//button[@aria-label="Apply filter"]');
    await applyFilterButton.click();
    await page.waitForTimeout(2000);

    const searchResultsLocator = page.locator(`//div[@data-testid="cell-name"]//div[contains(text(),"${name}")]`);
    const noResultsLocator = page.locator('//div[contains(text(),"No matching data found")]');

    const isSearchResultsVisible = await searchResultsLocator.first().isVisible();
    const isNoResultsVisible = await noResultsLocator.isVisible();

    if (isSearchResultsVisible) {
      await expect(searchResultsLocator.first()).toContainText(`${name}`);
      console.log("Search functionality for applications verified: Results found.");
      await page.locator('//i[@aria-label="EDIT"]').isVisible();
      await page.locator('//i[@aria-label="EDIT"]').click();
      await page.waitForTimeout(2000);
      await page.locator('//span[@title="Edit DNS Application Group"]').isVisible();

      await page.locator('//span[contains(text(),"Delete")]').click();
      await page.waitForTimeout(2000);

      await page.locator('//span[contains(text(),"Confirm")]').click();
      await page.waitForTimeout(2000);

      const popup = await page.locator('//div[@data-testid="modal-header"]/span/i');
      await page.waitForTimeout(2000);

      if(await popup.isVisible()) {
        await popup.click();
      }

    } else if (isNoResultsVisible) {
      await expect(noResultsLocator).toContainText("No matching data found");
      console.log("Search functionality for applications verified: No matching data found.");
    } else {
      throw new Error("Search did not yield expected results or no data message.");
    }
  }

  async delete(page: Page, name: string): Promise<void> {
    // Navigate to the DNS Application Group tab first
    const dnsTab = page.locator('//li[@data-label="APPLICATION_GROUPS"]/span');
    await expect(dnsTab).toBeVisible();
    await dnsTab.click();
    await page.waitForTimeout(1000);

    await page.waitForTimeout(2000);
    const searchInput = page.locator('//input[@aria-label="Filter"]');
    await expect(searchInput).toBeVisible();
    await searchInput.fill(`${name}`);
    await page.waitForTimeout(2000);
    const applyFilterButton = page.locator('//button[@aria-label="Apply filter"]');
    await applyFilterButton.click();
    await page.waitForTimeout(2000);

    const searchResultsLocator = page.locator(`//div[@data-testid="cell-name"]//div[contains(text(),"${name}")]`);
    const noResultsLocator = page.locator('//div[contains(text(),"No matching data found")]');

    const isSearchResultsVisible = await searchResultsLocator.first().isVisible();
    const isNoResultsVisible = await noResultsLocator.isVisible();

    if (isSearchResultsVisible) {
      await expect(searchResultsLocator.first()).toContainText(`${name}`);
      console.log("Search functionality for applications verified: Results found.");
      await page.locator('//i[@aria-label="EDIT"]').isVisible();
      await page.locator('//i[@aria-label="EDIT"]').click();
      await page.waitForTimeout(2000);
      await page.locator('//span[@title="Edit Network Application Group"]').isVisible();

      await page.locator('//span[contains(text(),"Delete")]').click();
      await page.waitForTimeout(2000);

      await page.locator('//span[contains(text(),"Confirm")]').click();
      await page.waitForTimeout(2000);

      const popup = await page.locator('//div[@data-testid="modal-header"]/span/i');
      await page.waitForTimeout(2000);

      if(await popup.isVisible()) {
        await popup.click();
      }

    } else if (isNoResultsVisible) {
      await expect(noResultsLocator).toContainText("No matching data found");
      console.log("Search functionality for applications verified: No matching data found.");
    } else {
      throw new Error("Search did not yield expected results or no data message.");
    }
  }

  async addAnApplicationGroup(page: Page, name: string, description: string): Promise<void> {
    console.log(`Adding an application group with Name: ${name}, Description: ${description}`);

    // Navigate to the DNS Application Group tab first
    const dnsTab = page.locator('//li[@data-label="APPLICATION_GROUPS"]/span');
    await expect(dnsTab).toBeVisible();
    await dnsTab.click();
    await page.waitForTimeout(1000);

    await page.getByRole('button', { name: ' Add Network Application' }).click();
    const label = await page.getByTestId('modal-header').getByText('Add Network Application Group');
    await expect(label).toBeVisible();
    const label1 = await page.getByText('Network Application Group', { exact: true });
    await expect(label1).toBeVisible();
    await page.getByRole('textbox', { name: 'name' }).click();
    await page.getByRole('textbox', { name: 'name' }).fill(`${name}`);
  }

  async addApplicationGroup(page: Page, name: string, description: string): Promise<void> {
    console.log(`Adding application group with Name: ${name}, Description: ${description}`);
    // Navigate to the DNS Application Group tab first
    await page.locator('//li[@data-label="DNS_APPLICATION_GROUP"]/span').click();
    await page.waitForTimeout(2000);

    await page.locator('//span[contains(text(),"Add DNS Application Group")]').isVisible();
    await page.locator('//span[contains(text(),"Add DNS Application Group")]').click();
    await page.waitForTimeout(2000);

    const nameField = await page.locator('//div[@data-testid="textBox-name"]/input');
    await nameField.isVisible();
    await nameField.fill(`${name}`);
    await page.waitForTimeout(1000);
  }

  
  async selectApplicationAndSaveDNSGroup(page: Page, application: string, action: string): Promise<void> {
    await page.waitForTimeout(2000);
    await page.getByTestId('multiDropdown-dnsApplications').click();
    await page.getByText('Selected Items ( 0 )').isVisible();
    await page.waitForTimeout(2000);
    await page.getByText('Clear Selection').click();
    await page.getByRole('textbox', { name: 'Search...' }).click();
    await page.getByRole('textbox', { name: 'Search...' }).fill(`${application}`);
    await page.getByRole('checkbox', { name: `${application}` }).check();

    await page.getByText('Selected Items ( 1 )').isVisible();
    await page.getByText(`${action}`).click();
    await page.getByTestId('textArea-description').getByText('Description', { exact: true }).click();
    await page.getByRole('textbox', { name: 'description' }).click();
    await page.getByRole('textbox', { name: 'description' }).fill('Test Description');
    await page.getByRole('button', { name: 'Save' }).click();

  }
  async selectApplicationAndSaveGroup(page: Page, application: string, action: string): Promise<void> {
    await page.waitForTimeout(2000);
    await page.getByTestId('multiDropdown-APPLICATIONS').getByText('Applications', { exact: true }).isVisible();
    await page.getByTestId('multi-dropdown').getByRole('button', { name: 'Applications' }).click();
    await page.waitForTimeout(2000);
    const count = await page.getByText('Selected Items ( 0 )');
    await expect(count).toBeVisible();
    await page.getByText('Clear Selection').click();
    await page.getByRole('textbox', { name: 'Search...' }).click();
    await page.getByRole('textbox', { name: 'Search...' }).fill(`${application}`);
    await page.waitForTimeout(2000);
    await page.locator(`//input[@id="${application}"]`).click();

    const reCount = await page.getByText('Selected Items ( 1 )');
    await expect(reCount).toBeVisible();
    await page.getByText(`${action}`).click();
    await page.getByTestId('textArea-description').getByText('Description', { exact: true }).isVisible();
    await page.getByRole('textbox', { name: 'description' }).click();
    await page.getByRole('textbox', { name: 'description' }).fill('Test Description');
    await page.getByRole('button', { name: 'Save' }).click();
    await page.getByText('All changes have been saved.').isVisible();
  }

  
  
  async selectOneMoreDNSApplicationAndSaveGroup(page: Page, application: string, action: string): Promise<void> {
    await page.waitForTimeout(2000);
    await page.getByTestId('multiDropdown-dnsApplications').click();
    await page.waitForTimeout(2000);
    await page.getByRole('textbox', { name: 'Search...' }).click();
    await page.getByRole('textbox', { name: 'Search...' }).fill(`${application}`);
    await page.getByRole('checkbox', { name: `${application}` }).check();

    await page.getByText('Selected Items ( 1 )').isVisible();
    await page.getByText(`${action}`).click();
    await page.getByTestId('textArea-description').getByText('Description', { exact: true }).click();
    await page.getByRole('textbox', { name: 'description' }).click();
    await page.getByRole('textbox', { name: 'description' }).fill('Test Description');
    await page.getByRole('button', { name: 'Save' }).click();
  }
  async selectOneMoreApplicationAndSaveGroup(page: Page, application: string, action: string): Promise<void> {
    await page.waitForTimeout(2000);
    await page.getByTestId('multiDropdown-APPLICATIONS').getByText('Applications', { exact: true }).isVisible();
    await page.getByTestId('multi-dropdown').getByRole('button', { name: 'Applications' }).click();
    await page.waitForTimeout(2000);
    await page.getByRole('textbox', { name: 'Search...' }).click();
    await page.getByRole('textbox', { name: 'Search...' }).fill(`${application}`);
    await page.waitForTimeout(2000);
    await page.locator(`//input[@id="${application}"]`).click();

    const reCount = await page.getByText('Selected Items ( 2 )');
    await expect(reCount).toBeVisible();
    await page.getByText(`${action}`).click();
    await page.getByTestId('textArea-description').getByText('Description', { exact: true }).isVisible();
    await page.getByRole('textbox', { name: 'description' }).click();
    await page.getByRole('textbox', { name: 'description' }).fill('Test Description');
    await page.getByRole('button', { name: 'Save' }).click();
    await page.getByText('All changes have been saved.').isVisible();
  }

  async editGroup(page: Page, application: string): Promise<void> {
    console.log("Editing group...");

    // Locate the Filter input and apply filtering
    const searchInput = page.locator('//input[@aria-label="Filter"]');
    await expect(searchInput).toBeVisible();
    await searchInput.fill(application);
    await page.waitForTimeout(2000);

    const applyFilterButton = page.locator('//button[@aria-label="Apply filter"]');
    await applyFilterButton.click();
    await page.waitForTimeout(2000);

    const searchResultsLocator = page.locator(`//div[@data-testid="cell-name"]//div[contains(text(),"${application}")]`);
    await expect(searchResultsLocator.first()).toBeVisible({ timeout: 5000 }); 
    await expect(searchResultsLocator.first()).toContainText(application);

    console.log("Search functionality for applications verified: Results found.");

    // Clicking the edit icon
    await page.locator('//i[@aria-label="EDIT"]').click();

    // Check visibility of the edit modals using if-else logic
    await page.locator('//span[@title="Edit Network Application Group"]').isVisible();

}

async editDNSGroup(page: Page, application: string): Promise<void> {
  console.log("Editing group...");

  // Locate the Filter input and apply filtering
  const searchInput = page.locator('//input[@aria-label="Filter"]');
  await expect(searchInput).toBeVisible();
  await searchInput.fill(application);
  await page.waitForTimeout(2000);

  const applyFilterButton = page.locator('//button[@aria-label="Apply filter"]');
  await applyFilterButton.click();
  await page.waitForTimeout(2000);

  const searchResultsLocator = page.locator(`//div[@data-testid="cell-name"]//div[contains(text(),"${application}")]`);
  await expect(searchResultsLocator.first()).toBeVisible({ timeout: 5000 }); 
  await expect(searchResultsLocator.first()).toContainText(application);

  console.log("Search functionality for applications verified: Results found.");

  // Clicking the edit icon
  await page.locator('//i[@aria-label="EDIT"]').click();

  // Check visibility of the edit modals using if-else logic
  await page.locator('//span[@title="Edit DNS Application Group"]').isVisible();

}

  async verifyApplicationIsPresent(page: Page, application: string): Promise<void> {
    console.log(`Verifying application ${application} is present...`);
    const alreadySelected = await page.locator(`//span[contains(text(),"${application}")]`);
    await expect(alreadySelected).toBeVisible();
  }

  async verifyMultipleApplicationsArePresent(page: Page, application1: string, application2: string): Promise<void> {
    console.log(`Verifying applications ${application1} and ${application2} are present...`);
    await page.waitForTimeout(2000);
    await page.locator(`//span[contains(text(),"${application1}; ${application2}")]`).isVisible();
    await page.locator('//span[contains(text(),"Cancel")]').click();
    await page.waitForTimeout(2000);
  }

}