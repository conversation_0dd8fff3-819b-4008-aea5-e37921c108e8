import { expect, Page } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions.js';
import { config } from 'dotenv';

config(); 

const url: string = process.env.ONE_UI_BASE_URL ?? "";

const Selectors: Record<string, string> = {
    "Allow FTP Over HTTP": "toggleSwitch-ftpOverHttpEnabled",
    "Allow Native FTP": "toggleSwitch-ftpEnabled",
    "Allow Any URL Category": "toggleSwitch-UrlCategory"
  };

class FTPControl {
    fields: {
        spanHeadings: (heading: string) => string;
        buttons: (button: string) => string;
    };

    constructor() {
        this.fields = {
          spanHeadings: (spanHeading) => `//span[text()='${spanHeading}']`,
          buttons: (button) => `//button[text()='${button}']`,
        };
    }

    async navigateToFTPControl(page: Page): Promise<void>{
          console.log("The Url used is : - "+url);
            if (!url) {
              throw new Error('ONE_UI_BASE_URL environment variable is not set');
            }
            if (url.includes("console")) {
              await page.goto(url+"internet-saas#policy/firewall/ftp-control");
              await page.waitForTimeout(15000);
              }
            else{
              await page.goto(url+"#policy/firewall/ftp-control");
              await page.waitForTimeout(15000);
            }
    }

    async verifyTitle(page: Page, title: string): Promise<void>{
        await page.locator(`//span[@title='${title}']`).hover();
        const value = await page.locator(`//span[@title='${title}']`);
        expect(value).toHaveText(title);
    }

    async verifySpanHeading(page: Page, title: string): Promise<void>{
        await page.waitForTimeout(4000);
        const value = await page.locator(this.fields.spanHeadings(title));
        expect(value).toHaveText(title);
    }

    async toggleAllowFTPOverHTTP(page: Page, toggle: string): Promise<void>{
        await page.getByTestId(Selectors[toggle]).getByRole('checkbox', { name: ' ' }).isVisible();
        await page.getByTestId(Selectors[toggle]).getByRole('checkbox', { name: ' ' }).click();
        await page.waitForTimeout(3000);
    }

    async toggleAllowFTPOverHTTP1(page: Page, toggle: string): Promise<void> {
        const disabledStatus = page.locator('//div[@id="ALLOW_ANY_URL_CATEGORY"]/following-sibling::span/div[@aria-checked="false"]');
        if (await disabledStatus.isVisible()) {
          console.log('It is already disabled.');
          return;
        }
        const checkbox = page.getByTestId(Selectors[toggle]).getByRole('checkbox', { name: ' ' });
        await expect(checkbox).toBeVisible();
        await checkbox.click();
        await page.waitForTimeout(3000);
      }

    async clickButton(page: Page, button: string): Promise<void>{
        await page.locator(this.fields.buttons(button)).isVisible();
        await page.locator(this.fields.buttons(button)).click();
        await page.waitForTimeout(5000);
        const messgae = await page.locator("//span[contains(text(),'All changes have been')]");
        expect(messgae).toContainText('All changes have been');
    }
    async clickButton1(page: Page, button: string): Promise<void> {
        // Define the locator for the button
        const buttonLocator = page.locator('//button[@type="submit"][1]');
      
        await page.locator('//span[@data-testid="toggleSwitch-ftpOverHttpEnabled"]/div/span[2]').click();
      
        await page.waitForFunction(
            async (locator) => {
              const button = document.evaluate(locator, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
              return button && !(button as HTMLButtonElement).disabled;
            },
            '//button[@type="submit"][1]', 
            { timeout: 10000 } 
          );
        
        // Validate the button is visible and enabled
        await expect(buttonLocator).toBeVisible();
        await expect(buttonLocator).toBeEnabled();
      
        // Attempt to click the button
        await buttonLocator.click();
      
        // Wait for any UI updates after the click action
        await page.waitForTimeout(5000);
      
        // Validate that the expected confirmation message appears
        const messageLocator = page.locator("//span[contains(text(),'All changes have been')]");
        await expect(messageLocator).toContainText('All changes have been');
      }

    async selectOptionAllowedURLCategories(page: Page): Promise<void> {
        await page.locator('//div[@data-testid="multiDropdown-allowedUrlCategories"]/div[2]/div/div').click();
        await page.locator('//span[contains(text(),"Clear Selection")]').click();
        await page.waitForTimeout(3000);
        await page.locator('(//label/input[1])[2]').click();
        await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
        await page.getByText('Done').click();
      }

    async deSelectOptionAllowedURLCategories(page: Page): Promise<void>{
        await page.getByRole('button', { name: 'Body Art ' }).click();
        await page.getByText('Clear Selection').click();
        await page.getByText('Done').click();
    }

    async clickRecommendedPolicy(page: Page): Promise<void>{
        await page.getByRole('button', { name: ' Recommended Policy' }).isVisible();
        await page.getByRole('button', { name: ' Recommended Policy' }).click();
        await page.waitForTimeout(3000);
        await page.getByText('View Recommended FTP Control').isVisible();
    }

    async closeRecommendedPolicyPopUp(page: Page): Promise<void>{
        await page.getByText('View Recommended FTP Control').isVisible();
        await page.getByTestId('modal-header').locator('i').click();
    }
}

export default new FTPControl();