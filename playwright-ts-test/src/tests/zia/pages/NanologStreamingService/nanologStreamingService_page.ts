import { Page, expect, APIRequestContext } from "@playwright/test";
import { config } from "dotenv";
import { ZiaAdminApiHelper, NssFeedApiResponse } from "../../../../../resources/utils/ZiaApiHelper";
import { asyncWrapProviders } from "async_hooks";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

interface NanologStreamingServiceFields {
  // Original fields (some might be unused by the refactored methods but kept for broader compatibility)
  nssFeedsPageTitle: string; // Original: nssFeedsTitle
  addNssFeedButton: string;
  feedNameLabelOnForm: string; // Original: feedNameInput (used as a label)
  feedTypeDropdown: string; // Unused in provided methods
  logTypeDropdown: string; // Unused in provided methods, specific one used
  genericSaveButton: string; // Original: saveButton (button tag, unused in provided methods)
  genericSearchBox: string; // Original: searchBox (unused in provided methods)
  logTypeLabel: string;
  nssServerOptionNss01: string;

  // Common Locators
  pageHeaderTitle: string;
  searchNssFeedInput: string;
  searchNssFeedButton: string;
  feedNameInGrid: string;
  editIconInGrid: string;
  editNssFeedPopupTitle: string;
  deleteButtonInEditPopup: string;
  confirmButtonInPopup: string;
  itemDeletedMessage: string;
  nssFeedsTab: string;
  formSaveButton: string;
  changesSavedMessage: string;

  // Add/Edit NSS Feed Form - Common Fields
  feedNameActualInputOnForm: string;
  nssServerLabel: string;
  nssServerDropdown: string;

  statusLabel: string;
  statusActiveRadioButton: string;
  statusEnabledRadioButton: string;
  siemDestinationTypeLabel: string;
  siemTypeActiveRadioButton: string; // Note: This locator points to an *active* button.
  siemIpAddressLabel: string;
  nssServerOptionTestNssForWeb: string;
  siemIpAddressInput: string;
  siemTcpPortLabel: string;
  siemTcpPortInput: string;
  siemRateLabel: string;
  logDomainLabel: string;
  logDomainToggleButton1: string;
  logTypeFormDropdown: string;
  timeZoneLabel: string;
  timeZoneDropdown: string;
  timeZoneSearchInputInDropdown: string;
  duplicateLogsLabel: string;
  duplicateLogsDropdown: string;
  duplicateLogsOption1: string;

  // Add/Edit NSS Feed Form - Web Specific
  siemRateToggleWebButton1: string;
  logTypeOptionAlert: string;
  logTypeOptionTunnel: string;
  logTypeOptionSaasSecurity: string;
  logTypeOptionEmailDLP: string;
  logTypeOptionLogTypeDLP: string;
  logTypeOptionSaasSecurityActivity: string;
  logTypeOptionFirewal: string;
  logTypeOptionDNS: string;
  timeZoneOptionSamoaPacificApia: string;
  timeZoneOptionAfricaAbidjan1: string;
  logFilterTitleWeb: string;
  criticalLabelWeb: string;
  criticalToggleSwitchFalseWeb: string;
  warnLabelWeb: string;
  warnToggleSwitchFalseWeb: string;

  // Add/Edit NSS Feed Form - Firewall Specific
  siemRateToggleFirewallButton2: string;
  siemRateLimitLabel: string;
  siemRateLimitInput: string;
  logTypeOptionDnsLog: string;
  feedOutputTypeLabel: string;
  feedOutputTypeDropdown: string;
  feedOutputTypeOptionNVP: string;
  feedEscapeCharacterLabel: string;
  timeZoneOptionAfricaAbidjan: string;

  // NSS Type
  nssTypeDropdown: string;
  nssTypeOptionFirewallCloudBranch: string;
  nssTypeOptionForWeb: string;

  // Filter Tabs
  filterActionTab: string;
  filterWhoTab: string;
  filterSourceTab: string;
  filterDestinationTab: string;
  filterSessionTab: string;

  // Filter Section Locators - Action
  filterPolicyActionLabel: string;
  filterPolicyActionDropdown: string;
  filterRuleNamesLabel: string;
  filterRuleNamesDropdown: string;

  // Filter Section Locators - Who
  filterUsersLabel: string;
  filterUsersDropdown: string;
  filterDepartmentsLabel: string;
  filterDepartmentsDropdown: string;

  // Filter Section Locators - Source
  filterLocationsLabel: string;
  filterLocationsDropdown: string;
  filterClientIpLabel: string;
  filterClientIpListBuilderContainer: string; // For context
  filterClientIpListBuilderSearchInput: string;
  filterClientIpListBuilderTextarea: string;
  timeZoneDropdownInDNS: string;
  filterClientIpListBuilderAddButton: string;
  filterClientIpExistingItem: (ip: string) => string;

  // Filter Section Locators - Destination
  filterServerIpLabel: string;
  filterServerIpListBuilderContainer: string;
  filterServerIpTextarea: string;
  filterServerIpAddButton: string;
  filterServerPortsLabel: string;
  filterServerPortsListBuilderContainer: string;
  filterServerPortsTextarea: string;
  filterServerPortsAddButton: string;
  filterIpDomainClassesLabel: string;
  filterIpDomainClassesDropdown: string;
  filterIpDomainSuperCatLabel: string;
  filterIpDomainSuperCatDropdown: string;
  filterIpDomainCatLabel: string;
  filterIpDomainCatDropdown: string;


  // Filter Section Locators - Session
  filterDomainsLabel: string;
  filterDomainsListBuilderContainer: string;
  filterDomainsTextarea: string;
  filterDomainsAddButton: string;
  filterDnsReqTypesLabel: string;
  filterDnsReqTypesDropdown: string;
  filterDnsResCodesLabel: string;
  filterDnsResCodesDropdown: string;
  filterDnsResponsesLabel: string;
  filterDurationsLabel: string;

  // MultiSelect Popup Generic Locators
  multiSelectPopupSearchInput: string;
  multiSelectPopupClearSelection: string;
  multiSelectPopupInitialSelectedItemsText: string;
  multiSelectPopupSelectedItemsCount: (count: number) => string;
  multiSelectPopupDoneButton: string;
  multiSelectCheckboxByText: (text: string) => string; // Checkbox inside multi-select popup

  // Form Buttons
  formCancelButton: string;

  // Specific list builder item locators (if needed, or use dynamic ones)
  // Example: clientIpItemInList: (ip: string) => string;

  // Specific checkbox text locators (can be generated dynamically too)
  // checkboxRequestAllow: string;
  // checkboxHighRiskDnsTunnels: string;
  // ... and so on for other specific values
}

class NanologStreamingServicePage {
  fields: NanologStreamingServiceFields;

  constructor() {
    this.fields = {
      // Original fields
      nssFeedsPageTitle: "//div[@class='page-title ']//span[contains(text(),'NSS Feeds')]",
      addNssFeedButton: '//span[contains(text(),"Add NSS Feed")]',
      feedNameLabelOnForm: '//div[@id="FEED_NAME"]',
      feedTypeDropdown: "//div[@id='feedTypeDropdown']",
      logTypeDropdown: "//div[@id='logTypeDropdown']",
      genericSaveButton: "//button[text()='Save']",
      genericSearchBox: "//input[@placeholder='Search Feeds...']",
      logTypeLabel: '//div[@id="LOG_TYPE"]//span/span',

      // Common Locators
      pageHeaderTitle: '//span[@title="Nanolog Streaming Service"]',
      searchNssFeedInput: '(//input[@placeholder="Search..."])[1]',
      searchNssFeedButton: '(//span[@role="button" and @aria-label="Search"])[1]',
      feedNameInGrid: '//span[@data-testid="Feed Name"]',
      editIconInGrid: '//i[@aria-label="EDIT"]',
      editNssFeedPopupTitle: '//span[@title="Edit NSS Feed"]',
      deleteButtonInEditPopup: '//span[contains(text(),"Delete")]',
      confirmButtonInPopup: '//span[contains(text(),"Confirm")]',
      itemDeletedMessage: '//span[contains(text(),"The item has been deleted.")]',
      nssFeedsTab: '//li[@data-label="CONFIGURE_FEEDS"]',
      formSaveButton: '//span[contains(text(),"Save")]',
      changesSavedMessage: '//span[contains(text(),"All changes have been saved.")]',

      // Add/Edit NSS Feed Form - Common Fields
      feedNameActualInputOnForm: '//input[@aria-label="name"]',
      nssServerLabel: '//div[@id="NSS_SERVER"]//span/span',
      nssServerDropdown: '(//div[@testid="dropdownAdvance-nssServer"]//span/span)[1]',
      nssServerOptionNss01: '//li[@id="NSS_01"]',
      statusLabel: '//div[@id="STATUS"]',
      statusActiveRadioButton: '//button[@class="radio-button active  active"]',
      statusEnabledRadioButton: '(//div[@data-testid="radioButton-feedStatus"]//button)[1]',
      siemDestinationTypeLabel: '//div[@id="SIEM_DESTINATION_TYPE"]',
      siemTypeActiveRadioButton: '//div[@data-testid="radioButton-type"]//button[@class="radio-button active  "]',
      siemIpAddressLabel: '//span[contains(text(),"SIEM IP Address")]',
      siemIpAddressInput: '//div[@data-testid="textBox-serviceIp"]//input',
      siemTcpPortLabel: '//div[@id="SIEM_TCP_PORT"]',
      siemTcpPortInput: '//div[@data-testid="textBox-servicePort"]//input',
      siemRateLabel: '//div[@id="SIEM_RATE"]//span/span',
      logDomainLabel: '//div[@id="LOG_DOMAIN"]//span/span',
      logDomainToggleButton1: '(//div[@data-testid="radioButton-nssLogDomain"]//button)[1]',
      logTypeFormDropdown: '//span[@data-testid="dropdown-nssLogType"]',
      timeZoneLabel: '//div[@id="NSS_TIMEZONE"]//span/span',
      timeZoneDropdown: '//span[@data-testid="dropdownAdvance-timeZone"]',
      timeZoneDropdownInDNS: '(//span[@data-testid="dropdownAdvance-timeZone"]//span/span)[1]',
      timeZoneSearchInputInDropdown: '(//input[@placeholder="Search..."])[4]',
      duplicateLogsLabel: '//div[@id="DUPLICATE_LOGS"]//span/span',
      duplicateLogsDropdown: '(//span[@data-testid="dropdownAdvance-duplicateLogs"]//span/span)[1]',
      duplicateLogsOption1: '//li[@id="1"]',

      // Add/Edit NSS Feed Form - Web Specific
      siemRateToggleWebButton1: '(//div[@data-testid="radioButton-epsRateToggle"]//button)[1]',
      logTypeOptionAlert: '//li[contains(text(),"Alert")]',
      logTypeOptionTunnel: '//li[contains(text(),"Tunnel")]',
      logTypeOptionSaasSecurity: '//li[@id="CASB_LOG"]',
      logTypeOptionLogTypeDLP: '//li[@id="ENDPOINT_DLP"]',
      logTypeOptionEmailDLP:'//li[@id="EMAIL_DLP"]',
      logTypeOptionSaasSecurityActivity: '//li[@id="USER_ACT_REP"]',
      logTypeOptionFirewal: '//li[contains(text(),"Firewall Logs")]',
      logTypeOptionDNS: '//li[contains(text(),"DNS")]',
      timeZoneOptionSamoaPacificApia: '//li[@id="SAMOA_PACIFIC_APIA"]',
      timeZoneOptionAfricaAbidjan1: '//li[@id="IVORY_COAST_AFRICA_ABIDJAN"]',
      logFilterTitleWeb: '//div[@id="LOG_FILTER"]//span[contains(text(),"Log Filter")]',
      criticalLabelWeb: '//div[@id="CRITICAL"]//span/span',
      criticalToggleSwitchFalseWeb: '//span[@data-testid="toggleSwitch-critical"]//div[@aria-checked="false"]',
      warnLabelWeb: '//div[@id="WARN"]//span/span',
      warnToggleSwitchFalseWeb: '//span[@data-testid="toggleSwitch-warn"]//div[@aria-checked="false"]',

      // Add/Edit NSS Feed Form - Firewall Specific
      siemRateToggleFirewallButton2: '(//div[@data-testid="radioButton-epsRateToggle"]//button)[2]',
      siemRateLimitLabel: '//div[@id="SIEM_RATE_LIMIT"]//span/span',
      siemRateLimitInput: '//div[@data-testid="textBox-epsRateTemp"]//input',
      logTypeOptionDnsLog: '//li[@id="DNSLOG"]',
      feedOutputTypeLabel: '//div[@id="FEED_OUTPUT_TYPE"]//span/span',
      feedOutputTypeDropdown: '(//span[@data-testid="dropdown-feedOutputType"]//span)[1]',
      feedOutputTypeOptionNVP: '//li[contains(text(),"Name Value Pairs")]',
      feedEscapeCharacterLabel: '//div[@id="FEED_ESCAPE_CHARACTER"]//span/span',
      timeZoneOptionAfricaAbidjan: '//li[contains(text(),"Africa/Abidjan")]',

      nssTypeDropdown: '//span[@data-testid="dropdown-TOOLTIP_NSS_SERVER_TYPE"]//span/span[1]',
      nssTypeOptionFirewallCloudBranch: '//li[normalize-space()="NSS for Firewall, Cloud & Branch Connector"]',
      nssTypeOptionForWeb: '//li[normalize-space()="NSS for Web"]',
      nssServerOptionTestNssForWeb: '//li[@id="Test NSS for Web"]',

      // Filter Tabs (Assuming a common parent like ul.filter-tabs)
      filterActionTab: '(//span[contains(text(),"Action")])[1]',
      filterWhoTab: '(//span[contains(text(),"Who")])[1]',
      filterSourceTab: '(//span[contains(text(),"Source")])[1]',
      filterDestinationTab: '(//li/span[contains(text(),"Destination")])[1]',
      filterSessionTab: '(//li/span[contains(text(),"Session")])[1]',

      // Filter Section Locators - Action
      filterPolicyActionLabel: '//div[@id="POLICY_ACTION"]//span[contains(text(),"Policy Action")]',
      filterPolicyActionDropdown: '(//input[@placeholder="Search..."])[6]',
      filterRuleNamesLabel: '//div[@id="RULE_NAMES"]',
      filterRuleNamesDropdown: '(//div[@data-testid="multi-dropdown"]//span)[4]',

      // Filter Section Locators - Who
      filterUsersLabel: '//div[@id="USERS"]//span[contains(text(),"Users")]',
      filterUsersDropdown: '//div[@data-testid="multiDropdown-users"]//div[@role="button"]//span[2]',
      filterDepartmentsLabel: '//div[@id="DEPARTMENTS"]//span[contains(text(),"Departments")]',
      filterDepartmentsDropdown: '//div[@data-testid="multiDropdown-departments"]//div[@role="button"]//span[2]',

      // Filter Section Locators - Source
      filterLocationsLabel: '//div[@id="LOCATIONS"]//span[contains(text(),"Locations")]',
      filterLocationsDropdown: '//div[@data-testid="multiDropdown-locations"]//div[@role="button"]//span[2]',
      filterClientIpLabel: '//div[@id="CLIENT_IP_ADDRESSES"]//span[contains(text(),"Client IP Addresses")]',
      filterClientIpListBuilderContainer: '//div[@data-testid="listBuilder-clientIps"]//textarea',
      filterClientIpListBuilderSearchInput: '//div[@data-testid="listBuilder-clientIps"]//input[@placeholder="Search..."]',
      filterClientIpListBuilderTextarea: '//div[@data-testid="listBuilder-clientIps"]//textarea',
      filterClientIpListBuilderAddButton: '//div[@data-testid="listBuilder-clientIps"]//button[normalize-space()="Add Items"]',
      filterClientIpExistingItem: (ip: string) => `//div[@data-testid="listBuilder-clientIps"]//div[@aria-label="${ip}"]`,

      // Filter Section Locators - Destination (derived from provided HTML snippet)
      filterServerIpLabel: '//div[@id="SERVER_IP_ADDRESSES"]//span[contains(text(),"Server IP Addresses")]',
      filterServerIpListBuilderContainer: '//div[@data-testid="listBuilder-serverIps"]',
      filterServerIpTextarea: '//div[@data-testid="listBuilder-serverIps"]//textarea',
      filterServerIpAddButton: '//div[@data-testid="listBuilder-serverIps"]//button[normalize-space()="Add Items"]',
      filterServerPortsLabel: '//div[@id="SERVER_PORTS"]//span[contains(text(),"Server Ports")]',
      filterServerPortsListBuilderContainer: '//div[@data-testid="listBuilder-serverDestinationPorts"]',
      filterServerPortsTextarea: '//div[@data-testid="listBuilder-serverDestinationPorts"]//textarea',
      filterServerPortsAddButton: '//div[@data-testid="listBuilder-serverDestinationPorts"]//button[normalize-space()="Add Items"]',
      filterIpDomainClassesLabel: '//div[@id="IP_DOMAIN_CLASSES"]//span[contains(text(),"IP Domain Classes")]',
      filterIpDomainClassesDropdown: '//div[@data-testid="multiDropdown-ipDomainClasses"]//div[@role="button"]',
      filterIpDomainSuperCatLabel: '//div[@id="IP_DOMAIN_SUPER_CATEGORIES"]//span[contains(text(),"IP Domain Super Categories")]',
      filterIpDomainSuperCatDropdown: '//div[@data-testid="multiDropdown-ipDomainSuperCategories"]//div[@role="button"]',
      filterIpDomainCatLabel: '//div[@id="IP_DOMAIN_CATEGORIES"]//span[contains(text(),"IP Domain Categories")]',
      filterIpDomainCatDropdown: '//div[@data-testid="multiDropdown-ipDomainCategories"]//div[@role="button"]',


      // Filter Section Locators - Session
      filterDomainsLabel: '//div[@id="DNS_FILTER_DOMAINS"]//span[contains(text(),"Domains")]', // Assuming an ID
      filterDomainsListBuilderContainer: '//div[@data-testid="listBuilder-dnsDomains"]',
      filterDomainsTextarea: '//div[@data-testid="listBuilder-dnsDomains"]//textarea', // Assuming data-testid
      filterDomainsAddButton: '//div[@data-testid="listBuilder-dnsDomains"]//button[normalize-space()="Add Items"]',
      filterDnsReqTypesLabel: '//div[@id="DNS_REQUEST_TYPES"]//span[contains(text(),"DNS Request Types")]',
      filterDnsReqTypesDropdown: '//div[@data-testid="multiDropdown-dnsRequestTypes"]//div[@role="button"]',
      filterDnsResCodesLabel: '//div[@id="DNS_RESPONSE_CODES"]//span[contains(text(),"DNS Response Codes")]',
      filterDnsResCodesDropdown: '//div[@data-testid="multiDropdown-dnsResponseCodes"]//div[@role="button"]',
      filterDnsResponsesLabel: '//div[@id="DNS_RESPONSES"]//span[contains(text(),"DNS Responses")]', // Assuming an ID
      filterDurationsLabel: '//div[@id="DURATION"]//span[contains(text(),"Durations")]', // Assuming an ID

      // MultiSelect Popup Generic Locators
      multiSelectPopupSearchInput: '//div[contains(@class,"multi-select-dialog")]//input[@placeholder="Search..."]',
      multiSelectPopupClearSelection: '//span[contains(text(),"Clear Selection")]',
      multiSelectPopupInitialSelectedItemsText: '(//div[@data-testid="multi-dropdown"]//div[@class="dialog-header"])[2]',
      multiSelectPopupSelectedItemsCount: (count: number) => `//span[contains(text(),"Selected Items ( ${count} )")]`,
      multiSelectPopupDoneButton: '//span[contains(text(),"Done")]',
      multiSelectCheckboxByText: (text: string) => `//label[normalize-space()="${text}"]//input[@type="checkbox"]`,

      // Form Buttons
      formCancelButton: '(//span[contains(text(),"Cancel")])[1]',
    };
  }

  async navigateToNSSFeeds(page: Page): Promise<void> {
    console.log("Executing navigateToNSSFeeds...");
    console.log("The Url used is: " + url);

    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
    }

    const nssFeedsPath = "administration/nss-settings";
    let targetUrl = "";

    if (url.includes("console")) {
      targetUrl = url + "internet-saas#" + nssFeedsPath;
      await page.goto(targetUrl);
      console.log("Page navigation initiated (console URL).");
      await page.waitForTimeout(45000); 

    } else {
      targetUrl = url + "#" + nssFeedsPath;
      await page.goto(targetUrl);
      await page.waitForTimeout
      console.log("Page navigation initiated (non-console URL).");
      await page.waitForTimeout(19000); 

    }

    // Dynamic wait for a specific element on the NSS Feeds page
    const nssFeedsHeader = page.locator('//span[@title="Nanolog Streaming Service"]');
    await nssFeedsHeader.waitFor({ state: 'visible', timeout: 60000 });

    // Verify that the expected URL matches
    await expect(page).toHaveURL(new RegExp(nssFeedsPath.replace(/\//g, '\\/')));
    console.log("Successfully navigated to NSS Feeds (Nanolog Streaming Service) screen.");
  }

  async saveForm(page: Page): Promise<void> {
    console.log("Executing saveForm...");
    const saveButton = await page.locator(this.fields.formSaveButton);
    await saveButton.isVisible();
    await saveButton.click();
    await page.waitForTimeout(2000); // Keep existing wait pattern
    console.log("Save button clicked.");
  }

  async verifyChangesSaved(page: Page): Promise<void> {
    console.log("Executing verifyChangesSaved...");
    const successMessage = await page.locator(this.fields.changesSavedMessage);
    await expect(successMessage).toBeVisible({ timeout: 10000 });
    await expect(successMessage).toContainText("All changes have been saved.");
    console.log("Changes saved message verified.");
  }

  async verifyFeedVisibleInGrid(page: Page, feedName: string): Promise<void> {
    console.log(`Executing verifyFeedVisibleInGrid for: "${feedName}"`);
    const feedInGridLocator = page.locator(`${this.fields.feedNameInGrid}[contains(text(),"${feedName}")]`);
    await expect(feedInGridLocator.first()).toBeVisible({ timeout: 10000 });
    console.log(`Verified: Feed "${feedName}" is visible in the grid.`);
  }


  /**
   * Fills the NSS Feed form for "NSS for Web" type.
   * Assumes the "Add NSS Feed" form is already open.
   * Uses parameters for feed name, log type, SIEM IP, and SIEM port.
   */
  async fillNSSFeedForWebForm(page: Page, feedName: string, logType: string, siemIp: string, siemPort: string): Promise<void> {
    console.log(`Filling NSS Feed form for Web: Name=${feedName}, LogType=${logType}, IP=${siemIp}, Port=${siemPort}`);

    const feedNameLabel = await page.locator(this.fields.feedNameLabelOnForm);
    await feedNameLabel.isVisible();
    await expect(feedNameLabel).toContainText("Feed Name");
    await page.waitForTimeout(2000);

    const name = await page.locator(this.fields.feedNameActualInputOnForm);
    await name.isVisible();
    await name.fill(feedName);
    await page.waitForTimeout(2000);

    const nssType = await page.locator(this.fields.nssTypeDropdown);
    await nssType.isVisible();
    await nssType.click();
    await page.waitForTimeout(2000);

    const nssSelectValue = await page.locator(this.fields.nssTypeOptionForWeb);
    await nssSelectValue.isVisible();
    await nssSelectValue.click();
    await page.waitForTimeout(2000);


    const nss_serverFieldName = await page.locator(this.fields.nssServerLabel);
    await nss_serverFieldName.isVisible();
    await expect(nss_serverFieldName).toContainText("NSS Server");
    await page.waitForTimeout(2000);

    const nss_serverDropdown = await page.locator(this.fields.nssServerDropdown);
    await nss_serverDropdown.isVisible();
    await nss_serverDropdown.click();
    await page.waitForTimeout(2000);

    const nss_Server = await page.locator(this.fields.nssServerOptionTestNssForWeb);
    await nss_Server.isVisible();
    await nss_Server.click();
    await page.waitForTimeout(2000);

    const statusFieldName = await page.locator(this.fields.statusLabel);
    await statusFieldName.isVisible();
    await expect(statusFieldName).toContainText("Status");
    await page.waitForTimeout(2000);

    const currentStatus = await page.locator(this.fields.statusActiveRadioButton);
    if (await currentStatus.isVisible()) {
      console.log("Status is already active");
    } else {
      const statusEnabled = await page.locator(this.fields.statusEnabledRadioButton);
      await statusEnabled.isVisible();
      await statusEnabled.click();
      console.log("Statue is enabled");
    }
    await page.waitForTimeout(2000);

    const siemFieldName = await page.locator(this.fields.siemDestinationTypeLabel);
    await siemFieldName.isVisible();
    await expect(siemFieldName).toContainText("SIEM Destination Type");
    await page.waitForTimeout(2000);

    const siemToggle = await page.locator(this.fields.siemTypeActiveRadioButton);
    await siemToggle.isVisible();
    await siemToggle.click();
    await page.waitForTimeout(2000);

    const ip_addressFieldName = await page.locator(this.fields.siemIpAddressLabel);
    await ip_addressFieldName.isVisible();
    await expect(ip_addressFieldName).toContainText("SIEM IP Address");
    await page.waitForTimeout(2000);

    const ip_address = await page.locator(this.fields.siemIpAddressInput);
    await ip_address.isVisible();
    await ip_address.click();
    await ip_address.fill(siemIp);
    await page.waitForTimeout(2000);

    const tcp_portFieldName = await page.locator(this.fields.siemTcpPortLabel);
    await tcp_portFieldName.isVisible();
    await expect(tcp_portFieldName).toContainText("SIEM TCP Port");
    await page.waitForTimeout(2000);

    const tcp_portfield = await page.locator(this.fields.siemTcpPortInput);
    await tcp_portfield.isVisible();
    await tcp_portfield.click();
    await tcp_portfield.fill(siemPort);
    await page.waitForTimeout(2000);

    const siemRateFieldName = await page.locator(this.fields.siemRateLabel);
    await siemRateFieldName.isVisible();
    await expect(siemRateFieldName).toContainText("SIEM Rate");
    await page.waitForTimeout(2000);

    const siemRateToggle = await page.locator(this.fields.siemRateToggleWebButton1);
    await siemRateToggle.isVisible();
    await siemRateToggle.click();
    await page.waitForTimeout(2000);

    const logTypeFieldName = await page.locator(this.fields.logTypeLabel);
    await logTypeFieldName.isVisible();
    await expect(logTypeFieldName).toContainText("Log Type");
    await page.waitForTimeout(2000);

    const logType_Dropdown = await page.locator(this.fields.logTypeFormDropdown);
    await logType_Dropdown.isVisible();
    await logType_Dropdown.click();
    await page.waitForTimeout(2000);

    let logTypeOptionLocatorValue;
    if (logType === "Alert") {
      logTypeOptionLocatorValue = this.fields.logTypeOptionAlert;
    } else if (logType === "Tunnel") {
      logTypeOptionLocatorValue = this.fields.logTypeOptionTunnel;
    } // Add other log types for Web if needed
    else {
      throw new Error(`Unsupported log type "${logType}" for NSS for Web in fillNSSFeedForWebForm.`);
    }
    await page.locator(logTypeOptionLocatorValue).click();
    await page.waitForTimeout(2000);

    const timeZoneFieldName = await page.locator(this.fields.timeZoneLabel);
    await timeZoneFieldName.isVisible();
    await page.waitForTimeout(2000);
    await expect(timeZoneFieldName).toContainText("Time Zone");
    await page.waitForTimeout(2000);

    const timeZoneDropDown = await page.locator(this.fields.timeZoneDropdown);
    await timeZoneDropDown.isVisible();
    await page.waitForTimeout(2000);
    await timeZoneDropDown.click();
    await page.waitForTimeout(2000);

    const searchTimeZone = await page.locator(this.fields.timeZoneSearchInputInDropdown);
    await searchTimeZone.isVisible();
    await page.waitForTimeout(2000);
    await searchTimeZone.fill('Pacific');
    await page.waitForTimeout(2000);

    const selectTimeZone = await page.locator(this.fields.timeZoneOptionSamoaPacificApia);
    await selectTimeZone.isVisible();
    await page.waitForTimeout(2000);
    await selectTimeZone.click();
    await page.waitForTimeout(2000);

    const duplicateLogs = await page.locator(this.fields.duplicateLogsLabel);
    await duplicateLogs.isVisible();
    await page.waitForTimeout(2000);
    await expect(duplicateLogs).toContainText("Duplicate Logs");
    await page.waitForTimeout(2000);

    const duplicateDropdown = await page.locator(this.fields.duplicateLogsDropdown);
    await duplicateDropdown.isVisible();
    await duplicateDropdown.click();
    await page.waitForTimeout(2000);

    const selectDuplicateValue = await page.locator(this.fields.duplicateLogsOption1);
    await selectDuplicateValue.isVisible();
    await selectDuplicateValue.click();
    await page.waitForTimeout(2000);

    const logFilterTitle = await page.locator(this.fields.logFilterTitleWeb);
    await logFilterTitle.isVisible();
    await expect(logFilterTitle).toContainText("Log Filter");
    await page.waitForTimeout(2000);

    const criticalFieldName = await page.locator(this.fields.criticalLabelWeb);
    await criticalFieldName.isVisible();
    await expect(criticalFieldName).toContainText("Critical");
    await page.waitForTimeout(2000);

    const criticalStatus = await page.locator(this.fields.criticalToggleSwitchFalseWeb);

    if (await criticalStatus.isVisible()) {
      console.log("Critical is already disabled");
      await criticalStatus.click();
    } else {
      console.log("Critical is already enabled");
    }
    await page.waitForTimeout(2000);

    const warnFieldName = await page.locator(this.fields.warnLabelWeb);
    await warnFieldName.isVisible();
    await expect(warnFieldName).toContainText("Warn");
    const warnStatus = await page.locator(this.fields.warnToggleSwitchFalseWeb);
    await page.waitForTimeout(2000);

    if (await warnStatus.isVisible()) {
      console.log("Warn is already disabled");
      await warnStatus.click();
    } else {
      console.log("Warn is already enabled");
    }
    await page.waitForTimeout(2000);

    // Post-cleanup will be handled by explicit steps in the feature file.
    // Saving and verifying success are handled by separate steps in the feature file.
    console.log(`Finished adding NSS Feed for Web: "${feedName}"`);
  }

  async checkNssServer(page: Page, s: string, s1: string): Promise<void> {
    await page.locator('//span[contains(text(),"NSS Servers")]').click();
    const search = await page.locator('(//input[@placeholder="Search..."])[1]');
    await search.click();
    await search.fill(s);
    await page.waitForTimeout(2000);
    await page.locator('(//span[@aria-label="Search"])[1]').click();
    await page.waitForTimeout(2000);

    const value = page.locator(`//div[contains(text(),"${s}")]`);
    const noMatchValue = page.locator('//div[contains(text(),"No matching data found")]');

    if (await value.isVisible()) {
      await expect(value).toContainText(s);
      await page.waitForTimeout(2000);
    } else if (await noMatchValue.isVisible()) {
      await page.waitForTimeout(2000);
      await this.createNssServer(page, s, s1);
    } else {
      throw new Error("Unexpected condition: Could not find the expected elements.");
    }
  }

  async createNssServer(page: Page, s: string, s1: string): Promise<void> {
    await page.locator('//span[contains(text(),"Add NSS Server")]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[@title="Add NSS Server"]').isVisible();
    const name = await page.locator('//input[@aria-label="name"]');
    await name.click();
    await name.fill(s);
    await page.locator('//span[@aria-labelledby="TOOLTIP_NSS_SERVER_TYPE"]/span[2]').click();
    await page.locator(`//ul/li[contains(text(),"${s1}")]`).click();
    await page.locator('//span[contains(text(),"Save")]').click();
    await page.waitForTimeout(2000);
  }
  async addNSSFeedForFirewall(page: Page, feedName: string): Promise<void> {
    const randomIPAddress = `${Math.floor(Math.random() * 254) + 1}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;

    const title1 = page.locator(this.fields.pageHeaderTitle);
    await title1.isVisible();
    await expect(title1).toHaveText("Nanolog Streaming Service");

    // Navigate to NSS Feeds tab first (as per original logic flow)
    const nssFeedOption = page.locator(this.fields.nssFeedsTab);
    await expect(nssFeedOption).toContainText("NSS Feeds");
    await nssFeedOption.click();
    await page.waitForTimeout(2000);

    // Pre-cleanup and Post-cleanup will be handled by explicit steps in the feature file.
    console.log(`Starting to add NSS Feed for Firewall: "${feedName}"`);
    // Ensure search input is visible before proceeding (good practice)
    await expect(page.locator(this.fields.searchNssFeedInput)).toBeVisible();

    const addNssFeedButton = await page.locator(this.fields.addNssFeedButton);
    await addNssFeedButton.isVisible();
    await addNssFeedButton.click();
    await page.waitForTimeout(2000);

    const feedNameLabel = await page.locator(this.fields.feedNameLabelOnForm);
    await feedNameLabel.isVisible();
    await expect(feedNameLabel).toContainText("Feed Name");
    await page.waitForTimeout(2000);

    const name = await page.locator(this.fields.feedNameActualInputOnForm);
    await name.isVisible();
    await name.fill(feedName);
    await page.waitForTimeout(2000);

    const nssType = await page.locator(this.fields.nssTypeDropdown);
    await nssType.isVisible();
    await nssType.click();
    await page.waitForTimeout(2000);

    const nssSelectValue = await page.locator(this.fields.nssTypeOptionFirewallCloudBranch);
    await nssSelectValue.isVisible();
    await nssSelectValue.click();
    await page.waitForTimeout(2000);

    const nss_serverFieldName = await page.locator(this.fields.nssServerLabel);
    await nss_serverFieldName.isVisible();
    await expect(nss_serverFieldName).toContainText("NSS Server");
    await page.waitForTimeout(2000);

    const nss_serverDropdown = await page.locator(this.fields.nssServerDropdown);
    await nss_serverDropdown.isVisible();
    await nss_serverDropdown.click();
    await page.waitForTimeout(2000);

    const nss_Server = await page.locator('//li[@id="NSS_01"]');
    await nss_Server.isVisible();
    await nss_Server.click();
    await page.waitForTimeout(2000);

    const statusFieldName = await page.locator(this.fields.statusLabel);
    await statusFieldName.isVisible();
    await expect(statusFieldName).toContainText("Status");
    await page.waitForTimeout(2000);

    const currentStatus = await page.locator(this.fields.statusActiveRadioButton);
    if (await currentStatus.isVisible()) {
      console.log("Status is already active");
    } else {
      const statusEnabled = await page.locator(this.fields.statusEnabledRadioButton);
      await statusEnabled.isVisible();
      await statusEnabled.click();
      console.log("Statue is enabled");
    }
    await page.waitForTimeout(2000);

    const siemFieldName = await page.locator(this.fields.siemDestinationTypeLabel);
    await siemFieldName.isVisible();
    await expect(siemFieldName).toContainText("SIEM Destination Type");
    await page.waitForTimeout(2000);

    const siemToggle = await page.locator('//div[@data-testid="radioButton-type"]//button[@class="radio-button active  "]');
    await siemToggle.isVisible();
    await siemToggle.click();
    await page.waitForTimeout(2000);

    const ip_addressFieldName = await page.locator(this.fields.siemIpAddressLabel);
    await ip_addressFieldName.isVisible();
    await expect(ip_addressFieldName).toContainText("SIEM IP Address");
    await page.waitForTimeout(2000);

    const ip_address = await page.locator(this.fields.siemIpAddressInput);
    await ip_address.isVisible();
    await ip_address.click();
    await ip_address.fill(randomIPAddress);
    await page.waitForTimeout(2000);

    const tcp_portFieldName = await page.locator(this.fields.siemTcpPortLabel);
    await tcp_portFieldName.isVisible();
    await expect(tcp_portFieldName).toContainText("SIEM TCP Port");
    await page.waitForTimeout(2000);

    const tcp_portfield = await page.locator(this.fields.siemTcpPortInput);
    await tcp_portfield.isVisible();
    await tcp_portfield.click();
    await tcp_portfield.fill('443');
    await page.waitForTimeout(2000);

    const siemRateFieldName = await page.locator(this.fields.siemRateLabel);
    await siemRateFieldName.isVisible();
    await expect(siemRateFieldName).toContainText("SIEM Rate");
    await page.waitForTimeout(2000);

    const siemRateToggle = await page.locator(this.fields.siemRateToggleFirewallButton2);
    await siemRateToggle.isVisible();
    await siemRateToggle.click();
    await page.waitForTimeout(2000);

    // SIEM Rate Limit (Events per Second)
    const siemRateLimitFieldName = await page.locator(this.fields.siemRateLimitLabel);
    await siemRateLimitFieldName.isVisible();
    await expect(siemRateLimitFieldName).toContainText("SIEM Rate Limit (Events per Second)");
    const siemRateLimitInput = await page.locator(this.fields.siemRateLimitInput);
    await siemRateLimitInput.isVisible();
    await siemRateLimitInput.fill("1000000");
    await page.waitForTimeout(2000);

    const logDomain = await page.locator(this.fields.logDomainLabel);
    await logDomain.isVisible();
    await expect(logDomain).toContainText("Log Domain");
    await page.waitForTimeout(2000);

    const logDomainToggle = await page.locator(this.fields.logDomainToggleButton1);
    await logDomainToggle.isVisible();
    await logDomainToggle.click();
    await page.waitForTimeout(2000);

    const logTypeFieldName = await page.locator(this.fields.logTypeLabel);
    await logTypeFieldName.isVisible();
    await expect(logTypeFieldName).toContainText("Log Type");
    await page.waitForTimeout(2000);

    const logType_Dropdown = await page.locator(this.fields.logTypeFormDropdown);
    await logType_Dropdown.isVisible();
    await logType_Dropdown.click();
    await page.waitForTimeout(2000);

    const selectLogTypeDNS = await page.locator(this.fields.logTypeOptionDnsLog);
    await selectLogTypeDNS.isVisible();
    await selectLogTypeDNS.click();
    await page.waitForTimeout(2000);

    // Feed Output Type
    const feedOutputTypeFieldName = await page.locator(this.fields.feedOutputTypeLabel);
    await feedOutputTypeFieldName.isVisible();
    await expect(feedOutputTypeFieldName).toContainText("Feed Output Type"); // Or actual text
    const feedOutputTypeDropdown = await page.locator(this.fields.feedOutputTypeDropdown);
    await feedOutputTypeDropdown.isVisible();
    await feedOutputTypeDropdown.click();
    await page.waitForTimeout(2000);
    const selectFeedOutputTypeNVP = await page.locator(this.fields.feedOutputTypeOptionNVP);
    await selectFeedOutputTypeNVP.isVisible();
    await selectFeedOutputTypeNVP.click();
    await page.waitForTimeout(2000);

    // Feed Escape Character
    const feedEscapeCharacterLabel = await page.locator(this.fields.feedEscapeCharacterLabel);
    await feedEscapeCharacterLabel.isVisible();
    const feedEscapeLabel = await feedEscapeCharacterLabel.textContent();
    await expect(feedEscapeLabel).toContain("Feed Escape Character");

    const timeZoneFieldName = await page.locator(this.fields.timeZoneLabel);
    await timeZoneFieldName.isVisible();
    await expect(timeZoneFieldName).toContainText("Time Zone");
    await page.waitForTimeout(2000);

    const timeZoneDropDown = await page.locator(this.fields.timeZoneDropdown);
    await timeZoneDropDown.isVisible();
    await timeZoneDropDown.click();
    await page.waitForTimeout(2000);

    const searchTimeZone = await page.locator(this.fields.timeZoneSearchInputInDropdown);
    await searchTimeZone.isVisible();
    await searchTimeZone.fill('Africa/Abidjan');
    await page.waitForTimeout(2000);

    const selectTimeZone = await page.locator(this.fields.timeZoneOptionAfricaAbidjan);
    await selectTimeZone.isVisible(); // Ensure it's visible before clicking
    await selectTimeZone.click();
    await page.waitForTimeout(2000);

    const duplicateLogs = await page.locator(this.fields.duplicateLogsLabel);
    await duplicateLogs.isVisible();
    await expect(duplicateLogs).toContainText("Duplicate Logs");
    await page.waitForTimeout(2000);

    const duplicateDropdown = await page.locator('(//span[@data-testid="dropdownAdvance-duplicateLogs"]//span/span)[1]');
    await duplicateDropdown.isVisible();
    await duplicateDropdown.click();
    await page.waitForTimeout(2000);

    const selectDuplicateValue = await page.locator(this.fields.duplicateLogsOption1);
    await selectDuplicateValue.isVisible();
    await selectDuplicateValue.click();
    await page.waitForTimeout(2000);

    const save = await page.locator(this.fields.formSaveButton);
    await save.isVisible();
    await save.click();
    await page.waitForTimeout(2000);

    const success = await page.locator(this.fields.changesSavedMessage);
    await success.waitFor();
    await expect(success).toContainText("All changes have been saved.");

    //search in list view page
    const title = page.locator(this.fields.pageHeaderTitle);
    await title.isVisible();
    await expect(title).toHaveText("Nanolog Streaming Service");

    // Post-cleanup will be handled by explicit steps in the feature file.
    console.log(`Finished adding NSS Feed for Firewall: "${feedName}"`);
  }

  async addNSSFeedForFirewallDNSLogWithAllFiltersAndCancel(page: Page, feedName: string): Promise<void> {

    const title1 = page.locator(this.fields.pageHeaderTitle);
    await title1.isVisible();
    await expect(title1).toHaveText("Nanolog Streaming Service");

    // Navigate to NSS Feeds tab first (as per original logic flow)
    const nssFeedOption = page.locator(this.fields.nssFeedsTab);
    await expect(nssFeedOption).toContainText("NSS Feeds");
    await nssFeedOption.click();
    await page.waitForTimeout(2000);

    const addNssFeedButton = await page.locator(this.fields.addNssFeedButton);
    await addNssFeedButton.isVisible();
    await addNssFeedButton.click();
    await page.waitForTimeout(2000);

    const feedNameLabel = await page.locator(this.fields.feedNameLabelOnForm);
    await feedNameLabel.isVisible();
    await expect(feedNameLabel).toContainText("Feed Name");
    await page.waitForTimeout(2000);

    const name = await page.locator(this.fields.feedNameActualInputOnForm);
    await name.isVisible();
    await name.fill(feedName);
    await page.waitForTimeout(2000);

    const nssType = await page.locator(this.fields.nssTypeDropdown);
    await nssType.isVisible();
    await nssType.click();
    await page.waitForTimeout(2000);

    const nssSelectValue = await page.locator(this.fields.nssTypeOptionFirewallCloudBranch);
    await nssSelectValue.isVisible();
    await nssSelectValue.click();
    await page.waitForTimeout(2000);

    const nss_serverFieldName = await page.locator(this.fields.nssServerLabel);
    await nss_serverFieldName.isVisible();
    await expect(nss_serverFieldName).toContainText("NSS Server");
    await page.waitForTimeout(2000);

    const nss_serverDropdown = await page.locator(this.fields.nssServerDropdown);
    await nss_serverDropdown.isVisible();
    await nss_serverDropdown.click();
    await page.waitForTimeout(2000);

    const nss_Server = await page.locator(this.fields.nssServerOptionNss01);
    await nss_Server.isVisible();
    await nss_Server.click();
    await page.waitForTimeout(2000);

    const statusFieldName = await page.locator(this.fields.statusLabel);
    await statusFieldName.isVisible();
    await expect(statusFieldName).toContainText("Status");
    await page.waitForTimeout(2000);

    const currentStatus = await page.locator(this.fields.statusActiveRadioButton);
    if (await currentStatus.isVisible()) {
      console.log("Status is already active");
    } else {
      const statusEnabled = await page.locator(this.fields.statusEnabledRadioButton);
      await statusEnabled.isVisible();
      await statusEnabled.click();
      console.log("Statue is enabled");
    }
    await page.waitForTimeout(2000);

    const siemFieldName = await page.locator(this.fields.siemDestinationTypeLabel);
    await siemFieldName.isVisible();
    await expect(siemFieldName).toContainText("SIEM Destination Type");
    await page.waitForTimeout(2000);

    const siemToggle = await page.locator(this.fields.siemTypeActiveRadioButton);
    await siemToggle.isVisible();
    await siemToggle.click();
    await page.waitForTimeout(2000);

    const ip_addressFieldName = await page.locator(this.fields.siemIpAddressLabel);
    await ip_addressFieldName.isVisible();
    await expect(ip_addressFieldName).toContainText("SIEM IP Address");
    await page.waitForTimeout(2000);

    const ip_address = await page.locator(this.fields.siemIpAddressInput);
    await ip_address.isVisible();
    await ip_address.click();
    await ip_address.fill("127.0.0.1");
    await page.waitForTimeout(2000);

    const tcp_portFieldName = await page.locator(this.fields.siemTcpPortLabel);
    await tcp_portFieldName.isVisible();
    await expect(tcp_portFieldName).toContainText("SIEM TCP Port");
    await page.waitForTimeout(2000);

    const tcp_portfield = await page.locator(this.fields.siemTcpPortInput);
    await tcp_portfield.isVisible();
    await tcp_portfield.click();
    await tcp_portfield.fill('443');
    await page.waitForTimeout(2000);

    const siemRateFieldName = await page.locator(this.fields.siemRateLabel);
    await siemRateFieldName.isVisible();
    await expect(siemRateFieldName).toContainText("SIEM Rate");
    await page.waitForTimeout(2000);

    const siemRateToggle = await page.locator(this.fields.siemRateToggleWebButton1);
    await siemRateToggle.isVisible();
    await siemRateToggle.click();
    await page.waitForTimeout(2000);

    const logDomain = await page.locator(this.fields.logDomainLabel);
    await logDomain.isVisible();
    await expect(logDomain).toContainText("Log Domain");
    await page.waitForTimeout(2000);

    const logDomainToggle = await page.locator(this.fields.logDomainToggleButton1);
    await logDomainToggle.isVisible();
    await logDomainToggle.click();
    await page.waitForTimeout(2000);

    const logTypeFieldName = await page.locator(this.fields.logTypeLabel);
    await logTypeFieldName.isVisible();
    await expect(logTypeFieldName).toContainText("Log Type");
    await page.waitForTimeout(2000);

    const logType_Dropdown = await page.locator(this.fields.logTypeFormDropdown);
    await logType_Dropdown.isVisible();
    await logType_Dropdown.click();
    await page.waitForTimeout(2000);

    const selectDNS_Dropdown = await page.locator(this.fields.logTypeOptionDNS);
    await selectDNS_Dropdown.isVisible();
    await selectDNS_Dropdown.click();
    await page.waitForTimeout(2000);

    const timeZoneFieldName = await page.locator(this.fields.timeZoneLabel);
    await timeZoneFieldName.isVisible();
    await expect(timeZoneFieldName).toContainText("Time Zone");
    await page.waitForTimeout(2000);

    const timeZoneDropDown = await page.locator(this.fields.timeZoneDropdownInDNS);
    await timeZoneDropDown.isVisible();
    await timeZoneDropDown.click();
    await page.waitForTimeout(2000);

    const searchTimeZone = await page.locator(this.fields.timeZoneSearchInputInDropdown);
    await searchTimeZone.isVisible();
    await searchTimeZone.fill('Africa/Abidjan');
    await page.waitForTimeout(3000);

    const selectTimeZone = await page.locator(this.fields.timeZoneOptionAfricaAbidjan1);
    await selectTimeZone.isVisible();
    await selectTimeZone.click();
    await page.waitForTimeout(3000);

    const duplicateLogs = await page.locator(this.fields.duplicateLogsLabel);
    await duplicateLogs.isVisible();
    await expect(duplicateLogs).toContainText("Duplicate Logs");
    await page.waitForTimeout(2000);

    const duplicateDropdown = await page.locator(this.fields.duplicateLogsDropdown);
    await duplicateDropdown.isVisible();
    await duplicateDropdown.click();
    await page.waitForTimeout(2000);

    const selectDuplicateValue = await page.locator(this.fields.duplicateLogsOption1);
    await selectDuplicateValue.isVisible();
    await selectDuplicateValue.click();
    await page.waitForTimeout(2000);

    const actionTabLocator = page.getByText('Action', { exact: true });
    await expect(actionTabLocator).toBeVisible();
    await expect(actionTabLocator).toHaveText('Action');
    await actionTabLocator.click();
    await page.waitForTimeout(2000);

    const policyActionLabel = page.getByText('Policy Action', { exact: true });
    await expect(policyActionLabel).toBeVisible();
    await expect(policyActionLabel).toHaveText('Policy Action');
    // await policyActionLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-dnsActions').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multiDropdown-dnsActions').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByTestId('multiDropdown-dnsActions').getByRole('textbox', { name: 'Search...' }).fill('Allow');
    await page.getByText('Allow', { exact: true }).click();
    await page.getByText('Selected Items ( 1 )').click();
    await page.getByTestId('multiDropdown-dnsActions').getByText('Done').click();

    const ruleNamesLabel = page.getByTestId('multiDropdown-rules').getByText('Rule Names');
    await expect(ruleNamesLabel).toBeVisible();
    await expect(ruleNamesLabel).toHaveText('Rule Names');
    // await ruleNamesLabel.click(); // Click removed
    await page.waitForTimeout(3000);

    await page.getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const whoTabLocator = page.getByText('Who'); // This will find the first visible 'Who'
    await expect(whoTabLocator.first()).toBeVisible();
    await expect(whoTabLocator.first()).toHaveText('Who');
    await whoTabLocator.first().click();
    await page.waitForTimeout(2000);

    const dnsFiltersHeading = page.locator('//span[contains(text(),"DNS Filters")]');
    await dnsFiltersHeading.isVisible();
    await expect(dnsFiltersHeading).toContainText('DNS Filters');
    await page.waitForTimeout(2000);

    const usersLabel = page.getByText('Users', { exact: true });
    await expect(usersLabel).toBeVisible();
    await expect(usersLabel).toHaveText('Users');
    // await usersLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-users').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const departmentsLabel = page.getByText('Departments', { exact: true });
    await expect(departmentsLabel).toBeVisible();
    await expect(departmentsLabel).toHaveText('Departments');
    // await departmentsLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const sourceTabLocator = page.getByText('Source', { exact: true });
    await expect(sourceTabLocator).toBeVisible();
    await expect(sourceTabLocator).toHaveText('Source');
    await sourceTabLocator.click();
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-locations').getByText('Locations', { exact: true }).isVisible();
    await page.locator('//div[@id="LOCATIONS"]/following-sibling::div/div/div/span[2]').click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const clientIpAddressesLabel = page.getByText('Client IP Addresses');
    await expect(clientIpAddressesLabel).toBeVisible();
    await expect(clientIpAddressesLabel).toContainText('Client IP Addresses');
    // await clientIpAddressesLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByRole('textbox', { name: 'Add Items' }).fill('*******');
    await page.getByRole('button', { name: 'Add Items' }).click();
    await page.getByText('*******').click();
    await page.waitForTimeout(3000);

    const destinationTabLocator = page.getByText('Destination', { exact: true });
    await expect(destinationTabLocator).toBeVisible();
    await expect(destinationTabLocator).toHaveText('Destination');
    await destinationTabLocator.click();
    await page.waitForTimeout(2000);

    const serverIpAddressesLabel = page.getByText('Server IP Addresses', { exact: true });
    await expect(serverIpAddressesLabel).toBeVisible();
    await expect(serverIpAddressesLabel).toHaveText('Server IP Addresses');
    // await serverIpAddressesLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-serverIps').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-serverIps').getByRole('textbox', { name: 'Add Items' }).fill('***********');
    await page.getByTestId('listBuilder-serverIps').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    const serverPortsLabel = page.getByText('Server Ports', { exact: true });
    await expect(serverPortsLabel).toBeVisible();
    await expect(serverPortsLabel).toHaveText('Server Ports');
    // await serverPortsLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-serverDestinationPorts').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-serverDestinationPorts').getByRole('textbox', { name: 'Add Items' }).fill('443');
    await page.getByTestId('listBuilder-serverDestinationPorts').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    const ipDomainClassesLabel = page.getByText('IP Domain Classes');
    await expect(ipDomainClassesLabel).toBeVisible();
    await expect(ipDomainClassesLabel).toContainText('IP Domain Classes');
    // await ipDomainClassesLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-ipDomainClasses').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const ipDomainSuperCategoriesLabel = page.getByText('IP Domain Super Categories');
    await expect(ipDomainSuperCategoriesLabel).toBeVisible();
    await expect(ipDomainSuperCategoriesLabel).toContainText('IP Domain Super Categories');
    // await ipDomainSuperCategoriesLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-ipDomainSuperCategories').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multiDropdown-ipDomainSuperCategories').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByTestId('multiDropdown-ipDomainSuperCategories').getByRole('textbox', { name: 'Search...' }).fill('Drugs');
    await page.waitForTimeout(2000);
    await page.getByText('Drugs').click();
    await page.getByText('Selected Items ( 1 )').click();
    await page.getByTestId('multiDropdown-ipDomainSuperCategories').getByText('Done').click();
    await page.waitForTimeout(3000);

    const ipDomainCategoriesLabel = page.getByText('IP Domain Categories');
    await expect(ipDomainCategoriesLabel).toBeVisible();
    await expect(ipDomainCategoriesLabel).toContainText('IP Domain Categories');
    // await ipDomainCategoriesLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const sessionTabLocator = page.getByText('Session', { exact: true });
    await expect(sessionTabLocator).toBeVisible();
    await expect(sessionTabLocator).toHaveText('Session');
    await sessionTabLocator.click();
    await page.waitForTimeout(2000);

    const domainsLabel = page.getByText('Domains', { exact: true });
    await expect(domainsLabel).toBeVisible();
    await expect(domainsLabel).toHaveText('Domains');
    // await domainsLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-domains').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-domains').getByRole('textbox', { name: 'Add Items' }).fill('domain.com');
    await page.waitForTimeout(3000);
    await page.getByTestId('listBuilder-domains').getByRole('button', { name: 'Add Items' }).click();
    await page.getByText('domain.com').click();
    await page.waitForTimeout(3000);

    const dnsRequestTypesLabel = page.getByText('DNS Request Types', { exact: true });
    await expect(dnsRequestTypesLabel).toBeVisible();
    await expect(dnsRequestTypesLabel).toHaveText('DNS Request Types');
    // await dnsRequestTypesLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-dnsRequestTypes').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const dnsResponseCodesLabel = page.getByText('DNS Response Codes', { exact: true });
    await expect(dnsResponseCodesLabel).toBeVisible();
    await expect(dnsResponseCodesLabel).toHaveText('DNS Response Codes');
    // await dnsResponseCodesLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const dnsResponsesLabel = page.getByText('DNS Responses', { exact: true });
    await expect(dnsResponsesLabel).toBeVisible();
    await expect(dnsResponsesLabel).toHaveText('DNS Responses');
    // await dnsResponsesLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-dnsResponses').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-dnsResponses').getByRole('textbox', { name: 'Add Items' }).fill('DNS Response');
    await page.waitForTimeout(3000);
    await page.getByTestId('listBuilder-dnsResponses').getByRole('button', { name: 'Add Items' }).click();
    await page.getByText('DNS Response', { exact: true }).click();
    await page.waitForTimeout(3000);

    const durationsLabel = page.getByText('Durations');
    await expect(durationsLabel).toBeVisible();
    await expect(durationsLabel).toContainText('Durations');
    // await durationsLabel.click(); // Click removed
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-durations').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-durations').getByRole('textbox', { name: 'Add Items' }).fill('10');
    await page.waitForTimeout(3000);
    await page.getByTestId('listBuilder-durations').getByRole('button', { name: 'Add Items' }).click();
    await page.getByLabel('DNS_FILTERS').getByText('10').click();
    await page.waitForTimeout(2000);
    await page.getByTestId('modal-footer').getByText('Cancel').click();
    await page.waitForTimeout(2000);
    const title = page.locator(this.fields.pageHeaderTitle);
    await title.isVisible();
    await expect(title).toHaveText("Nanolog Streaming Service");
  }

  async addNSSFeedFor_emailDLP(page: Page, feedName: string): Promise<void> {
    const title1 = page.locator(this.fields.pageHeaderTitle);
    await title1.isVisible();
    await expect(title1).toHaveText("Nanolog Streaming Service");
    await page.waitForTimeout(2000);

    const nssFeedOption = page.locator(this.fields.nssFeedsTab);
    await expect(nssFeedOption).toContainText("NSS Feeds");
    await nssFeedOption.click();
    await page.waitForTimeout(2000);

    const addNssFeedButton = await page.locator(this.fields.addNssFeedButton);
    await addNssFeedButton.isVisible();
    await addNssFeedButton.click();
    await page.waitForTimeout(2000);

    const feedNameLabel = await page.locator(this.fields.feedNameLabelOnForm);
    await feedNameLabel.isVisible();
    await expect(feedNameLabel).toContainText("Feed Name");
    await page.waitForTimeout(2000);

    const name = await page.locator(this.fields.feedNameActualInputOnForm);
    await name.isVisible();
    await name.fill(feedName);
    await page.waitForTimeout(2000);

    const nssType = await page.locator(this.fields.nssTypeDropdown);
    await nssType.isVisible();
    await nssType.click();
    await page.waitForTimeout(2000);

    const nssSelectValue = await page.locator(this.fields.nssTypeOptionForWeb);
    await nssSelectValue.isVisible();
    await nssSelectValue.click();
    await page.waitForTimeout(2000);


    const nss_serverFieldName = await page.locator(this.fields.nssServerLabel);
    await nss_serverFieldName.isVisible();
    await expect(nss_serverFieldName).toContainText("NSS Server");
    await page.waitForTimeout(2000);

    const nss_serverDropdown = await page.locator(this.fields.nssServerDropdown);
    await nss_serverDropdown.isVisible();
    await nss_serverDropdown.click();
    await page.waitForTimeout(2000);

    const nss_Server = await page.locator(this.fields.nssServerOptionTestNssForWeb);
    await nss_Server.isVisible();
    await nss_Server.click();
    await page.waitForTimeout(2000);

    const ip_addressFieldName = await page.locator(this.fields.siemIpAddressLabel);
    await ip_addressFieldName.isVisible();
    await expect(ip_addressFieldName).toContainText("SIEM IP Address");
    await page.waitForTimeout(2000);

    const ip_address = await page.locator(this.fields.siemIpAddressInput);
    await ip_address.isVisible();
    await ip_address.click();
    await ip_address.fill("127.0.0.1");
    await page.waitForTimeout(2000);

    const tcp_portFieldName = await page.locator(this.fields.siemTcpPortLabel);
    await tcp_portFieldName.isVisible();
    await expect(tcp_portFieldName).toContainText("SIEM TCP Port");
    await page.waitForTimeout(2000);

    const tcp_portfield = await page.locator(this.fields.siemTcpPortInput);
    await tcp_portfield.isVisible();
    await tcp_portfield.click();
    await tcp_portfield.fill('443');
    await page.waitForTimeout(2000);

    const logTypeFieldName = await page.locator(this.fields.logTypeLabel);
    await logTypeFieldName.isVisible();
    await expect(logTypeFieldName).toContainText("Log Type");
    await page.waitForTimeout(2000);

    const logType_Dropdown = await page.locator(this.fields.logTypeFormDropdown);
    await logType_Dropdown.isVisible();
    await logType_Dropdown.click();
    await page.waitForTimeout(2000);

    const selectValueFrom_Dropdown = await page.locator(this.fields.logTypeOptionEmailDLP);
    await selectValueFrom_Dropdown.isVisible();
    await selectValueFrom_Dropdown.click();
    await page.waitForTimeout(2000);

    await page.locator('//li[@data-label="WHO"]/span').click();

    await page.locator('//div[@id="USERS"]/following-sibling::div/div/div/span[2]').click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();

    await page.waitForTimeout(2000);

    await page.locator('//div[@id="DEPARTMENTS"]/following-sibling::div/div/div/span[2]').click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();


    await page.locator('//li[@data-label="DLP"]/span').click();

    await page.locator('//div[@id="DLP_ENGINES"]/following-sibling::div/div/div/span[2]').click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(2000);
    await page.locator('//div[@id="DLP_DICTIONARIES"]/following-sibling::div/div/div/span[2]').click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(2000);
    await page.locator('//div[@id="RECORD_TYPE"]/following-sibling::div/div/div/span[2]').click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();

    await page.locator('//li[@data-label="ACTION"]/span').click();

    await page.locator('//div[@id="SEVERITY"]/following-sibling::div/div/div/span[2]').click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(2000);

    await page.locator('//div[@id="POLICY_ACTION"]/following-sibling::span/div').click();
    await page.locator('//div[@id="POLICY_ACTION"]/following-sibling::span//li[@id="ALLOW"]').click();
    await page.waitForTimeout(2000);

    await page.locator('//span[contains(text(),"Cancel")]').click();
    await page.waitForTimeout(2000);
  }

  async addNSSFeedFor_endpointDLP(page: Page, feedName: string): Promise<void> {
    const title1 = page.locator(this.fields.pageHeaderTitle);
    await title1.isVisible();
    await expect(title1).toHaveText("Nanolog Streaming Service");
    await page.waitForTimeout(2000);

    const nssFeedOption = page.locator(this.fields.nssFeedsTab);
    await expect(nssFeedOption).toContainText("NSS Feeds");
    await nssFeedOption.click();
    await page.waitForTimeout(2000);

    const addNssFeedButton = await page.locator(this.fields.addNssFeedButton);
    await addNssFeedButton.isVisible();
    await addNssFeedButton.click();
    await page.waitForTimeout(2000);

    const feedNameLabel = await page.locator(this.fields.feedNameLabelOnForm);
    await feedNameLabel.isVisible();
    await expect(feedNameLabel).toContainText("Feed Name");
    await page.waitForTimeout(2000);

    const name = await page.locator(this.fields.feedNameActualInputOnForm);
    await name.isVisible();
    await name.fill(feedName);
    await page.waitForTimeout(2000);

    const nssType = await page.locator(this.fields.nssTypeDropdown);
    await nssType.isVisible();
    await nssType.click();
    await page.waitForTimeout(2000);

    const nssSelectValue = await page.locator(this.fields.nssTypeOptionForWeb);
    await nssSelectValue.isVisible();
    await nssSelectValue.click();
    await page.waitForTimeout(2000);


    const nss_serverFieldName = await page.locator(this.fields.nssServerLabel);
    await nss_serverFieldName.isVisible();
    await expect(nss_serverFieldName).toContainText("NSS Server");
    await page.waitForTimeout(2000);

    const nss_serverDropdown = await page.locator(this.fields.nssServerDropdown);
    await nss_serverDropdown.isVisible();
    await nss_serverDropdown.click();
    await page.waitForTimeout(2000);

    const nss_Server = await page.locator(this.fields.nssServerOptionTestNssForWeb);
    await nss_Server.isVisible();
    await nss_Server.click();
    await page.waitForTimeout(2000);

    const ip_addressFieldName = await page.locator(this.fields.siemIpAddressLabel);
    await ip_addressFieldName.isVisible();
    await expect(ip_addressFieldName).toContainText("SIEM IP Address");
    await page.waitForTimeout(2000);

    const ip_address = await page.locator(this.fields.siemIpAddressInput);
    await ip_address.isVisible();
    await ip_address.click();
    await ip_address.fill("127.0.0.1");
    await page.waitForTimeout(2000);

    const tcp_portFieldName = await page.locator(this.fields.siemTcpPortLabel);
    await tcp_portFieldName.isVisible();
    await expect(tcp_portFieldName).toContainText("SIEM TCP Port");
    await page.waitForTimeout(2000);

    const tcp_portfield = await page.locator(this.fields.siemTcpPortInput);
    await tcp_portfield.isVisible();
    await tcp_portfield.click();
    await tcp_portfield.fill('443');
    await page.waitForTimeout(2000);

    const logTypeFieldName = await page.locator(this.fields.logTypeLabel);
    await logTypeFieldName.isVisible();
    await expect(logTypeFieldName).toContainText("Log Type");
    await page.waitForTimeout(2000);

    const logType_Dropdown = await page.locator(this.fields.logTypeFormDropdown);
    await logType_Dropdown.isVisible();
    await logType_Dropdown.click();
    await page.waitForTimeout(2000);

    const selectValueFrom_Dropdown = await page.locator(this.fields.logTypeOptionLogTypeDLP);
    await selectValueFrom_Dropdown.isVisible();
    await selectValueFrom_Dropdown.click();
    await page.waitForTimeout(2000);

    await page.locator('//li[@data-label="WHO"]/span').click();
    await page.locator('//li[@data-label="DLP"]/span').click();
    await page.locator('//li[@data-label="FILE_TYPE"]/span').click();

    await page.locator('//div[@id="FILE_TYPE_CATEGORY"]/following-sibling::div/div/div/span[2]').click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(2000);

    await page.locator('//span[contains(text(),"Cancel")]').click();
    await page.waitForTimeout(2000);

  }
  async addNSSFeedForSaaSSecurity_SaaSLog(page: Page, feedName: string): Promise<void> {
    console.log("Executing add NSS Feed For SaaS Security_SaaSLog...");
    const title1 = page.locator(this.fields.pageHeaderTitle);
    await title1.isVisible();
    await expect(title1).toHaveText("Nanolog Streaming Service");
    await page.waitForTimeout(2000);

    const nssFeedOption = page.locator(this.fields.nssFeedsTab);
    await expect(nssFeedOption).toContainText("NSS Feeds");
    await nssFeedOption.click();
    await page.waitForTimeout(2000);

    const addNssFeedButton = await page.locator(this.fields.addNssFeedButton);
    await addNssFeedButton.isVisible();
    await addNssFeedButton.click();
    await page.waitForTimeout(2000);

    const feedNameLabel = await page.locator(this.fields.feedNameLabelOnForm);
    await feedNameLabel.isVisible();
    await expect(feedNameLabel).toContainText("Feed Name");
    await page.waitForTimeout(2000);

    const name = await page.locator(this.fields.feedNameActualInputOnForm);
    await name.isVisible();
    await name.fill(feedName);
    await page.waitForTimeout(2000);

    const nssType = await page.locator(this.fields.nssTypeDropdown);
    await nssType.isVisible();
    await nssType.click();
    await page.waitForTimeout(2000);

    const nssSelectValue = await page.locator(this.fields.nssTypeOptionForWeb);
    await nssSelectValue.isVisible();
    await nssSelectValue.click();
    await page.waitForTimeout(2000);


    const nss_serverFieldName = await page.locator(this.fields.nssServerLabel);
    await nss_serverFieldName.isVisible();
    await expect(nss_serverFieldName).toContainText("NSS Server");
    await page.waitForTimeout(2000);

    const nss_serverDropdown = await page.locator(this.fields.nssServerDropdown);
    await nss_serverDropdown.isVisible();
    await nss_serverDropdown.click();
    await page.waitForTimeout(2000);

    const nss_Server = await page.locator(this.fields.nssServerOptionTestNssForWeb);
    await nss_Server.isVisible();
    await nss_Server.click();
    await page.waitForTimeout(2000);

    const ip_addressFieldName = await page.locator(this.fields.siemIpAddressLabel);
    await ip_addressFieldName.isVisible();
    await expect(ip_addressFieldName).toContainText("SIEM IP Address");
    await page.waitForTimeout(2000);

    const ip_address = await page.locator(this.fields.siemIpAddressInput);
    await ip_address.isVisible();
    await ip_address.click();
    await ip_address.fill("127.0.0.1");
    await page.waitForTimeout(2000);

    const tcp_portFieldName = await page.locator(this.fields.siemTcpPortLabel);
    await tcp_portFieldName.isVisible();
    await expect(tcp_portFieldName).toContainText("SIEM TCP Port");
    await page.waitForTimeout(2000);

    const tcp_portfield = await page.locator(this.fields.siemTcpPortInput);
    await tcp_portfield.isVisible();
    await tcp_portfield.click();
    await tcp_portfield.fill('443');
    await page.waitForTimeout(2000);

    const logTypeFieldName = await page.locator(this.fields.logTypeLabel);
    await logTypeFieldName.isVisible();
    await expect(logTypeFieldName).toContainText("Log Type");
    await page.waitForTimeout(2000);

    const logType_Dropdown = await page.locator(this.fields.logTypeFormDropdown);
    await logType_Dropdown.isVisible();
    await logType_Dropdown.click();
    await page.waitForTimeout(2000);

    const selectValueFrom_Dropdown = await page.locator(this.fields.logTypeOptionSaasSecurity);
    await selectValueFrom_Dropdown.isVisible();
    await selectValueFrom_Dropdown.click();
    await page.waitForTimeout(2000);

    await page.getByText('Action', { exact: true }).click();
    await page.getByTestId('multiDropdown-casbPolicyTypes').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multiDropdown-casbPolicyTypes').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByTestId('multiDropdown-casbPolicyTypes').getByRole('textbox', { name: 'Search...' }).fill('DLP');
    await page.getByTestId('multiDropdown-casbPolicyTypes').getByText('DLP').click();
    await page.getByText('Selected Items ( 1 )').click();
    // await page.getByText('Policy Action', { exact: true }).click(); 
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-casbAction').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multiDropdown-casbAction').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByText('Apply BOX tag', { exact: true }).click();
    await page.getByText('Selected Items ( 1 )').click();
    await page.getByTestId('multiDropdown-casbAction').getByText('Done').click();
    await page.waitForTimeout(2000);

    // await page.getByText('Scan Time', { exact: true }).click(); 
    await page.getByTestId('listBuilder-scanTime').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-scanTime').getByRole('textbox', { name: 'Add Items' }).fill('10');
    await page.getByTestId('listBuilder-scanTime').getByRole('button', { name: 'Add Items' }).click();
    await page.getByLabel('SAAS_SECURITY_FILTERS').getByText('10').click();
    await page.waitForTimeout(2000);

    // await page.getByText('Download Time', { exact: true }).click(); 
    await page.getByTestId('listBuilder-downloadTime').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-downloadTime').getByRole('textbox', { name: 'Add Items' }).fill('10');
    await page.getByTestId('listBuilder-downloadTime').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    await page.locator('//span[@data-testid="dropdown-event"]//span/span[2]').click();
    await page.waitForTimeout(2000);
    await page.getByRole('option', { name: 'Incident' }).click();
    await page.waitForTimeout(2000);

    await page.getByText('Who').click();
    // await page.getByText('User', { exact: true }).click(); 
    await page.getByRole('button', { name: 'None ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(2000);
    // await page.getByText('Departments', { exact: true }).click(); 
    await page.getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(2000);
    await page.getByRole('tab', { name: 'SaaS Security File' }).locator('span').click();
    // await page.getByText('File Name', { exact: true }).click(); 
    await page.getByTestId('listBuilder-fileName').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-fileName').getByRole('textbox', { name: 'Add Items' }).fill('Test File');
    await page.getByTestId('listBuilder-fileName').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(2000);
    await page.getByText('Test File').click();
    // await page.getByText('File Size', { exact: true }).click(); 
    await page.getByTestId('listBuilder-fileSizes').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-fileSizes').getByRole('textbox', { name: 'Add Items' }).fill('1200');
    await page.getByTestId('listBuilder-fileSizes').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(2000);
    await page.getByText('1200').click();
    // await page.getByText('File Source', { exact: true }).click(); 
    await page.getByTestId('listBuilder-fileSource').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-fileSource').getByRole('textbox', { name: 'Add Items' }).fill('test');
    await page.getByTestId('listBuilder-fileSource').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(2000);
    await page.getByText('test', { exact: true }).click();
    // await page.getByText('Non-Provisioned Owner').click(); 
    await page.getByTestId('multiDropdown-externalOwners').getByRole('button', { name: 'Any ' }).click();
    await page.getByTestId('multiDropdown-externalOwners').getByText('Done').click();
    await page.waitForTimeout(2000);
    // await page.getByText('Internal Collaborators').click(); 
    await page.getByTestId('multiDropdown-internalCollaborators').getByRole('button', { name: 'Any ' }).click();
    await page.waitForTimeout(2000);
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    // await page.getByText('External Collaborators').click(); 
    await page.getByTestId('multiDropdown-externalCollaborators').getByRole('button', { name: 'Any ' }).click();
    await page.getByTestId('multiDropdown-externalCollaborators').getByText('Done').click();
    await page.waitForTimeout(2000);
    await page.getByTestId('content-tabs-container').getByText('Malware').click();
    // await page.getByText('Malware Classes').click(); 
    await page.getByTestId('multiDropdown-casbMalwareClasses').getByRole('button', { name: 'Any ' }).click();
    await page.waitForTimeout(2000);
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    // await page.getByText('Malware Names').click(); 
    await page.getByRole('button', { name: 'Any ' }).click();
    await page.waitForTimeout(2000);
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    // await page.getByText('Threat Names', { exact: true }).click(); 
    await page.getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByRole('textbox', { name: 'Add Items' }).fill('Name');
    await page.getByRole('button', { name: 'Add Items' }).click();
    await page.getByText('Name', { exact: true }).click();
    await page.waitForTimeout(2000);
    await page.getByText('Application', { exact: true }).click();
    await page.getByTestId('multiDropdown-casbApplications').getByRole('button', { name: 'Any ' }).click();
    await page.waitForTimeout(2000);
    await page.getByTestId('multiDropdown-casbApplications').getByText('Done').click();
    // await page.getByText('SaaS Application Tenant', { exact: true }).click(); 
    await page.getByTestId('multiDropdown-casbTenant').getByRole('button', { name: 'Any ' }).click();
    await page.waitForTimeout(2000);
    await page.getByTestId('modal-footer').getByText('Cancel').click();
    await page.waitForTimeout(2000);
    const title = page.locator(this.fields.pageHeaderTitle);
    await title.isVisible();
    await expect(title).toHaveText("Nanolog Streaming Service");

  }

  async addNSSFeedForSaaSSecurity_ActivityLog(page: Page, feedName: string): Promise<void> {
    console.log("Executing add NSS Feed For SaaS Security_ActivityLog...");

    const title1 = page.locator(this.fields.pageHeaderTitle);
    await title1.isVisible();
    await expect(title1).toHaveText("Nanolog Streaming Service");
    await page.waitForTimeout(2000);

    const nssFeedOption = page.locator(this.fields.nssFeedsTab);
    await expect(nssFeedOption).toContainText("NSS Feeds");
    await nssFeedOption.click();
    await page.waitForTimeout(2000);

    const addNssFeedButton = await page.locator(this.fields.addNssFeedButton);
    await addNssFeedButton.isVisible();
    await addNssFeedButton.click();
    await page.waitForTimeout(2000);

    const feedNameLabel = await page.locator(this.fields.feedNameLabelOnForm);
    await feedNameLabel.isVisible();
    await expect(feedNameLabel).toContainText("Feed Name");
    await page.waitForTimeout(2000);

    const name = await page.locator(this.fields.feedNameActualInputOnForm);
    await name.isVisible();
    await name.fill(feedName);
    await page.waitForTimeout(2000);

    const nssType = await page.locator(this.fields.nssTypeDropdown);
    await nssType.isVisible();
    await nssType.click();
    await page.waitForTimeout(2000);

    const nssSelectValue = await page.locator(this.fields.nssTypeOptionForWeb);
    await nssSelectValue.isVisible();
    await nssSelectValue.click();
    await page.waitForTimeout(2000);


    const nss_serverFieldName = await page.locator(this.fields.nssServerLabel);
    await nss_serverFieldName.isVisible();
    await expect(nss_serverFieldName).toContainText("NSS Server");
    await page.waitForTimeout(2000);

    const nss_serverDropdown = await page.locator(this.fields.nssServerDropdown);
    await nss_serverDropdown.isVisible();
    await nss_serverDropdown.click();
    await page.waitForTimeout(2000);

    const nss_Server = await page.locator(this.fields.nssServerOptionTestNssForWeb);
    await nss_Server.isVisible();
    await nss_Server.click();
    await page.waitForTimeout(2000);

    const ip_addressFieldName = await page.locator(this.fields.siemIpAddressLabel);
    await ip_addressFieldName.isVisible();
    await expect(ip_addressFieldName).toContainText("SIEM IP Address");
    await page.waitForTimeout(2000);

    const ip_address = await page.locator(this.fields.siemIpAddressInput);
    await ip_address.isVisible();
    await ip_address.click();
    await ip_address.fill("127.0.0.1");
    await page.waitForTimeout(2000);

    const tcp_portFieldName = await page.locator(this.fields.siemTcpPortLabel);
    await tcp_portFieldName.isVisible();
    await expect(tcp_portFieldName).toContainText("SIEM TCP Port");
    await page.waitForTimeout(2000);

    const tcp_portfield = await page.locator(this.fields.siemTcpPortInput);
    await tcp_portfield.isVisible();
    await tcp_portfield.click();
    await tcp_portfield.fill('443');
    await page.waitForTimeout(2000);

    const logTypeFieldName = await page.locator(this.fields.logTypeLabel);
    await logTypeFieldName.isVisible();
    await expect(logTypeFieldName).toContainText("Log Type");
    await page.waitForTimeout(2000);

    const logType_Dropdown = await page.locator(this.fields.logTypeFormDropdown);
    await logType_Dropdown.isVisible();
    await logType_Dropdown.click();
    await page.waitForTimeout(2000);

    const selectValueFrom_Dropdown = await page.locator(this.fields.logTypeOptionSaasSecurityActivity);
    await selectValueFrom_Dropdown.isVisible();
    await selectValueFrom_Dropdown.click();
    await page.waitForTimeout(2000);

    const userLabelLocator = page.getByText('User', { exact: true });
    await expect(userLabelLocator).toBeVisible();
    await expect(userLabelLocator).toContainText('User');
    await page.waitForTimeout(2000);
    await userLabelLocator.click();
    await page.getByTestId('multiDropdown-securityUsers').getByRole('button', { name: 'None ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multiDropdown-securityUsers').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByTestId('multiDropdown-securityUsers').getByRole('textbox', { name: 'Search...' }).fill('Automation_test');

    const autoLocOptionLocator = page.getByText('Automation_test');
    await expect(autoLocOptionLocator).toBeVisible();
    await expect(autoLocOptionLocator).toContainText('Automation_test');
    await page.waitForTimeout(2000);
    await autoLocOptionLocator.click();

    const selectedItems1UserLocator = page.getByText('Selected Items ( 1 )');
    await expect(selectedItems1UserLocator).toBeVisible();
    await expect(selectedItems1UserLocator).toContainText('Selected Items ( 1 )');
    await page.waitForTimeout(2000);
    await selectedItems1UserLocator.click();

    const tenantLabelLocator = page.getByText('Tenant', { exact: true });
    await expect(tenantLabelLocator).toBeVisible();
    await expect(tenantLabelLocator).toContainText('Tenant');
    await page.waitForTimeout(2000);
    await tenantLabelLocator.click();

    const activityLabelLocator = page.getByText('Activity', { exact: true });
    await expect(activityLabelLocator).toBeVisible();
    await expect(activityLabelLocator).toContainText('Activity');
    await page.waitForTimeout(2000);
    await activityLabelLocator.click();
    await page.getByTestId('multiDropdown-activity').getByRole('button', { name: 'None ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multiDropdown-activity').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByTestId('multiDropdown-activity').getByRole('textbox', { name: 'Search...' }).fill('Add');

    // Assuming 'Add' is the text of the item to click after search.
    // If it's a checkbox, page.getByRole('checkbox', { name: 'Add' }) might be more accurate.
    const addOptionActivityLocator = page.getByText('Add', { exact: true });
    await expect(addOptionActivityLocator).toBeVisible();
    await expect(addOptionActivityLocator).toContainText('Add');
    await page.waitForTimeout(2000);
    await addOptionActivityLocator.click();

    const selectedItems1ActivityLocator = page.getByText('Selected Items ( 1 )');
    await expect(selectedItems1ActivityLocator).toBeVisible();
    await expect(selectedItems1ActivityLocator).toContainText('Selected Items ( 1 )');
    await page.waitForTimeout(2000);
    await selectedItems1ActivityLocator.click();

    const doneButtonActivityLocator = page.getByTestId('multiDropdown-activity').getByText('Done');
    await expect(doneButtonActivityLocator).toBeVisible();
    await expect(doneButtonActivityLocator).toContainText('Done');
    await page.waitForTimeout(2000);
    await doneButtonActivityLocator.click();

    const object1TypeLabelLocator = page.getByText('Object 1 Type');
    await expect(object1TypeLabelLocator).toBeVisible();
    await expect(object1TypeLabelLocator).toContainText('Object 1 Type');
    await page.waitForTimeout(2000);
    await object1TypeLabelLocator.click();
    await page.getByTestId('multiDropdown-objectType1').getByRole('button', { name: 'None ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multiDropdown-objectType1').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByTestId('multiDropdown-objectType1').getByRole('textbox', { name: 'Search...' }).fill('Auth');

    const authOptionObjectType1Locator = page.getByTestId('multiDropdown-objectType1').getByText('Auth');
    await expect(authOptionObjectType1Locator).toBeVisible();
    await expect(authOptionObjectType1Locator).toContainText('Auth');
    await page.waitForTimeout(2000);
    await authOptionObjectType1Locator.click();

    const selectedItems1ObjectType1Locator = page.getByText('Selected Items ( 1 )');
    await expect(selectedItems1ObjectType1Locator).toBeVisible();
    await expect(selectedItems1ObjectType1Locator).toContainText('Selected Items ( 1 )');
    await page.waitForTimeout(2000);
    await selectedItems1ObjectType1Locator.click();

    const doneButtonObjectType1Locator = page.getByTestId('multiDropdown-objectType1').getByText('Done');
    await expect(doneButtonObjectType1Locator).toBeVisible();
    await expect(doneButtonObjectType1Locator).toContainText('Done');
    await page.waitForTimeout(2000);
    await doneButtonObjectType1Locator.click();

    const object2TypeLabelLocator = page.getByText('Object 2 Type');
    await expect(object2TypeLabelLocator).toBeVisible();
    await expect(object2TypeLabelLocator).toContainText('Object 2 Type');
    await page.waitForTimeout(2000);
    await object2TypeLabelLocator.click();
    await page.getByTestId('multiDropdown-objectType2').getByRole('button', { name: 'None ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multiDropdown-objectType2').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByTestId('multiDropdown-objectType2').getByRole('textbox', { name: 'Search...' }).fill('Config');

    const configOptionObjectType2Locator = page.getByTestId('multiDropdown-objectType2').getByText('Config');
    await expect(configOptionObjectType2Locator).toBeVisible();
    await expect(configOptionObjectType2Locator).toContainText('Config');
    await page.waitForTimeout(2000);
    await configOptionObjectType2Locator.click();

    const selectedItems1ObjectType2Locator = page.getByText('Selected Items ( 1 )');
    await expect(selectedItems1ObjectType2Locator).toBeVisible();
    await expect(selectedItems1ObjectType2Locator).toContainText('Selected Items ( 1 )');
    await page.waitForTimeout(2000);
    await selectedItems1ObjectType2Locator.click();

    const doneButtonObjectType2Locator = page.getByTestId('multiDropdown-objectType2').getByText('Done');
    await expect(doneButtonObjectType2Locator).toBeVisible();
    await expect(doneButtonObjectType2Locator).toContainText('Done');
    await page.waitForTimeout(2000);
    await doneButtonObjectType2Locator.click();

    const cancelButtonModalFooterLocator = page.getByTestId('modal-footer').getByText('Cancel');
    await expect(cancelButtonModalFooterLocator).toBeVisible();
    await expect(cancelButtonModalFooterLocator).toContainText('Cancel');
    await page.waitForTimeout(2000);
    await cancelButtonModalFooterLocator.click();

  }

  async addNSSFeedForTunnelLog(page: Page, feedName: string): Promise<void> {
    console.log("Executing add NSS Feed For TunnelLog...");

    const title1 = page.locator(this.fields.pageHeaderTitle);
    await title1.isVisible();
    await expect(title1).toHaveText("Nanolog Streaming Service");
    await page.waitForTimeout(2000);

    // Navigate to NSS Feeds tab first (as per original logic flow)
    const nssFeedOption = page.locator(this.fields.nssFeedsTab);
    await expect(nssFeedOption).toContainText("NSS Feeds");
    await nssFeedOption.click();
    await page.waitForTimeout(2000);

    const addNssFeedButton = await page.locator(this.fields.addNssFeedButton);
    await addNssFeedButton.isVisible();
    await addNssFeedButton.click();
    await page.waitForTimeout(2000);

    const feedNameLabel = await page.locator(this.fields.feedNameLabelOnForm);
    await feedNameLabel.isVisible();
    await expect(feedNameLabel).toContainText("Feed Name");
    await page.waitForTimeout(2000);

    const name = await page.locator(this.fields.feedNameActualInputOnForm);
    await name.isVisible();
    await name.fill(feedName);
    await page.waitForTimeout(2000);

    const nssType = await page.locator(this.fields.nssTypeDropdown);
    await nssType.isVisible();
    await nssType.click();
    await page.waitForTimeout(2000);

    const nssSelectValue = await page.locator(this.fields.nssTypeOptionForWeb);
    await nssSelectValue.isVisible();
    await nssSelectValue.click();
    await page.waitForTimeout(2000);


    const nss_serverFieldName = await page.locator(this.fields.nssServerLabel);
    await nss_serverFieldName.isVisible();
    await expect(nss_serverFieldName).toContainText("NSS Server");
    await page.waitForTimeout(2000);

    const nss_serverDropdown = await page.locator(this.fields.nssServerDropdown);
    await nss_serverDropdown.isVisible();
    await nss_serverDropdown.click();
    await page.waitForTimeout(2000);

    const nss_Server = await page.locator(this.fields.nssServerOptionTestNssForWeb);
    await nss_Server.isVisible();
    await nss_Server.click();
    await page.waitForTimeout(2000);

    const ip_addressFieldName = await page.locator(this.fields.siemIpAddressLabel);
    await ip_addressFieldName.isVisible();
    await expect(ip_addressFieldName).toContainText("SIEM IP Address");
    await page.waitForTimeout(2000);

    const ip_address = await page.locator(this.fields.siemIpAddressInput);
    await ip_address.isVisible();
    await ip_address.click();
    await ip_address.fill("127.0.0.1");
    await page.waitForTimeout(2000);

    const tcp_portFieldName = await page.locator(this.fields.siemTcpPortLabel);
    await tcp_portFieldName.isVisible();
    await expect(tcp_portFieldName).toContainText("SIEM TCP Port");
    await page.waitForTimeout(2000);

    const tcp_portfield = await page.locator(this.fields.siemTcpPortInput);
    await tcp_portfield.isVisible();
    await tcp_portfield.click();
    await tcp_portfield.fill('443');
    await page.waitForTimeout(2000);

    const logTypeFieldName = await page.locator(this.fields.logTypeLabel);
    await logTypeFieldName.isVisible();
    await expect(logTypeFieldName).toContainText("Log Type");
    await page.waitForTimeout(2000);

    const logType_Dropdown = await page.locator(this.fields.logTypeFormDropdown);
    await logType_Dropdown.isVisible();
    await logType_Dropdown.click();
    await page.waitForTimeout(2000);

    const selectTunnel_Dropdown = await page.locator(this.fields.logTypeOptionTunnel);
    await selectTunnel_Dropdown.isVisible();
    await selectTunnel_Dropdown.click();
    await page.waitForTimeout(2000);

    const recordTypeLocator = page.getByText('Record Type', { exact: true });
    await expect(recordTypeLocator).toBeVisible();
    await expect(recordTypeLocator).toContainText('Record Type');
    await page.locator('//div[@data-testid="multiDropdown-recordType"]//div[2]//span[2]').click();
    await page.locator('//span[contains(text(),"Clear Selection")]').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.locator('(//li/label/input[@type="checkbox"])[2]').click();
    await page.locator('(//li/label/input[@type="checkbox"])[3]').click();
    await page.locator('(//li/label/input[@type="checkbox"])[4]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 4 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const feedOutputTypeLocator = page.getByText('Feed Output Type');
    await expect(feedOutputTypeLocator).toBeVisible();
    await expect(feedOutputTypeLocator).toContainText('Feed Output Type');
    await feedOutputTypeLocator.click();
    await page.locator('//span[@data-testid="dropdown-feedOutputType"]/div/span').click();
    await page.getByRole('option', { name: 'JSON' }).click();
    await page.waitForTimeout(3000);

    await page.locator('//ul/li[@data-label="IPSEC_PHASE1"]/span').click();
    await page.locator('//li[@data-label="IPSEC_PHASE2"]/span').click();
    await page.locator('//li[@data-label="TUNNEL_EVENT"]/span').click();
    await page.locator('//li[@data-label="TUNNEL_SAMPLES"]/span').click();

    const tunnelFiltersLocator = page.getByText('Tunnel Filters');
    await expect(tunnelFiltersLocator).toContainText('Tunnel Filters');
    await page.waitForTimeout(3000);

    const tunnelTypeLocator = page.getByText('Tunnel Type', { exact: true });
    await expect(tunnelTypeLocator).toContainText('Tunnel Type');
    await page.waitForTimeout(3000);
    await page.locator('//div[@data-testid="multiDropdown-tunnelTypes"]//span[2]').click();
    await page.getByText('Selected Items ( 7 )').isVisible();
    await page.getByTestId('multiDropdown-tunnelTypes').getByText('Done').click();
    await page.waitForTimeout(3000);

    const locationsLocator = page.getByTestId('multiDropdown-locations').getByText('Locations', { exact: true });
    await expect(locationsLocator).toBeVisible();
    await expect(locationsLocator).toContainText('Locations');
    await locationsLocator.click();
    await page.getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const vpnCredentialsLocator = page.getByTestId('multiDropdown-vpnCredentials').getByText('VPN Credentials', { exact: true });
    await expect(vpnCredentialsLocator).toBeVisible();
    await expect(vpnCredentialsLocator).toContainText('VPN Credentials');
    await vpnCredentialsLocator.click();
    await page.waitForTimeout(1000);
    await page.getByRole('button', { name: 'None ' }).click();
    await page.waitForTimeout(1000);
    await page.getByTestId('multiDropdown-vpnCredentials').getByText('Cancel').click();
    await page.waitForTimeout(3000);

    const sourceIPsLocator = page.getByText('Source IPs');
    await expect(sourceIPsLocator).toBeVisible();
    await expect(sourceIPsLocator).toContainText('Source IPs');
    await sourceIPsLocator.click();
    await page.waitForTimeout(3000);
    await page.getByTestId('listBuilder-tunnelSourceIps').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-tunnelSourceIps').getByRole('textbox', { name: 'Add Items' }).fill('***********');
    await page.waitForTimeout(3000);
    await page.getByTestId('listBuilder-tunnelSourceIps').getByRole('button', { name: 'Add Items' }).click();
    await page.getByText('***********').click();
    await page.waitForTimeout(3000);

    const destinationVIPsLocator = page.getByText('Destination VIPs');
    await expect(destinationVIPsLocator).toBeVisible();
    await expect(destinationVIPsLocator).toContainText('Destination VIPs');
    await destinationVIPsLocator.click();
    await page.waitForTimeout(3000);
    await page.getByTestId('listBuilder-tunnelDestIps').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-tunnelDestIps').getByRole('textbox', { name: 'Add Items' }).fill('***********');
    await page.waitForTimeout(3000);
    await page.getByTestId('listBuilder-tunnelDestIps').getByRole('button', { name: 'Add Items' }).click();
    await page.getByText('***********').click();
    await page.waitForTimeout(3000);

    const tunnelSourcePortLocator = page.getByText('Tunnel Source Port');
    await expect(tunnelSourcePortLocator).toBeVisible();
    await expect(tunnelSourcePortLocator).toContainText('Tunnel Source Port');
    await tunnelSourcePortLocator.click();
    await page.waitForTimeout(2000);
    await page.getByTestId('listBuilder-tunnelSourcePort').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-tunnelSourcePort').getByRole('textbox', { name: 'Add Items' }).fill('443');
    await page.waitForTimeout(2000);
    await page.getByTestId('listBuilder-tunnelSourcePort').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(2000);
    await page.getByText('443').click();
    await page.waitForTimeout(2000);
    await page.getByTestId('modal-footer').getByText('Cancel').click();

    await page.waitForTimeout(2000);
    const title = page.locator(this.fields.pageHeaderTitle);
    await title.isVisible();
    await expect(title).toHaveText("Nanolog Streaming Service");
  }

  async addNSSFeedForFirewall_AlertsLog(page: Page, feedName: string): Promise<void> {
    console.log("Executing add NSS Feed For Firewall_AlertsLog...");

    const title1 = page.locator(this.fields.pageHeaderTitle);
    await title1.isVisible();
    await expect(title1).toHaveText("Nanolog Streaming Service");

    // Navigate to NSS Feeds tab first (as per original logic flow)
    const nssFeedOption = page.locator(this.fields.nssFeedsTab);
    await expect(nssFeedOption).toContainText("NSS Feeds");
    await nssFeedOption.click();
    await page.waitForTimeout(2000);

    const addNssFeedButton = await page.locator(this.fields.addNssFeedButton);
    await addNssFeedButton.isVisible();
    await addNssFeedButton.click();
    await page.waitForTimeout(2000);

    const feedNameLabel = await page.locator(this.fields.feedNameLabelOnForm);
    await feedNameLabel.isVisible();
    await expect(feedNameLabel).toContainText("Feed Name");
    await page.waitForTimeout(2000);

    const name = await page.locator(this.fields.feedNameActualInputOnForm);
    await name.isVisible();
    await name.fill(feedName);
    await page.waitForTimeout(2000);

    const nssType = await page.locator(this.fields.nssTypeDropdown);
    await nssType.isVisible();
    await nssType.click();
    await page.waitForTimeout(2000);

    const nssSelectValue = await page.locator(this.fields.nssTypeOptionFirewallCloudBranch);
    await nssSelectValue.isVisible();
    await nssSelectValue.click();
    await page.waitForTimeout(2000);

    const logTypeFieldName = await page.locator(this.fields.logTypeLabel);
    await logTypeFieldName.isVisible();
    await expect(logTypeFieldName).toContainText("Log Type");
    await page.waitForTimeout(2000);

    const logType_Dropdown = await page.locator(this.fields.logTypeFormDropdown);
    await logType_Dropdown.isVisible();
    await logType_Dropdown.click();
    await page.waitForTimeout(2000);

    const selectAlert_Dropdown = await page.locator(this.fields.logTypeOptionAlert);
    await selectAlert_Dropdown.isVisible();
    await selectAlert_Dropdown.click();
    await page.waitForTimeout(2000);

    const logFilter = await page.getByLabel('LOG_FILTER').getByText('Log Filter');
    await expect(logFilter).toHaveText('Log Filter');
    await page.waitForTimeout(2000);
    // const criticalLabel = await page.getByText('Critical');
    // await expect(criticalLabel).toHaveText('Critical');
    // await page.waitForTimeout(2000);  
    await page.getByTestId('toggleSwitch-critical').getByRole('checkbox', { name: ' ' }).click();
    await page.waitForTimeout(2000);
    // Use a more specific locator for the "Warn" label within the log filter section
    const warnLabel = await page.locator(this.fields.warnLabelWeb); // Assuming this is specific to the log filter's warn label
    await expect(warnLabel).toHaveText('Warn');
    await page.waitForTimeout(2000);
    await page.getByTestId('toggleSwitch-warn').getByRole('checkbox', { name: ' ' }).click();
    await page.waitForTimeout(2000);
    await page.getByTestId('modal-footer').getByText('Cancel').click();
    await page.waitForTimeout(2000);
    const title = page.locator(this.fields.pageHeaderTitle);
    await title.isVisible();
    await expect(title).toHaveText("Nanolog Streaming Service");
  }




  async addNSSFeedForFirewall_FirewallLogs(page: Page, feedName: string): Promise<void> {
    console.log("Executing add NSS Feed For Firewall_FirewallLogs...");

    const title1 = page.locator(this.fields.pageHeaderTitle);
    await title1.isVisible();
    await expect(title1).toHaveText("Nanolog Streaming Service");

    // Navigate to NSS Feeds tab first (as per original logic flow)
    const nssFeedOption = page.locator(this.fields.nssFeedsTab);
    await expect(nssFeedOption).toContainText("NSS Feeds");
    await nssFeedOption.click();
    await page.waitForTimeout(2000);

    const addNssFeedButton = await page.locator(this.fields.addNssFeedButton);
    await addNssFeedButton.isVisible();
    await addNssFeedButton.click();
    await page.waitForTimeout(2000);

    const feedNameLabel = await page.locator(this.fields.feedNameLabelOnForm);
    await feedNameLabel.isVisible();
    await expect(feedNameLabel).toContainText("Feed Name");
    await page.waitForTimeout(2000);

    const name = await page.locator(this.fields.feedNameActualInputOnForm);
    await name.isVisible();
    await name.fill(feedName);
    await page.waitForTimeout(2000);

    const nssType = await page.locator(this.fields.nssTypeDropdown);
    await nssType.isVisible();
    await nssType.click();
    await page.waitForTimeout(2000);

    const nssSelectValue = await page.locator(this.fields.nssTypeOptionFirewallCloudBranch);
    await nssSelectValue.isVisible();
    await nssSelectValue.click();
    await page.waitForTimeout(2000);

    const logTypeFieldName = await page.locator(this.fields.logTypeLabel);
    await logTypeFieldName.isVisible();
    await expect(logTypeFieldName).toContainText("Log Type");
    await page.waitForTimeout(2000);

    const logType_Dropdown = await page.locator(this.fields.logTypeFormDropdown);
    await logType_Dropdown.isVisible();
    await logType_Dropdown.click();
    await page.waitForTimeout(2000);

    const selectLogTypeFirewal = await page.locator(this.fields.logTypeOptionFirewal);
    await selectLogTypeFirewal.isVisible();
    await selectLogTypeFirewal.click();
    await page.waitForTimeout(2000);

    // Original: await page.getByText('Action', { exact: true }).click(); 12
    const actionTabLocator = page.getByText('Action', { exact: true });
    await expect(actionTabLocator).toBeVisible();
    await expect(actionTabLocator).toHaveText('Action');
    await actionTabLocator.click();
    await page.waitForTimeout(2000);

    // Original: await page.getByText('Firewall Filters').click();11
    const firewallFiltersLocator = page.getByText('Firewall Filters');
    await expect(firewallFiltersLocator).toBeVisible();
    await expect(firewallFiltersLocator).toHaveText('Firewall Filters');
    await page.waitForTimeout(2000);

    // Original: await page.getByText('DNAT Policy Action').click();11
    const dnatPolicyActionLocator = page.getByText('DNAT Policy Action');
    await expect(dnatPolicyActionLocator).toBeVisible();
    await expect(dnatPolicyActionLocator).toHaveText('DNAT Policy Action');
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-natActions').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multiDropdown-natActions').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByTestId('multiDropdown-natActions').getByRole('textbox', { name: 'Search...' }).fill('NAT');
    await page.getByTestId('multiDropdown-natActions').getByRole('textbox', { name: 'Search...' }).press('Enter');
    await page.getByText('NAT Control').click();
    await page.getByText('Selected Items ( 1 )').click();
    await page.getByTestId('multiDropdown-natActions').getByText('Done').click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('DNAT Destination Names').click();11
    const dnatDestinationNamesLocator = page.getByText('DNAT Destination Names');
    await expect(dnatDestinationNamesLocator).toBeVisible();
    await expect(dnatDestinationNamesLocator).toHaveText('DNAT Destination Names');
    await page.waitForTimeout(2000);

    await page.getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByRole('textbox', { name: 'Add Items' }).fill('***********');
    await page.getByRole('button', { name: 'Add Items' }).click();
    await page.getByText('***********').click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('Firewall & IPS Policy Actions').click();11
    const firewallIpsPolicyActionsLocator = page.getByText('Firewall & IPS Policy Actions');
    await expect(firewallIpsPolicyActionsLocator).toBeVisible();
    await expect(firewallIpsPolicyActionsLocator).toHaveText('Firewall & IPS Policy Actions');
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-firewallActions').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('Firewall Filtering Rule Name').click(); 11
    const firewallFilteringRuleNameLocator = page.getByText('Firewall Filtering Rule Name');
    await expect(firewallFilteringRuleNameLocator).toBeVisible();
    await expect(firewallFilteringRuleNameLocator).toHaveText('Firewall Filtering Rule Name');
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-ruleNames').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('IPS Rule Name').click(); 11
    const ipsRuleNameLocator = page.getByText('IPS Rule Name');
    await expect(ipsRuleNameLocator).toBeVisible();
    await expect(ipsRuleNameLocator).toHaveText('IPS Rule Name');
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multiDropdown-ipsRules').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByTestId('multiDropdown-ipsRules').getByRole('textbox', { name: 'Search...' }).fill('Default Cloud IPS');
    await page.getByText('Default Cloud IPS Rule').click();
    await page.getByText('Selected Items ( 1 )').click();
    await page.getByTestId('multiDropdown-ipsRules').getByText('Done').click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('Who').click(); 12
    const whoTabLocator = page.getByText('Who').first();
    await expect(whoTabLocator).toBeVisible();
    await expect(whoTabLocator).toHaveText('Who');
    await whoTabLocator.click();
    await page.waitForTimeout(2000);

    // Original: await page.getByText('Users', { exact: true }).click(); 11
    const usersLabelLocator = page.getByText('Users', { exact: true });
    await expect(usersLabelLocator).toBeVisible();
    await expect(usersLabelLocator).toHaveText('Users');
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-users').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('Departments', { exact: true }).click(); 11
    const departmentsLabelLocator = page.getByText('Departments', { exact: true });
    await expect(departmentsLabelLocator).toBeVisible();
    await expect(departmentsLabelLocator).toHaveText('Departments');
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('Source', { exact: true }).click();12
    const sourceTabLocator = page.getByText('Source', { exact: true });
    await expect(sourceTabLocator).toBeVisible();
    await expect(sourceTabLocator).toHaveText('Source');
    await sourceTabLocator.click();
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-locations').getByText('Locations', { exact: true }).click();
    await page.getByTestId('multiDropdown-locations').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('Client Source IP Addresses').click();11
    const clientSourceIPAddressesLocator = page.getByText('Client Source IP Addresses');
    await expect(clientSourceIPAddressesLocator).toBeVisible();
    await expect(clientSourceIPAddressesLocator).toHaveText('Client Source IP Addresses');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-clientSourceIps').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-clientSourceIps').getByRole('textbox', { name: 'Add Items' }).fill('***********');
    await page.getByTestId('listBuilder-clientSourceIps').getByRole('button', { name: 'Add Items' }).click();

    // Original: await page.getByText('***********').click();11
    const text10101011Locator = page.getByText('***********').first();
    await expect(text10101011Locator).toBeVisible();
    await expect(text10101011Locator).toHaveText('***********');
    await page.waitForTimeout(2000);

    // Original: await page.getByText('Client Source Ports', { exact: true }).click();11
    const clientSourcePortsLocator = page.getByText('Client Source Ports', { exact: true });
    await expect(clientSourcePortsLocator).toBeVisible();
    await expect(clientSourcePortsLocator).toContainText('Client Source Ports');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-clientSourcePorts').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-clientSourcePorts').getByRole('textbox', { name: 'Add Items' }).fill('443');
    await page.getByTestId('listBuilder-clientSourcePorts').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('443').click();
    const text443Locator = page.getByText('443').first();
    await expect(text443Locator).toBeVisible();
    await expect(text443Locator).toContainText('443');
    await page.waitForTimeout(2000);

    // Original: await page.getByText('Client Destination Names').first().click();
    const clientDestinationNamesLocator = page.getByText('Client Destination Names').first();
    await expect(clientDestinationNamesLocator).toBeVisible();
    await expect(clientDestinationNamesLocator).toContainText('Client Destination Names');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-clientDestinationAddr').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-clientDestinationAddr').getByRole('textbox', { name: 'Add Items' }).fill('***********');
    await page.getByTestId('listBuilder-clientDestinationAddr').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('***********').nth(1).click(); 11
    const ip10101011Nth1Locator = page.getByText('***********').nth(1);
    await expect(ip10101011Nth1Locator).toBeVisible();
    await expect(ip10101011Nth1Locator).toContainText('***********');
    await page.waitForTimeout(2000);

    // Original: await page.getByText('Client Destination IP Addresses', { exact: true }).click(); 11
    const clientDestIPLocator = page.getByText('Client Destination IP Addresses', { exact: true });
    await expect(clientDestIPLocator).toBeVisible();
    await expect(clientDestIPLocator).toContainText('Client Destination IP Addresses');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-clientDestinationIps').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-clientDestinationIps').getByRole('textbox', { name: 'Add Items' }).fill('***********');
    await page.getByTestId('listBuilder-clientDestinationIps').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('***********').click();11
    const text10101012Locator = page.getByText('***********').first();
    await expect(text10101012Locator).toBeVisible();
    await expect(text10101012Locator).toContainText('***********');
    await page.waitForTimeout(2000);

    // Original: await page.getByText('Client Destination Ports', { exact: true }).click();11
    const clientDestPortsLocator = page.getByText('Client Destination Ports', { exact: true });
    await expect(clientDestPortsLocator).toBeVisible();
    await expect(clientDestPortsLocator).toContainText('Client Destination Ports');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-clientDestinationPorts').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-clientDestinationPorts').getByRole('textbox', { name: 'Add Items' }).fill('554');
    await page.getByTestId('listBuilder-clientDestinationPorts').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('554').click();11
    const text554Locator = page.getByText('554').first();
    await expect(text554Locator).toBeVisible();
    await expect(text554Locator).toContainText('554');
    await page.waitForTimeout(2000);

    // Original: await page.getByText('Client Public IP Addresses').click();11
    const clientPublicIPLocator = page.getByText('Client Public IP Addresses');
    await expect(clientPublicIPLocator).toBeVisible();
    await expect(clientPublicIPLocator).toContainText('Client Public IP Addresses');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-clientIps').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-clientIps').getByRole('textbox', { name: 'Add Items' }).fill('***********');
    await page.getByTestId('listBuilder-clientIps').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    // Original: await page.getByText('***********').click();11
    const text10101013Locator = page.getByText('***********').first();
    await expect(text10101013Locator).toBeVisible();
    await expect(text10101013Locator).toContainText('***********');
    await page.waitForTimeout(2000);

    // Original: await page.getByTestId('multiDropdown-trafficForwarding').getByText('Traffic Forwarding', { exact: true }).click();11
    const trafficForwardingLocator = page.getByTestId('multiDropdown-trafficForwarding').getByText('Traffic Forwarding', { exact: true });
    await expect(trafficForwardingLocator).toBeVisible();
    await expect(trafficForwardingLocator).toContainText('Traffic Forwarding');
    await page.waitForTimeout(2000);

    await page.locator('//div[@id="TRAFFIC_FORWARDING"]/following-sibling::div/div/div/span[2]').click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const serverTrafficLocator = page.getByText('Server traffic');
    await expect(serverTrafficLocator).toBeVisible();
    await expect(serverTrafficLocator).toContainText('Server traffic');
    await serverTrafficLocator.click();
    await page.waitForTimeout(2000);

    const serverSourceIPLocator = page.getByText('Server Source IP Addresses', { exact: true });
    await expect(serverSourceIPLocator).toBeVisible();
    await expect(serverSourceIPLocator).toContainText('Server Source IP Addresses');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-serverSourceIps').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-serverSourceIps').getByRole('textbox', { name: 'Add Items' }).fill('***********');
    await page.getByTestId('listBuilder-serverSourceIps').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    const text10101014Locator = page.getByText('***********').first();
    await expect(text10101014Locator).toBeVisible();
    await expect(text10101014Locator).toContainText('***********');
    await page.waitForTimeout(2000);

    const serverSourcePortsLocator = page.getByText('Server Source Ports', { exact: true });
    await expect(serverSourcePortsLocator).toBeVisible();
    await expect(serverSourcePortsLocator).toContainText('Server Source Ports');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-serverSourcePorts').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-serverSourcePorts').getByRole('textbox', { name: 'Add Items' }).fill('643');
    await page.getByTestId('listBuilder-serverSourcePorts').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    const text643Locator = page.getByText('643').first();
    await expect(text643Locator).toBeVisible();
    await expect(text643Locator).toContainText('643');
    await page.waitForTimeout(2000);

    const serverDestIPLocator = page.getByText('Server Destination IP Addresses', { exact: true });
    await expect(serverDestIPLocator).toBeVisible();
    await expect(serverDestIPLocator).toContainText('Server Destination IP Addresses');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-serverDestinationIps').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-serverDestinationIps').getByRole('textbox', { name: 'Add Items' }).fill('***********');
    await page.getByTestId('listBuilder-serverDestinationIps').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    const text10101015Locator = page.getByText('***********').first();
    await expect(text10101015Locator).toBeVisible();
    await expect(text10101015Locator).toContainText('***********');
    await page.waitForTimeout(2000);

    const serverDestPortsLocator = page.getByText('Server Destination Ports', { exact: true });
    await expect(serverDestPortsLocator).toBeVisible();
    await expect(serverDestPortsLocator).toContainText('Server Destination Ports');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-serverDestinationPorts').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-serverDestinationPorts').getByRole('textbox', { name: 'Add Items' }).fill('777');
    await page.getByTestId('listBuilder-serverDestinationPorts').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    const text777Locator = page.getByText('777').first();
    await expect(text777Locator).toBeVisible();
    await expect(text777Locator).toContainText('777');
    await page.waitForTimeout(2000);

    const tunnelIPLocator = page.getByText('Tunnel IP Addresses');
    await expect(tunnelIPLocator).toBeVisible();
    await expect(tunnelIPLocator).toContainText('Tunnel IP Addresses');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-tunnelIps').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-tunnelIps').getByRole('textbox', { name: 'Add Items' }).fill('***********');
    await page.getByTestId('listBuilder-tunnelIps').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    const text10101016Locator = page.getByText('***********').first();
    await expect(text10101016Locator).toBeVisible();
    await expect(text10101016Locator).toContainText('***********');
    await page.waitForTimeout(2000);

    const serverIPClassesLocator = page.getByText('Server IP Classes');
    await expect(serverIPClassesLocator).toBeVisible();
    await expect(serverIPClassesLocator).toContainText('Server IP Classes');
    await page.waitForTimeout(2000);

    await page.locator('//div[@id="SERVER_IP_CLASSES"]/following-sibling::div/div/div/span[2]').click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const serverIPSuperCatLocator = page.getByText('Server IP Super Categories');
    await expect(serverIPSuperCatLocator).toBeVisible();
    await expect(serverIPSuperCatLocator).toContainText('Server IP Super Categories');
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-ipDomainSuperCategories').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const serverIPCatLocator = page.getByText('Server IP Categories');
    await expect(serverIPCatLocator).toBeVisible();
    await expect(serverIPCatLocator).toContainText('Server IP Categories');
    await page.waitForTimeout(2000);

    await page.getByTestId('multiDropdown-ipDomainCategories').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    console.log("The Server IP category is selected successfully")
    await page.waitForTimeout(3000);

    const countriesLocator = page.getByText('Countries');
    await expect(countriesLocator).toBeVisible();
    await expect(countriesLocator).toContainText('Countries');
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);

    const sessionTabLocator = page.getByText('Session', { exact: true });
    await expect(sessionTabLocator).toBeVisible();
    await expect(sessionTabLocator).toContainText('Session');
    await sessionTabLocator.click();
    await page.waitForTimeout(2000);

    const inboundBytesLocator = page.getByText('Inbound Bytes');
    await expect(inboundBytesLocator).toBeVisible();
    await expect(inboundBytesLocator).toContainText('Inbound Bytes');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-inBoundBytes').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-inBoundBytes').getByRole('textbox', { name: 'Add Items' }).fill('1200');
    await page.getByTestId('listBuilder-inBoundBytes').getByRole('button', { name: 'Add Items' }).click();
    await page.getByText('1200').click();
    await page.getByText('Outbound Bytes').click();
    await page.getByTestId('listBuilder-outBoundBytes').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-outBoundBytes').getByRole('textbox', { name: 'Add Items' }).fill('12000');
    await page.getByTestId('listBuilder-outBoundBytes').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    const text12000Locator = page.getByText('12000').first();
    await expect(text12000Locator).toBeVisible();
    await expect(text12000Locator).toContainText('12000');
    await page.waitForTimeout(2000);

    const durationsLocator = page.getByText('Durations');
    await expect(durationsLocator).toBeVisible();
    await expect(durationsLocator).toContainText('Durations');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-durations').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-durations').getByRole('textbox', { name: 'Add Items' }).fill('10');
    await page.getByTestId('listBuilder-durations').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    const firewallFilters10Locator = page.getByLabel('FIREWALL_FILTERS').getByText('10');
    await expect(firewallFilters10Locator).toBeVisible();
    await expect(firewallFilters10Locator).toContainText('10');
    await page.waitForTimeout(2000);

    const numberOfSessionsLocator = page.getByText('Number of Sessions', { exact: true });
    await expect(numberOfSessionsLocator).toBeVisible();
    await expect(numberOfSessionsLocator).toContainText('Number of Sessions');
    await page.waitForTimeout(2000);

    await page.getByTestId('listBuilder-sessionCounts').getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByTestId('listBuilder-sessionCounts').getByRole('textbox', { name: 'Add Items' }).fill('2');
    await page.getByTestId('listBuilder-sessionCounts').getByRole('button', { name: 'Add Items' }).click();
    await page.waitForTimeout(3000);

    const firewallFilters2Locator = page.getByLabel('FIREWALL_FILTERS').getByText('2', { exact: true });
    await expect(firewallFilters2Locator).toBeVisible();
    await expect(firewallFilters2Locator).toContainText('2');
    await page.waitForTimeout(2000);

    const protoClassTabItselfLocator = page.getByRole('tab', { name: 'Protocol Classification' });
    await expect(protoClassTabItselfLocator).toBeVisible();
    await expect(protoClassTabItselfLocator).toContainText('Protocol Classification');
    await protoClassTabItselfLocator.locator('span').click();
    await page.waitForTimeout(2000);

    const nwApplicationsLocator = page.getByTestId('multiDropdown-nwApplications').getByText('Network Applications', { exact: true });
    await expect(nwApplicationsLocator).toBeVisible();
    await expect(nwApplicationsLocator).toContainText('Network Applications');
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'INCLUDE' }).click();
    await page.getByTestId('multiDropdown-nwApplications').getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multiDropdown-nwApplications').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByTestId('multiDropdown-nwApplications').getByRole('textbox', { name: 'Search...' }).fill('Business');
    await page.getByTestId('multiDropdown-nwApplications').getByRole('textbox', { name: 'Search...' }).press('Enter');
    await page.waitForTimeout(3000);
    await page.getByRole('button', { name: '' }).click();
    await page.getByRole('checkbox', { name: 'Business Weekly' }).check();
    await page.getByText('Selected Items ( 1 )').click();
    await page.getByTestId('multiDropdown-nwApplications').getByText('Done').click();
    await page.waitForTimeout(3000);

    const nwServicesLocator = page.getByTestId('multiDropdown-nwServices').getByText('Network Services', { exact: true });
    await expect(nwServicesLocator).toBeVisible();
    await expect(nwServicesLocator).toContainText('Network Services');
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Any ' }).click();
    await page.getByText('Clear Selection').click();
    await page.getByTestId('multi-dropdown').getByRole('textbox', { name: 'Search...' }).click();
    await page.getByTestId('multi-dropdown').getByRole('textbox', { name: 'Search...' }).fill('DNS');
    await page.locator('label').click();
    await page.getByText('Selected Items ( 1 )').click();
    await page.getByTestId('multi-dropdown').getByText('Done').click();
    await page.waitForTimeout(3000);

    const securityTabLocator = page.getByTestId('content-tabs-container').getByText('Security');
    await expect(securityTabLocator).toBeVisible();
    await expect(securityTabLocator).toContainText('Security');
    await securityTabLocator.click();
    await page.waitForTimeout(2000);

    const threatNamesLocator = page.getByText('Threat Names', { exact: true });
    await expect(threatNamesLocator).toBeVisible();
    await expect(threatNamesLocator).toContainText('Threat Names');
    await page.waitForTimeout(2000);

    await page.getByRole('textbox', { name: 'Add Items' }).click();
    await page.getByRole('textbox', { name: 'Add Items' }).fill('Threat 101');
    await page.getByRole('button', { name: 'Add Items' }).click();
    await page.getByText('Threat 101').click();
    await page.waitForTimeout(3000);

    const advThreatCatLocator = page.getByText('Advanced Threat Category');
    await expect(advThreatCatLocator).toBeVisible();
    await expect(advThreatCatLocator).toContainText('Advanced Threat Category');
    await page.waitForTimeout(2000);

    await page.getByRole('button', { name: 'Any ' }).click();
    await page.waitForTimeout(2000);
    await page.getByText('Clear Selection').click();
    await page.locator('(//li/label/input[@type="checkbox"])[1]').click();
    await page.waitForTimeout(2000);
    await page.locator('//span[contains(text(),"Selected Items ( 1 )")]').isVisible();
    await page.locator('//span[contains(text(),"Done")]').click();
    await page.waitForTimeout(3000);
    await page.locator('//div[@data-testid="modal-footer"]//div/span[2]').click();
    await page.waitForTimeout(2000);
    const title = page.locator(this.fields.pageHeaderTitle);
    await title.isVisible();
    await expect(title).toHaveText("Nanolog Streaming Service");

  }


  async verifySearchOnNSSFeedGrid(page: Page, feedName: string): Promise<void> {
    console.log("Executing verify Search On NSSFeedGrid...");

    const title1 = page.locator(this.fields.pageHeaderTitle);
    await title1.isVisible();
    await expect(title1).toContainText("Nanolog Streaming Service");

    // Pre-cleanup will be handled by explicit steps in the feature file.
    console.log(`Starting to verify search for feed: "${feedName}" by first adding it.`);
    await page.waitForTimeout(2000);

    // Navigate to NSS Feeds tab first (as per original logic flow)
    const nssFeedOption = page.locator(this.fields.nssFeedsTab);
    await expect(nssFeedOption).toContainText("NSS Feeds");
    await nssFeedOption.click();
    await page.waitForTimeout(2000);

    const addNssFeedButton = await page.locator(this.fields.addNssFeedButton);
    await addNssFeedButton.isVisible();
    await addNssFeedButton.click();
    await page.waitForTimeout(2000);

    const feedNameLabel = await page.locator(this.fields.feedNameLabelOnForm);
    await feedNameLabel.isVisible();
    await expect(feedNameLabel).toContainText("Feed Name");
    await page.waitForTimeout(2000);

    const name = await page.locator(this.fields.feedNameActualInputOnForm);
    await name.isVisible();
    await name.fill(feedName);
    await page.waitForTimeout(2000);

    const nssType = await page.locator(this.fields.nssTypeDropdown);
    await nssType.isVisible();
    await nssType.click();
    await page.waitForTimeout(2000);

    const nssSelectValue = await page.locator(this.fields.nssTypeOptionForWeb);
    await nssSelectValue.isVisible();
    await nssSelectValue.click();
    await page.waitForTimeout(2000);


    const nss_serverFieldName = await page.locator(this.fields.nssServerLabel);
    await nss_serverFieldName.isVisible();
    await expect(nss_serverFieldName).toContainText("NSS Server");
    await page.waitForTimeout(2000);

    const nss_serverDropdown = await page.locator(this.fields.nssServerDropdown);
    await nss_serverDropdown.isVisible();
    await nss_serverDropdown.click();
    await page.waitForTimeout(2000);

    const nss_Server = await page.locator(this.fields.nssServerOptionTestNssForWeb);
    await nss_Server.isVisible();
    await nss_Server.click();
    await page.waitForTimeout(2000);

    const ip_addressFieldName = await page.locator(this.fields.siemIpAddressLabel);
    await ip_addressFieldName.isVisible();
    await expect(ip_addressFieldName).toContainText("SIEM IP Address");
    await page.waitForTimeout(2000);

    const ip_address = await page.locator(this.fields.siemIpAddressInput);
    await ip_address.isVisible();
    await ip_address.click();
    await ip_address.fill("127.0.0.1");
    await page.waitForTimeout(2000);

    const tcp_portFieldName = await page.locator(this.fields.siemTcpPortLabel);
    await tcp_portFieldName.isVisible();
    await expect(tcp_portFieldName).toContainText("SIEM TCP Port");
    await page.waitForTimeout(2000);

    const tcp_portfield = await page.locator(this.fields.siemTcpPortInput);
    await tcp_portfield.isVisible();
    await tcp_portfield.click();
    await tcp_portfield.fill('443');
    await page.waitForTimeout(2000);

    const logTypeFieldName = await page.locator(this.fields.logTypeLabel);
    await logTypeFieldName.isVisible();
    await expect(logTypeFieldName).toContainText("Log Type");
    await page.waitForTimeout(2000);

    const logType_Dropdown = await page.locator(this.fields.logTypeFormDropdown);
    await logType_Dropdown.isVisible();
    await logType_Dropdown.click();
    await page.waitForTimeout(2000);

    const selectAlert_Dropdown = await page.locator(this.fields.logTypeOptionAlert);
    await selectAlert_Dropdown.isVisible();
    await selectAlert_Dropdown.click();
    await page.waitForTimeout(2000);

    const save = await page.locator(this.fields.formSaveButton);
    await save.isVisible();
    await save.click();
    await page.waitForTimeout(2000);

    const success = await page.locator(this.fields.changesSavedMessage);
    await success.isVisible();
    await expect(success).toContainText("All changes have been saved.");

    const title = page.locator(this.fields.pageHeaderTitle);
    await title.isVisible();
    await expect(title).toHaveText("Nanolog Streaming Service");

    // Verify the search for the newly created feed
    console.log(`Now verifying search for the created feed: ${feedName}`);
    await this.searchTestFeed(page, feedName); // This method performs search and logs

    // Add an explicit assertion for verification
    const feedInGridLocator = page.locator(`${this.fields.feedNameInGrid}[contains(text(),"${feedName}")]`);
    await expect(feedInGridLocator.first()).toBeVisible({ timeout: 10000 });
    console.log(`Verified: Feed "${feedName}" is visible in the grid after creation and search.`);
    // Post-cleanup will be handled by explicit steps in the feature file.
  }

  async searchTestFeed(page: Page, feedNameToSearch: string): Promise<void> {
    console.log(`Executing searchTestFeed for: "${feedNameToSearch}"`);

    const searchInput = page.locator(this.fields.searchNssFeedInput);
    await expect(searchInput).toBeVisible({ timeout: 10000 });
    console.log(`Search input is visible. Filling with: ${feedNameToSearch}`);
    await searchInput.fill(feedNameToSearch);

    const searchButton = page.locator(this.fields.searchNssFeedButton);
    await expect(searchButton).toBeVisible();
    console.log('Search button is visible. Clicking search button.');

    // Option 1: Wait for a specific network response if the search triggers one.
    // Replace '**/api/nss/search**' with the actual search API endpoint.
    // await page.waitForResponse(response => response.url().includes('**/api/nss/search**') && response.status() === 200, { timeout: 10000 });

    await searchButton.click();
    console.log('Search button clicked.');

    // Option 2: Wait for network activity to settle after the click.
    // This is a more generic approach if you don't know the exact API endpoint or if there isn't one.
    console.log('Waiting for network to be idle after search click...');
    await page.waitForLoadState('networkidle', { timeout: 10000 });
    console.log('Network is idle.');

    // Check if the feed is visible in the grid after search
    const feedInGridLocator = page.locator(`${this.fields.feedNameInGrid}[contains(text(),"${feedNameToSearch}")]`);
    if (await feedInGridLocator.isVisible({ timeout: 5000 })) {
      console.log(`Feed "${feedNameToSearch}" found in grid after search.`);
    } else {
      console.log(`Record not found in Feed: "${feedNameToSearch}"`);
    }
    console.log(`Search action for "${feedNameToSearch}" completed.`);
  }

  async deleteFeed(page: Page, feedNameToDelete: string): Promise<void> {
    const apiHelper = new ZiaAdminApiHelper(page);
    await apiHelper.deleteNssFeedByName(feedNameToDelete);
  }
  async deleteTestFeed(page: Page, feedNameToDelete: string): Promise<void> {
    await page.locator('//li[@data-label="CONFIGURE_FEEDS"]/span').click();

    const search = await page.locator('(//input[@placeholder="Search..."])[1]');
    await search.click();
    await search.fill(feedNameToDelete);
    await page.locator('(//span[@aria-label="Search"])[1]').click();
    await page.waitForTimeout(2000);

    const value = page.locator(`(//div[@role="listitem"])[1]//span[contains(text(),"${feedNameToDelete}")]`);
    const noMatchValue = page.locator('//div[contains(text(),"No matching data found")]');

    if (await value.isVisible()) {
      await expect(value).toContainText(feedNameToDelete);
      await page.locator('//i[@aria-label="EDIT"]').click();
      await page.locator('//span[@title="Edit NSS Feed"]').isVisible();
      await page.locator('//span[contains(text(),"Delete")]').click();
      await page.locator('//span[contains(text(),"Confirm")]').click();
      await page.waitForTimeout(2000);
      await page.locator('//span[contains(text(),"The item has been deleted.")]').isVisible();
    } else if (await noMatchValue.isVisible()) {
      await page.waitForTimeout(2000);
    } else {
      throw new Error("Unexpected condition: Could not find the expected elements.");
    }
  }

}

export default new NanologStreamingServicePage();