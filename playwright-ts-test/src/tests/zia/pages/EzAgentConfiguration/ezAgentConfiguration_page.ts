import { Page, expect } from "@playwright/test";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";


class EzAgentConfiguration {

  async navigateToEzAgentConfiguration(page: Page): Promise<void> {
    console.log("Executing navigateToEzAgentConfiguration...");

    console.log("The Url used is : - " + url);
    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
    }

    const ezAgentConfigPath = "administration/ez-agent-configurations";
    let targetUrl = "";

    if (url.includes("console")) {
      targetUrl = url + "internet-saas#" + ezAgentConfigPath;
      await page.goto(targetUrl);
      console.log("Page navigation initiated (console URL).");
      await page.waitForTimeout(25000);
    } else {
      targetUrl = url + "#" + ezAgentConfigPath;
      await page.goto(targetUrl);
      console.log("Page navigation initiated (non-console URL).");
      await page.waitForTimeout(19000);
    }
    await expect(page).toHaveURL(new RegExp(ezAgentConfigPath.replace(/\//g, '\\/')));
    console.log("Successfully navigated to EZ Agent Configuration.");
    await page.waitForTimeout(5000);

  }

  async checkTitle(page: Page, titleName: string): Promise<void> {
    const title = page.locator(`//div/span[contains(text(),"${titleName}")]`);
    await expect(title).toBeVisible({ timeout: 2000});
    await expect(title).toContainText(titleName);
  }

}

export default new EzAgentConfiguration(); 