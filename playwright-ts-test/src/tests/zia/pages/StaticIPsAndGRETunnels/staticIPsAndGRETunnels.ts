import { Page, expect } from "@playwright/test";
import { config } from "dotenv";
import ZIAReusableFunctions from "../../../../../resources/utils/ZIAReusableFunctions";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";

interface StaticIPsGRETunnelsLocators {
  
    pageTitle: string;
    currentButton: string;
    manualRegionStatus: string;
    selectDropdownText: string;
    AddStaticIPButton: string;
    AddGRETunnelButton: string;
    recommendedButton: string;
    importButton: string;
}
class StaticIPsGRETunnelsPage {
  private locators: StaticIPsGRETunnelsLocators;

  constructor() {
    this.locators = {
      pageTitle: '//*[contains(@class,"page-title")]//*[contains(@class,"title") and text()]',
      recommendedButton: '(//*[contains(@class,"dropdown-list")]//*[contains(text(),"Recommended")])[1]',
      currentButton: '(//*[contains(@class,"dropdown-list")]//*[contains(text(),"Current")])[1]',
      manualRegionStatus: '//*[contains(@class,"active") and @aria-label="Manual"]',
      selectDropdownText: '(//*[contains(@class,"dropdown-list")]//*[text()])[1]',
      importButton: '(//*[contains(text(),"Import CSV File")]/i)[1]',
      AddStaticIPButton: '(//*[contains(text(),"Add Static IP")]/i)[1]',
      AddGRETunnelButton: '(//*[contains(text(),"Add GRE Tunnel")]/i)[1]',
    };
  }
  async navigateToStaticIPsAndGRETunnels(page: Page): Promise<void> {

    console.log("The Url used is : - "+url);
          if (!url) {
              throw new Error('ONE_UI_BASE_URL environment variable is not set');
          }
          if (url.includes("console")) {
            await page.goto(url+"internet-saas#administration/gre-self-sign");
            await page.waitForTimeout(15000);
          }
          else{
            await page.goto(url+"#administration/gre-self-sign");
            await page.waitForTimeout(15000);
            console.log("Successfully navigated to Static IPs & GRE Tunnels screen.");
          }
  }
  async provideInputFieldTextBoxInStaticIP(page:Page,labelName: string,inputText: string): Promise<void> {

    await ZIAReusableFunctions.provideZIAInputFieldText(page,labelName,inputText);
  }
  async clickOnStaticIPLabelDropdown(page:Page,labelName: string,indexNo: number): Promise<void> {

    await ZIAReusableFunctions.clickOnZIALabelDropdown(page,labelName,indexNo);
  }
  async singleSelectDropDownRecord(page: Page,dropdownName: string) {

    await page.locator('//*[contains(@class,"list")]//*[text()="'+dropdownName+'"]').hover();
    await page.locator('//*[contains(@class,"list")]//*[text()="'+dropdownName+'"]').click();
  }
  async clickOnStaticIPGRETunnelSearchBoxOption(page: Page,holderName: string,indexNo: Number,searchText: string) {

    await page.locator('(//*[contains(@class,"search")]//input[contains(@placeholder,"'+holderName+'")])['+indexNo+']').hover();
    await page.locator('(//*[contains(@class,"search")]//input[contains(@placeholder,"'+holderName+'")])['+indexNo+']').click();
    await page.locator('(//*[contains(@class,"search")]//input[contains(@placeholder,"'+holderName+'")])['+indexNo+']').fill(searchText);
  }
  async captureSavedNotificationMessage(page: Page): Promise<void> {

    const messgae = await ZIAReusableFunctions.captureZIANotificationMessage(page);    
    expect(messgae).toEqual('All changes have been saved.');
  }
  async clickOnAddStaticIPButton(page:Page): Promise<void> {

    await page.locator(this.locators.pageTitle).isVisible();
    const pageTitle = await page.locator(this.locators.pageTitle).textContent();
    expect(pageTitle).toEqual('Static IPs & GRE Tunnels');
    await page.locator(this.locators.AddStaticIPButton).click();
    const messgae = await ZIAReusableFunctions.captureWizardHeaderTitleInZIA(page);    
    expect(messgae).toEqual('Add Static IP Configuration');
  }
  async fillGRETunnelConfigurationWizard(page:Page): Promise<void> {

    await this.clickOnStaticIPLabelDropdown(page,'Static IP Address',1)
    await page.waitForTimeout(4000);
    await page.locator(this.locators.selectDropdownText).hover();
    await page.locator(this.locators.selectDropdownText).click();
  }
  async clickOnMenuOptionForStaticIPAndGRETunnel(page: Page,ipAddress: string,menuOption: string) {

    await ZIAReusableFunctions.selectZIAMenuOptions(page,ipAddress,menuOption);
  }
  async fillEditWizardDetails(page:Page,labelName: string,indexNo: number): Promise<void> {

    await ZIAReusableFunctions.clickOnZIALabelDropdown(page,labelName,indexNo);
    await page.waitForTimeout(4000);
    await page.locator(this.locators.selectDropdownText).hover();
    await page.locator(this.locators.selectDropdownText).click();
  }
  async clickOnGRETunnelTab(page:Page): Promise<void> {
    
    await ZIAReusableFunctions.clickOnContentTabTitleInZIA(page,'GRE Tunnels');
    await page.waitForTimeout(2000);
  }
  async clickOnFooterButtonOptionForStaticIPAndGRETunnels(page: Page,wizardHeaderName: string,footerButtonAction: string) {

    await ZIAReusableFunctions.clickOnFooterButtonInsideZIAWizard(page,wizardHeaderName,footerButtonAction);
  }
  async clickOnSaveButtonAndCaptureNotificationMessage(page: Page,wizardHeaderName: string) {

    await this.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,wizardHeaderName,'Save');
    const messgae = await ZIAReusableFunctions.captureZIANotificationMessage(page);
    expect(messgae).toEqual('All changes have been saved.');
  }
  async clickOnDeleteButtonAndCaptureNotificationMessage(page: Page,wizardHeaderName: string): Promise<void> {

    await this.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,wizardHeaderName,'Delete');
    const messgae = await ZIAReusableFunctions.captureZIANotificationMessage(page);
    expect(messgae).toEqual('The item has been deleted.');
  }
  async fillRegionDetailsInStaticIP(page: Page) {

    if(await page.locator(this.locators.manualRegionStatus).getAttribute('class') === 'radio-button active'){

      await this.clickOnStaticIPGRETunnelSearchBoxOption(page,'Enter Region',1,'South');
      await page.waitForTimeout(4000);
      await page.locator(this.locators.selectDropdownText).hover();
      await page.locator(this.locators.selectDropdownText).click();
      await this.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,'Static IP Configuration','Next');
    }else{

      await this.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,'Static IP Configuration','Next');
    }
  }
  async clickOnAddGRETunnelsButton(page:Page): Promise<void> {

    await page.locator(this.locators.pageTitle).isVisible();
    const pageTitle = await page.locator(this.locators.pageTitle).textContent();
    expect(pageTitle).toEqual('Static IPs & GRE Tunnels');
    await ZIAReusableFunctions.clickOnContentTabTitleInZIA(page,'GRE Tunnels');
    await page.waitForTimeout(4000);
    await page.locator(this.locators.AddGRETunnelButton).click();
    const messgae = await ZIAReusableFunctions.captureWizardHeaderTitleInZIA(page);    
    expect(messgae).toEqual('Add GRE Tunnel Configuration');
  }
  async selectStaticIPInGRETunnels(page: Page,labelName: string,indexNo: number,ipAddress: string,greDescription: string): Promise<void> {

    await ZIAReusableFunctions.clickOnZIALabelDropdown(page,labelName,indexNo);
    await ZIAReusableFunctions.clickOnSingleSelectDropdownRecord(page,ipAddress)
    await ZIAReusableFunctions.provideZIAInputFieldText(page,'Description',greDescription);
    await ZIAReusableFunctions.clickOnFooterButtonInsideZIAWizard(page,'Add GRE Tunnel','Next');
  }
  async chooseDataCenterInGRETunnels(page: Page,labelName: string,indexNo: number): Promise<void> {

    await ZIAReusableFunctions.clickOnZIALabelDropdown(page,labelName,indexNo);
    await page.locator(this.locators.recommendedButton).click();
    await ZIAReusableFunctions.clickOnFooterButtonInsideZIAWizard(page,'Add GRE Tunnel','Next');
  }
  async selectInternalGREIPRangeInGRETunnels(page: Page,labelName: string,indexNo: number): Promise<void> {

    await ZIAReusableFunctions.clickOnZIALabelDropdown(page,labelName,indexNo);
    await page.locator(this.locators.selectDropdownText).isEnabled();
    await page.locator(this.locators.selectDropdownText).click();
    await ZIAReusableFunctions.clickOnFooterButtonInsideZIAWizard(page,'Add GRE Tunnel','Next');
  }
  async changeDomesticPreferenceToggleForDataCenter(page:Page,labelName: string): Promise<void> {
    
    await ZIAReusableFunctions.clickOnToggleButtonIconInZIA(page,labelName);
  }
  async clickOnCurrentTextButton(page:Page): Promise<void> {
    
    await page.locator(this.locators.currentButton).hover();
    await page.locator(this.locators.currentButton).click();
  }
  async clickOnImportGRETunnelButtonAndCaptureNotificationMessage(page: Page): Promise<void> {

    await page.locator(this.locators.importButton).hover();
    await page.locator(this.locators.importButton).click();
    await this.clickOnFooterButtonOptionForStaticIPAndGRETunnels(page,'Import GRE Tunnel','Import');
    const messgae = await ZIAReusableFunctions.captureZIANotificationMessage(page);
    expect(messgae).toEqual('CSV File Import Error - File extension is not csv');
  }
}

export default new StaticIPsGRETunnelsPage();