import { Page, expect } from "@playwright/test";
import { config } from "dotenv";

config();

const url: string = process.env.ONE_UI_BASE_URL ?? "";
interface NanologStreamingServiceFields {
}

export default class PartnerIntegrationsPage {
  fields: NanologStreamingServiceFields;
  selectedSdwanPartnerName: string | null; 

  constructor() {
    this.fields = {
      // Original fields
      nssFeedsPageTitle: "//div[@class='page-title ']//span[contains(text(),'NSS Feeds')]"
    };
    this.selectedSdwanPartnerName = null; 
  }


  async navigateToPartnerIntegrations(page: Page): Promise<void> {
    console.log("Executing navigateToPartnerIntegrations...");

    const url = process.env.ONE_UI_BASE_URL;
    console.log("The Url used is : - " + url);
    if (!url) {
      throw new Error("ONE_UI_BASE_URL environment variable is not set or is empty.");
    }

    const partnerIntegrationPath = "administration/partner-integration";
    let targetUrl = "";

    if (url.includes("console")) {
      targetUrl = url + "internet-saas#" + partnerIntegrationPath;
      await page.goto(targetUrl);
      console.log("Page navigation initiated (console URL) to Partner Integrations.");
      await page.waitForTimeout(60000);
    } else {
      targetUrl = url + "#" + partnerIntegrationPath;
      await page.goto(targetUrl);
      console.log("Page navigation initiated (non-console URL) to Partner Integrations.");
      await page.waitForTimeout(20000);
    }
    await expect(page).toHaveURL(new RegExp(partnerIntegrationPath.replace(/\//g, '\\/')));
    console.log("Successfully navigated to Partner Integrations screen.");
  }

  async navigateToIntegrationSubSection(page: Page, sectionName: string): Promise<void> {
    console.log(`Navigating to ${sectionName} integration section`);
    await page.locator('//span[@title="Partner Integrations"]').isVisible({ timeout: 10000 });
    await page.waitForTimeout(5000);

    const microsoftCloudLabel = await page.locator(`//span[contains(text(),"${sectionName}")]`);
    await expect(microsoftCloudLabel).toBeVisible();
    await expect(microsoftCloudLabel).toContainText(sectionName);
    await microsoftCloudLabel.click({ timeout: 5000 });

  }

  // XC-10265: Microsoft Cloud App Security
  async verifyMicrosoftCloudAppPageDetails(page: Page, pageTitle: string, section1: string, section2: string): Promise<void> {
    console.log(`Verifying MCAS page details: Title="${pageTitle}", Sections="${section1}", "${section2}"`);
    const microsoftCloudAppSecurityLabel = await page.locator(`//h1[contains(text(),"${pageTitle}")]`);
    await expect(microsoftCloudAppSecurityLabel).toBeVisible();
    await expect(microsoftCloudAppSecurityLabel).toContainText(pageTitle);
    await page.waitForTimeout(2000);


    const nssSubscriptionLabel = await page.locator(`//h1[contains(text(),"${section1}")]`);
    await expect(nssSubscriptionLabel).toBeVisible();
    await expect(nssSubscriptionLabel).toContainText(section1);
    await page.waitForTimeout(2000);

    const unsanctionedCloudLabel = await page.locator(`//h1[contains(text(),"${section2}")]`);
    await expect(unsanctionedCloudLabel).toBeVisible();
    await expect(unsanctionedCloudLabel).toContainText(section2);
    await page.waitForTimeout(2000);


  }

  async verifyLinkNavigationAndReturn(page: Page, linkText: string, targetPage: string): Promise<void> {
    console.log(`Verifying link "${linkText}" navigates to "${targetPage}" and back`);
    const nss_Server_link = await page.locator(`//div/a[contains(text(),"${linkText}")]`);
    await expect(nss_Server_link).toBeVisible();
    await expect(nss_Server_link).toContainText(linkText);
    await nss_Server_link.click();
    await page.waitForTimeout(2000);

    const redirectNanologPage = await page.locator(`//div/span[contains(text(),"${targetPage}")]`);
    await expect(redirectNanologPage).toBeVisible({ timeout: 10000 });
    await page.waitForTimeout(2000);
    await expect(redirectNanologPage).toContainText(targetPage);
    await page.waitForTimeout(2000);

    console.log("Navigating back using browser's back button...");
    await page.goBack();
    await page.waitForTimeout(5000);
    await page.locator('//span[@title="Partner Integrations"]').isVisible({ timeout: 10000 });
  }

  // XC-10266: SD-WAN
  async addAndVerifyNewSdwanKey(page: Page): Promise<void> {
    console.log("Adding and verifying new SD-WAN key");
    const addKeyLink = await page.locator('//span[contains(text(),"Add Partner Key")]');
    await expect(addKeyLink).toBeVisible();
    await addKeyLink.click();
    await page.waitForTimeout(2000);

    await page.locator('//span[@title="Add Partner Key"]').isVisible({ timeout: 10000 });
    const partnerLabel = await page.locator('//div[@id="PARTNER"]//span[contains(text(),"Partner")]');
    await expect(partnerLabel).toBeVisible();
    await expect(partnerLabel).toContainText("Partner");
    await page.waitForTimeout(2000);

    await page.locator('//div[@testid="dropdownAdvance-partner"]//span[@id]').click();
    await page.waitForTimeout(2000);
    const firstValue = await page.locator('//span[@data-testid="dropdownAdvance-partner"]//div/ul/li[1]');
    await expect(firstValue).toBeVisible();
    const partnerName = await firstValue.textContent();
    this.selectedSdwanPartnerName = partnerName ? partnerName.trim() : null; 
    console.log("Value Selected and stored:", this.selectedSdwanPartnerName);
    await firstValue.click();
    await page.waitForTimeout(2000);

    const generateButton = await page.locator('//div[@data-testid="modal-footer"]//span[contains(text(),"Generate")]');
    await expect(generateButton).toBeVisible();
    await generateButton.click();
    await page.waitForTimeout(2000);

    const successMessage = await page.locator('//span[contains(text(),"All changes have been saved.")]');
    await expect(successMessage).toBeVisible();
    await expect(successMessage).toContainText("All changes have been saved.");
    await page.waitForTimeout(2000);

    if (this.selectedSdwanPartnerName) {
      const verifyKey = await page.locator(`//div[@data-testid="cell-partnerName"]//div[contains(text(),"${this.selectedSdwanPartnerName}")]`);
      await expect(verifyKey).toBeVisible();
      await expect(verifyKey).toContainText(this.selectedSdwanPartnerName);
      await page.waitForTimeout(2000);
    } else {
      throw new Error("selectedSdwanPartnerName was null, cannot verify key in grid.");
    }


  }

  async regenerateAndVerifySdwanKey(page: Page): Promise<void> {
    console.log("Regenerating and verifying SD-WAN key");
    if (this.selectedSdwanPartnerName) {
      console.log(`Searching for the partner key associated with: ${this.selectedSdwanPartnerName}`);
      const regenerateAndVerifySdwanKey = await page.locator(`//div[contains(text(),"${this.selectedSdwanPartnerName}")]/ancestor::div[5]/div[3]//i[2]`)
      await expect(regenerateAndVerifySdwanKey).toBeVisible();
      await regenerateAndVerifySdwanKey.click();
      await page.waitForTimeout(2000);

      const regeneratePopupTitle = await page.locator('//span[contains(text(),"Regenerate API Key")]');
      await expect(regeneratePopupTitle).toBeVisible();
      await expect(regeneratePopupTitle).toContainText("Regenerate API Key");
      await page.waitForTimeout(2000);

      await page.locator('//span[contains(text(),"OK")]').click();
      await page.waitForTimeout(2000);

    }
  }

  async editAndVerifySdwanKey(page: Page, newKey: string): Promise<void> {
    console.log(`Editing SD-WAN key to "${newKey}" and verifying`);
    if (this.selectedSdwanPartnerName) {
      console.log(`Searching for the partner key associated with: ${this.selectedSdwanPartnerName}`);
      const regenerateAndVerifySdwanKey = await page.locator(`//div[contains(text(),"${this.selectedSdwanPartnerName}")]/ancestor::div[5]/div[3]//i[1]`)
      await expect(regenerateAndVerifySdwanKey).toBeVisible();
      await regenerateAndVerifySdwanKey.click();
      await page.waitForTimeout(2000);

      const editPopupTitle = await page.locator('//span[contains(text(),"Edit Partner Key")]');
      await expect(editPopupTitle).toBeVisible();
      await expect(editPopupTitle).toContainText("Edit Partner Key");
      await page.waitForTimeout(2000);

      await page.locator('//div[@data-testid="textBox-newKeyValue"]/input').click();
      await page.waitForTimeout(2000);
      await page.locator('//div[@data-testid="textBox-newKeyValue"]/input').fill(newKey);
      await page.waitForTimeout(2000);
      await page.locator('//span[contains(text(),"Confirm")]').click();
      await page.waitForTimeout(2000);

      const successMessage = await page.locator('//span[contains(text(),"All changes have been saved.")]');
      await expect(successMessage).toBeVisible();
      await expect(successMessage).toContainText("All changes have been saved.");
      await page.waitForTimeout(2000);

    }
  }

  async deleteAndVerifySdwanKey(page: Page, key: string): Promise<void> {
    console.log(`Deleting SD-WAN key "${key}" and verifying`);
    if (this.selectedSdwanPartnerName) {
      console.log(`Searching for the partner key associated with: ${this.selectedSdwanPartnerName}`);
      const regenerateAndVerifySdwanKey = await page.locator(`//div[contains(text(),"${this.selectedSdwanPartnerName}")]/ancestor::div[5]/div[3]//i[3]`)
      await expect(regenerateAndVerifySdwanKey).toBeVisible();
      await regenerateAndVerifySdwanKey.click();
      await page.waitForTimeout(2000);

      const deletePopupTitle = await page.locator('//span[contains(text(),"Delete API Key")]');
      await expect(deletePopupTitle).toBeVisible();
      await expect(deletePopupTitle).toContainText("Delete API Key");
      await page.waitForTimeout(2000);

      await page.locator('//span[contains(text(),"OK")]').click();
      await page.waitForTimeout(2000);

      const verifyKeyUpdated = await page.locator(`//div[@data-testid="cell-keyValue"]//div[contains(text(),"${key}")]`);
      await expect(verifyKeyUpdated, `The key "${key}" should NOT be visible if the edit was to a different value or if it was deleted.`).not.toBeVisible();
      await page.waitForTimeout(2000);
    }

  }

  // XC-10267: Azure Virtual WAN
  async verifyTitleVisible(page: Page, expectedTitle: string): Promise<void> {
    console.log(`Verifying title "${expectedTitle}" is visible`);
    const azureTitle = await page.locator('//h1[contains(text(),"Azure Virtual WAN Authentication Credentials")]');
    await expect(azureTitle).toBeVisible();
    await expect(azureTitle).toContainText(expectedTitle);
    await page.waitForTimeout(2000);

  }

  async enterAzureCredential(page: Page, fieldName: string, value: string): Promise<void> {
    console.log(`Entering "${value}" for Azure field "${fieldName}"`);
    await page.waitForTimeout(2000);

    // Locate the label for the field
    const fieldLabelLocator = page.locator(`//span[contains(text(),"${fieldName}")]`);
    await expect(fieldLabelLocator, `Label for "${fieldName}" should be visible`).toBeVisible();
    await expect(fieldLabelLocator, `Label for "${fieldName}" should contain text "${fieldName}"`).toContainText(fieldName);

    // Determine the input field locator based on the fieldName
    let inputFieldLocator;
    switch (fieldName) {
      case "Application ID":
        inputFieldLocator = page.locator('//div[@data-testid="textBox-clientId"]/input');
        break;
      case "Application Key":
        inputFieldLocator = page.locator('//div[@data-testid="textBox-clientSecret"]/input');
        break;
      case "Tenant ID":
        inputFieldLocator = page.locator('//div[@data-testid="textBox-tenantId"]/input');
        break;
      case "Subscription ID":
        inputFieldLocator = page.locator('//div[@data-testid="textBox-subscriptionId"]/input');
        break;
      default:
        throw new Error(`Unknown Azure credential field: ${fieldName}`);
    }

    await expect(inputFieldLocator, `Input field for "${fieldName}" should be visible`).toBeVisible();
    await inputFieldLocator.click();
    await inputFieldLocator.fill(value);
    await page.waitForTimeout(2000);
  }

  async verifyAzureTestButtonIsClickable(page: Page, buttonName: string): Promise<void> {
    console.log(`Verifying Azure "${buttonName}" button is clickable`);
    const testButton = await page.locator(`//div/span[contains(text(),"${buttonName}")]`);
    await expect(testButton).toBeVisible();
    await expect(testButton).toContainText(buttonName);
  }

  // XC-10268: CrowdStrike
  async configureCrowdStrike(page: Page, fqdnListName: string, credentials: { Field: string, Value: string }[]): Promise<void> {
    console.log(`Configuring CrowdStrike: selecting from "${fqdnListName}" and entering credentials`);
    
    const apiAuthLabel = await page.locator(`//span[contains(text(),"${fqdnListName}")]`);
    await expect(apiAuthLabel).toContainText(fqdnListName);
    await page.waitForTimeout(2000);

    const dropdown = await page.locator('//span[@data-testid="dropdown-baseUrl"]//span[@id]');
    await expect(dropdown).toBeVisible();
    await dropdown.click();
    await page.waitForTimeout(2000);

    await page.locator('//span[@data-testid="dropdown-baseUrl"]//ul/li[1]').click();
    await page.waitForTimeout(2000);

    for (const cred of credentials) {
      const fieldName = cred.Field;
      const fieldValue = cred.Value;
      console.log(`Processing CrowdStrike field: "${fieldName}" with value: "${fieldValue}"`);

      // Optional: Verify the label for each field
      const fieldLabelLocator = page.locator(`//span[contains(text(),"${fieldName}")]`);
      await page.waitForTimeout(2000);
      await expect(fieldLabelLocator, `Label for "${fieldName}" should be visible`).toBeVisible();
      await expect(fieldLabelLocator, `Label for "${fieldName}" should contain text "${fieldName}"`).toContainText(fieldName);
      await page.waitForTimeout(1000); 
      let inputFieldLocator;
      switch (fieldName) {
        case "Client ID":
          inputFieldLocator = page.locator('//input[@aria-label="clientId"]');
          break;
        case "Secret":
          inputFieldLocator = page.locator('//input[@type="password"]'); 
          break;
        case "Customer ID":
          inputFieldLocator = page.locator('//input[@aria-label="customerId"]');
          break;
        default:
          console.warn(`Unknown CrowdStrike credential field encountered: ${fieldName}. Skipping.`);
          continue; 
      }
      await expect(inputFieldLocator, `Input field for "${fieldName}" should be visible`).toBeVisible();
      await inputFieldLocator.click();
      await inputFieldLocator.fill(fieldValue);
      await page.waitForTimeout(1000);
    }
  }


  async verifyCrowdStrikeButtonIsClickable(page: Page, buttonName: string): Promise<void> {
    await page.waitForTimeout(2000);
    console.log(`Verifying CrowdStrike "${buttonName}" button is clickable`);
    const saveButton = await page.locator(`//span[contains(text(),"${buttonName}")]`);
    await saveButton.click();
    await page.waitForTimeout(2000);

    }

  async deleteAndVerifyCrowdStrike(page: Page): Promise<void> {
    console.log("Deleting and verifying CrowdStrike integration");
    await page.locator('//span[contains(text(),"Delete")]').click();
    await page.waitForTimeout(2000);
    
    const successDelete = await page.locator('//span[contains(text(),"CrowdStrike Credentials has been deleted.")]');
    await expect(successDelete).toBeVisible();
    await expect(successDelete).toContainText("CrowdStrike Credentials has been deleted.");
    await page.waitForTimeout(2000);
    
  }

  // XC-10269: Microsoft Defender
  async verifyMicrosoftDefenderPage(page: Page, pageTitle: string, buttonName: string): Promise<void> {
    console.log(`Verifying Microsoft Defender page: Title="${pageTitle}", Button="${buttonName}" is clickable`);
    const authorizePageTitle = await page.locator(`//h1[contains(text(),"${pageTitle}")]`);
    await expect(authorizePageTitle).toBeVisible();
    await expect(authorizePageTitle).toContainText(pageTitle);
    await page.waitForTimeout(2000);

    const adminCredentialsButton = await page.locator(`//span[contains(text(),"${buttonName}")]`);
    await expect(adminCredentialsButton).toBeVisible();
    await expect(adminCredentialsButton).toContainText(buttonName);
    await page.waitForTimeout(2000);
    await adminCredentialsButton.click();
    await page.waitForTimeout(2000);
  }

  async configureAndSaveVotiro(page: Page, hostname: string, jwtToken: string): Promise<void> {
    console.log(`Configuring and saving Votiro with hostname "${hostname}" and JWT token`);
    await page.locator('//div[@id="SERVICE_TOKEN"]//span[contains(text(),"Service Token")]').isVisible();
    const serviceToken = await page.locator('//textarea');
    await expect(serviceToken).toBeVisible();
    await serviceToken.click();
    await serviceToken.fill(jwtToken);
    await page.waitForTimeout(2000);

    const hostnameLabel = await page.locator('//span[contains(text(),"Votiro Hostname")]');
    await expect(hostnameLabel).toBeVisible();
    await expect(hostnameLabel).toContainText("Votiro Hostname");
    await page.waitForTimeout(2000);

    const hostNameField = await page.locator('//input[@aria-label="hostname"]');
    await expect(hostNameField).toBeVisible();
    await hostNameField.click();
    await hostNameField.fill(hostname);
    await page.waitForTimeout(2000);

    const saveButton = await page.locator('//span[contains(text(),"Save")]');
    await expect(saveButton).toBeVisible();
    await saveButton.click();
    await page.waitForTimeout(2000);
  }

  async verifyVotiroSaved(page: Page): Promise<void> {
    console.log("Verifying Votiro integration saved successfully");
    const successDelete = await page.locator('//span[contains(text(),"All changes have been canceled.")]');
    await expect(successDelete).toBeVisible();
    await expect(successDelete).toContainText("All changes have been canceled.");
    await page.waitForTimeout(2000);
  }

  async deleteAndVerifyVotiro(page: Page): Promise<void> {
    console.log("Deleting and verifying Votiro integration");

    await page.locator('//span[contains(text(),"Delete")]').click();
    await page.waitForTimeout(2000);

    await page.locator('//span[@title="Delete Votiro Token"]').isVisible();
    await page.waitForTimeout(2000);

    await page.locator('//span[contains(text(),"OK")]').click();
    await page.waitForTimeout(2000);

    await page.waitForTimeout(2000);
    const successAdded = await page.locator('//span[contains(text(),"The item has been deleted.")]');
    await expect(successAdded).toBeVisible();
    await expect(successAdded).toContainText("The item has been deleted.");
    await page.waitForTimeout(2000);

  }
}