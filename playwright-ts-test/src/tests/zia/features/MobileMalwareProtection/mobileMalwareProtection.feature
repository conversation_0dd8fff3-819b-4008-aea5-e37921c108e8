@ZIA_admin @mobile_malware_protection @k
Feature: Mobile Malware Protection Toggles and Actions
  This feature verifies the functionality of various toggles and actions within the Mobile Malware Protection screen.

  #XC-9767
  Scenario: To verify the Mobile Malware Protection toggle Malicious Activity
    Given User navigates to the Mobile Malware Protection screen
    Then Verify the toggle for the "Malicious Activity" feature

  #XC-9768
  Scenario: To verify the Mobile Malware Protection toggle Known Vulnerabilities
    Given User navigates to the Mobile Malware Protection screen
    Then Verify the toggle for the "Known Vulnerabilities" feature

  #XC-9769
  Scenario: To verify the Mobile Malware Protection toggle Unencrypted User Credentials
    Given User navigates to the Mobile Malware Protection screen
    Then Verify the toggle for the "Unencrypted User Credentials" feature

  #XC-9770
  Scenario: To verify the Mobile Malware Protection toggle Location Information
    Given User navigates to the Mobile Malware Protection screen
    Then Verify the toggle for the "Location Information" feature

  #XC-9771
  Scenario: To verify the Mobile Malware Protection toggle Personally Identifiable Information
    Given User navigates to the Mobile Malware Protection screen
    Then Verify the toggle for the "Personally Identifiable Information" feature

  #XC-9772
  Scenario: To verify the Mobile Malware Protection toggle Device Identifiers Information
    Given User navigates to the Mobile Malware Protection screen
    Then Verify the toggle for the "Device Identifiers" feature

  #XC-9773
  Scenario: To verify the Mobile Malware Protection toggle Communication with Ad Servers
    Given User navigates to the Mobile Malware Protection screen
    Then Verify the toggle for the "Communication with Ad Servers" feature

  #XC-9774
  Scenario: To verify the Mobile Malware Protection toggle Communication with Unknown Servers
    Given User navigates to the Mobile Malware Protection screen
    Then Verify the toggle for the "Communication with Unknown Servers" feature

  #XC-9775
  Scenario: To verify click at recommended policy in Mobile Malware Protection
    Given User navigates to the Mobile Malware Protection screen
    Then Verify click at recommended policy

