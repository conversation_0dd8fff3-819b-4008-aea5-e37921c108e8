@ZIA_admin @malware_policy @k
Feature: Malware Policy Toggles and Exceptions
  This feature verifies the functionality of all toggles and exceptions within the Malware Policy screen.

  #XC-9520
  Scenario: To verify the Malware Policy toggle Inspect Inbound Traffic
    Given User navigates to the Malware Policy screen
    Then Verify the toggle "Inspect Inbound Traffic" feature

  #XC-9534
  Scenario: To verify the Malware Policy->Security Exceptions toggle Password-Protected Files
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for file exception "Password-Protected Files"

  #XC-9535
  Scenario: To verify the Malware Policy->Security Exceptions toggle Unscannable Files
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for file exception "Unscannable Files"

  #XC-9536
  Scenario: To verify the Malware Policy->Security Exceptions toggle Do Not Scan Content from these URLs label
    Given User navigates to the Malware Policy screen
    Then Verify the toggle Do Not Scan Content from these URLs feature 

  #XC-9537
  Scenario: To verify the Malware Policy->Security Exceptions Delete Do Not Scan Content from these URLs
    Given User navigates to the Malware Policy screen
    Then Verify the ability to Delete Do Not Scan Content from these URLs

  #XC-9538
  Scenario: To verify the Malware Policy->Security Exceptions Remove All Do Not Scan Content from these URLs
    Given User navigates to the Malware Policy screen
    Then Verify the ability to Remove All Do Not Scan Content from these URLs

  #XC-9539
  Scenario: To verify the Malware Policy->Security Exceptions Remove Page Do Not Scan Content from these URLs
    Given User navigates to the Malware Policy screen
    Then Verify the ability to Remove Page Do Not Scan Content from these URLs

  #XC-9521
  Scenario: To verify the Malware Policy toggle Inspect Outbound Traffic
    Given User navigates to the Malware Policy screen
    Then Verify the toggle "Inspect Outbound Traffic" feature

  #XC-9522
  Scenario: To verify the Malware Policy toggle Inspect HTTP
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for protocol "Inspect HTTP"

  #XC-9523
  Scenario: To verify the Malware Policy toggle Inspect FTP over HTTP
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for protocol "Inspect FTP over HTTP"

  #XC-9524
  Scenario: To verify the Malware Policy toggle Inspect FTP
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for protocol "Inspect FTP"

  #XC-9525
  Scenario: To verify the Malware Policy toggle Unwanted Applications
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for threat type "Unwanted Applications"

  #XC-9526
  Scenario: To verify the Malware Policy toggle Trojans
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for threat type "Trojans"

  #XC-9527
  Scenario: To verify the Malware Policy toggle Worms
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for threat type "Worms"

  #XC-9528
  Scenario: To verify the Malware Policy toggle Ransomware
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for threat type "Ransomware"

  #XC-9529
  Scenario: To verify the Malware Policy toggle Remote Access Tool
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for threat type "Remote Access Tool"

  #XC-9530
  Scenario: To verify the Malware Policy toggle Other Viruses
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for threat type "Other Viruses"

  #XC-9531
  Scenario: To verify the Malware Policy toggle Adware
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for threat type "Adware"

  #XC-9532
  Scenario: To verify the Malware Policy toggle Spyware
    Given User navigates to the Malware Policy screen
    Then Verify the toggle for threat type "Spyware"

  #XC-9533
  Scenario: To verify the Malware Policy toggle Inspect Inbound Traffic and click at Cancel
    Given User navigates to the Malware Policy screen
    Then Verify the toggle Inspect Inbound Traffic and ability to cancel the action