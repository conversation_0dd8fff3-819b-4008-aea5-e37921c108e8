@ZIA_admin @vpn_credentials @k
Feature: VPN Credentials Management
  As an administrator
  I want to manage VPN credentials via the administration interface
  So that I can configure and maintain secure VPN access for the system.

  Background:
    Given User navigates to the VPN Credentials Management

# XC-10231
  Scenario: Manage VPN Credentials (Add, Edit, Delete)
    Given the VPN credential with User ID "vpn_user_01" does not exist
    When I add a new VPN credential with the following details:
      | User ID     | Type | Password        |
      | vpn_user_01 | FQDN | S3cureP@ssw0rd! |
    Then search the "vpn_user_01"
    When I edit the VPN credential "vpn_user_01", changing its password to "NewS3cur3P@ssw0rd!"
    When I delete the VPN credential "vpn_user_01"

# XC-10232
  Scenario: Verify Import VPN Credentials dialog
    When I open the "Import VPN Credentials" dialog
    Then the dialog appears with an enabled "Choose File" button
    And when I cancel it, the dialog closes

# XC-10233
  Scenario: Verify Download CSV file for VPN Credentials
    When I click the "Download CSV" button on the VPN credentials page
    Then a CSV file for VPN credentials should be downloaded successfully

# XC-10234
  Scenario Outline: Verify Authentication Type filter on VPN credentials grid
    Given the user click on the Authentication Type filter dropdown and select "FQDN" value from it