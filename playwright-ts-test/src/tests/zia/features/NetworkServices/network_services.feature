@ZIA_admin @network_services_management @k
Feature: Network Services and Service Groups Management
  As an administrator
  I want to manage Network Services and Service Groups
  So that I can ensure functionality such as adding, editing, deleting, searching, and sorting works correctly.

  Background:
    Given User navigates to the Network Services screen

  # XC-10338
  Scenario: XC-10338: Add, edit, and delete a Network Service
    Given I delete the Network Service "Automation_Network_Service" if it exists
    When I add a Network Service with name "Automation_Network_Service", TCP port "1050", and UDP port "1050"
    Then the Network Service "Automation_Network_Service" should be present
    When I search for Network Service by port "1050"
    Then the Network Service "Automation_Network_Service" should be visible
    When I edit the Network Service "Automation_Network_Service" to change its description to "Updated description"
    When I delete the Network Service "Automation_Network_Service"
    Then the Network Service "Automation_Network_Service" should not be present

  # XC-10339
  Scenario: XC-10339: Verify sorting and protocol filtering for Network Services
    Then I should be able to sort Network Services by name and description
    And I should be able to filter Network Services by protocol "UDP"
    And I should be able to filter Network Services by protocol "TCP"

  # XC-10340
  Scenario: XC-10340: Add, edit, and delete a Network Service Group
    Given I delete the Network Service Group "Automation_Service_Group" if it exists
    When I add a Network Service Group with name "Automation_Service_Group" and select service "Zscaler Proxy Network Services"
    Then the Network Service Group "Automation_Service_Group" should be present
    When I edit the Network Service Group "Automation_Service_Group" to add description "Service Group Description"
    When I delete the Network Service Group "Automation_Service_Group"
    Then the Network Service Group "Automation_Service_Group" should not be present

  # XC-10341
  Scenario: XC-10341: Verify search and sorting for Network Service Groups
    Given I delete the Network Service Group "Test_Service_Group_1" if it exists
    And I delete the Network Service Group "Test_Service_Group_2" if it exists
    When I add a Network Service Group with name "Test_Service_Group_1" and select service "Zscaler Proxy Network Services"
    And I add a Network Service Group with name "Test_Service_Group_2" and select service "Zscaler Proxy Network Services"
    Then I should be able to search for Network Service Group "Test_Service_Group_1"
    And I should be able to sort Network Service Groups by name and description
    When I delete the Network Service Group "Test_Service_Group_1"
    And I delete the Network Service Group "Test_Service_Group_2"
    Then the Network Service Group "Test_Service_Group_1" should not be present
    And the Network Service Group "Test_Service_Group_2" should not be present