@ZIA_admin @AdvancedSettings
Feature: To verify the advanced settings functionality

# # XC-9887
#   Scenario: To verify the Allow FTP Over HTTP toggle
#    Given User navigates to the Advanced Settings screen
#    When User verify the heading "Advanced Settings"
#    And Verify the Advanced setting subHeading "Admin Ranking"
#    And Verify the title "Enable Admin Ranking"
#    Then Change the Enable Admin Ranking toggle
#    And User clicks on "Save" button
#    Then Enable the Enable Admin Ranking toggle
#    And User clicks on "Save" button

# #  XC-9888
#   Scenario: To verify the Allow Cascading to URL Filtering toggle
#    Given User navigates to the Advanced Settings screen
#    When User verify the heading "Advanced Settings"
#    And Verify the Advanced setting subHeading "Advanced Web App Control Options"
#    And Verify the title "Allow Cascading to URL Filtering"
#    Then Change the Allow Cascading to URL Filtering toggle
#    And User clicks on "Save" button
#    Then Enable the Allow Cascading to URL Filtering toggle
#    And User clicks on "Save" button

# # XC-9889
#   Scenario: To verify the Session Timeout field check
#    Given User navigates to the Advanced Settings screen
#    When User verify the heading "Advanced Settings"
#    And Verify the Advanced setting subHeading "Admin Portal Session Timeout"
#    And Verify the title "Session Timeout Duration (In Minutes)"
#    Then User enter session timeout "5"
#    And User clicks on "Save" button
#    Then User enter session timeout "600"
#    And User clicks on "Save" button

# # XC-9890
#   Scenario: To verify the Authentication Exemptions section
#    Given User navigates to the Advanced Settings screen
#    When User verify the heading "Advanced Settings"
#    And Verify the Advanced setting subHeading "Authentication Exemptions"
#    And User enter the details for authentication exemptions
#    Then User clicks on "Save" button
#    And User de-selects the details for authentication exemptions
#    Then User clicks on "Save" button

#  # XC-9891
#   Scenario: To verify the Kerberos Authentication Exemptions section
#    Given User navigates to the Advanced Settings screen
#    When User verify the heading "Advanced Settings"
#    And Verify the Advanced setting subHeading "Kerberos Authentication Exemptions"
#    And User enter the details for Kerberos authentication exemptions
#    Then User clicks on "Save" button
#    And User de-selects the details for Kerberos authentication exemptions
#    Then User clicks on "Save" button

# # XC-9894
#   Scenario: To verify the Several toggle buttons enablement
#    Given User navigates to the Advanced Settings screen
#    When User verify the heading "Advanced Settings"
#    And User enable the toggle button
#    Then User clicks on "Save" button
#    And User disable the toggle button
#    Then User clicks on "Save" button

# # XC-9895
#   Scenario: To verify the drop down verification
#    Given User navigates to the Advanced Settings screen
#    When User verify the heading "Advanced Settings"
#    And User select the options in the drop down
#    Then User clicks on "Cancel" button

# # XC-9896
#   Scenario: To verify the 2 options are shown when Prefer SNI Over CONNECT Host for DNS Resolution enabled
#    Given User navigates to the Advanced Settings screen
#    When User verify the heading "Advanced Settings"
#    And User enable the option and select values in the option
#    Then User clicks on "Cancel" button