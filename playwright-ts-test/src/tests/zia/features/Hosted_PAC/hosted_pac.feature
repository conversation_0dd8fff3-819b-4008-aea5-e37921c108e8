@ZIA_admin @hosted_pac_files @k
Feature: Hosted PAC File Versioning
  As an administrator
  I want to manage Hosted PAC files
  So that I can control network traffic routing for my users.

  Background:
    Given User navigates to the Hosted PAC Files page

  # @XC-10235
  Scenario: Add, branch, and delete a PAC file
    Given I delete the PAC file "PAC-E2E-Automation-Test" from the main grid
    When I create and deploy a new PAC file with the following details:
      | PAC File Name           | Description          | Domain          | Deploy Comment     |
      | PAC-E2E-Automation-Test | A random description | First available | Initial deployment |
    Then the PAC file "PAC-E2E-Automation-Test" should be in the grid
    When I create a new branch for the PAC file "PAC-E2E-Automation-Test" with comment "New branch created"
    Then I should be on the file history page for "PAC-E2E-Automation-Test"
    When I delete the PAC file "PAC-E2E-Automation-Test" from the main grid
    Then the PAC file "PAC-E2E-Automation-Test" should not be in the grid

  # @XC-10271
  Scenario: Export a PAC file from the main grid
    Given I delete the PAC file "PAC-E2E-ExportAutomation-Test" from the main grid
    When I create and deploy a new PAC file with the following details:
      | PAC File Name                 | Description          | Domain          | Deploy Comment     |
      | PAC-E2E-ExportAutomation-Test | A random description | First available | Initial deployment |
    Then the PAC file "PAC-E2E-ExportAutomation-Test" should be in the grid
    When I choose to export the PAC file "PAC-Export-Test" from the grid
    Given I delete the PAC file "PAC-E2E-ExportAutomation-Test" from the main grid

  # @XC-10272
  Scenario: Manage versions of a PAC file
    Given I delete the PAC file "PAC-E2E-ManageVer-Automation-Test" from the main grid
    When I create and deploy a new PAC file with the following details:
      | PAC File Name                     | Description          | Domain          | Deploy Comment     |
      | PAC-E2E-ManageVer-Automation-Test | A random description | First available | Initial deployment |
    Then the PAC file "PAC-E2E-ManageVer-Automation-Test" should be in the grid
    When I manage versions for the PAC file "PAC-Version-Test"
    Then I can view the deployed version
    And I can see the correct export options for the latest version
    And I can create a new branch with comment "Branch from Manage Versions"
    And I can initiate and cancel a version comparison
    And I navigate back to the PAC file list
    Then the PAC file "PAC-E2E-ManageVer-Automation-Test" should be in the grid
    Then I delete the PAC file "PAC-E2E-ManageVer-Automation-Test" from the main grid

  # @XC-10273
  Scenario: Sort PAC files by name
    Given I delete the PAC file "AA" from the main grid
    Given I delete the PAC file "ZZ" from the main grid
    When I create and deploy a new PAC file with the following details:
      | PAC File Name | Description          | Domain          | Deploy Comment     |
      | AA            | A random description | First available | Initial deployment |
    When I create and deploy a new PAC file with the following details:
      | PAC File Name | Description          | Domain          | Deploy Comment     |
      | ZZ            | A random description | First available | Initial deployment |
    Then sort the grid ascending to descending and "AA" should be at top
    Given I delete the PAC file "AA" from the main grid
    Given I delete the PAC file "ZZ" from the main grid

  # @XC-10274
  Scenario: Search for a PAC file
    Given I delete the PAC file "PAC-E2E-SearchAutomation-Test" from the main grid
    When I create and deploy a new PAC file with the following details:
      | PAC File Name                 | Description          | Domain          | Deploy Comment     |
      | PAC-E2E-SearchAutomation-Test | A random description | First available | Initial deployment |
    Then the PAC file "PAC-E2E-SearchAutomation-Test" should be in the grid
    Given I delete the PAC file "PAC-E2E-SearchAutomation-Test" from the main grid
