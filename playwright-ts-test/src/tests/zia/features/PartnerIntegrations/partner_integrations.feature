@ZIA_admin @partner_integration @k
Feature: Partner Integrations Management
  As a ZIA admin
  I want to manage various partner integrations
  So that I can extend ZIA's capabilities with third-party services

  Background:
    Given User navigates to the Partner Integration screen

# XC-10265 
  Scenario: Verify Microsoft Cloud App Security (MCAS) Authentication Setup
    When User navigates to the "Microsoft Cloud App Security" integration section
    Then User verifies the "Microsoft Cloud App Security (MCAS) Authentication Setup" page details including "NSS Subscription" and "Unsanctioned Cloud Application URLs Sync" sections
    And User verifies the "View NSS Servers." link navigates to "Nanolog Streaming Service" and back
    And User verifies the "View NSS Feeds" link navigates to "Nanolog Streaming Service" and back

# XC-10266 
  Scenario: Verify SD-WAN add, edit, and delete functionality
    When User navigates to the "SD-WAN" integration section
    And User adds a new SD-WAN key and verifies it is generated
    When User re-generates the SD-WAN key and verifies it is updated
    When User edits the SD-WAN key to "123456789012" and verifies the update
    When User deletes the SD-WAN key "123456789012" and verifies it is removed

# XC-10267 
  Scenario: Verify Azure Virtual WAN Authentication Credentials configuration
    When User navigates to the "Azure Virtual WAN" integration section
    Then User should see the title "Azure Virtual WAN Authentication Credentials"
    When User enters random Azure Virtual WAN credentials:
      | Field           | Value                    |
      | Application ID  | AppId_Automation_Test    |
      | Application Key | AppKey_Automation_Test   |
      | Tenant ID       | TenantId_Automation_Test |
      | Subscription ID | SubId_Automation_Test    |
    Then the "Test" button for Azure Virtual WAN is clickable

# XC-10268 
  Scenario: Verify CrowdStrike integration configuration, save, and delete
    When User navigates to the "CrowdStrike" integration section
    And User configures CrowdStrike by selecting the first "API Auth FQDN" and entering random credentials:
      | Field       | Value |
      | Client ID   |  1234 |
      | Secret      |  5678 |
      | Customer ID | 91011 |
    Then the "Save" button for CrowdStrike is clickable
    When User deletes the CrowdStrike integration and verifies it

# XC-10269 
  Scenario: Verify Microsoft Defender for Endpoint authorization
    When User navigates to the "Microsoft Defender" integration section
    Then User verifies the "Authorize Microsoft Defender for Endpoint" title and the "Provide Admin Credentials" button is clickable


