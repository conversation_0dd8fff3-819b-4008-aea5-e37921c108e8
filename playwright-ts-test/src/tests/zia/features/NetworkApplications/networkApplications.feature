
@ZIA_admin @network_applications @k
Feature: Network Applications Management
  As a user,
  I want to manage network applications and application groups
  So that I can ensure functionality such as searching, sorting, adding, editing, and deleting works correctly.

  Background:
    Given User navigates to the Network Applications screen

  # Search Functionality
  Scenario: XC-10493: Verify search functionality for applications
    Then I should be able to search for applications using valid keywords

  Sc<PERSON>rio: XC-10497: Verify search functionality for DNS application groups
    Then I should be able to search within the DNS application groups

  Scenario: XC-10499: Verify search functionality in application groups
    Then I should be able to search for application groups using valid keywords

  # Sorting Functionality
  Scenario: XC-10494: Verify sorting functionality for applications
    Then I should be able to sort applications by columns such as name and description

  Scenario: XC-10498: Verify sorting functionality for DNS application groups
    Then I should be able to sort DNS application groups by columns such as name and description

  Scenario: XC-10500: Verify sorting functionality for application groups
    Then I should be able to sort application groups by columns such as name and description

  # Application Group Management
  Scenario: XC-10495: Verify creation, editing, and deletion of application groups
    Given I should be able to delete the group "Automation_Group"
    When I add an application group with name "Automation_Group" and description "Automation Group Description"
    Then I should be able to select "APNS", click "Done", and save the group
    When I edit the group for "Automation_Group"
    Then I should see "APNS" is still present
    Then I should be able to select "SMB" also, click "Done", and save the group
    When I edit the group for "Automation_Group"
    Then I should see both "APNS" and "SMB" are present
    Then I should be able to delete the group "Automation_Group"

  Scenario: XC-10495: Verify creation, editing, and deletion of DNS application groups
    Given I should be able to delete the DNS application group "DNS Test Group"
    When I add a DNS application group with name "DNS Test Group" and description "DNS Test Group Description"
    Then I should be able to select "AmazonVideo", click "Done", and save the DNS group
    When I edit the group for "DNS Test Group"
    Then I should see "AmazonVideo" is still present
    Then I should be able to select "BrightSpace" also, click "Done", and save the DNS group
    When I edit the group for "DNS Test Group"
    Then I should see both "AmazonVideo" and "BrightSpace" are present
    And I should be able to delete the DNS application group "DNS Test Group"