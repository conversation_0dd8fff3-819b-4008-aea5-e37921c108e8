@ZIA_admin @dlp_incident_receiver @k
Feature: DLP Incident Receiver
  This feature verifies the functionality of the DLP Incident Receiver settings,
  including ICAP, Zscaler Incident Receiver, and Cloud-to-Cloud Incident Forwarding.

  #XC-9978
  Scenario: Verify downloading MTLS CA Certificate from ICAP Settings
    Given User navigates to the DLP Incident Receiver screen
    Then the DLP Incident Receiver page title is visible
    When User navigates to the "ICAP Settings" tab
    And User clicks the Download MTLS CA Certificate button
    Then a file download should be initiated for the MTLS CA Certificate

  #XC-9979
  Scenario: Add, verify, and clean up an ICAP Receiver
    Given User navigates to the DLP Incident Receiver screen
    And the DLP Incident Receiver page title is visible
    And the ICAP receiver "Test DLP Incident" does not exist
    When User navigates to the "ICAP Settings" tab
    And User clicks the Add ICAP Receiver button
    Then the ICAP Receiver Configuration modal should appear
    When User fills the ICAP Receiver form with name "Test DLP Incident", URL "icaps://1.1.1.1:332/", and status "Enabled"
    And User saves the receiver form
    Then the ICAP receiver "Test DLP Incident" should be visible in the grid
    And the ICAP receiver "Test DLP Incident" does not exist
  

  #XC-9980
  Scenario: Add, search for, and clean up an ICAP Receiver
    Given User navigates to the DLP Incident Receiver screen
    And the DLP Incident Receiver page title is visible
    And the ICAP receiver "Searchable ICAP Receiver" does not exist
    When User navigates to the "ICAP Settings" tab
    And User clicks the Add ICAP Receiver button
    And User fills the ICAP Receiver form with name "Searchable ICAP Receiver", URL "icaps://search.me:333/", and status "Enabled"
    And User saves the receiver form
    Then the ICAP receiver "Searchable ICAP Receiver" should be visible in the grid
    When User searches for receiver "Searchable ICAP Receiver"
    Then the ICAP receiver "Searchable ICAP Receiver" should be visible in the grid
    When User clears the search
    And the ICAP receiver "Searchable ICAP Receiver" does not exist

  #XC-9981
  Scenario: Add ICAP Receivers, verify sorting, and clean up
    Given User navigates to the DLP Incident Receiver screen
    And the DLP Incident Receiver page title is visible
    And the ICAP receiver "AA Test DLP 1" does not exist
    And the ICAP receiver "AA Test DLP 999" does not exist
    When User navigates to the "ICAP Settings" tab
    And User clicks the Add ICAP Receiver button
    And User fills the ICAP Receiver form with name "AA Test DLP 1", URL "icaps://1.1.1.1:331/", and status "Enabled"
    And User saves the receiver form
    And User clicks the Add ICAP Receiver button
    And User fills the ICAP Receiver form with name "AA Test DLP 999", URL "icaps://1.1.1.1:330/", and status "Enabled"
    And User saves the receiver form
    When User sorts the grid by the "name" column
    Then the receiver "AA Test DLP 999" should be at the top of the grid
    And the ICAP receiver "AA Test DLP 1" does not exist 
    And the ICAP receiver "AA Test DLP 999" does not exist 

  #XC-9982
  Scenario: Verify downloading Zscaler Incident Receiver CA Certificate
    Given User navigates to the DLP Incident Receiver screen
    Then the DLP Incident Receiver page title is visible
    When User navigates to the "Zscaler Incident Receiver" tab
    And User clicks the Download Zscaler Incident Receiver button
    Then a file download should be initiated for the Zscaler Incident Receiver

  #XC-9983
  Scenario: Add, verify, and clean up a Zscaler Incident Receiver
    Given User navigates to the DLP Incident Receiver screen
    And the DLP Incident Receiver page title is visible
    And the Zscaler Incident Receiver "Add Test Incident Receiver" does not exist
    When User navigates to the "Zscaler Incident Receiver" tab
    And User clicks the Add Zscaler Incident Receiver button
    Then the Zscaler Incident Receiver Configuration modal should appear
    When User fills the Zscaler Incident Receiver form with name "Add Test Incident Receiver", URL "icaps://1.1.1.1:1344/", and status "Enabled"
    And User saves the receiver form
    Then the Zscaler Incident Receiver "Add Test Incident Receiver" should be visible in the grid
    And the Zscaler Incident Receiver "Add Test Incident Receiver" does not exist

  #XC-9984
  Scenario: Add, search for, and clean up a Zscaler Incident Receiver
    Given User navigates to the DLP Incident Receiver screen
    And the DLP Incident Receiver page title is visible
    And the Zscaler Incident Receiver "Searchable Zscaler Receiver" does not exist
    When User navigates to the "Zscaler Incident Receiver" tab
    And User clicks the Add Zscaler Incident Receiver button
    And User fills the Zscaler Incident Receiver form with name "Searchable Zscaler Receiver", URL "icaps://zscaler.search:1345/", and status "Enabled"
    And User saves the receiver form
    Then the Zscaler Incident Receiver "Searchable Zscaler Receiver" should be visible in the grid
    When User searches for receiver "Searchable Zscaler Receiver"
    Then the Zscaler Incident Receiver "Searchable Zscaler Receiver" should be visible in the grid
    When User clears the search
    And the Zscaler Incident Receiver "Searchable Zscaler Receiver" does not exist

  #XC-9985
  Scenario: Add Zscaler Incident Receivers, verify sorting, and clean up
    Given User navigates to the DLP Incident Receiver screen
    And the DLP Incident Receiver page title is visible
    And the Zscaler Incident Receiver "AA Test Incident 1" does not exist
    And the Zscaler Incident Receiver "AA Test Incident 999" does not exist
    When User navigates to the "Zscaler Incident Receiver" tab
    And User clicks the Add Zscaler Incident Receiver button
    And User fills the Zscaler Incident Receiver form with name "AA Test Incident 1", URL "icaps://1.1.1.1:1333/", and status "Enabled"
    And User saves the receiver form
    And User clicks the Add Zscaler Incident Receiver button
    And User fills the Zscaler Incident Receiver form with name "AA Test Incident 999", URL "icaps://1.1.1.1:1322/", and status "Enabled"
    And User saves the receiver form
    When User sorts the grid by the "name" column
    Then the receiver "AA Test Incident 999" should be at the top of the grid
    And the Zscaler Incident Receiver "AA Test Incident 1" does not exist
    And the Zscaler Incident Receiver "AA Test Incident 999" does not exist

  # #XC-9986 BLOCKED
  # Scenario: Initiate adding a Cloud-to-Cloud Incident Forwarding Tenant for Amazon S3
  #   Given User navigates to the DLP Incident Receiver screen
  #   Then the DLP Incident Receiver page title is visible
  #   When User navigates to the "Cloud-to-Cloud Incident Forwarding" tab
  #   And User clicks the Add Tenant button
  #   And User selects "Amazon S3" as the cloud tenant type

  # #XC-9987 BLOCKED
  # Scenario: Initiate adding a Cloud-to-Cloud Incident Forwarding Tenant for Google Cloud
  #   Given User navigates to the DLP Incident Receiver screen
  #   Then the DLP Incident Receiver page title is visible
  #   When User navigates to the "Cloud-to-Cloud Incident Forwarding" tab
  #   And User clicks the Add Tenant button
  #   And User selects "Google Cloud" as the cloud tenant type

  # #XC-9988 BLOCKED
  # Scenario: Initiate adding a Cloud-to-Cloud Incident Forwarding Tenant for Microsoft Azure
  #   Given User navigates to the DLP Incident Receiver screen
  #   Then the DLP Incident Receiver page title is visible
  #   When User navigates to the "Cloud-to-Cloud Incident Forwarding" tab
  #   And User clicks the Add Tenant button
  #   And User selects "Microsoft Azure" as the cloud tenant type
