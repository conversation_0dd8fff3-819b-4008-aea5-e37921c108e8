@ZIA_admin @root_certificate_management
Feature: Root Certificate Management
  As an administrator
  I want to manage root certificates
  So that I can secure my organization's network.

  Background:
    Given User navigates to the Root Certificates page

  Scenario: Add, download, edit, and delete a root certificate
    When I delete the certificate "Automation Root".
    When I add a root certificate "Automation Root" with all types and upload "Isolation.pem"
    Then the certificate "Automation Root" should be saved
    When I download, edit, and uncheck one type for "Automation Root" and save it
    When I delete the certificate "Automation Root".
    Then "Automation Root" should no longer be visible

  # # @XC-10756
  # Scenario: Verify the default Zscaler Root Certificate
  #   When I view the default Zscaler Root Certificate
  #   Then I should see the name and types of the certificate

  # # @XC-10757
  # Scenario: Verify search functionality
  #   When I search for a certificate named "Random Name"
  #   Then it should appear in the search results
  #   When I clear the search
  #   Then all certificates should reappear

  # # @XC-10758
  # Scenario: Verify sorting functionality
  #   When I sort certificates by name
  #   Then they should be displayed in alphabetical order