@ZIA_admin @virtual_serviceEdge @k
Feature: Virtual ZENs Management
  This feature verifies the functionality of Virtual ZENs and Virtual ZEN Clusters.

  #XC-9858
  Scenario: Verify Virtual ZENs - Add VZEN
    Given User navigates to the Virtual ZENs screen
    Then Verify Virtual ZENs add VZEN with the following details:
      | Name                | Type  | Initial Status | Proxy IP    | Subnet    | Gateway     | Load Balancer IP | On-demand Tunnel |
      | TestVzens           | Small | Disabled       | *********** | ********* | *********** | ***********      | Disable          |
    And then update the VZEN "TestVzens" to status "Enabled" and on-demand tunnel "Enable"
    And then delete the VZEN "TestVzens"

  #XC-9862
  Scenario: Verify Virtual ZENs - Add VZEN and verify New Client Certificate
    Given User navigates to the Virtual ZENs screen
    Then Verify Virtual ZENs add VZEN and new client certificate functionality

  #XC-9863
  Scenario: Verify Virtual ZENs - Download Virtual ZEN VM link
    Given User navigates to the Virtual ZENs screen
    Then Verify Virtual ZENs download Virtual ZEN VM link functionality

  #XC-9864
  Scenario: Verify Virtual ZENs - Download MIB Files link
    Given User navigates to the Virtual ZENs screen
    Then Verify Virtual ZENs download MIB Files link functionality

  #XC-9866
  Scenario: Verify Virtual ZENs - Download Hyper-V File link
    Given User navigates to the Virtual ZENs screen
    Then Verify Virtual ZENs download Hyper-V File link functionality

  #XC-9867
  Scenario: Verify Virtual ZEN Clusters - Add new cluster
    Given User navigates to the Virtual ZENs screen
    Then Verify Virtual ZEN Clusters add new cluster with the following details:
      | Name        | Status  | IPSec Local Termination | IP          | Subnet     |
      | TestCluster | Enabled | enabled                 | *********** | ***********|
    And then delete the VZEN Cluster "TestCluster"