@ZIA_admin @location_management @k
Feature: Location Management
  This feature verifies the functionality of adding, editing, importing, and managing Locations and Location Groups.

  Background:
    Given User navigates to the Location Management screen

  Scenario Outline: Manage a location (add with all options, edit, delete)
    Given the location "<LocationName>" should not exist
    When User adds a location with the following details:
      | Field                                   | Value                             |
      | Name                                    | <LocationName>                    |
      | Country                                 | <Country>                         |
      | City, State, Province                   | <CityStateProvince>               |
      | Time Zone                               | <Time Zone>                       |
      | Exclude from Manual Location Groups     | <ExcludeManualGroups>             |
      | Exclude from Dynamic Location Groups    | <ExcludeDynamicGroups>            |
      | Location Type                           | <location>                        |
      | Static IP Address or GRE Tunnel         | <Static IP>                       |
      | Use XFF from Client Request             | <UseXFF>                          |
      | Enforce Authentication                  | <EnforceAuth>                     |
      | Enable IP Surrogate                     | <EnableIPSurrogate>               |
      | Enforce Surrogate IP for Known Browsers | <EnforceSurrogateIPKnownBrowsers> |
      | Enable Kerberos Authentication          | <EnableKerberos>                  |
      | Enforce Firewall Control                | <EnforceFirewall>                 |
      | Enable IPS Control                      | <EnableIPS>                       |
      | Enforce Bandwidth Control               | <EnforceBandwidth>                |
      | Bandwidth Download (Mbps)               | <BandwidthDownload>               |
      | Bandwidth Upload (Mbps)                 | <BandwidthUpload>                 |
    Then User should see a success message "<SaveSuccessMessage>"
    Then the location "<LocationName>" should be visible in the grid
    When User edits the location "<LocationName>" to set description "<Description>"
    Then User should see a success message "<SaveSuccessMessage>"
    Then the location "<LocationName>" should not exist

    Examples:
      | LocationName    | Country        | CityStateProvince | Time Zone | Static IP    | ExcludeManualGroups | ExcludeDynamicGroups | location    | UseXFF | EnforceAuth | EnableIPSurrogate | EnforceSurrogateIPKnownBrowsers | EnableKerberos | EnforceFirewall | EnableIPS | EnforceBandwidth | BandwidthDownload | BandwidthUpload | SaveSuccessMessage           | Description               | DeleteSuccessMessage       |
      | AUTOLOC_XC10167 | United Kingdom | London            | London    | ************ | true                | true                 | IoT traffic | true   | true        | true              | true                            | true           | true            | true      | true             |                10 |              10 | All changes have been saved. | XC-10167 Test Description | The item has been deleted. |

# XC-10226: Location Management: Location: Verify Import Location.

  Scenario: Verify Import Location functionality
    When User clicks the "Import Location" button
    Then the "Import Locations" modal should appear
    And the "Choose a File" button in the import modal should be clickable
    Then User clicks the "Cancel" button on the import modal
  # XC-10228: Location Management: Location: Verify Sample Import CSV file

  Scenario: Verify Sample Import CSV file download
    When User clicks the Sample Import CSV File button for locations
  # XC-10227: Location Management: Location: Verify Download CSV file

  Scenario: Verify Download CSV file download
    When User clicks the Download CSV button for locations
  #XC-10229 : Location Management: LocationGroups: Verify Add Add Manual Group

  Scenario: LocationGroups: Verify Add, Edit, and Delete the Manual Group
    Given the manual group 'Automation_ManualGroup' should not exist
    When User click on Add Manual Group button
    Then Add manual group 'Automation_ManualGroup' with valid details
    And Verify the manual group 'Automation_ManualGroup' is added
    Then Edit the manual group and update the description as 'Automation_Description'
    Then Delete the manual group
    Then Verify the manual group 'Automation_ManualGroup' is deleted
  #XC-10230 Location Management: LocationGroups: Verify Add Add Dynamic Group

  Scenario: LocationGroups: Verify Add, Edit, and Delete the Dynamic Group
    Given the dynamic group 'Automation_DynamicGroup' should not exist
    When User click on Add Dynamic Group button
    Then Add dynamic group 'Automation_DynamicGroup' with valid details
    And Verify the Dynamic group 'Automation_DynamicGroup' is added
    Then Edit the dynamic group and update the description as 'Automation_Description'
    Then Delete the dynamic group
    Then Verify the dynamic group 'Automation_ManualGroup' is deleted
  # XC-10544 : Location Management: LocationGroups: Verify Edit Add Manual Group

  Scenario: Location Management: LocationGroups: Verify Edit Add Manual Group
    Given the manual group 'AUTOGROUP_XC10544' should not exist
    When User click on Add Dynamic Group button
    Then Add dynamic group 'AUTOGROUP_XC10544' with valid details
    And Verify the Dynamic group 'Automation_DynamicGroup' is added

  # Scenario Outline: LocationGroups: Verify pre selected values in Edit Manual Group functionality
  #   Given the location "<LocationName>" should not exist
  #   When User adds a location with the following details:
  #     | Field                                   | Value                             |
  #     | Name                                    | <LocationName>                    |
  #     | Country                                 | <Country>                         |
  #     | City, State, Province                   | <CityStateProvince>               |
  #     | Time Zone                               | <Time Zone>                       |
  #     | Exclude from Manual Location Groups     | <ExcludeManualGroups>             |
  #     | Exclude from Dynamic Location Groups    | <ExcludeDynamicGroups>            |
  #     | Location Type                           | <location>                        |
  #     | Static IP Address or GRE Tunnel         | <Static IP>                       |
  #     | Use XFF from Client Request             | <UseXFF>                          |
  #     | Enforce Authentication                  | <EnforceAuth>                     |
  #     | Enable IP Surrogate                     | <EnableIPSurrogate>               |
  #     | Enforce Surrogate IP for Known Browsers | <EnforceSurrogateIPKnownBrowsers> |
  #     | Enable Kerberos Authentication          | <EnableKerberos>                  |
  #     | Enforce Firewall Control                | <EnforceFirewall>                 |
  #     | Enable IPS Control                      | <EnableIPS>                       |
  #     | Enforce Bandwidth Control               | <EnforceBandwidth>                |
  #     | Bandwidth Download (Mbps)               | <BandwidthDownload>               |
  #     | Bandwidth Upload (Mbps)                 | <BandwidthUpload>                 |
  #   Then User should see a success message "<SaveSuccessMessage>"
  #   Then the location "<LocationName>" should be visible in the grid
  #   When User edits the location "<LocationName>" to set description "<Description>"
  #   Then User should see a success message "<SaveSuccessMessage>"
  #   Then the location "<LocationName>" should not exist

  #   Examples:
  #     | LocationName    | Country        | CityStateProvince | Time Zone    | Static IP    | ExcludeManualGroups | ExcludeDynamicGroups | location    | UseXFF | EnforceAuth | EnableIPSurrogate | EnforceSurrogateIPKnownBrowsers | EnableKerberos | EnforceFirewall | EnableIPS | EnforceBandwidth | BandwidthDownload | BandwidthUpload | SaveSuccessMessage           | Description               | DeleteSuccessMessage       |
  #     | AUTOLOC_XC10544 | United Kingdom | London            | London       | ************ | true                | true                 | IoT traffic | true   | true        | true              | true                            | true           | true            | true      | true             |                10 |              10 | All changes have been saved. | XC-10167 Test Description | The item has been deleted. |
  #     | AUTOLOC_XC10544 | France         | Paris             | Europe/Paris | ************ | true                | true                 | IoT traffic | true   | true        | true              | true                            | true           | true            | true      | true             |                10 |              10 | All changes have been saved. | XC-10167 Test Description | The item has been deleted. |
