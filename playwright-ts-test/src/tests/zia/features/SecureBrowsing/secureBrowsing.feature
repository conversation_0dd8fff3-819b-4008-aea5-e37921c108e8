@ZIA_admin @secure_browser @k
Feature: Secure Browser Control
  This feature verifies the functionality of all controls within the Secure Browser (Browser Control) screen.

  #XC-9540
  Scenario: To verify Smart Browsing: toggle Enable AI/ML based Smart Browser Isolation
    Given User navigates to the Secure Browser Control screen
    Then Verify Smart Browsing toggle Enable AI ML based Smart Browser Isolation

  #XC-9541
  Scenario: To verify Smart Browsing: select Users
    Given User navigates to the Secure Browser Control screen
    Then Verify Smart Browsing selects User "First User From List"

  #XC-9542
  Scenario: To verify Smart Browsing: Verify search in Users
    Given User navigates to the Secure Browser Control screen
    Then Verify Smart Browsing search in Users for user "First User From List"

  #XC-9543
  Scenario: To verify Smart Browsing: Verify search in Groups
    Given User navigates to the Secure Browser Control screen
    Then Verify Smart Browsing search in Groups for group "First Group From List"

  #XC-9544
  Scenario: To verify Smart Browsing: select Groups
    Given User navigates to the Secure Browser Control screen
    Then Verify Smart Browsing selects Group "First Group From List"

  #XC-9545
  Scenario: To verify Smart Browsing: Verify Browser Isolation Profile default value
    Given User navigates to the Secure Browser Control screen
    Then Verify Smart Browsing Browser Isolation Profile default value

  #XC-9546
  Scenario: To verify Smart Browsing: Verify Cancel by toggle Enable AI/ML based Smart Browser Isolation
    Given User navigates to the Secure Browser Control screen
    Then Verify Smart Browsing cancel toggle Enable AI ML based Smart Browser Isolation

  #XC-9547
  Scenario: To verify Browser Control: toggle Enable Checks & User Notification
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control toggle Enable Checks & User Notification

  #XC-9548
  Scenario: To verify Browser Control: drop down How Often to Check
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown How Often to Check

  #XC-9549
  Scenario: To verify Browser Control: drop down Disable Notification for Plugins
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown "Disable Notification for Plugins" selects plugin "DivX"

  #XC-9550
  Scenario: To verify Browser Control: drop down verify search Disable Notification for Plugins
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown search in "Disable Notification for Plugins" for plugin "Google Gears"

  #XC-9551
  Scenario: To verify Browser Control: drop down verify search Disable Notification for Applications
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown search in "Disable Notification for Applications" for application "Microsoft Office"

  #XC-9552
  Scenario: To verify Browser Control: drop down Disable Notification for Applications
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown Disable Notification for Applications

  #XC-9553
  Scenario: To verify Browser Control: toggle Allow All Browsers
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control toggle Allow All Browsers

  #XC-9554
  Scenario: To verify Browser Control: drop down Microsoft Browsers
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown for "Microsoft Browsers" selects version "IE11"

  #XC-9555
  Scenario: To verify Browser Control: drop down verify search Microsoft Browsers
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown search for "Microsoft Browsers" with version "IE11"

  #XC-9556
  Scenario: To verify Browser Control: drop down Chrome
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown for "Chrome" selects version "127"

  #XC-9557
  Scenario: To verify Browser Control: drop down verify search Chrome
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown search for "Chrome" with version "127"

  #XC-9558
  Scenario: To verify Browser Control: drop down Firefox
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown for "Firefox" selects version "135"

  #XC-9559
  Scenario: To verify Browser Control: drop down verify search Firefox
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown search for "Firefox" with version "135"

  #XC-9560
  Scenario: To verify Browser Control: drop down Safari
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown for "Safari" selects version "14"

  #XC-9561
  Scenario: To verify Browser Control: drop down verify search Safari
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown search for "Safari" with version "14"

  #XC-9562
  Scenario: To verify Browser Control: drop down Opera
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown for "Opera" selects version "115"

  #XC-9563
  Scenario: To verify Browser Control: drop down verify search Opera
    Given User navigates to the Secure Browser Control screen
    Then Verify Browser Control dropdown search for "Opera" with version "115"