@ZIA_admin @event_logs @k
Feature: Event Logs
  This feature verifies the functionality of all controls within the Event Logs screen.

  #XC-10054: Event Logs: Verify Time Range
  Scenario: Verify Event Logs - Time Range selections and application
    Given User navigates to the Event Logs screen

    When User selects "Current Day" from the time range options
    Then the event logs should display entries for "Current Day"

    When User selects "Current Week" from the time range options
    Then the event logs should display entries for "Current Week"

    When User selects "Previous Day" from the time range options
    Then the event logs should display entries for "Previous Day"

    When User selects "Previous Week" from the time range options
    Then the event logs should display entries for "Previous Week"

    When User selects a custom time range from "14 days ago" to "today"
    And User clicks the "Apply" button for time range

  #XC-10055: Event Logs: Verify filters
  Scenario: Verify Event Logs - Filter functionality for Category, Sub-Category, and Result
    Given User navigates to the Event Logs screen

    When User selects "Provisioning" from the "Category" filter dropdown
    Then event logs should be filtered by the selected "Category" for value "Provisioning"

    When User selects "SCIM" from the "Sub-Category" filter dropdown
    And User applies the filters
    Then event logs should be filtered by the selected "Sub-Category" for value "SCIM"

    # (and potentially the Category if filters are additive)
    When User selects "Success" from the "Result" filter dropdown
    Then event logs should be filtered by "Result" status "Success"
    When User selects "Failure" from the "Result" filter dropdown
    Then event logs should be filtered by "Result" status "Failure"

  #XC-10056: Event Logs: Verify Search
  Scenario: Verify Event Logs - Search functionality by different fields
    Given User navigates to the Event Logs screen
    When User selects "Message" as the search field, enters "test login message" and clicks search
    When User selects "Error code" as the search field, enters "AUTH_01" and clicks search
    When User selects "Status Code" as the search field, enters "404" and clicks search

  #XC-10057: Event Logs: Verify Download
  Scenario: Verify Event Logs - Download functionality
    Given User navigates to the Event Logs screen
    When User clicks the download button for event logs
    Then the event logs download process should initiate successfully