@ZIA_admin @risk_profiles @k
Feature: Risk Profiles Management
  As a ZIA Administrator
  I want to manage risk profiles
  So that I can define and apply security policies

  Background:
    Given I am on the "Risk Profiles" administration page

# XC-10333
  Scenario Outline: Manage a new risk profile
    Then I delete the risk profile "<Name>"
    When I add a risk profile with the following details:
      | Name   | Application Status   | Password Strength   | Poor Terms of Service   | Admin Audit Logs   | SSL Pinned   | HTTP Security Header Support   |
      | <Name> | <Application Status> | <Password Strength> | <Poor Terms of Service> | <Admin Audit Logs> | <SSL Pinned> | <HTTP Security Header Support> |
    And I update the risk profile "<Name>" with the following details:
      | Valid SSL Certificate   |
      | <Valid SSL Certificate> |
    Then I delete the risk profile "<Name>"

    Examples:
      | Name                         | Application Status | Password Strength | Poor Terms of Service | Admin Audit Logs | SSL Pinned | HTTP Security Header Support | Valid SSL Certificate |
      | Automation Test Risk Profile | Sanctioned         | Good              | No                    | No               | No         | No                           | No                    |

# XC-10334
  Scenario: Copy and search for a risk profile
    Then I delete the risk profile "<clone Name>"
    Then I delete the risk profile "<Name>"
    When I add a risk profile for clone scenario with the following details:
      | Name   |
      | <Name> |
    And I update the risk profile "<Name>" with the following details:
      | Valid SSL Certificate   |
      | <Valid SSL Certificate> |
    And I copy the risk profile "<Name>"
    When I search for "<clone Name>"
    Then I delete the risk profile "<clone Name>"
    Then I delete the risk profile "<Name>"
    
    Examples:
      | Name                       | Valid SSL Certificate | clone Name                       |
      | Risk Profile for Copy Test | No                    | Clone_Risk Profile for Copy Test |
