@ZIA_admin @FTPControl @k
Feature: To verify FTP Control functionality

# XC-9854 
  Scenario: To verify the Allow FTP Over HTTP toggle
   Given User navigates to the FTP Control screen
   When User verify the "FTP Control" heading
   And Verify the title "FTP over HTTP Traffic"
   And Verify the title "Allow FTP Over HTTP"
   Then Change the "Allow FTP Over HTTP" toggle
   And Click on "Save" button
   Then Change the "Allow FTP Over HTTP" toggle
   And Click on "Save" button

# XC-9855
  Scenario: To verify the Allow Native FTP toggle
   Given User navigates to the FTP Control screen
   When User verify the "FTP Control" heading
   And Verify the title "Native FTP Traffic"
   And Verify the title "Allow Native FTP"
   Then Change the "Allow Native FTP" toggle
   And Click on "Save" button
   Then Change the "Allow Native FTP" toggle
   And Click on "Save" button

# XC-9856 
  Scenario: To verify the Allow Any URL Category toggle
   Given User navigates to the FTP Control screen
   When User verify the "FTP Control" heading
   And Verify the title "Native FTP Traffic"
   And Verify the title "Allow Any URL Category"
   Then Change the "Allow Any URL Category" toggle for URL Category toggle case
   And User select the option in Allowed URL Categories
   And Click on the "Save" button
   And User deselect the option in Allowed URL Categories


# XC-9857
  Scenario: To verify the Recommended Policy option
   Given User navigates to the FTP Control screen
   When User verify the "FTP Control" heading
   And User clicks on the Recommended Policy option
   Then Verify the pop up and close