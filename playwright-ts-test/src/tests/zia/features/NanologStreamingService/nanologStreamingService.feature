@ZIA_admin @nanolog @k
Feature: Nanolog Streaming Service (NSS) Feed Management
  This feature verifies the functionality of adding and managing NSS Feeds for various log types.

  Background:
    Given User navigates to the NSS Feeds screen
    Then verify "NSS_01" NSS Server for type "NSS for Firewall, Cloud & Branch Connector" is exists.
    Then verify "Test NSS for Web" NSS Server for type "NSS for Web" is exists.

  # XC-9776
  Scenario: To verify adding NSS Feed with type NSS for Web
    Then Delete the NSS Feed "Test Feed" by using UI
    When Add NSS Feed with name "Test Feed" type "NSS for Web" log type "Alert" siem ip "127.0.0.1" and siem port "443"
    Then Save the NSS Feed form 
    Then Verify changes are saved successfully
    Then Delete the NSS Feed "Test Feed" by using UI

  # XC-9777
  Scenario: To verify adding NSS Feed with type NSS for Firewall, Cloud & Branch Connector
    Then Delete the NSS Feed "Test NSS for Firewall" by using UI
    Then Verify adding NSS Feed "Test NSS for Firewall" with type NSS for Firewall, Cloud & Branch Connector
    Then Delete the NSS Feed "Test NSS for Firewall" by using UI

  #XC-9778 
  Scenario: To verify adding NSS Feed with type NSS for Email DLP
    Then Delete the NSS Feed "Test feed for endpoint DLP" by using UI
    Then Verify adding NSS Feed "Test feed for endpoint DLP" with type NSS for Email DLP
    Then Delete the NSS Feed "Test feed for endpoint DLP" by using UI
  #XC-9779 
  Scenario: To verify adding NSS Feed with type NSS for Endpoint DLP
    Then Delete the NSS Feed "Test feed for endpoint DLP" by using UI
    Then Verify adding NSS Feed "Test feed for endpoint DLP" with type NSS for Endpoint DLP
    Then Delete the NSS Feed "Test feed for endpoint DLP" by using UI

  #XC-9780 
  Scenario: To verify adding NSS Feed for SaaS Security with Log Type SaaS Security
    Then Delete the NSS Feed "Test Feed for Saas Security" by using UI
    Then Verify adding NSS Feed "Test Feed for Saas Security" for SaaS Security with Log Type SaaS Security
    Then Delete the NSS Feed "Test Feed for Saas Security" by using UI

  #XC-9825 
  Scenario: To verify adding NSS Feed with Log Type SaaS Security Activity
    Then Delete the NSS Feed "Test Feed for Saas Security Activity" by using UI
    Then Verify adding NSS Feed "Test Feed for Saas Security Activity" with Log Type SaaS Security Activity
    Then Delete the NSS Feed "Test Feed for Saas Security Activity" by using UI

  #XC-9826 
  Scenario: To verify adding NSS Feed with Log Type Tunnel
    Then Delete the NSS Feed "Test Feed for TunnelLog" by using UI
    Then Verify adding NSS Feed "Test Feed for TunnelLog" with Log Type Tunnel
    Then Delete the NSS Feed "Test Feed for TunnelLog" by using UI

  #XC-9827
  Scenario: To verify adding NSS Feed for Firewall, Cloud & Branch Connector with Log Type DNS and then cancel it
    Then Delete the NSS Feed "Test Feed for DNS Type" by using UI
    Then Verify adding NSS Feed "Test Feed for DNS Type" with type NSS for Firewall, Cloud & Branch Connector and Log Type DNS
    Then Delete the NSS Feed "Test Feed for DNS Type" by using UI

  #XC-9828
  Scenario: To verify adding NSS Feed for Firewall, Cloud & Branch Connector with Log Type Alerts
    Then Delete the NSS Feed "Test Feed for Alert Type" by using UI
    Then Verify adding NSS Feed "Test Feed for Alert Type" with type NSS for Firewall, Cloud & Branch Connector and Log Type Alerts
    Then Delete the NSS Feed "Test Feed for Alert Type" by using UI

  #XC-9829 
  Scenario: To verify adding NSS Feed for Firewall, Cloud & Branch Connector with Log Type Firewall Logs
    Then Delete the NSS Feed "Test Feed for Firewal logs" by using UI
    Then Verify adding NSS Feed "Test Feed for Firewal logs" with type NSS for Firewall, Cloud & Branch Connector and Log Type Firewall Logs
    Then Delete the NSS Feed "Test Feed for Firewal logs" by using UI

  #XC-9830
  Scenario: To verify search functionality on the NSS Feed grid
    # Pre-cleanup
    Then Delete the NSS Feed "Test Feed for search" by using UI
    # Add the feed to search for
    When Add NSS Feed with name "Test Feed for search" type "NSS for Web" log type "Alert" siem ip "127.0.0.1" and siem port "443"
    Then Save the NSS Feed form
    Then Verify changes are saved successfully
    # Search and verify
    When Search the test feed "Test Feed for search" from the NSS Feed grid
    Then Verify feed "Test Feed for search" is visible in the grid
    # Post-cleanup
    Then Delete the NSS Feed "Test Feed for search" by using UI


  ## Scenario: To Delete the NSS Feed by using API
  ##   When Search the test feed "NSS_02_Feed" from the NSS Feed grid
  ##   Then Delete the NSS Feed "NSS_02_Feed" by using API
