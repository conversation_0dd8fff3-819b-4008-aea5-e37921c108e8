@ZIA_admin @subclouds @k
Feature: SubClouds Management
  As a user,
  I want to verify the UI elements and functionality of the SubClouds page
  So that I can ensure its usability and correctness.

  Background:
    Given User navigates to the SubClouds screen

  Scenario: XC-10336: Verify UI elements and steps for SubClouds
    Then I should see the page title as "Subclouds"
    And I should see the search bar present on the page
