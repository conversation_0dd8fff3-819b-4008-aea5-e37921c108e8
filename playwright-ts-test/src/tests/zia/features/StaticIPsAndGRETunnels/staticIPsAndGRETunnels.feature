@ZIA_admin @ZIA_StaticIPsGRETunnels
Feature: To verify Static IPs & GRE Tunnels functionality

  Scenario: To verify the Add Static IPs In Static IPs & GRE Tunnels Module
   Given User navigates to the Static IPs & GRE Tunnels screen
   When Click On Add Static IP Button
   Then Verify User Trying to Create Static IP "***********" and Description "Automation Static IP" Inside Wizard
   Then Verify User Provide Region For Static IP Configuration
   Then Click On "Static IP Configuration" Header Save & Verify Notification Message In Static IPs & GRE Tunnels

  Scenario: To verify the Edit Static IPs In Static IPs & GRE Tunnels Module
   Given User navigates to the Static IPs & GRE Tunnels screen
   When Search the created Static IPs "***********"
   Then Click On StaticIP "***********" Menu Button "pencil" Option In Static IPs & GRE Tunnels
   Then Verify User Trying to Edit Static IP
   |     labelName  |            inputText             |
   |    Description |  Update Automation Static IP     |
   Then Click On "Static IP Configuration" Header Save & Verify Notification Message In Static IPs & GRE Tunnels

  Scenario: To verify the GRE Tunnel Configuration In Static IPs & GRE Tunnels Module
   Given User navigates to the Static IPs & GRE Tunnels screen
   When Click On Add GRE Tunnel Configuration Button And Verify Wizard Header
   Then Click On Static IP Address Dropdown & Select StaticIP "***********" In GRE Tunnels
   Then Provide Data Center In Add GRE Tunnel Configuration
   Then Select Internal IP Range In Add GRE Tunnel Configuration
   Then Click On "GRE Tunnel Configuration" Header Save & Verify Notification Message In Static IPs & GRE Tunnels

  Scenario: To verify the Edit GRE Tunnel Configuration In Static IPs & GRE Tunnels Module
   Given User navigates to the Static IPs & GRE Tunnels screen
   When Click On GRE Tunnel Tab & Search the created GRE Tunnels "***********"
   And Click On StaticIP "***********" Menu Button "pencil" Option In Static IPs & GRE Tunnels
   Then Verify User Perform Update GRE Tunnel Configuration
   |     labelName  |            inputText                  |
   |    Description |  Update Automation GRE Tunnels IP     |
   Then Verify Update Data Center deatils In GRE Tunnel Configuration
   Then Verify Update Internal GRE IP Range In GRE Tunnel Configuration
   Then Click On "GRE Tunnel Configuration" Header Save & Verify Notification Message In Static IPs & GRE Tunnels

  Scenario: To verify the Delete GRE Tunnel Configuration In Static IPs & GRE Tunnels Module
   Given User navigates to the Static IPs & GRE Tunnels screen
   When Click On GRE Tunnel Tab & Search the created GRE Tunnels "***********"
   And Click On StaticIP "***********" Menu Button "pencil" Option In Static IPs & GRE Tunnels
   Then Click On "GRE Tunnel Configuration" Header Delete & Verify Notification Message In Static IPs & GRE Tunnels

  Scenario: To verify the Delete Static IP In Static IPs & GRE Tunnels Module
   Given User navigates to the Static IPs & GRE Tunnels screen
   When Search the created Static IPs "***********"
   Then Click On StaticIP "***********" Menu Button "pencil" Option In Static IPs & GRE Tunnels
   Then Click On "Static IP Configuration" Header Delete & Verify Notification Message In Static IPs & GRE Tunnels

  Scenario: To verify the Delete Static IP In Static IPs & GRE Tunnels Module
   Given User navigates to the Static IPs & GRE Tunnels screen
   When Search the created Static IPs "***********"
   Then Click Import CSV Button & Verify Notification Message In Static IPs & GRE Tunnels

