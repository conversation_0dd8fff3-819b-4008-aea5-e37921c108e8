@ZIA_admin @bandwidth_control @k
Feature: Bandwidth Control
  This feature verifies the functionality of all controls within the Bandwidth Control screen.

  #XC-9664
  Scenario: Verify Bandwidth Control - Search functionality
    Given User navigates to the Bandwidth Control screen
    # Then verify that the "AAA_Test" record exists
    Then Verify Bandwidth Control search functionality with search term "AAA" and expected filter text "AAA_Test"

  #XC-9665
  Scenario: Verify Bandwidth Control - Sort functionality
    Given User navigates to the Bandwidth Control screen
    Then Verify Bandwidth Control sort functionality for rule "AAA_Test"

  #XC-9666
  Scenario: Verify Bandwidth Control - Rule Label click
    Given User navigates to the Bandwidth Control screen
    Then Verify Bandwidth Control rule label click for rule "AAA_Test"

  #XC-9667
  Scenario: Verify Bandwidth Control - Rule Order click
    Given User navigates to the Bandwidth Control screen
    Then Verify Bandwidth Control rule order click expecting first rule order "1"

  #XC-9668
  Scenario: Verify Bandwidth Control - Default Rule
    Given User navigates to the Bandwidth Control screen
    Then Verify Bandwidth Control default rule selection for rule "Default" with initial min bandwidth "5" max bandwidth "95" expecting "5 - 95%" and reset min bandwidth "0" max bandwidth "100" expecting "0 - 100%"

  #XC-9669
  Scenario: Verify Bandwidth Control - Bandwidth Rule all fields
    Given User navigates to the Bandwidth Control screen
    Then Verify Bandwidth Control add new bandwidth rule with the following details:
      | Rule Order | Rule Name   | Admin Rank | Rule Status | Bandwidth Class | Location Group    |
      | 1          | test rule   | 0          | Enabled     | random          | IoT Traffic Group |

  #XC-9670
  Scenario: Verify Bandwidth Control - Copy new Bandwidth Rule
    Given User navigates to the Bandwidth Control screen
    Then Verify Bandwidth Control copy bandwidth rule created with the following details:
      | Rule Order | Rule Name        | Admin Rank | Rule Status | Bandwidth Class | Location Group |
      | 1          | rule to be copied | 0          | Enabled     | CopyBC          | CopyLG         |

  #XC-9671
  Scenario: Verify Bandwidth Control - Add Bandwidth Classes in wizard
    Given User navigates to the Bandwidth Control screen
    Then Verify Bandwidth Control add classes in wizard for a new rule with order "1", name "WizardClassRule", admin rank "0", status "Enabled"

  #XC-9673
  Scenario: Verify Bandwidth Control - Search in drop-downs within wizard
    Given User navigates to the Bandwidth Control screen
    Then Verify Bandwidth Control dropdown search in wizard for location group "Unassigned Locations" and protocol "DNS Over HTTPS"

  #XC-9674
  Scenario: Verify Bandwidth Control - Click Recommended Policy
    Given User navigates to the Bandwidth Control screen
    Then Verify Bandwidth Control click recommended policy link "Recommended Policy" and verify popup title "View Recommended Bandwidth Control Policy" and content "Configure Bandwidth Control Policy"