import { defineConfig, devices } from '@playwright/test';
import { defineBddConfig } from 'playwright-bdd';
import { loginProjects } from './projects/login.projects';
import { uiProjects } from './projects/ui.projects';
import { ziaProjects } from './projects/zia.projects';
import { ztwProjects } from './projects/ztw.projects';
export default defineConfig({
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 0 : 0,
  workers: undefined,
  reporter: [
    ['list'],
    ['html'],
    ['allure-playwright', { outputFolder: 'allure-results' }]
  ],
  use: {
    trace: 'retain-on-failure',
    headless: true,
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    launchOptions: {
      slowMo: process.env.CI ? 500 : 500,
    },
    actionTimeout: 30000,
    navigationTimeout: 30000,
  },
  projects: [
    ...loginProjects,
    ...uiProjects,
    ...ziaProjects,
    ...ztwProjects,
  ],
});