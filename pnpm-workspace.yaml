# pnpm-workspace.yaml
packages:
  # all packages in subdirs of packages/ and components/
  - "packages/**/*"
  # test automation
  - "test-automation"
  # exclude packages that are inside test directories
  - "!**/templates/**"
  # ignore deprecated pacakges
  - "!**/deprecated/**"
  # exclude example packages
  # - "!**/examples/**"
  # exclude mockery
  - "!**/mockery/**"

catalogs:
  vite:
    "vite": "6.3.5"
    "@vitejs/plugin-react": "4.5.0"
    "vite-plugin-dts": "4.5.4"
  vitest:
    "vitest": "3.2.4"
    "@vitest/ui": "3.2.4"
    "@vitest/coverage-istanbul": "3.2.4"
  storybook:
    "@chromatic-com/storybook": "3.2.4"
    "@storybook/addon-designs": 8.2.0
    "@storybook/addon-essentials": 8.6.0
    "@storybook/addon-interactions": 8.6.0
    "@storybook/addon-links": 8.6.0
    "@storybook/addon-mdx-gfm": 8.6.0
    "@storybook/nextjs": 8.6.0
    "@storybook/react": 8.6.0
    "storybook": "8.6.0"
    "@storybook/addon-onboarding": "8.6.0"
    "@storybook/addon-styling-webpack": "1.0.1"
    "@storybook/addon-webpack5-compiler-swc": "2.0.0"
    "@storybook/blocks": "8.6.0"
    "@storybook/react-vite": "8.6.0"
    "@storybook/react-webpack5": "8.6.0"
    "@storybook/test": "8.6.0"
  nimbus:
    "@zs-nimbus/core": "1.2.0"
    "@zs-nimbus/dataviz-colors": "1.1.0"
    "@zs-nimbus/foundations": "1.3.0"
  playwright:
    "playwright-bdd": "^8.3.1"
    "@playwright/test": "1.54.1"