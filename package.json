{"name": "zuep-ui", "private": "true", "scripts": {"cli": "pnpm --filter @xc/cli", "one-ui": "pnpm --filter one-ui", "test": "pnpm --filter test", "std": "pnpm --filter std", "telemetry": "pnpm --filter std", "components": "pnpm --filter @up/components", "legacy-components": "pnpm --filter legacy-components", "feature-flags": "pnpm --filter feature-flags", "navigation": "pnpm --filter @up/navigation", "stores": "pnpm --filter @up/stores", "doc-site": "pnpm --filter @xc/docs-site", "prelint": "pnpm --filter='!./packages/xc/tools/**' --filter='!./packages/examples/**' --filter=!one-ui --filter=!@xc/docs-site build", "test:packages": "pnpm --filter='./packages/platform/**' --filter='./packages/platform/xc/**' --filter='!./packages/xc/common/**' --filter='!./packages/platform/telemetry/**' --filter='!./packages/xc/legacy-components/**' --filter='./packages/xc/doc-site/**' test", "prepare": "husky", "verify": "pnpm --filter components --filter legacy-components --filter std build && pnpm -r verify", "scaffold": "pnpm --filter @xc/cli scaffold:package", "routing:breadcrumbs": "pnpm --filter one-ui routing:breadcrumbs", "xc-e2e": "pnpm --filter @xc/e2e"}, "volta": {"node": "22.17.0", "pnpm": "10.10.0"}, "devDependencies": {"@xc/cli": "workspace:*", "@commitlint/cli": "19.7.1", "@commitlint/config-conventional": "19.7.1", "husky": "9.1.7"}, "pnpm": {"overrides": {"esbuild": "0.23.0", "@opentelemetry/api": "1.9.0"}}, "prettier": {"singleQuote": false, "arrowParens": "always", "bracketSameLine": false, "endOfLine": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": false, "printWidth": 80, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "tabWidth": 2, "trailingComma": "all"}, "resolutions": {"@types/react": "18.3.12"}}